/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Document
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Integer docType;
/*     */   private String docFormat;
/*     */   private String docIssueCountry;
/*     */   private String docID;
/*     */   private String docHolderNationality;
/*     */   private String expireDate;
/*     */ 
/*     */   public Integer getDocType()
/*     */   {
/*  43 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(Integer docType)
/*     */   {
/*  50 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocFormat()
/*     */   {
/*  57 */     return this.docFormat;
/*     */   }
/*     */ 
/*     */   public void setDocFormat(String docFormat)
/*     */   {
/*  64 */     this.docFormat = docFormat;
/*     */   }
/*     */ 
/*     */   public String getDocIssueCountry()
/*     */   {
/*  71 */     return this.docIssueCountry;
/*     */   }
/*     */ 
/*     */   public void setDocIssueCountry(String docIssueCountry)
/*     */   {
/*  78 */     this.docIssueCountry = docIssueCountry;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/*  85 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/*  92 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getDocHolderNationality()
/*     */   {
/*  99 */     return this.docHolderNationality;
/*     */   }
/*     */ 
/*     */   public void setDocHolderNationality(String docHolderNationality)
/*     */   {
/* 106 */     this.docHolderNationality = docHolderNationality;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/* 113 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/* 120 */     this.expireDate = expireDate;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.input.Document
 * JD-Core Version:    0.6.0
 */