/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CarrierInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8758587550808827387L;
/*    */   private String airline;
/*    */   private String flightNumber;
/*    */   private String flightSuffix;
/*    */   private String rbdCode;
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 46 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 53 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ 
/*    */   public String getFlightSuffix()
/*    */   {
/* 60 */     return this.flightSuffix;
/*    */   }
/*    */ 
/*    */   public void setFlightSuffix(String flightSuffix)
/*    */   {
/* 67 */     this.flightSuffix = flightSuffix;
/*    */   }
/*    */ 
/*    */   public String getRbdCode()
/*    */   {
/* 74 */     return this.rbdCode;
/*    */   }
/*    */ 
/*    */   public void setRbdCode(String rbdCode)
/*    */   {
/* 81 */     this.rbdCode = rbdCode;
/*    */   }
/*    */ 
/*    */   public String getAirline()
/*    */   {
/* 88 */     return this.airline;
/*    */   }
/*    */ 
/*    */   public void setAirline(String airline)
/*    */   {
/* 95 */     this.airline = airline;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.CarrierInfo
 * JD-Core Version:    0.6.0
 */