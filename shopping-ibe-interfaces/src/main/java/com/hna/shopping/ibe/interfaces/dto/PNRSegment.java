package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

//import com.travelsky.ibe.client.pnr.PNRObjectInterface;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=true)
//@NoArgsConstructor
@ApiModel
public class PNRSegment extends BaseSegment {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7296624866983625173L;

	
	  private Date arrivalTime;
	 
	  private String selectedFlag = "";

	  private String departureTerm = "--";

	  private String arrivalTerm = "--";

	  private String subClass = "";

	  private boolean codeShare = false;
	  private String operateCarrier;
	  private boolean etktairseg = false;
	  private int index;
	  private String originalAirSeg = null;
	
	  private String textInPNR = "N/A";
	  
//		@Override
//		public int getIndex() {
//			// TODO Auto-generated method stub
//			return this.lineIndex;
//		}
//		public void setIndex(int index) {  
//			  this.lineIndex = index;          
//			}                                  


//		@Override
//		public String getPsgrid() throws Exception, UnsupportedOperationException {
//			// TODO Auto-generated method stub
//			throw new UnsupportedOperationException("这个类型的条目不支持指定适用旅客序号");
//		}

	  
	  public String toString()                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
	  {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
	    StringBuffer strtmp = new StringBuffer();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
	    if (getIndex() < 0) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   
	      strtmp.append("   ");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
	    } else {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
	      if (getIndex() < 10)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
	        strtmp.append(" ");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
	      strtmp.append(getIndex() + ". ");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
	    }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
	    strtmp.append(getAirNo() + " " + getFltClass() + " " + getOrgCity() + getDesCity() + " " + getActionCode() + getTktNum() + " " + getDepartureTimeString() + " " + getArrivalTimeString() + ((isEtktairseg()) ? " 电子票" : "       ") + ((isSkChanged()) ? " 变更" : "     ") + ((isCodeShare()) ? "  代码共享航班由" + this.operateCarrier + "执行" : "                          "));                                                                                                                                                                                  
	    strtmp.append("  离港航站楼:" + this.departureTerm + "  到港航站楼:" + this.arrivalTerm + (((this.subClass != null) && (this.subClass.length() > 0)) ? " subClass" + this.subClass : ""));                                                                                                                                                                                                                                                                                                                                                                              
	    if ((getSelectedFlag() != null) && (getSelectedFlag().length() > 0))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
	      strtmp.append(" 选择出票标识:" + getSelectedFlag());                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
	    if (getType() == 2)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
	      strtmp.append("  普通航段\n");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
	    else if (getType() == 1)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
	      strtmp.append("  信息航段\n");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
	    else if (getType() == 3) {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
	      strtmp.append("  开放航段\n");                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
	    }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
	    strtmp.append(this.originalAirSeg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
	    return strtmp.toString();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
	  }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
	  public String getArrivalTimeString() 
	  {                                    
	    if (this.arrivalTime == null) {    
	      return null;                     
	    }                                  
	    return this.arrivalTime.toString();
	  }                                    

	  public String getDepartureTimeString()   
	  {                                        
	    if (this.getDepartureTime() == null) {      
	      return null;                         
	    }                                      
	    return this.getDepartureTime().toString();  
	  }


//	  public String getTextInPNR()
//	  {                           
//	    return this.textInPnr; }  
                              

	
}
