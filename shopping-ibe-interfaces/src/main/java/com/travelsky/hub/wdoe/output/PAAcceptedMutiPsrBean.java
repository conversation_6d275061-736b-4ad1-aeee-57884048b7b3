/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PAAcceptedMutiPsrBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7120967969318665992L;
/* 18 */   private String seatNumber = "";
/*    */   private String bordingStreams;
/* 28 */   private String boardingNumber = "";
/*    */ 
/* 33 */   private String surName = "";
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 40 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String i)
/*    */   {
/* 47 */     this.seatNumber = i;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 53 */     this.seatNumber = "";
/* 54 */     this.boardingNumber = "";
/* 55 */     this.bordingStreams = null;
/*    */   }
/*    */ 
/*    */   public String getBoardingNumber()
/*    */   {
/* 63 */     return this.boardingNumber;
/*    */   }
/*    */ 
/*    */   public void setBoardingNumber(String i)
/*    */   {
/* 70 */     this.boardingNumber = i;
/*    */   }
/*    */ 
/*    */   public String getBordingStreams()
/*    */   {
/* 77 */     return this.bordingStreams;
/*    */   }
/*    */ 
/*    */   public void setBordingStreams(String bordingStreams)
/*    */   {
/* 84 */     this.bordingStreams = bordingStreams;
/*    */   }
/*    */ 
/*    */   public String getSurName()
/*    */   {
/* 91 */     return this.surName;
/*    */   }
/*    */ 
/*    */   public void setSurName(String surName)
/*    */   {
/* 98 */     this.surName = surName;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PAAcceptedMutiPsrBean
 * JD-Core Version:    0.6.0
 */