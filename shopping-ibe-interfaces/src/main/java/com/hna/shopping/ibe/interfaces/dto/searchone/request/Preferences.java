
package com.hna.shopping.ibe.interfaces.dto.searchone.request;
import java.util.List;

import lombok.Data;
@Data
public class Preferences {

    private Cabins cabins = new Cabins();
    private Boolean changeable = null;
    private Boolean refundable = null;
    private Boolean upgradeable = null;
    private List<String> faresAllowedCarriers = null;
    private List<String> flightsAllowedOperatingCarriers = null;
    private boolean noCodeshare = false;
    private String allowedStops = null;
    private String lowestFareMode = null;

}