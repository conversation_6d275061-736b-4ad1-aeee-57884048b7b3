/*     */ package com.travelsky.hub.model.peentity.trr.query.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SemantVo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2821231495540847894L;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String flightDateTime;
/*     */   private String flightNo;
/*     */   private String arrivalDateTime;
/*     */   private String termDep;
/*     */   private String cabin;
/*     */   private String isVVIP;
/*     */   private String termArr;
/*     */   private String isUse;
/*     */   private String isSelf;
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  76 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  83 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  90 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  97 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightDateTime()
/*     */   {
/* 104 */     return this.flightDateTime;
/*     */   }
/*     */ 
/*     */   public void setFlightDateTime(String flightDateTime)
/*     */   {
/* 111 */     this.flightDateTime = flightDateTime;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 117 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 124 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 131 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 138 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getArrivalDateTime()
/*     */   {
/* 144 */     return this.arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalDateTime(String arrivalDateTime)
/*     */   {
/* 151 */     this.arrivalDateTime = arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public String getTermArr()
/*     */   {
/* 158 */     return this.termArr;
/*     */   }
/*     */ 
/*     */   public void setTermArr(String termArr)
/*     */   {
/* 165 */     this.termArr = termArr;
/*     */   }
/*     */ 
/*     */   public String getTermDep()
/*     */   {
/* 172 */     return this.termDep;
/*     */   }
/*     */ 
/*     */   public void setTermDep(String termDep)
/*     */   {
/* 179 */     this.termDep = termDep;
/*     */   }
/*     */ 
/*     */   public String getIsSelf()
/*     */   {
/* 186 */     return this.isSelf;
/*     */   }
/*     */ 
/*     */   public void setIsSelf(String isSelf)
/*     */   {
/* 193 */     this.isSelf = isSelf;
/*     */   }
/*     */ 
/*     */   public String getIsVVIP()
/*     */   {
/* 200 */     return this.isVVIP;
/*     */   }
/*     */ 
/*     */   public void setIsVVIP(String isVVIP)
/*     */   {
/* 207 */     this.isVVIP = isVVIP;
/*     */   }
/*     */ 
/*     */   public String getIsUse()
/*     */   {
/* 214 */     return this.isUse;
/*     */   }
/*     */ 
/*     */   public void setIsUse(String isUse)
/*     */   {
/* 221 */     this.isUse = isUse;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.SemantVo
 * JD-Core Version:    0.6.0
 */