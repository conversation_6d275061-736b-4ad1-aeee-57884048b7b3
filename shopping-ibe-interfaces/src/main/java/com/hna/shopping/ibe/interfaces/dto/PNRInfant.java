package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class P<PERSON>Infant extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 44559409724065493L;

	 private Date brith;                                                                    
	 private String carrier;                                                                
	 private String name;                                                                   
	 private String nameInPnr;                                                              
	 private int index;                                                                         
	                                                                                        
	 public PNRInfant()                                                                     
	   throws Exception                                                                     
	 {                                                                                      
	 }                                                                                      
	                                                                                        
	 public PNRInfant(Date brith, String carrier, String name)                              
	   throws Exception                                                                     
	 {                                                                                      
	   this.brith = brith;                                                                  
	   this.carrier = carrier;                                                              
	   this.name = name;                                                                    
	 }                                                                                      
	                                                                                        
//	 public PNRInfant(String brith, String carrier, String name)                            
//	   throws Exception                                                                     
//	 {                                                                                      
//	   try                                                                                  
//	   {                                                                                    
//	     setBrith(brith);                                                                   
//	     this.carrier = carrier;                                                            
//	     this.name = name;                                                                  
//	   } catch (Exception e) {                                                              
//	     throw e;                                                                           
//	   }                                                                                    
//	 }                                                                                      
//	                                                                                        
//	 void setBrith(Date brith)                                                              
//	 {                                                                                      
//	   this.brith = brith;                                                                  
//	 }                                                                                      
//	                                                                                        
//	 void setBrith(String brith) throws Exception                                           
//	 {                                                                                      
//	   try {                                                                                
//	     this.brith = QDateTime.stringToDate(brith, "YYYYMMDD");                            
//	   } catch (Exception e) {                                                              
//	     throw e;                                                                           
//	   }                                                                                    
//	 }                                                                                      
//	                                                                                        
//	 /** @deprecated */                                                                     
//	 public Date getBrith()                                                                 
//	 {                                                                                      
//	   return this.brith;                                                                   
//	 }                                                                                      
//	                                                                                        
//	 public Date getBirth()                                                                 
//	 {                                                                                      
//	   return this.brith;                                                                   
//	 }                                                                                      
//	                                                                                        
//	 /** @deprecated */                                                                     
//	 public String getBrithString()                                                         
//	 {              
//		 if(null==this.brith)
//			 return "";
//	   return this.brith.toString();                                                        
//	 }                                                                                      
//	                                                                                        
//	 public String getBirthString()                                                         
//	 {            
//		 if(null==this.brith)
//			 return "";
//	   return this.brith.toString();                                                        
//	 }                                                                                      
//	                                                                                        
//	 void setCarrier(String carrier) {                                                      
//	   this.carrier = carrier;                                                              
//	 }                                                                                      
//	                                                                                        
//	 public String getCarrier()                                                             
//	 {                                                                                      
//	   return this.carrier;                                                                 
//	 }                                                                                      
//	                                                                                        
//	 void setName(String name) {                                                            
//	   this.name = name;                                                                    
//	 }                                                                                      
//	                                                                                        
//	 public String getName()                                                                
//	 {                                                                                      
//	   return this.name;                                                                    
//	 }                                                                                      
//	                                                                                        
//                                                                               
//	 public String getNameInPnr()                                                           
//	 {                                                                                      
//	   return this.nameInPnr;                                                               
//	 }                                                                                      
//	                                                                                        
//	 void setNameInPnr(String nameInPnr) {                                                  
//	   this.nameInPnr = nameInPnr;                                                          
//	 }                                                                                      
//	                                                                                        
//	 public String getPsgrid() throws Exception, UnsupportedOperationException              
//	 {                                                                                      
//	   return getCarrier();                                                                 
//	 }                                                                                      


}
