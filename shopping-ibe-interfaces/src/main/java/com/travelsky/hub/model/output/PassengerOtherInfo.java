/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerOtherInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3269649768103141443L;
/*    */   private String itemCode;
/*    */   private String itemKeyword;
/*    */   private String itemRemarks;
/*    */ 
/*    */   public String getItemCode()
/*    */   {
/* 39 */     return this.itemCode;
/*    */   }
/*    */ 
/*    */   public void setItemCode(String itemCode)
/*    */   {
/* 47 */     this.itemCode = itemCode;
/*    */   }
/*    */ 
/*    */   public String getItemKeyword()
/*    */   {
/* 54 */     return this.itemKeyword;
/*    */   }
/*    */ 
/*    */   public void setItemKeyword(String itemKeyword)
/*    */   {
/* 61 */     this.itemKeyword = itemKeyword;
/*    */   }
/*    */ 
/*    */   public String getItemRemarks()
/*    */   {
/* 68 */     return this.itemRemarks;
/*    */   }
/*    */ 
/*    */   public void setItemRemarks(String itemRemarks)
/*    */   {
/* 75 */     this.itemRemarks = itemRemarks;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerOtherInfo
 * JD-Core Version:    0.6.0
 */