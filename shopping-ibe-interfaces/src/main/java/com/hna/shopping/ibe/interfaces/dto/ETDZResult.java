package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.List;

public class ETDZResult implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8397527565514135950L;
	String cmd;                                                                            
	boolean openet = false;                                                                
	List multiResps;                                                                       
	String pnrno;                                                                          
	String[] currency;                                                                     
	double[] amount;                                                                       
	String startTktno;                                                                     
	String endTktno;                                                                       
	private String originalMsg;                                                            
	private boolean leagalResp = false;                                                    
	                                                                                       
	public String getCmd()                                                                 
	{                                                                                      
	  return this.cmd; }                                                                   
	                                                                                       
	public void setCmd(String cmd) {                                                       
	  this.cmd = cmd;                                                                      
	}                                                                                      
	                                                                                       
	                                                                            
	                                                                                       
	public String toString() {                                                             
	  StringBuffer sb = new StringBuffer();                                                
	  for (int i = 0; i < this.currency.length; ++i) {                                     
	    sb.append("\r\ncurrency:" + this.currency[i]);                                     
	    sb.append("\r\namount:" + new DecimalFormat("0.00").format(this.amount[i]));       
	  }                                                                                    
	  sb.append("\r\nfirsttkt's tn(s):" + this.startTktno);                                
	  sb.append("\r\nlasttkt's tn(s):" + this.endTktno);                                   
	  sb.append("\r\npnrno:" + this.pnrno);                                                
	  sb.append((this.openet) ? "\r\nOPENET" : "");                                        
	  sb.append("\r\n打票指令:" + this.cmd);                                               
	  sb.append("\r\n原文:" + this.originalMsg);                                           
	  sb.append("\r\nALL RESPS:\r\n");                                                     
	  for (int i = 0; i < ((this.multiResps == null) ? 0 : this.multiResps.size()); ++i)       
	    sb.append(this.multiResps.get(i) + "\r\n");                                        
	  return sb.toString();                                                                
	}                                                                                      
	                                                                                       
	public List getMultiResps()                                                            
	{                                                                                      
	  return this.multiResps; }                                                            
	                                                                                       
	public void setMultiResps(List multiResps) {                                           
	  this.multiResps = multiResps; }                                                      
	                                                                                       
	public boolean isOpenet() {                                                            
	  return this.openet; }                                                                
	                                                                                       
	public void setOpenet(boolean openet) {                                                
	  this.openet = openet;                                                                
	}                                                                                      
	                                                                                       
	public boolean isLeagalResp()                                                          
	{                                                                                      
	  return this.leagalResp; }                                                            
	                                                                                       
	public void setLeagalResp(boolean leagalResp) {                                        
	  this.leagalResp = leagalResp; }                                                      
	                                                                                       
	public String getOriginalMsg() {                                                       
	  return this.originalMsg; }                                                           
	                                                                                       
	public void setOriginalMsg(String originalMsg) {                                       
	  this.originalMsg = originalMsg;                                                      
	}                                                                                      
	                                                                                       
	public double[] getAmount()                                                            
	{                                                                                      
	  return this.amount;                                                                  
	}                                                                                      
	                                                                                       
	public void setAmount(double[] amount)                                                 
	{                                                                                      
	  this.amount = amount;                                                                
	}                                                                                      
	                                                                                       
	public String[] getCurrency()                                                          
	{                                                                                      
	  return this.currency;                                                                
	}                                                                                      
	                                                                                       
	public void setCurrency(String[] currency)                                             
	{                                                                                      
	  this.currency = currency;                                                            
	}                                                                                      
	                                                                                       
	public String getEndTktno()                                                            
	{                                                                                      
	  return this.endTktno;                                                                
	}                                                                                      
	                                                                                       
	public void setEndTktno(String endTktno)                                               
	{                                                                                      
	  this.endTktno = endTktno;                                                            
	}                                                                                      
	                                                                                       
	public String getPnrno()                                                               
	{                                                                                      
	  return this.pnrno;                                                                   
	}                                                                                      
	                                                                                       
	public void setPnrno(String pnrno)                                                     
	{                                                                                      
	  this.pnrno = pnrno;                                                                  
	}                                                                                      
	                                                                                       
	public String getStartTktno()                                                          
	{                                                                                      
	  return this.startTktno;                                                              
	}                                                                                      
	                                                                                       
	public void setStartTktno(String startTktno)                                           
	{                                                                                      
	  this.startTktno = startTktno;                                                        
	}                                                                                      
	                                                                                       
	public static void main(String[] args)                                                 
	{                                                                                      
	}                                                                                      

}
