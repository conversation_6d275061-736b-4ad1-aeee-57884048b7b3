package com.hna.shopping.ibe.interfaces.dto;

import java.util.List;

import com.google.common.collect.Lists;

public class SSResult extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3933750211140685288L;
	private List<SSSegment> segments = Lists.newArrayList();                                                                                                                                                                              
	private List<String> comments = Lists.newArrayList();                                                                                                                                                                              
	private String pnrno;                                                                                                                                                                                                
	 private String attl = "";                                                                                                                                                                                    
	                                                                                                                                                                                                              
	 public void addSegment(SSSegment seg)                                                                                                                                                                        
	 {                                                                                                                                                                                                            
	   this.segments.add(seg); }                                                                                                                                                                           
	                                                                                                                                                                                                              
	 public void addComment(String comment) {                                                                                                                                                                     
	   if (comment.startsWith("-"))                                                                                                                                                                               
	     comment = comment.substring(1);                                                                                                                                                                          
	   if (comment.trim().length() == 0)                                                                                                                                                                          
	     return;                                                                                                                                                                                                  
	   this.comments.add(comment); }                                                                                                                                                                       
	                                                                                                                                                                                                              
	 public void setPnrno(String pnr) {                                                                                                                                                                           
	   this.pnrno = pnr;                                                                                                                                                                                          
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public String getPnrno()                                                                                                                                                                                     
	 {                                                                                                                                                                                                            
	   return this.pnrno;                                                                                                                                                                                         
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public String getCommentAt(int i)                                                                                                                                                                            
	 {                                                                                                                                                                                                            
	   if (i < this.comments.size())                                                                                                                                                                              
	     return ((String)this.comments.get(i));                                                                                                                                                             
	   return null;                                                                                                                                                                                               
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public SSSegment getSegmentAt(int i)                                                                                                                                                                         
	 {                                                                                                                                                                                                            
	   if (i < this.segments.size())                                                                                                                                                                              
	     return ((SSSegment)this.segments.get(i));                                                                                                                                                          
	   return null;                                                                                                                                                                                               
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public int getCommentsCount()                                                                                                                                                                                
	 {                                                                                                                                                                                                            
	   return this.comments.size();                                                                                                                                                                               
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public int getSegmentsCount()                                                                                                                                                                                
	 {                                                                                                                                                                                                            
	   return this.segments.size();                                                                                                                                                                               
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public List<String> getComments()                                                                                                                                                                                  
	 {                                                                                                                                                                                                            
	   return this.comments; }                                                                                                                                                                                    
	                                                                                                                                                                                                              
	 public String toString() {                                                                                                                                                                                   
	   try {                                                                                                                                                                                                      
	     StringBuffer str = new StringBuffer("pnr编号" + this.pnrno);                                                                                                                                             
	     str.append("\r\n航段");                                                                                                                                                                                  
	     for (int i = 0; i < this.segments.size(); ++i) {                                                                                                                                                         
	       SSSegment s = getSegmentAt(i);                                                                                                                                                                         
	       str.append("\r\n" + s.getAirNo() + " " + s.getFltClass() + " " + s.getOrgCity() + s.getDesCity() + " " + s.getActionCode() + s.getTktNum() + " " + s.getDepartureTime() + " " + s.getArrivalTime());   
	       str.append(" subClass:" + s.getSubClass());                                                                                                                                                            
	     }                                                                                                                                                                                                        
	     str.append("\r\n原文(comments)");                                                                                                                                                                        
	     for (int i = 0; i < this.comments.size(); ++i) {                                                                                                                                                             
	       str.append("\r\n" + getCommentAt(i));                                                                                                                                                                  
	     }                                                                                                                                                                                                        
	     str.append("\r\n原文(comments) END");                                                                                                                                                                    
	     if ((this.attl != null) && (this.attl.length() > 0)) {                                                                                                                                                   
	       str.append("\r\nattl:" + this.attl);                                                                                                                                                                   
	     }                                                                                                                                                                                                        
	     return str.toString(); } catch (Exception e) {                                                                                                                                                           
	   }                                                                                                                                                                                                          
	   return super.toString();                                                                                                                                                                                   
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public void setAttl(String attl) {                                                                                                                                                                           
	   this.attl = attl;                                                                                                                                                                                          
	 }                                                                                                                                                                                                            
	                                                                                                                                                                                                              
	 public String getAttl()                                                                                                                                                                                      
	 {                                                                                                                                                                                                            
	   return this.attl;                                                                                                                                                                                          
	 }

	public void setComments(List<String> comments) {
		this.comments = comments;
	}

	public List<SSSegment> getSegments() {
		return segments;
	}                                                                                                                                                                                                            

}
