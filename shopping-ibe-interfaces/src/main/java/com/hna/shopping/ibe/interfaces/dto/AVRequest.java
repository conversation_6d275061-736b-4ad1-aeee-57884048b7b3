package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class AVRequest extends BaseRequest {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2521195611232551064L;
	private String origin; 
	private String destination; 
	@DateTimeFormat( pattern = "yyyy-MM-dd" )
	private Date depart;
	private String carrier;
	private boolean direct = true;
	
}
