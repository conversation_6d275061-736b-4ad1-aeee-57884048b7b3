/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Document
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5392120765774706182L;
/*     */   private String documentTypeID;
/*     */   private String documentNumber;
/*     */   private String issuingCountry;
/*     */   private String surname;
/*     */   private String firstName;
/*     */   private String middleName;
/*     */   private String gender;
/*     */   private String nationality;
/*     */   private String dateOfBirth;
/*     */   private String expiryDate;
/*     */   private String issueDate;
/*     */   private FreeText freeText;
/*     */   private String documentGUID;
/*     */ 
/*     */   public String getDocumentTypeID()
/*     */   {
/*  73 */     return this.documentTypeID;
/*     */   }
/*     */ 
/*     */   public String getDocumentNumber()
/*     */   {
/*  79 */     return this.documentNumber;
/*     */   }
/*     */ 
/*     */   public String getIssuingCountry()
/*     */   {
/*  85 */     return this.issuingCountry;
/*     */   }
/*     */ 
/*     */   public String getSurname()
/*     */   {
/*  91 */     return this.surname;
/*     */   }
/*     */ 
/*     */   public String getFirstName()
/*     */   {
/*  97 */     return this.firstName;
/*     */   }
/*     */ 
/*     */   public String getMiddleName()
/*     */   {
/* 103 */     return this.middleName;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 109 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public String getNationality()
/*     */   {
/* 115 */     return this.nationality;
/*     */   }
/*     */ 
/*     */   public String getDateOfBirth()
/*     */   {
/* 121 */     return this.dateOfBirth;
/*     */   }
/*     */ 
/*     */   public String getExpiryDate()
/*     */   {
/* 127 */     return this.expiryDate;
/*     */   }
/*     */ 
/*     */   public String getIssueDate()
/*     */   {
/* 133 */     return this.issueDate;
/*     */   }
/*     */ 
/*     */   public FreeText getFreeText()
/*     */   {
/* 139 */     return this.freeText;
/*     */   }
/*     */ 
/*     */   public String getDocumentGUID()
/*     */   {
/* 145 */     return this.documentGUID;
/*     */   }
/*     */ 
/*     */   public void setDocumentTypeID(String documentTypeID)
/*     */   {
/* 151 */     this.documentTypeID = documentTypeID;
/*     */   }
/*     */ 
/*     */   public void setDocumentNumber(String documentNumber)
/*     */   {
/* 157 */     this.documentNumber = documentNumber;
/*     */   }
/*     */ 
/*     */   public void setIssuingCountry(String issuingCountry)
/*     */   {
/* 163 */     this.issuingCountry = issuingCountry;
/*     */   }
/*     */ 
/*     */   public void setSurname(String surname)
/*     */   {
/* 169 */     this.surname = surname;
/*     */   }
/*     */ 
/*     */   public void setFirstName(String firstName)
/*     */   {
/* 175 */     this.firstName = firstName;
/*     */   }
/*     */ 
/*     */   public void setMiddleName(String middleName)
/*     */   {
/* 181 */     this.middleName = middleName;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 187 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public void setNationality(String nationality)
/*     */   {
/* 193 */     this.nationality = nationality;
/*     */   }
/*     */ 
/*     */   public void setDateOfBirth(String dateOfBirth)
/*     */   {
/* 199 */     this.dateOfBirth = dateOfBirth;
/*     */   }
/*     */ 
/*     */   public void setExpiryDate(String expiryDate)
/*     */   {
/* 205 */     this.expiryDate = expiryDate;
/*     */   }
/*     */ 
/*     */   public void setIssueDate(String issueDate)
/*     */   {
/* 211 */     this.issueDate = issueDate;
/*     */   }
/*     */ 
/*     */   public void setFreeText(FreeText freeText)
/*     */   {
/* 217 */     this.freeText = freeText;
/*     */   }
/*     */ 
/*     */   public void setDocumentGUID(String documentGUID)
/*     */   {
/* 223 */     this.documentGUID = documentGUID;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Document
 * JD-Core Version:    0.6.0
 */