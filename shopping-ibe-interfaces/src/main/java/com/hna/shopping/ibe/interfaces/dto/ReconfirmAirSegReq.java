package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.PNRAirSeg;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by lm.tang on 2018/8/20.
 * 确认航段的请求参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class ReconfirmAirSegReq extends BaseRequest{

    private String pnrNo;
    //要确认的航段，可以为null
    private PNRAirSeg pnrAirSeg;

}
