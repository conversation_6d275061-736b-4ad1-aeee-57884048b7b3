/*    */ package com.travelsky.hub.model.output.govsecuritycheck;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class GovSecurityCheckOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7161051340415687766L;
/*    */   private String flightNumber;
/*    */   private String flightDate;
/*    */   private String hostNumber;
/*    */   private List<GovSecurityInformation> govSecurityInformations;
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 36 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 43 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ 
/*    */   public String getFlightDate()
/*    */   {
/* 50 */     return this.flightDate;
/*    */   }
/*    */ 
/*    */   public void setFlightDate(String flightDate)
/*    */   {
/* 57 */     this.flightDate = flightDate;
/*    */   }
/*    */ 
/*    */   public String getHostNumber()
/*    */   {
/* 64 */     return this.hostNumber;
/*    */   }
/*    */ 
/*    */   public void setHostNumber(String hostNumber)
/*    */   {
/* 71 */     this.hostNumber = hostNumber;
/*    */   }
/*    */ 
/*    */   public List<GovSecurityInformation> getGovSecurityInformations()
/*    */   {
/* 78 */     return this.govSecurityInformations;
/*    */   }
/*    */ 
/*    */   public void setGovSecurityInformations(List<GovSecurityInformation> govSecurityInformations)
/*    */   {
/* 85 */     this.govSecurityInformations = govSecurityInformations;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.govsecuritycheck.GovSecurityCheckOutput
 * JD-Core Version:    0.6.0
 */