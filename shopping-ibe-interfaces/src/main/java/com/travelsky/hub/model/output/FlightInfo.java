/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5822269410773198489L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String departureDate;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  49 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  56 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  63 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  70 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  77 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  84 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  91 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  98 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 105 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 112 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.FlightInfo
 * JD-Core Version:    0.6.0
 */