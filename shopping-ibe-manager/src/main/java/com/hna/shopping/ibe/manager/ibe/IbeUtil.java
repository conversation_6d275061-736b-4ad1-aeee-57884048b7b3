package com.hna.shopping.ibe.manager.ibe;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeIbe;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.config.ApplicationContextProvider;
import com.hna.shopping.ibe.config.memcached.CacheManagerUtil;
import com.hna.shopping.ibe.config.memcached.CacheName;
import com.hna.shopping.ibe.interfaces.dto.CabinDTO;
import com.hna.shopping.ibe.interfaces.dto.SegmentDTO;
import com.hna.shopping.ibe.interfaces.dto.StopPointDTO;

import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ibe.client.AV;
import com.travelsky.ibe.client.AvItem;
import com.travelsky.ibe.client.AvResult;
import com.travelsky.ibe.client.AvSegment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * DOC
 *
 * <AUTHOR>
 */
@Slf4j
public class IbeUtil {

    public static long MIN_TIME = 2 * 60 * 50 * 1000;//2个小时
    public static long MAX_TIME = 6 * 60 * 50 * 1000;//6个小时

    private static final SimpleDateFormat sdf = new SimpleDateFormat("ddMMM", Locale.ENGLISH);
    ;
    private static final SimpleDateFormat avsdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");


    /**
     * 进行一次 av，不经过缓存，这样以后这块代码便于复用
     *
     * @param airline    主要用来根据航司加载配置
     * @param orgCity
     * @param dstCity
     * @param flightDate
     * @return
     */
    public static List<SegmentDTO> av(String supplier, String airline, String orgCity, String dstCity, Date flightDate, boolean isDirect, String sellerChannels, String allAirline) {
        long t1 = System.currentTimeMillis();
        Validate.isTrue(StringUtils.isNoneBlank(airline));
        Validate.isTrue(StringUtils.isNoneBlank(orgCity));
        Validate.isTrue(StringUtils.isNoneBlank(dstCity));
        Validate.notNull(flightDate);

        if (StringUtils.isEmpty(supplier)) {
            supplier = airline;
        }

        AV av = IbeCmd.getIbeClient(airline, AV.class);
        AvResult avResult;
        try {
            if ("Y".equals(allAirline)) {
                avResult = av.getAvailability(orgCity, dstCity, flightDate);
            } else {
                avResult = av.getAvailability(orgCity, dstCity, flightDate, supplier, isDirect, true);
            }
        } catch (Exception e) {
            log.error("av err supplier=" + supplier + " airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(flightDate) + " sellerChannels=" + (sellerChannels == null ? airline + "|SYS" : sellerChannels));
            throw new GlobalException(CodeIbe.AV_EXCEPTION, e);
        }
        long t2 = System.currentTimeMillis();
        if (t2 - t1 > 4000) {
            log.info("avTime airline={}, supplier={}, orgCity={}, dstCity={}, flightDate={}, t = {}", airline, supplier, orgCity, dstCity, DateUtil.toStringYMD(flightDate), (t2 - t1));
        }
        List<SegmentDTO> ss = toAvResultDTO(av, avResult, orgCity, dstCity, flightDate, supplier, allAirline, sellerChannels);
        long t3 = System.currentTimeMillis();
        if (t3 - t1 > 4000) {
            log.info("avAndToAvResultDTOTime airline={}, supplier={}, orgCity={}, dstCity={}, flightDate={}, t = {}", airline, supplier, orgCity, dstCity, DateUtil.toStringYMD(flightDate), (t3 - t1));
        }
        return ss;
    }


    private static boolean isAllMyFlight(AvItem avItem, String airline) {
        Map<String, String> m = Maps.newHashMap();
        m.put(airline, airline);

        for (int j = 0; j < avItem.getSegmentnumber(); j++) {
            m.put(avItem.getSegment(j).getAirline().substring(0, 2), avItem.getSegment(j).getAirline().substring(0, 2));
        }

        if (m.keySet().size() > 1) return false;

        return true;
    }

    /**
     * avResult 转成 DTO
     *
     * @param av
     * @param avResult
     * @param flightDate
     * @return
     */
    private static List<SegmentDTO> toAvResultDTO(AV av, AvResult avResult, String orgCity, String dstCity, Date flightDate, String airline, String allAirline, String sellerChannels) {
        if (avResult == null) {
            return Lists.newArrayList();
        }
        StringBuffer sb = new StringBuffer();//av简要结果

        List<SegmentDTO> result = Lists.newArrayList();
        for (int i = 0; i < avResult.getItemamount(); i++) {
            AvItem avItem = avResult.getItem(i);

            if (avItem.getSegmentnumber() > 2) continue;

            if (avItem.getSegmentnumber() == 2) {

                if (!isAllMyFlight(avItem, airline)) continue;

                long offsetTime = (avItem.getSegment(1).getDepdate().getTime() - avItem.getSegment(0).getArridate().getTime());
                if (offsetTime < MIN_TIME || offsetTime > MAX_TIME) continue;
            }

            //String its = "";
            for (int j = 0; j < avItem.getSegmentnumber(); j++) {
                AvSegment avSegment = avItem.getSegment(j);

                //非HU、CN时，过滤掉共享航班
                if (!("HU#CN".contains(airline.substring(0, 2)))) {
                    if (avSegment.isCodeShare()) {
                        continue;
                    }
                } else {
                    if (avSegment.isCodeShare()) {
                        String carrier = avSegment.getCarrier();
                        if (!carrier.startsWith("HU") && !carrier.startsWith("CN")) {
                            continue;
                        }
                    }
                }
                if (!"Y".equals(allAirline)) {
                    if (!avSegment.getAirline().startsWith(airline)) continue;
                }
//                if("JD".equals(airline)){
                if (!orgCity.equals(avSegment.getOrgcity()) || !dstCity.equals(avSegment.getDstcity())) continue;
//                }

                parseCabin(i, j, sb, avSegment);

                // 比较航班日期与查询日期是否相同，不同的就不再处理。
                if (!DateUtils.isSameDay(avSegment.getDepdate(), flightDate)) {
                    break;
                }


                ////PEK-HAK-1#PVG-HAK-20190101-HU9876-2

                // 航段
                SegmentDTO segment = new SegmentDTO();
                segment.setShareFlightNo(avSegment.getAirline());
                if (!StringUtils.isEmpty(avSegment.getCarrier())) {
                    segment.setCarrierFlightNo(avSegment.getCarrier());
                    segment.setFlightNo(avSegment.getCarrier());
                } else {
                    segment.setCarrierFlightNo(avSegment.getAirline());
                    segment.setFlightNo(avSegment.getAirline());
                }
                segment.setPlaneStyle(avSegment.getPlanestyle());

                segment.setDepartDate(avSegment.getDepdate());
                segment.setOrgCity(avSegment.getOrgcity());
                segment.setDstCity(avSegment.getDstcity());
                segment.setArriveDate(avSegment.getArridate());
                segment.setStops(avSegment.getStopnumber());
                segment.setCodeShare(avSegment.isCodeShare());
                segment.setDepTerm(avSegment.getDepTerm());        // 出发航站楼
                segment.setArriTerm(avSegment.getArriTerm());      // 到达航站楼
                segment.setHasMeal(avSegment.getMeal());
                segment.setMealCode(avSegment.getMealCode());

                String it = String.format("%s-%s-%s-%s#%s-%s-%s-%s-%s", avSegment.getAirline().substring(0, 2), orgCity, dstCity, i + 1, avSegment.getOrgcity(), avSegment.getDstcity(), DateUtil.toStringYMD(avSegment.getDepdate()), segment.getFlightNo(), j + 1);


                segment.setSeq(it);//PEK-HAK-1#PVG-HAK-20190101-HU9876-2

                // 查询经停城市
                if (avSegment.getStopnumber() > 0) {
                    // 如果有经停，则根据航班号和航班时间获取包含经停地的航班信息 默认只有一个经停地，取 0
                    try {
                        String flightTime = String.format("%s %s:%s:00", DateUtil.toStringYMD(avSegment.getDepdate()),
                                avSegment.getDeptime().substring(0, 2), avSegment.getDeptime().substring(2, 4));
                        //PEK-HAK-20200101-HU1234
                        String keyFormat = "%s-%s-%s-%s";
                        String keyValue = String.format(keyFormat, segment.getOrgCity(), segment.getDstCity(), DateUtil.dateToString(flightDate, "yyyyMMdd"), segment.getFlightNo());

                        Object o = CacheManagerUtil.get(CacheName.AV_STOP_CITY, keyValue);
                        if (o != null) {
                            log.info("AvailByFltNoCache airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(flightDate) + " sellerChannels=" + (sellerChannels == null ? airline + "|SYS" : sellerChannels) + " flightNo= " + segment.getFlightNo() + " stopnumber=" + avSegment.getStopnumber());
                            List<StopPointDTO> stopPointDTOs = (List<StopPointDTO>) o;
                            segment.setStopPoints(stopPointDTOs);
                        } else {
                            log.info("AvailByFltNo airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(flightDate) + " sellerChannels=" + (sellerChannels == null ? airline + "|SYS" : sellerChannels) + " flightNo= " + segment.getFlightNo() + " stopnumber=" + avSegment.getStopnumber());
                            AvResult avStopResult = av.getAvailByFltNo(avSegment.getAirline(), flightTime);
                            List<StopPointDTO> stopPointDTOs = getStopPointDTOs(avStopResult);
                            segment.setStopPoints(stopPointDTOs);
//                            AsyncTask asyncTask = ApplicationContextProvider.getBean("asyncTask", AsyncTask.class);
//                            if (stopPointDTOs != null && stopPointDTOs.size() > 0) {
//                                asyncTask.putStopCity2Cache(keyValue, stopPointDTOs);
//                            }
                            CacheManagerUtil.put(CacheName.AV_STOP_CITY, keyValue, stopPointDTOs);
                        }

                    } catch (Exception e) {
                        log.warn("get flightTime error ! orgCity = {} ,dstCity = {},flightDate={}", av.getTxnTraceKey(), orgCity, dstCity, DateUtil.dateToString(flightDate, "yyyy-MM-dd"));
                    }
                }
                Map<String, Object> subCabinMap = new HashMap();
                // 仓位
                for (int k = 0; k < 26; k++) {
                    char cangwei = avSegment.getCangweiCodeSort(k);
                    if (cangwei == '-') {
                        break;
                    }

                    parseSubCabin(cangwei, avSegment, sb, subCabinMap);

                    char cabinInfo = avSegment.getCangweiinfoOfSort(avSegment.getCangweiCodeSort(k));
                    CabinDTO cabin = new CabinDTO();
                    cabin.setCabinName(String.valueOf(cangwei));
                    cabin.setQuantity(String.valueOf(cabinInfo));
                    addCabin(segment.getCabins(), cabin);

                    // 查找子舱位
                    String[] subCabins = avSegment.getSubClassList(cangwei);
                    if (subCabins != null) {
                        for (String subCabin : subCabins) {
                            String classInfo = avSegment.getSubClassInfoOf(cangwei, subCabin);
                            CabinDTO cabinSub = new CabinDTO();
                            cabinSub.setCabinName(subCabin);
                            cabinSub.setQuantity(classInfo);
                            addCabin(segment.getCabins(), cabinSub);
                        }
                    }
                }
                sb.append("\n");

                result.add(segment);
            }
        }
        try {
            log.info("\nav orgCity = {},dstCity = {},flightDate={},TxnTraceKey={}\n{}", orgCity, dstCity, DateUtil.toStringYMD(flightDate), av.getTxnTraceKey(), sb.toString());
        } catch (Exception e) {
            log.warn("av orgCity = {},dstCity = {},flightDate={},TxnTraceKey={},e={}", orgCity, dstCity, DateUtil.toStringYMD(flightDate), av.getTxnTraceKey(), e);
        }
        return result;
    }

    private static void parseCabin(int i, int j, StringBuffer sb, AvSegment avSegment) {
        try {
            /*********av展示结果**********/
            if (j == 0) {
                sb.append(fillWithSpace(Integer.toString(i + 1), 6));
            } else {
                sb.append(fillWithSpace("", 6));
            }

            sb.append(fillWithSpace(avSegment.getAirline(), 9));
            sb.append(fillWithSpace((j == 0 ? avSegment.getOrgcity() : "   ") + avSegment.getDstcity(), 8));
            sb.append(fillWithSpace(avSegment.getDeptime(), 8));
            sb.append(fillWithSpace(avSegment.getArritime(), 6));
            sb.append(fillWithSpace(avSegment.getPlanestyle(), 4));
            sb.append(fillWithSpace(Integer.toString(avSegment.getStopnumber()), 1));
            sb.append(avSegment.getAsr() ? fillWithSpace("^", 1) : fillWithSpace(" ", 1));
            sb.append(avSegment.getMeal() ? fillWithSpace(avSegment.getMealCode(), 3) : fillWithSpace(" ", 3));
            sb.append(fillWithSpace(avSegment.getLink(), 4));
            /*********av展示结果**********/
        } catch (Exception e) {
            log.warn("parseCabin error,e={}", e);
        }
    }

    private static void parseSubCabin(char cangwei, AvSegment avSegment, StringBuffer sb, Map<String, Object> subCabinMap) {
        try {
            /*****AV结果展示*****/
            sb.append(cangwei).append(avSegment.getCangweiinfoOfSort(cangwei));
            String[] subCabins4Show = avSegment.getSubClassList(cangwei);
            if (subCabins4Show != null) {
                sb.append("(");
                String[] var7 = subCabins4Show;
                int var8 = subCabins4Show.length;

                for (int var9 = 0; var9 < var8; ++var9) {
                    String subCabin = var7[var9];
                    if (!subCabinMap.containsKey(cangwei + subCabin)) {
                        subCabinMap.put(cangwei + subCabin, (Object) null);
                        String classInfo = avSegment.getSubClassInfoOf(cangwei, subCabin);
                        sb.append(subCabin);
                        sb.append(classInfo);
                        sb.append(" ");
                    }
                }

                sb.delete(sb.length() - 1, sb.length());
                sb.append(")");
            }

            sb.append(" ");
            /*****AV结果展示*****/
        } catch (Exception e) {
            log.warn("parseSubCabin error,e={}", e);
        }
    }


    /**
     * 获取经停点信息
     *
     * @param avStopResult
     * @return
     */
    private static List<StopPointDTO> getStopPointDTOs(AvResult avStopResult) {
        List<StopPointDTO> result = Lists.newArrayList();
        for (int i = 0; i < avStopResult.getItemamount(); i++) {
            AvItem avItem = avStopResult.getItem(i);

            for (int j = 0; j < avItem.getSegmentnumber(); j++) {
                if ((j + 1) < avItem.getSegmentnumber()) {
                    AvSegment stopSegment = avItem.getSegment(j);
                    AvSegment stopSegment2 = avItem.getSegment(j + 1);

                    StopPointDTO stopPointDTO = new StopPointDTO();
                    stopPointDTO.setStopCityCode(stopSegment.getDstcity());
                    stopPointDTO.setStopCityTerm(stopSegment.getArriTerm());

                    // 经停城市到达时间
                    String arriveTime = String.format("%s %s00", DateUtil.toStringYMD(stopSegment.getArridate()),
                            stopSegment.getArritime());
                    // 经停城市起飞时间
                    String departureTime = String.format("%s %s00", DateUtil.toStringYMD(stopSegment2.getDepdate()),
                            stopSegment2.getDeptime());

                    stopPointDTO.setArrivalTime(DateUtil.toDateYMDHMS(arriveTime));
                    stopPointDTO.setDepartureTime(DateUtil.toDateYMDHMS(departureTime));

                    result.add(stopPointDTO);
                }
            }
        }

        return result;
    }

    /**
     * 添加仓位，重复的不添加
     *
     * @param cabinDTOS
     * @param cabinDTO
     */
    private static void addCabin(List<CabinDTO> cabinDTOS, CabinDTO cabinDTO) {
        for (CabinDTO dto : cabinDTOS) {
            if (StringUtils.equals(dto.getCabinName(), cabinDTO.getCabinName())) {
                return;
            }
        }

        cabinDTOS.add(cabinDTO);
    }

    //////////////////以下为展示av简要信息///////////////////
    private static String fillWithSpace(String orgStr, int length) {
        if (null == orgStr) {
            orgStr = "";
        }

        return (orgStr + "                                                  ").substring(0, length);
    }
}
