/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BagTagInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1269420007109243102L;
/*    */   private String bagTagNumber;
/*    */   private String bagCancel;
/*    */   private String isCrewBag;
/*    */ 
/*    */   public String getBagTagNumber()
/*    */   {
/* 32 */     return this.bagTagNumber;
/*    */   }
/*    */ 
/*    */   public void setBagTagNumber(String bagTagNumber)
/*    */   {
/* 39 */     this.bagTagNumber = bagTagNumber;
/*    */   }
/*    */ 
/*    */   public String getBagCancel()
/*    */   {
/* 46 */     return this.bagCancel;
/*    */   }
/*    */ 
/*    */   public void setBagCancel(String bagCancel)
/*    */   {
/* 53 */     this.bagCancel = bagCancel;
/*    */   }
/*    */ 
/*    */   public String getIsCrewBag()
/*    */   {
/* 60 */     return this.isCrewBag;
/*    */   }
/*    */ 
/*    */   public void setIsCrewBag(String isCrewBag)
/*    */   {
/* 67 */     this.isCrewBag = isCrewBag;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BagTagInfo
 * JD-Core Version:    0.6.0
 */