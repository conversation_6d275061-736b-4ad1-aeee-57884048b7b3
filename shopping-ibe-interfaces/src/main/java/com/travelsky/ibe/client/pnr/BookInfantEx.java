package com.travelsky.ibe.client.pnr;

import java.util.Date;

public class BookInfantEx extends BookInfant {

	public BookInfantEx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookInfantEx(Date birth, String carrierName, String name) throws Exception {
		super(birth, carrierName, name);
		// TODO Auto-generated constructor stub
	}

	public BookInfantEx(String birth, String carrierName, String name) throws Exception {
		super(birth, carrierName, name);
		// TODO Auto-generated constructor stub
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = -2974807217857138763L;
	/*     */ public  Date getBirth() {
		/* 108 */     return super.getBirth();//.birth;
		/*     */   }
		/*     */ 
		/*     */ public  String getBirthString() {
		/* 112 */     return super.getBirthString();//.birth.toString();
		/*     */   }
		/*     */ public  String getCarrierName() {
			/* 128 */     return super.getCarrierName();//.carrierName;
			/*     */   }
		public String getName() {
			/* 143 */     return super.getName();//.name;
			/*     */   }
		 public void setBirth(String birth)
				 /*     */     throws Exception
				 /*     */   {
			 
			 if(null==birth || birth.trim().equals(""))
					return;
				if(birth.length()==5||birth.length()==7||birth.length()==10) {
					super.setBirth(birth);
				}else {
					super.setBirth(new Date(Long.parseLong(birth)));
				}
		 }
}
