/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class VisaInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1513662059291325485L;
/*     */   private String visaIssuePlace;
/*     */   private String expireDate;
/*     */   private String effectiveDate;
/*     */   private String visaType;
/*     */   private String visaID;
/*     */ 
/*     */   public String getEffectiveDate()
/*     */   {
/*  41 */     return this.effectiveDate;
/*     */   }
/*     */ 
/*     */   public void setEffectiveDate(String effectiveDate)
/*     */   {
/*  48 */     this.effectiveDate = effectiveDate;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/*  55 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/*  62 */     this.expireDate = expireDate;
/*     */   }
/*     */ 
/*     */   public String getVisaID()
/*     */   {
/*  69 */     return this.visaID;
/*     */   }
/*     */ 
/*     */   public void setVisaID(String visaID)
/*     */   {
/*  76 */     this.visaID = visaID;
/*     */   }
/*     */ 
/*     */   public String getVisaIssuePlace()
/*     */   {
/*  83 */     return this.visaIssuePlace;
/*     */   }
/*     */ 
/*     */   public void setVisaIssuePlace(String visaIssuePlace)
/*     */   {
/*  90 */     this.visaIssuePlace = visaIssuePlace;
/*     */   }
/*     */ 
/*     */   public String getVisaType()
/*     */   {
/*  97 */     return this.visaType;
/*     */   }
/*     */ 
/*     */   public void setVisaType(String visaType)
/*     */   {
/* 104 */     this.visaType = visaType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.VisaInfo
 * JD-Core Version:    0.6.0
 */