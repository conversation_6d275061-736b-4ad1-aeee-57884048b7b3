/*     */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PaymentDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4113008301856907933L;
/*     */   private String upgPrice;
/*     */   private String autoIndicator;
/*     */   private String currencyCode;
/*     */   private String payType;
/*     */   private String payText;
/*     */   private PaymentCard paymentCard;
/*     */   private LoyaltyRedemption loyaltyRedemption;
/*     */ 
/*     */   public String getUpgPrice()
/*     */   {
/*  56 */     return this.upgPrice;
/*     */   }
/*     */ 
/*     */   public void setUpgPrice(String upgPrice)
/*     */   {
/*  63 */     this.upgPrice = upgPrice;
/*     */   }
/*     */ 
/*     */   public String getAutoIndicator()
/*     */   {
/*  70 */     return this.autoIndicator;
/*     */   }
/*     */ 
/*     */   public void setAutoIndicator(String autoIndicator)
/*     */   {
/*  77 */     this.autoIndicator = autoIndicator;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/*  84 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/*  91 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ 
/*     */   public String getPayType()
/*     */   {
/*  98 */     return this.payType;
/*     */   }
/*     */ 
/*     */   public void setPayType(String payType)
/*     */   {
/* 105 */     this.payType = payType;
/*     */   }
/*     */ 
/*     */   public String getPayText()
/*     */   {
/* 112 */     return this.payText;
/*     */   }
/*     */ 
/*     */   public void setPayText(String payText)
/*     */   {
/* 119 */     this.payText = payText;
/*     */   }
/*     */ 
/*     */   public PaymentCard getPaymentCard()
/*     */   {
/* 126 */     return this.paymentCard;
/*     */   }
/*     */ 
/*     */   public void setPaymentCard(PaymentCard paymentCard)
/*     */   {
/* 133 */     this.paymentCard = paymentCard;
/*     */   }
/*     */ 
/*     */   public LoyaltyRedemption getLoyaltyRedemption()
/*     */   {
/* 140 */     return this.loyaltyRedemption;
/*     */   }
/*     */ 
/*     */   public void setLoyaltyRedemption(LoyaltyRedemption loyaltyRedemption)
/*     */   {
/* 147 */     this.loyaltyRedemption = loyaltyRedemption;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.PaymentDetail
 * JD-Core Version:    0.6.0
 */