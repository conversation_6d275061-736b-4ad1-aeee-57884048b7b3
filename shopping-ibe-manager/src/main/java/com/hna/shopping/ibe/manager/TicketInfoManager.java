package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.hna.shopping.ibe.interfaces.dto.DETRCreResult;
import com.hna.shopping.ibe.interfaces.dto.DtoAllChanTicketInfoResponse;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPassengerNoRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPnrRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoTicketNoRequest;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

/**
 * longx.yang 2019/12/17
 * 分离出来，根据特定的信息提取票面信息
 */
public interface TicketInfoManager {

	/**
	 * 获取换开信息，暂时没有使用到
	 * @param result
	 * @return
	 */
	public String getExchangeInfo(DETRTKTResult result);

	/**
	 * 根据pnr提取客票信息
	 * @param request
	 * @return
     */
	DtoAllChanTicketInfoResponse getAllChanTicketInfoByPnr(DtoTicketInfoPnrRequest request);


	/**
	 * 根据票号提取客票信息
	 * @return
	 */
	DtoAllChanTicketInfoResponse getAllChanTicketInfoByTicketNo(DtoTicketInfoTicketNoRequest request);

	/**
	 * 根据证件号和证件类型
	 * @param request
	 * @return
	 */
	DtoAllChanTicketInfoResponse getAllChanTicketInfoByPassengerNo(DtoTicketInfoPassengerNoRequest request);

	/**
	 * 根据票号获取历史记录的pnr和出票日期
     * 查询历史客票信息 出票时间为空的时候需要提取客票历史记录
	 * @param detr
	 * @param ticketNo
	 * @return
	 */
	public Map<String, Object> getInfoFromHistory(DETR detr, String ticketNo);

	public List<AllChanTicketInfo> mapTicketInfoResult(String airline , Boolean filterOpenforUse, DETRTKTResult result, DETR2F detr2f, Integer index, String segInfo, String needBookTime, String needTicketChangeHis, int cycleNum, String secondFactorCode, String secondFactorValue) ;

}
