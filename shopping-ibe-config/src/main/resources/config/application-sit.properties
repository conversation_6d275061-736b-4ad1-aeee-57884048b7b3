server.port=8082

# 引入公共配置
spring.profiles.include=common

# spring-boot-starter-actuator 测试环境访问不需要鉴权
management.security.enabled=false

# 每个系统都应该不一样
memcached.prefix=uni-offer-sit
memcached.serverHost[0]=*************:11211
memcached.userName[0]=
memcached.password[0]=


# logback 配置
#logging.path=/Users/<USER>/logs/uni-ibe
logging.path=/app/logs/jd-ibe
logging.level.com.hna.eking.basedemo=INFO
logging.level.root=INFO
logging.level.com.google.code.ssm=WARN

# eureka 注册中心地址
#eureka.client.serviceUrl.defaultZone=http://*************:8088/eureka/
#eureka.client.serviceUrl.defaultZone=http://*********:8761/eureka/,http://*********:8761/eureka/
eureka.client.serviceUrl.defaultZone=http://*********:8761/eureka/

# redis配置
redisson.address=redis://*********:6379
redisson.password=Redis@123
redisson.config.open=true

spring.redis.database=1
# Redis服务器地址
spring.redis.host=************
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=ENC(BavCb+skeCZq9ID4BVlLiJxxFD8foz6An1tdi1whXZnaX42Q+3EO/QRcUd8mKAp6AHl+Xt10Qfx1vmuCjcrStA==)
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=20
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=3000

# fu 配置
ibe.fu.server.ip=***********
ibe.fu.server.backupIp=***********
ibe.fu.server.port=6891
ibe.fu.client.app=fub2c
ibe.fu.client.office=foc023
ibe.fu.client.customNo=0
ibe.fu.client.validationNo=81
ibe.fu.client.printNos=1,2
ibe.fu.config.openBalance=true
ibe.fu.config.replaceContactNo=false
ibe.fu.config.openEI=true

# hu 配置
ibe.hu.server.ip=***********
ibe.hu.server.backupIp=***********
ibe.hu.server.port=6892
ibe.hu.client.app=huair918
ibe.hu.client.office=hkk918
ibe.hu.client.customNo=0
ibe.hu.client.validationNo=15

# hu 强k配置
ibe.hu.k.server.ip=***********
ibe.hu.k.server.backupIp=***********
ibe.hu.k.server.port=6891
ibe.hu.k.client.app=huairb_k
ibe.hu.k.client.office=hkk001
ibe.hu.k.client.customNo=0
ibe.hu.k.client.validationNo=15


# jd 配置
ibe.jd.server.ip=*************
ibe.jd.server.backupIp=*************
ibe.jd.server.port=6891
ibe.jd.client.app=jdcrm
ibe.jd.client.office=xiy258
ibe.jd.client.customNo=0
ibe.jd.client.validationNo=64
ibe.jd.client.printNos=1,2,3,4,6,7,8

# jd ç¥¨å¡å¤ç
ibe.jd.pw.server.ip=***************
ibe.jd.pw.server.backupIp=***************
ibe.jd.pw.server.port=6891
ibe.jd.pw.client.app=jdcrm
ibe.jd.pw.client.office=xiy258
ibe.jd.pw.client.customNo=0
ibe.jd.pw.client.validationNo=64
ibe.jd.pw.client.printNos=1,2,3,4,6,7,8

# jd IBEå°å·¥å· xiy255éç½®
ibe.jd.xiy255.server.ip=***********
ibe.jd.xiy255.server.backupIp=***********
ibe.jd.xiy255.server.port=6891
ibe.jd.xiy255.client.app=jdhaiwai
ibe.jd.xiy255.client.office=xiy255
ibe.jd.xiy255.client.customNo=0
ibe.jd.xiy255.client.validationNo=64
ibe.jd.xiy255.client.printNos=1,2,3,4,6,7,8

# jd IBEå°å·¥å· xiy258éç½®
ibe.jd.xiy258.server.ip=*************
ibe.jd.xiy258.server.backupIp=*************
ibe.jd.xiy258.server.port=6891
ibe.jd.xiy258.client.app=jdcrm
ibe.jd.xiy258.client.office=xiy258
ibe.jd.xiy258.client.customNo=0
ibe.jd.xiy258.client.validationNo=64
ibe.jd.xiy258.client.printNos=1,2,3,4,6,7,8

# jd gw配置
ibe.jd.gw.server.ip=***********
ibe.jd.gw.server.backupIp=***********
ibe.jd.gw.server.port=6895
ibe.jd.gw.client.app=jdibe
ibe.jd.gw.client.office=xiy210
ibe.jd.gw.client.customNo=0
ibe.jd.gw.client.validationNo=64
ibe.jd.gw.client.printNos=1,2,3,4,6,7,8

# jd 强k配置
ibe.jd.k.server.ip=***********
ibe.jd.k.server.backupIp=***********
ibe.jd.k.server.port=6891
ibe.jd.k.client.app=jdair
ibe.jd.k.client.office=xiy201
ibe.jd.k.client.customNo=0
ibe.jd.k.client.validationNo=64
ibe.jd.k.client.printNos=1,2,3,4,6,7,8

# y8 配置
ibe.y8.server.ip=***********
ibe.y8.server.backupIp=***********
ibe.y8.server.port=6891
ibe.y8.client.app=y8airb2c
ibe.y8.client.office=pvg402
ibe.y8.client.customNo=0
ibe.y8.client.validationNo=23
ibe.y8.client.printNos=1,2
ibe.y8.config.openBalance=true
ibe.y8.config.replaceContactNo=false
ibe.y8.config.openEI=true

ibe.y8.k.server.ip=***********
ibe.y8.k.server.backupIp=***********
ibe.y8.k.server.port=6891
ibe.y8.k.client.app=y8airb2c
ibe.y8.k.client.office=pvg402
ibe.y8.k.client.customNo=0
ibe.y8.k.client.validationNo=23
ibe.y8.k.client.printNos=1,2
ibe.y8.k.config.openBalance=true
ibe.y8.k.config.replaceContactNo=false
ibe.y8.k.config.openEI=true

# gt 配置
ibe.gt.server.ip=***********
ibe.gt.server.backupIp=***********
ibe.gt.server.port=6891
ibe.gt.client.app=gtairb2c
ibe.gt.client.office=kwl407
ibe.gt.client.customNo=0
ibe.gt.client.validationNo=94
ibe.gt.client.printNos=1
ibe.gt.config.openBalance=true
ibe.gt.config.replaceContactNo=false
ibe.gt.config.openEI=true

# 8l 配置
ibe.8l.server.ip=***********
ibe.8l.server.backupIp=***********
ibe.8l.server.port=6891
ibe.8l.client.app=8libe305
ibe.8l.client.office=lke305
ibe.8l.client.customNo=0
ibe.8l.client.validationNo=57
ibe.8l.client.printNos=1,3,4,5
ibe.8l.config.openBalance=true
ibe.8l.config.replaceContactNo=false
ibe.8l.config.openEI=true

# 8l 配置
ibe.8l.k.server.ip=***********
ibe.8l.k.server.backupIp=***********
ibe.8l.k.server.port=6891
ibe.8l.k.client.app=8libek
ibe.8l.k.client.office=lke001
ibe.8l.k.client.customNo=0
ibe.8l.k.client.validationNo=57
ibe.8l.k.client.printNos=1,3,4,5
ibe.8l.k.config.openBalance=true
ibe.8l.k.config.replaceContactNo=false
ibe.8l.k.config.openEI=true

# 8l 国际票
ibe.8l.gjp.server.ip=***********
ibe.8l.gjp.server.backupIp=***********
ibe.8l.gjp.server.port=6891
ibe.8l.gjp.client.app=8libe305
ibe.8l.gjp.client.office=lke305
ibe.8l.gjp.client.customNo=0
ibe.8l.gjp.client.validationNo=57
ibe.8l.gjp.client.printNos=7
ibe.8l.gjp.config.openBalance=true
ibe.8l.gjp.config.replaceContactNo=false
ibe.8l.gjp.config.openEI=true
ibe.8l.gjp.agencies=[{"channel":"1E","pos":"KMG","IATA_Number":"********","travelAgencyCode":"LKE305","departmentCode":"C8L","accountCodes":[{"carrier":"8L","code":"GJXF1801"}]}]
ibe.8l.gjp.system=B

# pn 配置
ibe.pn.server.ip=***********
ibe.pn.server.backupIp=***********
ibe.pn.server.port=6891
ibe.pn.client.app=pnsk
ibe.pn.client.office=ckg107
ibe.pn.client.customNo=0
ibe.pn.client.validationNo=15
ibe.pn.client.printNos=1,2,3,4,5
ibe.pn.config.openBalance=true
ibe.pn.config.replaceContactNo=false
ibe.pn.config.openEI=true

ibe.pn.k.server.ip=***********
ibe.pn.k.server.backupIp=***********
ibe.pn.k.server.port=6891
ibe.pn.k.client.app=pnsk
ibe.pn.k.client.office=ckg107
ibe.pn.k.client.customno=0
ibe.pn.k.client.validationno=15

# gs 配置
#ibe.gs.server.ip=***********
#ibe.gs.server.backupIp=***********
ibe.gs.server.ip=***********
ibe.gs.server.backupIp=***********
ibe.gs.server.port=6891
ibe.gs.client.app=gsibe
ibe.gs.client.office=tsn319
ibe.gs.client.customNo=0
ibe.gs.client.validationNo=61
ibe.gs.client.printNos=2,3,5,7
ibe.gs.config.openBalance=true
ibe.gs.config.replaceContactNo=false
ibe.gs.config.openEI=true

#ibe.gs.k.server.ip=***********
#ibe.gs.k.server.backupIp=***********
ibe.gs.k.server.ip=***********
ibe.gs.k.server.backupIp=***********
ibe.gs.k.server.port=6891
ibe.gs.k.client.app=gsibe_k
ibe.gs.k.client.office=tsn301
ibe.gs.k.client.customNo=0
ibe.gs.k.client.validationNo=61
ibe.gs.k.client.printNos=1,2,3,4,6,7,8


#ibe.9h.k.server.ip=***********
#ibe.9h.k.server.backupIp=***********
ibe.9h.server.ip=***********
ibe.9h.server.backupIp=***********
ibe.9h.server.port=6891
ibe.9h.client.app=9hairb2c
ibe.9h.client.office=xiy511
ibe.9h.client.customNo=0
ibe.9h.client.validationNo=95
ibe.9h.client.printNos=2,3,5,7,8,9,10,11,12,13
ibe.9h.config.openBalance=true
ibe.9h.config.replaceContactNo=false
ibe.9h.config.openEI=true

#ibe.9h.k.server.ip=***********
#ibe.9h.k.server.backupIp=***********
ibe.9h.k.server.ip=***********
ibe.9h.k.server.backupIp=***********
ibe.9h.k.server.port=6891
ibe.9h.k.client.app=9hairb2c
ibe.9h.k.client.office=xiy511
ibe.9h.k.client.customNo=0
ibe.9h.k.client.validationNo=95
ibe.9h.k.client.printNos=1,2,3,4,6,7,8

# uq 配置
ibe.uq.server.ip=***********
ibe.uq.server.backupIp=***********
ibe.uq.server.port=6891
ibe.uq.client.app=uqairb2c
ibe.uq.client.office=urc012
ibe.uq.client.customNo=0
ibe.uq.client.validationNo=68
ibe.uq.client.printNos=1

ibe.gx.server.ip=************
ibe.gx.server.backupIp=************
ibe.gx.server.port=6891
ibe.gx.client.app=gxair310
ibe.gx.client.office=NNG310
ibe.gx.client.customNo=0
ibe.gx.client.validationNo=87
ibe.gx.client.printNos=1

#travel sky 配置
travelsky.service.url=http://*************:8087/TravelSkySvc/remoting/

travelsky.luggage.login=https://bts.travelsky.com/gateway/login
travelsky.luggage.bagNo=https://bts.travelsky.com/gateway/openApi/ext/bagNodeTracker/xml
travelsky.luggage.paxName=https://bts.travelsky.com/gateway/openApi/dataOutput/bagNodeTracker/paxEngNm
travelsky.luggage.bordNo=https://bts.travelsky.com/gateway/openApi/dataOutput/bagNodeTracker/bordNo
travelsky.luggage.bsmByBagNo=https://bts.travelsky.com/gateway/openApi/ext/bsmSource/xml/bagNo
travelsky.luggage.bsmByFlightNo=https://bts.travelsky.com/gateway/openApi/ext/bsmSource/xml/flightNo
travelsky.luggage.username[GS]=gsapi
travelsky.luggage.password[GS]=ENC(TskykF5FeTCMbwq/Zvcxhn9yfZR5Qwxp)

# 股份 esb 接口配置信息
odsapi.url=https://odsuat.hnair.net/api
odsapi.serialization=fastjson2
odsapi.timeout=600000
#必填配置 #uat 地址为https://odsuat.hnair.net/api
#以下为生产环境各区的调用地址，消费系统根据各自调用情况，自行选择
#美兰公共：https://odsapi-ml.hnair.net/api
#南数公共：https://odsapi-ns.hnair.net/api
#北数公共：https://odsapi-bs.hnair.net/api
#序列化框架可选配置，默认hessian2，综合性能 fst  > fastjson2 > hessian2 > hessian > json
#推荐使用 hessian2 和 fastjson2
#default 600000 可选配置

spring.datasource.dynamic.primary=master
#type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.master.url=****************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=jdmanager
spring.datasource.dynamic.datasource.master.password=ENC(mzU+w49iaWymnsrthDJKj6ZWnv/lawUW)
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver