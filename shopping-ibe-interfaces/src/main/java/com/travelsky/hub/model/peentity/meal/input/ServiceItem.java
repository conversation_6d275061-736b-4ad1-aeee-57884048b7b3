/*    */ package com.travelsky.hub.model.peentity.meal.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ServiceItem
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9195927039611960271L;
/*    */   private String segmentID;
/*    */   private String serviceID;
/*    */   private String ssrCode;
/*    */   private String tabooMealDescription;
/*    */ 
/*    */   public String getSegmentID()
/*    */   {
/* 23 */     return this.segmentID;
/*    */   }
/*    */ 
/*    */   public void setSegmentID(String segmentID)
/*    */   {
/* 30 */     this.segmentID = segmentID;
/*    */   }
/*    */ 
/*    */   public String getServiceID()
/*    */   {
/* 38 */     return this.serviceID;
/*    */   }
/*    */ 
/*    */   public void setServiceID(String serviceID)
/*    */   {
/* 46 */     this.serviceID = serviceID;
/*    */   }
/*    */ 
/*    */   public String getSsrCode()
/*    */   {
/* 54 */     return this.ssrCode;
/*    */   }
/*    */ 
/*    */   public void setSsrCode(String ssrCode)
/*    */   {
/* 61 */     this.ssrCode = ssrCode;
/*    */   }
/*    */ 
/*    */   public String getTabooMealDescription()
/*    */   {
/* 69 */     return this.tabooMealDescription;
/*    */   }
/*    */ 
/*    */   public void setTabooMealDescription(String tabooMealDescription)
/*    */   {
/* 76 */     this.tabooMealDescription = tabooMealDescription;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.ServiceItem
 * JD-Core Version:    0.6.0
 */