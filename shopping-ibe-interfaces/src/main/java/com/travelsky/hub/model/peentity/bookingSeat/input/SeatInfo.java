/*    */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6219652324035128805L;
/*    */   private String seatNumber;
/*    */   private String deckCode;
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 36 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 43 */     this.seatNumber = seatNumber;
/*    */   }
/*    */ 
/*    */   public String getDeckCode()
/*    */   {
/* 50 */     return this.deckCode;
/*    */   }
/*    */ 
/*    */   public void setDeckCode(String deckCode)
/*    */   {
/* 57 */     this.deckCode = deckCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.SeatInfo
 * JD-Core Version:    0.6.0
 */