/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class BoardInfoResult
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String boardingGateNumber;
/*     */   private String boardingTime;
/*     */   private String deptTime;
/*     */   private String extraGates;
/*     */   private String planeType;
/*     */   private String planeClass;
/*     */   private String expDeptTime;
/*     */   private List<Map<String, String>> segInfos;
/*     */   private List<Limit> gsList;
/*     */   private String av;
/*     */   private String registNum;
/*     */   private String flightStatus;
/*     */   private String flightStatusHO;
/*     */   private String bn;
/*     */   private String ak;
/*     */   private String cd;
/*     */   private String cwt;
/*     */   private String uwt;
/*     */   private String caw;
/*     */   private String uaw;
/*     */   private String wtn;
/*     */   private String wtnunit;
/*     */   private String statusecho;
/*     */   private List<FlightHoldType> flightHoldType;
/*     */   private List<SegHoldType> segHoldType;
/*     */   private List<LegHoldType> legHoldType;
/*     */ 
/*     */   public String getBn()
/*     */   {
/* 128 */     return this.bn;
/*     */   }
/*     */ 
/*     */   public void setBn(String bn)
/*     */   {
/* 135 */     this.bn = bn;
/*     */   }
/*     */ 
/*     */   public String getAk()
/*     */   {
/* 142 */     return this.ak;
/*     */   }
/*     */ 
/*     */   public void setAk(String ak)
/*     */   {
/* 149 */     this.ak = ak;
/*     */   }
/*     */ 
/*     */   public String getCd()
/*     */   {
/* 156 */     return this.cd;
/*     */   }
/*     */ 
/*     */   public void setCd(String cd)
/*     */   {
/* 163 */     this.cd = cd;
/*     */   }
/*     */ 
/*     */   public String getCwt()
/*     */   {
/* 170 */     return this.cwt;
/*     */   }
/*     */ 
/*     */   public void setCwt(String cwt)
/*     */   {
/* 177 */     this.cwt = cwt;
/*     */   }
/*     */ 
/*     */   public String getUwt()
/*     */   {
/* 184 */     return this.uwt;
/*     */   }
/*     */ 
/*     */   public void setUwt(String uwt)
/*     */   {
/* 191 */     this.uwt = uwt;
/*     */   }
/*     */ 
/*     */   public String getCaw()
/*     */   {
/* 198 */     return this.caw;
/*     */   }
/*     */ 
/*     */   public void setCaw(String caw)
/*     */   {
/* 205 */     this.caw = caw;
/*     */   }
/*     */ 
/*     */   public String getUaw()
/*     */   {
/* 212 */     return this.uaw;
/*     */   }
/*     */ 
/*     */   public void setUaw(String uaw)
/*     */   {
/* 219 */     this.uaw = uaw;
/*     */   }
/*     */ 
/*     */   public String getWtn()
/*     */   {
/* 226 */     return this.wtn;
/*     */   }
/*     */ 
/*     */   public void setWtn(String wtn)
/*     */   {
/* 233 */     this.wtn = wtn;
/*     */   }
/*     */ 
/*     */   public String getWtnunit()
/*     */   {
/* 240 */     return this.wtnunit;
/*     */   }
/*     */ 
/*     */   public void setWtnunit(String wtnunit)
/*     */   {
/* 247 */     this.wtnunit = wtnunit;
/*     */   }
/*     */ 
/*     */   public String getStatusecho()
/*     */   {
/* 254 */     return this.statusecho;
/*     */   }
/*     */ 
/*     */   public void setStatusecho(String statusecho)
/*     */   {
/* 261 */     this.statusecho = statusecho;
/*     */   }
/*     */ 
/*     */   public List<SegHoldType> getSegHoldType()
/*     */   {
/* 268 */     return this.segHoldType;
/*     */   }
/*     */ 
/*     */   public void setSegHoldType(List<SegHoldType> segHoldType)
/*     */   {
/* 275 */     this.segHoldType = segHoldType;
/*     */   }
/*     */ 
/*     */   public List<LegHoldType> getLegHoldType()
/*     */   {
/* 282 */     return this.legHoldType;
/*     */   }
/*     */ 
/*     */   public void setLegHoldType(List<LegHoldType> legHoldType)
/*     */   {
/* 289 */     this.legHoldType = legHoldType;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 297 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 304 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 311 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 318 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 325 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 332 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getExtraGates()
/*     */   {
/* 339 */     return this.extraGates;
/*     */   }
/*     */ 
/*     */   public void setExtraGates(String extraGates)
/*     */   {
/* 346 */     this.extraGates = extraGates;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/* 353 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/* 360 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getPlaneClass()
/*     */   {
/* 367 */     return this.planeClass;
/*     */   }
/*     */ 
/*     */   public void setPlaneClass(String planeClass)
/*     */   {
/* 374 */     this.planeClass = planeClass;
/*     */   }
/*     */ 
/*     */   public List<Map<String, String>> getSegInfos()
/*     */   {
/* 381 */     return this.segInfos;
/*     */   }
/*     */ 
/*     */   public void setSegInfos(List<Map<String, String>> segInfos)
/*     */   {
/* 388 */     this.segInfos = segInfos;
/*     */   }
/*     */ 
/*     */   public List<Limit> getGsList()
/*     */   {
/* 395 */     return this.gsList;
/*     */   }
/*     */ 
/*     */   public void setGsList(List<Limit> gsList)
/*     */   {
/* 402 */     this.gsList = gsList;
/*     */   }
/*     */ 
/*     */   public String getRegistNum()
/*     */   {
/* 409 */     return this.registNum;
/*     */   }
/*     */ 
/*     */   public void setRegistNum(String registNum)
/*     */   {
/* 416 */     this.registNum = registNum;
/*     */   }
/*     */ 
/*     */   public String getExpDeptTime()
/*     */   {
/* 423 */     return this.expDeptTime;
/*     */   }
/*     */ 
/*     */   public void setExpDeptTime(String expDeptTime)
/*     */   {
/* 430 */     this.expDeptTime = expDeptTime;
/*     */   }
/*     */ 
/*     */   public String getAv()
/*     */   {
/* 437 */     return this.av;
/*     */   }
/*     */ 
/*     */   public void setAv(String av)
/*     */   {
/* 444 */     this.av = av;
/*     */   }
/*     */ 
/*     */   public String getFlightStatusHO()
/*     */   {
/* 450 */     return this.flightStatusHO;
/*     */   }
/*     */ 
/*     */   public void setFlightStatusHO(String flightStatusHO)
/*     */   {
/* 456 */     this.flightStatusHO = flightStatusHO;
/*     */   }
/*     */ 
/*     */   public String getFlightStatus()
/*     */   {
/* 462 */     return this.flightStatus;
/*     */   }
/*     */ 
/*     */   public void setFlightStatus(String flightStatus)
/*     */   {
/* 468 */     this.flightStatus = flightStatus;
/*     */   }
/*     */ 
/*     */   public List<FlightHoldType> getFlightHoldType()
/*     */   {
/* 475 */     return this.flightHoldType;
/*     */   }
/*     */ 
/*     */   public void setFlightHoldType(List<FlightHoldType> flightHoldType)
/*     */   {
/* 482 */     this.flightHoldType = flightHoldType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BoardInfoResult
 * JD-Core Version:    0.6.0
 */