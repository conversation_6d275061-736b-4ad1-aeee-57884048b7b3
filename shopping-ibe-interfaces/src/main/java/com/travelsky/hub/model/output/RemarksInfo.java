/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class RemarksInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -521219439938602195L;
/*    */   private String remarksType;
/*    */   private String remarksText;
/*    */ 
/*    */   public String getRemarksType()
/*    */   {
/* 33 */     return this.remarksType;
/*    */   }
/*    */ 
/*    */   public void setRemarksType(String remarksType)
/*    */   {
/* 40 */     this.remarksType = remarksType;
/*    */   }
/*    */ 
/*    */   public String getRemarksText()
/*    */   {
/* 47 */     return this.remarksText;
/*    */   }
/*    */ 
/*    */   public void setRemarksText(String remarksText)
/*    */   {
/* 54 */     this.remarksText = remarksText;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.RemarksInfo
 * JD-Core Version:    0.6.0
 */