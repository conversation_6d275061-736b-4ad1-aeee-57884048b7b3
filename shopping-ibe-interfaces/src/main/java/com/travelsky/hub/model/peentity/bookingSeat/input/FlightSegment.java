/*     */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightSegment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1117592700310634055L;
/*     */   private String segmentID;
/*     */   private String departureDate;
/*     */   private String departureTime;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String mcRBD;
/*     */   private String mcFlightSuffix;
/*     */   private String mcFlightNumber;
/*     */   private String mcAirlineID;
/*     */   private String ocFlightNumber;
/*     */   private String ocAirlineID;
/*     */   private String ocFlightSuffix;
/*     */ 
/*     */   public String getSegmentID()
/*     */   {
/*  79 */     return this.segmentID;
/*     */   }
/*     */ 
/*     */   public void setSegmentID(String segmentID)
/*     */   {
/*  86 */     this.segmentID = segmentID;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  93 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 100 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 107 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/* 114 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 121 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getMcRBD()
/*     */   {
/* 127 */     return this.mcRBD;
/*     */   }
/*     */ 
/*     */   public void setMcRBD(String mcRBD)
/*     */   {
/* 134 */     this.mcRBD = mcRBD;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 141 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 148 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 155 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 161 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 168 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getMcAirlineID()
/*     */   {
/* 175 */     return this.mcAirlineID;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineID(String mcAirlineID)
/*     */   {
/* 182 */     this.mcAirlineID = mcAirlineID;
/*     */   }
/*     */ 
/*     */   public String getMcFlightSuffix()
/*     */   {
/* 189 */     return this.mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setMcFlightSuffix(String mcFlightSuffix)
/*     */   {
/* 196 */     this.mcFlightSuffix = mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 203 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 210 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineID()
/*     */   {
/* 217 */     return this.ocAirlineID;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineID(String ocAirlineID)
/*     */   {
/* 224 */     this.ocAirlineID = ocAirlineID;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 231 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 238 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.FlightSegment
 * JD-Core Version:    0.6.0
 */