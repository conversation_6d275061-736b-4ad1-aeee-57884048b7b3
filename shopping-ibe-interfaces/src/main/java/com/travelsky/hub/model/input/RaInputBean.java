/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class RaInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4656754446836402449L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String flightDate;
/*     */   private String seatNumber;
/*     */   private String flightClass;
/*     */   private String seatType;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  53 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  60 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  67 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  74 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  81 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  88 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  95 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 102 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 109 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 116 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 123 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 130 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 137 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 144 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getSeatType()
/*     */   {
/* 151 */     return this.seatType;
/*     */   }
/*     */ 
/*     */   public void setSeatType(String seatType)
/*     */   {
/* 159 */     this.seatType = seatType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.RaInputBean
 * JD-Core Version:    0.6.0
 */