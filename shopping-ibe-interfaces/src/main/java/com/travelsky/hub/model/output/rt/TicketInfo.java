/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TicketInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1045910743956990613L;
/*    */   private String ticketNumber;
/*    */   private Coupon coupon;
/*    */ 
/*    */   public String getTicketNumber()
/*    */   {
/* 30 */     return this.ticketNumber;
/*    */   }
/*    */ 
/*    */   public void setTicketNumber(String ticketNumber)
/*    */   {
/* 37 */     this.ticketNumber = ticketNumber;
/*    */   }
/*    */ 
/*    */   public Coupon getCoupon()
/*    */   {
/* 44 */     return this.coupon;
/*    */   }
/*    */ 
/*    */   public void setCoupon(Coupon coupon)
/*    */   {
/* 51 */     this.coupon = coupon;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.TicketInfo
 * JD-Core Version:    0.6.0
 */