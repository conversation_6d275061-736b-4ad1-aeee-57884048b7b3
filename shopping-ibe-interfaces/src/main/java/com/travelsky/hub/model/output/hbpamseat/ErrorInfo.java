/*     */ package com.travelsky.hub.model.output.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ErrorInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2971890525740463039L;
/*     */   private String errorCode;
/*     */   private String errorText;
/*     */   private String ticketId;
/*     */   private String sequenceNumber;
/*     */   private String surName;
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/*  42 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/*  49 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public String getErrorText()
/*     */   {
/*  56 */     return this.errorText;
/*     */   }
/*     */ 
/*     */   public void setErrorText(String errorText)
/*     */   {
/*  63 */     this.errorText = errorText;
/*     */   }
/*     */ 
/*     */   public String getTicketId()
/*     */   {
/*  70 */     return this.ticketId;
/*     */   }
/*     */ 
/*     */   public void setTicketId(String ticketId)
/*     */   {
/*  77 */     this.ticketId = ticketId;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/*  84 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/*  91 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/*  98 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 105 */     this.surName = surName;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpamseat.ErrorInfo
 * JD-Core Version:    0.6.0
 */