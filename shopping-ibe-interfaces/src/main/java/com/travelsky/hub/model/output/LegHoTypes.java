/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class LegHoTypes
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 507163068777799465L;
/*    */   private String station;
/*    */   private HoldTypes HoldTypes;
/*    */ 
/*    */   public String getStation()
/*    */   {
/* 29 */     return this.station;
/*    */   }
/*    */ 
/*    */   public void setStation(String station)
/*    */   {
/* 36 */     this.station = station;
/*    */   }
/*    */ 
/*    */   public HoldTypes getHoldTypes()
/*    */   {
/* 43 */     return this.HoldTypes;
/*    */   }
/*    */ 
/*    */   public void setHoldTypes(HoldTypes holdTypes)
/*    */   {
/* 50 */     this.HoldTypes = holdTypes;
/*    */   }
/*    */ }

/* Location:           D:\海航科技文档\一步就坐\2022-01首都航网上值机生产包5.8.56\客户端需引入的jar\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.LegHoTypes
 * JD-Core Version:    0.6.0
 */