/*     */ package com.travelsky.hub.model.peentity.meal.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class QueryMealOrderRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7015643026079843177L;
/*     */   private OfficeInfo officeInfo;
/*     */   private String certificateNumber;
/*     */   private String certificateType;
/*     */   private String pnrCode;
/*     */   private FlightSegment flightSegment;
/*     */ 
/*     */   public OfficeInfo getOfficeInfo()
/*     */   {
/*  47 */     return this.officeInfo;
/*     */   }
/*     */ 
/*     */   public void setOfficeInfo(OfficeInfo officeInfo)
/*     */   {
/*  55 */     this.officeInfo = officeInfo;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/*  63 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/*  71 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/*  79 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/*  87 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getPnrCode()
/*     */   {
/*  95 */     return this.pnrCode;
/*     */   }
/*     */ 
/*     */   public void setPnrCode(String pnrCode)
/*     */   {
/* 103 */     this.pnrCode = pnrCode;
/*     */   }
/*     */ 
/*     */   public FlightSegment getFlightSegment()
/*     */   {
/* 111 */     return this.flightSegment;
/*     */   }
/*     */ 
/*     */   public void setFlightSegment(FlightSegment flightSegment)
/*     */   {
/* 119 */     this.flightSegment = flightSegment;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.QueryMealOrderRQ
 * JD-Core Version:    0.6.0
 */