/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SeatRuleInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List seatRule;
/*    */ 
/*    */   public List getSeatRule()
/*    */   {
/* 25 */     return this.seatRule;
/*    */   }
/*    */ 
/*    */   public void setSeatRule(List seatRule)
/*    */   {
/* 32 */     this.seatRule = seatRule;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatRuleInfo
 * JD-Core Version:    0.6.0
 */