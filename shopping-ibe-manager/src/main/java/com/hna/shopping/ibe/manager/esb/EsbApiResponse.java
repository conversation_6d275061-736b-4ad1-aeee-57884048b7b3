
package com.hna.shopping.ibe.manager.esb;

import com.alibaba.fastjson.JSONArray;
import com.hnair.opcnet.api.complextype.PageResult;
import com.hnair.opcnet.api.complextype.Result;
import com.hnair.opcnet.api.v2.ApiResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 *  ESB V2接口返回封装类
 */
@Data
public class EsbApiResponse<T> {
    protected Result result;
    private List<T> data;
    protected PageResult pageResult;
    protected Map<String, Object> extend;
    protected String head;

    public EsbApiResponse<T> wrapper(ApiResponse apiResponse, Class<T> tClass) {
        this.result = apiResponse.getResult();
        this.pageResult = apiResponse.getPageResult();
        this.extend = apiResponse.getExtend();
        this.head = apiResponse.getHead();

        JSONArray jsonArray = new JSONArray();
        jsonArray.addAll(apiResponse.getData());
        List<T> list = jsonArray.toJavaList(tClass);
        this.data = list;
        return this;
    }

}