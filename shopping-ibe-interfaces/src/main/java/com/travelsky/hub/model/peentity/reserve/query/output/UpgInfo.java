/*     */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class UpgInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5094591560270571760L;
/*     */   private String cabin;
/*     */   private String seatNumber;
/*     */   private String seniorCabin;
/*     */   private String seniorSeatNumber;
/*     */   private String seniorRBD;
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  48 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  55 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  62 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  69 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getSeniorCabin()
/*     */   {
/*  76 */     return this.seniorCabin;
/*     */   }
/*     */ 
/*     */   public void setSeniorCabin(String seniorCabin)
/*     */   {
/*  83 */     this.seniorCabin = seniorCabin;
/*     */   }
/*     */ 
/*     */   public String getSeniorSeatNumber()
/*     */   {
/*  90 */     return this.seniorSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeniorSeatNumber(String seniorSeatNumber)
/*     */   {
/*  97 */     this.seniorSeatNumber = seniorSeatNumber;
/*     */   }
/*     */ 
/*     */   public String getSeniorRBD()
/*     */   {
/* 104 */     return this.seniorRBD;
/*     */   }
/*     */ 
/*     */   public void setSeniorRBD(String seniorRBD)
/*     */   {
/* 111 */     this.seniorRBD = seniorRBD;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.UpgInfo
 * JD-Core Version:    0.6.0
 */