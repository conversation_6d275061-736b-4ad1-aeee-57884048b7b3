/*     */ package com.travelsky.hub.model.input.poolingbaggageallowance;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PoolingBaggageAllowanceInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3331447004216072160L;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String opType;
/*     */   private List<String> hostNumberList;
/*     */   private List<String> boardingNumberList;
/*     */   private String gfbaCode;
/*     */ 
/*     */   public String getAirline()
/*     */   {
/*  59 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/*  66 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  73 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  80 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  87 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  94 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 101 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 108 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 115 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 122 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getOpType()
/*     */   {
/* 129 */     return this.opType;
/*     */   }
/*     */ 
/*     */   public void setOpType(String opType)
/*     */   {
/* 136 */     this.opType = opType;
/*     */   }
/*     */ 
/*     */   public List<String> getHostNumberList()
/*     */   {
/* 143 */     return this.hostNumberList;
/*     */   }
/*     */ 
/*     */   public void setHostNumberList(List<String> hostNumberList)
/*     */   {
/* 150 */     this.hostNumberList = hostNumberList;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardingNumberList()
/*     */   {
/* 157 */     return this.boardingNumberList;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumberList(List<String> boardingNumberList)
/*     */   {
/* 164 */     this.boardingNumberList = boardingNumberList;
/*     */   }
/*     */ 
/*     */   public String getGfbaCode()
/*     */   {
/* 171 */     return this.gfbaCode;
/*     */   }
/*     */ 
/*     */   public void setGfbaCode(String gfbaCode)
/*     */   {
/* 178 */     this.gfbaCode = gfbaCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.poolingbaggageallowance.PoolingBaggageAllowanceInput
 * JD-Core Version:    0.6.0
 */