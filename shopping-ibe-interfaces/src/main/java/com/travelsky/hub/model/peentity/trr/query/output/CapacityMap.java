/*    */ package com.travelsky.hub.model.peentity.trr.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CapacityMap
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5492765296507170689L;
/*    */   private String key;
/*    */   private Capacity capacity;
/*    */ 
/*    */   public String getKey()
/*    */   {
/* 36 */     return this.key;
/*    */   }
/*    */ 
/*    */   public void setKey(String key)
/*    */   {
/* 43 */     this.key = key;
/*    */   }
/*    */ 
/*    */   public Capacity getCapacity()
/*    */   {
/* 50 */     return this.capacity;
/*    */   }
/*    */ 
/*    */   public void setCapacity(Capacity capacity)
/*    */   {
/* 57 */     this.capacity = capacity;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.CapacityMap
 * JD-Core Version:    0.6.0
 */