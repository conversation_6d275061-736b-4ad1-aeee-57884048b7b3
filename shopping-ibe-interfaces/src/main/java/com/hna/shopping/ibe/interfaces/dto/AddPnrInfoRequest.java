package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.BookInformation;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class AddPnrInfoRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2507858944539441796L;

	private String pnrNo;
	private BookInformation bookInfomation;
}
