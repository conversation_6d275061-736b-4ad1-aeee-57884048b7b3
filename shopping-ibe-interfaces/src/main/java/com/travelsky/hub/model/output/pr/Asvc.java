/*     */ package com.travelsky.hub.model.output.pr;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Asvc
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1L;
/*     */   private String emdTypeCode;
/*     */   private String subCode;
/*     */   private String ssrCode;
/*     */   private String couponNum;
/*     */   private String documentNum;
/*     */   private String bagWeight;
/*     */   private String emdStatus;
/*     */   private String connectionInfo;
/*     */   private String expense;
/*     */   private String comServiceName;
/*     */   private String autoIndicator;
/*     */ 
/*     */   public String getEmdStatus()
/*     */   {
/*  71 */     return this.emdStatus;
/*     */   }
/*     */ 
/*     */   public void setEmdStatus(String emdStatus)
/*     */   {
/*  79 */     this.emdStatus = emdStatus;
/*     */   }
/*     */ 
/*     */   public String getComServiceName()
/*     */   {
/*  85 */     return this.comServiceName;
/*     */   }
/*     */ 
/*     */   public void setComServiceName(String comServiceName)
/*     */   {
/*  92 */     this.comServiceName = comServiceName;
/*     */   }
/*     */ 
/*     */   public String getExpense()
/*     */   {
/* 101 */     return this.expense;
/*     */   }
/*     */ 
/*     */   public void setExpense(String expense)
/*     */   {
/* 108 */     this.expense = expense;
/*     */   }
/*     */ 
/*     */   public String getDocumentNum()
/*     */   {
/* 115 */     return this.documentNum;
/*     */   }
/*     */ 
/*     */   public void setDocumentNum(String documentNum)
/*     */   {
/* 123 */     this.documentNum = documentNum;
/*     */   }
/*     */ 
/*     */   public String getConnectionInfo()
/*     */   {
/* 131 */     return this.connectionInfo;
/*     */   }
/*     */ 
/*     */   public void setConnectionInfo(String connectionInfo)
/*     */   {
/* 138 */     this.connectionInfo = connectionInfo;
/*     */   }
/*     */ 
/*     */   public String getAutoIndicator()
/*     */   {
/* 145 */     return this.autoIndicator;
/*     */   }
/*     */ 
/*     */   public void setAutoIndicator(String autoIndicator)
/*     */   {
/* 152 */     this.autoIndicator = autoIndicator;
/*     */   }
/*     */ 
/*     */   public String getEmdTypeCode()
/*     */   {
/* 160 */     return this.emdTypeCode;
/*     */   }
/*     */ 
/*     */   public void setEmdTypeCode(String emdTypeCode)
/*     */   {
/* 168 */     this.emdTypeCode = emdTypeCode;
/*     */   }
/*     */ 
/*     */   public String getSubCode()
/*     */   {
/* 178 */     return this.subCode;
/*     */   }
/*     */ 
/*     */   public void setSubCode(String subCode)
/*     */   {
/* 185 */     this.subCode = subCode;
/*     */   }
/*     */ 
/*     */   public String getSsrCode()
/*     */   {
/* 192 */     return this.ssrCode;
/*     */   }
/*     */ 
/*     */   public void setSsrCode(String ssrCode)
/*     */   {
/* 200 */     this.ssrCode = ssrCode;
/*     */   }
/*     */ 
/*     */   public String getCouponNum()
/*     */   {
/* 208 */     return this.couponNum;
/*     */   }
/*     */ 
/*     */   public void setCouponNum(String couponNum)
/*     */   {
/* 216 */     this.couponNum = couponNum;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 224 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 231 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.pr.Asvc
 * JD-Core Version:    0.6.0
 */