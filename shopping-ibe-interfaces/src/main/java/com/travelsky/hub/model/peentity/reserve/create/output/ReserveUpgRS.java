/*    */ package com.travelsky.hub.model.peentity.reserve.create.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ReserveUpgRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1097831805967552061L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */   private String reverseNumber;
/*    */   private String reverseStatus;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 49 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 56 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 63 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 70 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ 
/*    */   public String getReverseNumber()
/*    */   {
/* 77 */     return this.reverseNumber;
/*    */   }
/*    */ 
/*    */   public void setReverseNumber(String reverseNumber)
/*    */   {
/* 84 */     this.reverseNumber = reverseNumber;
/*    */   }
/*    */ 
/*    */   public String getReverseStatus()
/*    */   {
/* 91 */     return this.reverseStatus;
/*    */   }
/*    */ 
/*    */   public void setReverseStatus(String reverseStatus)
/*    */   {
/* 98 */     this.reverseStatus = reverseStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.output.ReserveUpgRS
 * JD-Core Version:    0.6.0
 */