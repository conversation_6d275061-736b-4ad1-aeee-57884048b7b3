package com.hna.shopping.ibe.interfaces.dto.checkin;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * Zero
 * 2022/3/7 11:29
 **/
@Data
public class SeatMap {
    private int cols;
    private int rows;
    private List<List<String>> arrange;
    private String hasFClass;
    private String status;
    private List<SeatInfo> seat;
    private ExitInfo exit;

    @Data
    public static class SeatInfo{
        private String col;
        private String row;
        private BigDecimal price;
        private String seatStatus;
        private String styleclass;
        private String tips;
    }

    @Data
    public static class ExitInfo{
        private List<String> row;
    }
}
