package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.travelsky.ibe.client.pnr.RTResult;

import java.util.Date;

/**
 * longx.yang 2020/03/19
 * 分离出来，根据rt结果获取一些比较不好处理的信息
 */
public interface RtResultManager {
    /**
     * 暂未使用
     * @param rtResult
     * @param seg
     * @return
     */
    Date getBirthday(RTResult rtResult, AllChanTicketInfo seg);

    /**
     * 从ssr内获取生日，目前只对儿童、婴儿有效
     * @param rtResult
     * @param seg
     * @return
     */
    Date getBirthdayFromSsr(RTResult rtResult, AllChanTicketInfo seg);

    /**
     * 根据票号和rt结果获取psgrid(旅客序号)
     * @param rtResult
     * @param seg
     * @return
     */
    Integer getPaxIndex(RTResult rtResult, AllChanTicketInfo seg);

    /**
     * 根据票号和rt结果获取psgrid(旅客序号)
     * @param rtResult
     * @param seg
     * @return
     */
    String getPsgrID(RTResult rtResult, AllChanTicketInfo seg);

}
