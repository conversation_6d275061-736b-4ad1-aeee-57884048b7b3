/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TdcError
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6972540444195247536L;
/*    */   private String errorCode;
/*    */   private String pnr;
/*    */   private String airlinePaxReference;
/*    */   private String errorMessage;
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 39 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 46 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getPnr()
/*    */   {
/* 53 */     return this.pnr;
/*    */   }
/*    */ 
/*    */   public void setPnr(String pnr)
/*    */   {
/* 60 */     this.pnr = pnr;
/*    */   }
/*    */ 
/*    */   public String getAirlinePaxReference()
/*    */   {
/* 67 */     return this.airlinePaxReference;
/*    */   }
/*    */ 
/*    */   public void setAirlinePaxReference(String airlinePaxReference)
/*    */   {
/* 74 */     this.airlinePaxReference = airlinePaxReference;
/*    */   }
/*    */ 
/*    */   public String getErrorMessage()
/*    */   {
/* 81 */     return this.errorMessage;
/*    */   }
/*    */ 
/*    */   public void setErrorMessage(String errorMessage)
/*    */   {
/* 88 */     this.errorMessage = errorMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TdcError
 * JD-Core Version:    0.6.0
 */