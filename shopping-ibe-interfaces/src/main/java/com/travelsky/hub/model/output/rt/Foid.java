/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Foid
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7787869398826678766L;
/*    */   private String foidType;
/*    */   private String foidNumber;
/*    */ 
/*    */   public String getFoidNumber()
/*    */   {
/* 22 */     return this.foidNumber;
/*    */   }
/*    */ 
/*    */   public void setFoidNumber(String foidNumber)
/*    */   {
/* 29 */     this.foidNumber = foidNumber;
/*    */   }
/*    */ 
/*    */   public String getFoidType()
/*    */   {
/* 35 */     return this.foidType;
/*    */   }
/*    */ 
/*    */   public void setFoidType(String foidType)
/*    */   {
/* 42 */     this.foidType = foidType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Foid
 * JD-Core Version:    0.6.0
 */