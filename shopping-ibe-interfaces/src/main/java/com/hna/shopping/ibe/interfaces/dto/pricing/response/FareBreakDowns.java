package com.hna.shopping.ibe.interfaces.dto.pricing.response;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
@Data
public class FareBreakDowns implements Serializable {

    private static final long serialVersionUID = -1955552687032509110L;
    private String UnstructuredFareCalc;
    private int baseFare_Amount;
    private String baseFare_CurrencyCode;
    private String baseFare_FromCurrency;
    private double baseFare_Rate;
    private String baseFare_ToCurrency;
    private int equivFare_Amount;
    private String equivFare_CurrencyCode;
    private List<String> fareBasisCodes;
    private String passengerType;
    private String pricingSource;
    private List<TaxBreakDowns> taxBreakDowns;
    private int totalFare_Amount;
    private String totalFare_CurrencyCode;
    private String unstructuredFareCalc;


}