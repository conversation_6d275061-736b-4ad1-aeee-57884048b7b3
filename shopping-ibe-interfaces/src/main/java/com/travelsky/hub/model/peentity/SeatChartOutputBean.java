/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class SeatChartOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7595648660443903858L;
/*     */   private String seatMap;
/*     */   private String planeType;
/*     */   private List<Rules> seatRules;
/*     */   private String airlineCode;
/*     */   private String departureAirport;
/*     */   private String flightNumber;
/*     */   private List<LineInfo> lineInfos;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getSeatMap()
/*     */   {
/*  23 */     return this.seatMap;
/*     */   }
/*     */ 
/*     */   public void setSeatMap(String seatMap)
/*     */   {
/*  30 */     this.seatMap = seatMap;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/*  39 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/*  46 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public List<Rules> getSeatRules()
/*     */   {
/*  55 */     return this.seatRules;
/*     */   }
/*     */ 
/*     */   public void setSeatRules(List<Rules> seatRules)
/*     */   {
/*  62 */     this.seatRules = seatRules;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  71 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  78 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  88 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  95 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 104 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 111 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public List<LineInfo> getLineInfos()
/*     */   {
/* 119 */     return this.lineInfos;
/*     */   }
/*     */ 
/*     */   public void setLineInfos(List<LineInfo> lineInfos)
/*     */   {
/* 126 */     this.lineInfos = lineInfos;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 135 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 142 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatChartOutputBean
 * JD-Core Version:    0.6.0
 */