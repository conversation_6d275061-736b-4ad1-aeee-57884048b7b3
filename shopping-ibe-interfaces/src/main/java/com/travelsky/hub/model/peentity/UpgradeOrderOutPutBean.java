/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class UpgradeOrderOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String isSuccess;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String orderNum;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 32 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 39 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 46 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 53 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 60 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 67 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 75 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 83 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 90 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 97 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgradeOrderOutPutBean
 * JD-Core Version:    0.6.0
 */