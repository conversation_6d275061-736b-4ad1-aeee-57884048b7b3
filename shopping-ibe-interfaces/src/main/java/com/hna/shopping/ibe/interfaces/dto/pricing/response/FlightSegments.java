package com.hna.shopping.ibe.interfaces.dto.pricing.response;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
@Data
public class FlightSegments implements Serializable {

    private static final long serialVersionUID = -734315862773334831L;
    private String arrivalAirport;
    private long arrivalDateTime;
    private String departureAirport;
    private long departureDateTime;
    private String fareBasis;
    private String fareruleid;
    private String flightNumber;
    private String freeBaggageAllowance;
    private int globalIndicator;
    private String marketingAirline;
    private long notValidAfter;
    private long notValidBefore;
    private String operatingAirline;
    private int passengerTypeQuantity;
    private String rPH;
    private List<Rebookrbdcodes> rebookrbdcodes;
    private String requestBookDesigCode;
    private String resBookDesigCode;
    private List<String> responseBookDesigCodes;
    private int status;
    private int stopQuantity;
    private boolean stopoverPermitted;


}