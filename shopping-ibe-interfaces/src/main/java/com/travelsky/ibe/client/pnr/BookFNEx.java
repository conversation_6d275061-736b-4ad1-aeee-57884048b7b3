package com.travelsky.ibe.client.pnr;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Locale;

public class BookFNEx extends com.hna.shopping.ibe.interfaces.dto.FareBox {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8853457493047342556L;

	private List<String> psgrname = new ArrayList<String>();   
    
	private String inputCode = null;  

	public BookFNEx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public void addPsgrname(String newPsgrname)                                                      
	{                                                                                                
	  if ((newPsgrname != null) && (newPsgrname.length() > 0))                                       
	    this.psgrname.add(newPsgrname);                                                       
	}                                                                                                
	                                                                                                 
	public String getFn()                                                                            
	{                                                                                                
	  return super.getFn();                                                                                
	}                                                                                                
	                                                                                                 
                                                                                            
	                                                                                                 
	public void setFn(String newFn)                                                                  
	{                                                                                                
	  String newFn1 = newFn.trim().toUpperCase(Locale.ENGLISH);
	  if ((newFn1.startsWith("FN/")) || (newFn1.startsWith("FN:")) || (newFn1.startsWith("FN ")))    
	    newFn1 = newFn1.substring(3);                                                                
	  super.setFn(newFn1);                                                                              
	}                                                                                                
	                                                                                                 
	public BookFNEx(String fn, String name)                                                            
	{                                                                                                
	  setFn(fn);                                                                                     
	  if ((name != null) && (name.length() > 0))                                                     
	    this.psgrname.add(name);                                                              
	}                                                                                                
	                                                                                                 
	public BookFNEx(String fn, String name, boolean inf)                                               
	{                                                                                                
	  setFn(fn);                                                                                     
	  if ((name != null) && (name.length() > 0))                                                     
	    this.psgrname.add(name);                                                              
	  super.setInfant( inf);                                                                             
	}                                                                                                
	                                                                                                 
	public boolean isInfant() {                                                                      
	  return super.isInfant();                                                                            
	}                                                                                                
	                                                                                                 
	public BookFNEx(String fn)                                                                         
	{                                                                                                
	  setFn(fn);                                                                                     
	}                                                                                                
	                                                                                                 
	public void setInfant(boolean newInfant)                                                         
	{                                                                                                
	  super.setInfant(newInfant);                                                                       
	}                                                                                                
	                                                                                                 
	public void setAssociatedPassengerName(String name) throws UnsupportedOperationException {       
	  this.psgrname.clear();                                                                         
	  this.psgrname.add(name);                                                                       
	}                                                                                                
	                                                                                                 
	public void setAssociatedPassengerName(Collection<String> names)                                 
	  throws UnsupportedOperationException, NullPointerException                                     
	{                                                                                                
	  this.psgrname.clear();                                                                         
	  this.psgrname.addAll(names);                                                                   
	}                                                                                                
	                                                                                                 
	public void setAppliedForInfant(boolean infant) throws UnsupportedOperationException             
	{                                                                                                
	  setInfant(infant); }                                                                           
	                                                                                                 
	public String getInputCode() {                                                                   
	  return this.inputCode; }                                                                       
	                                                                                                 
	public void setInputCode(String inputCode) {                                                     
	  this.inputCode = inputCode;                                                                    
	}

	public List<String> getPsgrname() {
		return psgrname;
	}

	public void setPsgrname(List<String> psgrname) {
		this.psgrname = psgrname;
	}

                                                                                          

}
