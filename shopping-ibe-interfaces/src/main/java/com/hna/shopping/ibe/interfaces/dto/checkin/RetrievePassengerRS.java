package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:06
 * @Version 1.0
 */
@Data
public class RetrievePassengerRS extends BaseResponse {
    @ApiModelProperty("乘机人姓名")
    private String pName;
    @ApiModelProperty("乘机人英文姓名")
    private String pEnName;
    @ApiModelProperty("旅客值机状态 AC：已值机 SB：候补 DL：拉下 NA：未值机 BD：登机")
    private String pCiStatus;
    @ApiModelProperty("常旅客等级")
    private String ffLevel;
    @ApiModelProperty("ASR 旅客座位")
    private String asrSeat;
    @ApiModelProperty("常客卡等级")
    private String cardLevel;
    @ApiModelProperty("常客卡号")
    private String cardId;
    @ApiModelProperty("常客卡公司")
    private String cardAirline;
    @ApiModelProperty("舱位")
    private String cabinType;
    @ApiModelProperty("特殊服务信息")
    private String speicialSvc;
    @ApiModelProperty("计划起飞时间")
    private String schDeptTime;
    @ApiModelProperty("始发城市三字码")
    private String fromCity;
    @ApiModelProperty("到达城市三字码")
    private String toCity;
    @ApiModelProperty("预计起飞时间")
    private String expDeptTime;
    @ApiModelProperty("航班是否初始化")
    private boolean flightOpened;
    @ApiModelProperty("登机时间")
    private String boardingTime;
    @ApiModelProperty("登机口")
    private String boardingGateNumber;
    @ApiModelProperty("机型")
    private String planeType;
    @ApiModelProperty("承运方航班号")
    private String carrFlightNo;
    @ApiModelProperty("值机渠道 WEB：网上值机 MAP：手机值机 SMS：短信值机 WEIX：微信值机")
    private String ckiInChannel;
    @ApiModelProperty("预留座位标示0：不是ASR座位 1：订座ASR座位 2：离港ASR座位 3：无预留座位")
    private String asrStatus;
    @ApiModelProperty("主机序号")
    private String hostNum;
    @ApiModelProperty("儿童标志")
    private String chdFlag;
    @ApiModelProperty("订票儿童标示")
    private String chd;
    @ApiModelProperty("asvc信息")
    private List<SyprAsvcBean> asvcInfo;
    @ApiModelProperty("票号")
    private String tkNo;

    @Data
    public static class SyprAsvcBean {
        @ApiModelProperty("emd票号")
        private String emdNumber;
        @ApiModelProperty("COUPON编号,航段序号")
        private String couponNum;
        @ApiModelProperty("服务标示\n" +
                "PDBG：付费行李\n" +
                "SEAT：付费座位\n" +
                "PDUG：付费升舱")
        private String ssrCode;
        @ApiModelProperty("asvc票面状态\n" +
                "0：EMD Status uncerten(状态未确定)\n" +
                "1：EMD Check in（已值机）\n" +
                "2：EMD Open for use（未值机）\n" +
                "3：EMD service failed（主机调用EMD 的外系统出现错误产生）")
        private String asvcStatus;
        @ApiModelProperty("行李重量")
        private String bagWeight;
        @ApiModelProperty("座位号")
        private String seatNo;
        @ApiModelProperty("费用")
        private String expense;
    };
}


