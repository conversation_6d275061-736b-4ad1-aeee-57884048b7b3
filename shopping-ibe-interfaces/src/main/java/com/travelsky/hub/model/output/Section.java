/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Section
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String rowNumber;
/*    */   private String rowSequence;
/*    */   private String rowAttributes;
/*    */ 
/*    */   public String getRowNumber()
/*    */   {
/* 28 */     return this.rowNumber;
/*    */   }
/*    */ 
/*    */   public void setRowNumber(String rowNumber)
/*    */   {
/* 35 */     this.rowNumber = rowNumber;
/*    */   }
/*    */ 
/*    */   public String getRowSequence()
/*    */   {
/* 42 */     return this.rowSequence;
/*    */   }
/*    */ 
/*    */   public void setRowSequence(String rowSequence)
/*    */   {
/* 49 */     this.rowSequence = rowSequence;
/*    */   }
/*    */ 
/*    */   public String getRowAttributes()
/*    */   {
/* 56 */     return this.rowAttributes;
/*    */   }
/*    */ 
/*    */   public void setRowAttributes(String rowAttributes)
/*    */   {
/* 63 */     this.rowAttributes = rowAttributes;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Section
 * JD-Core Version:    0.6.0
 */