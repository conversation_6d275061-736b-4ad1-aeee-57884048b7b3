package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class DtoTicketInfoByFlightInfoAndNameRequest implements Serializable {

	/**
	 * 航班号。RTEE清洗后的承运航班号。必输。HU0496 （航司代码+4位航班号，如航班号为3位需补0为4位。如HU469需输入HU0469）
	 */
	private String flightNo;

	/**
	 * 航班日期。只能为指定日期，不能为日期范围. 格式： yyyyMMdd
	 */
	private String flightDate;

	/**
	 * 旅客姓名
	 */
	private String passengerName;

}
