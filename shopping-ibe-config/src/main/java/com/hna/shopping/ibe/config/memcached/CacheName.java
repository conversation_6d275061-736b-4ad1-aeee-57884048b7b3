/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.memcached;

import java.util.Map;

import com.google.common.collect.Maps;

/**
 * 统一定义 CacheName 值, 避免 String 到处飞
 *
 * <AUTHOR>
 */
public class CacheName {
    // 默认 cachename 空间
    public static final String DEFAULT = "base_demo";

    // 航班查询 缓存空间
    public static final String FLIGHT_SEARCH = "flight_search";

    public static final String AV_STOP_CITY = "AV_STOP_CITY";//48小时

    // av 查询 缓存空间
    //public static final String AV_1 = "av_1";//1分钟
    public static final String AV_5 = "av_5";//5分钟
//    public static final String AV_10 = "av_10";//10分钟
//    public static final String AV_30 = "av_30";//30分钟
//    public static final String AV_60 = "av_60";//1小时
//    public static final String AV_120 = "av_120";//2小时
//    public static final String AV_360 = "av_360";//6小时
//    public static final String AV_1440 = "av_1440";//24小时
    //public static final String[] AV_SPACES = new String[]{AV_10,AV_30,AV_60,AV_120,AV_360,AV_1440};
//public static final String[] AV_SPACES = new String[]{AV_10,AV_30};
    private static final String[] AV_SPACES = new String[]{AV_5};


    // 需要在这里配置每个 cacheName 的超时时间, 单位为 秒; (Enum 看起来比较好, 但是在注解上没法使用)
    // 每个 CacheName 都必须配置, 否则启动报错
    public static final Map<String, Integer> CACHE_NAME2_EXPIRATION_SECONDS = Maps.newHashMap();

    static {
        // 默认缓存时间, 10 分钟
        CACHE_NAME2_EXPIRATION_SECONDS.put(DEFAULT, 10 * 60);

        // 缓存过期时间 15 分钟
        CACHE_NAME2_EXPIRATION_SECONDS.put(FLIGHT_SEARCH, 15 * 60);


        CACHE_NAME2_EXPIRATION_SECONDS.put(AV_STOP_CITY, 48 * 60 * 60);//48小时

        // 缓存过期时间 10 分钟
        for(String avSpace : AV_SPACES) {
            int count = Integer.parseInt(avSpace.split("\\_")[1]);
            CACHE_NAME2_EXPIRATION_SECONDS.put(avSpace, count * 60);
        }
    }
}
