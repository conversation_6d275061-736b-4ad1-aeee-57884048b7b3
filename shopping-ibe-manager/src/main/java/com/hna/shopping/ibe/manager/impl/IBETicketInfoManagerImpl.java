package com.hna.shopping.ibe.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.responsecode.CodeIbe;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.common.util.PassengerType;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPassengerNoRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPnrRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoTicketNoRequest;
import com.hna.shopping.ibe.manager.RtResultManager;
import com.hna.shopping.ibe.manager.SegInfoManager;
import com.hna.shopping.ibe.manager.TicketInfoManager;
import com.hna.shopping.ibe.manager.ibe.IBERetry;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.hna.shopping.ibe.manager.util.REMatchUtil;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.client.pnr.DETRHistoryInfoItem;
import com.travelsky.ibe.client.pnr.DETRHistoryResult;
import com.travelsky.ibe.client.pnr.PNRInfant;
import com.travelsky.ibe.client.pnr.PNRPassenger;
import com.travelsky.ibe.client.pnr.PNRResp;
import com.travelsky.ibe.client.pnr.PNRSSR;
import com.travelsky.ibe.client.pnr.PNRSSR_FOID;
import com.travelsky.ibe.client.pnr.PNRTktNo;
import com.travelsky.ibe.client.pnr.RTResult;
import com.travelsky.ibe.exceptions.RTNotAuthorizedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 只处理客票信息查询
 * longx.yang 2019/12/17
 */
@Component("ibeTicketInfoManager")
@Slf4j
public class IBETicketInfoManagerImpl implements TicketInfoManager {

    private final static int CYCLE_MAX_NUM = 4;

    @Autowired(required = false)
    private IBERetry ibeRetry;

    @Autowired
    private SegInfoManager segInfoManager;

    @Autowired
    private RtResultManager rtResultManager;

    @Autowired
    private AsyncGetTicketInfo asyncGetTicketInfo;

    private static ListeningExecutorService EXECUTOR_SERVICE =
            MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(200));

    //根据pnr调用这个接口目前报错detr.getAllChanTicketInfoByPNR(request.getPnrNo())
    @Override
    public DtoAllChanTicketInfoResponse getAllChanTicketInfoByPnr(DtoTicketInfoPnrRequest request) {
        DtoAllChanTicketInfoResponse response = new DtoAllChanTicketInfoResponse();
        DETR detr = IbeCmd.getIbeClient(request.getAirlineCode(), DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(request.getAirlineCode(), DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            //根据pnr调用这个接口目前报错detr.getAllChanTicketInfoByPNR(request.getPnrNo())
            Vector<DETRTKTResult> vector = detr.getTicketInfoByPNR(request.getPnrNo());
            if (!vector.isEmpty()) {
                for (DETRTKTResult result : vector) {
                    response.getTicketInfos().addAll(mapTicketInfoResult(request.getAirlineCode(), false, result, detr2f, 0, null,
                            request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), 0, secondFactorCode, secondFactorValue));
                }
            }
        } catch (Exception e) {
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "通过pnrdetr失败", new RuntimeException("通过pnrdetr失败"));
        }
        return response;
    }

    @Override
    public DtoAllChanTicketInfoResponse getAllChanTicketInfoByTicketNo(DtoTicketInfoTicketNoRequest request) {
        DtoAllChanTicketInfoResponse response = new DtoAllChanTicketInfoResponse();
        DETR detr = IbeCmd.getIbeClient(request.getAirlineCode(), DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(request.getAirlineCode(), DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        DETRTKTResult result = detrDoubleTime(request.getTicketNo(), detr2f, secondFactorCode, secondFactorValue);
        response.setTicketInfos(mapTicketInfoResult(request.getAirlineCode(), false, result, detr2f, 0, null,
                request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), 0, secondFactorCode, secondFactorValue));
        return response;
    }

    private DETRTKTResult detrDoubleTime(String ticketNo, DETR2F detr2f, String secondFactorCode, String secondFactorValue) {
        DETRTKTResult result = null;
        for (int i = 0; i < 2; i++) {
            try {
                result = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
                break;
            } catch (Exception e) {
                log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ,next step retry", ticketNo, detr2f.getTxnTraceKey());
            }
        }
        if (result == null) {
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "通过票号detr失败", new RuntimeException("通过票号detr失败"));
        }
        return result;
    }

    /**
     * 证件号只返回open for use的，否则可能会很慢
     *
     * @param request
     * @return
     */
    @Override
    public DtoAllChanTicketInfoResponse getAllChanTicketInfoByPassengerNo(DtoTicketInfoPassengerNoRequest request) {
        DETR detr = IbeCmd.getIbeClient(request.getAirlineCode(), DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(request.getAirlineCode(), DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        DtoAllChanTicketInfoResponse response = new DtoAllChanTicketInfoResponse();
        try {
            //证件号只提取open for use的
            Vector<DETRDigestResult> vector;
            if (org.springframework.util.StringUtils.hasText(request.getDepCode())) {
               vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), request.getDepCode().toLowerCase());
            } else {
                vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), true, request.getAirlineCode(), null);
            }

            //没有命中，都查一次
            if (vector == null || vector.isEmpty()) {
                vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), null);
            }

            if (vector != null && !vector.isEmpty()) {
                log.info("ticketlist.size():" + vector.size() + "begin");
                CountDownLatch countDownLatch = new CountDownLatch(vector.size());
                List<Future<List<AllChanTicketInfo>>> listenableFutureList = new ArrayList<>();
                int i = 0;

                for (DETRDigestResult result : vector) {
                    listenableFutureList.add(asyncGetTicketInfo.mulThreadGetTicketInfo(request.getAirlineCode(), countDownLatch, detr2f, result.getTktno(), request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), secondFactorCode, secondFactorValue));
                    i++;
                    //目前只支持最多提取10张
                    if (i >= 10){
                        break;
                    }
                }
                countDownLatch.await(10, TimeUnit.SECONDS);
                for (Future<List<AllChanTicketInfo>> listListenableFuture : listenableFutureList) {
                    List<AllChanTicketInfo> list = null;
                    try {
                        list = listListenableFuture.get(5, TimeUnit.SECONDS);
                    } catch (Exception e) {
                        log.error("超时", e);
                    }
                    if (list != null) {
                        response.getTicketInfos().addAll(list);
                    }
                }
                log.info("ticketlist.size():" + vector.size() + "end");
            }
            log.info(" detr passengerNo: {}  passengerType: {},getTxnTraceKey: {} ", request.getPassengerNo(), request.getPassengerType(), detr.getTxnTraceKey());
        } catch (Exception e) {
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "通过证件类型detr失败", new RuntimeException("通过证件类型detr失败"));
        }
        response.setSuccess(true);
        return response;
    }

    @Override
    public Map<String, Object> getInfoFromHistory(DETR detr, String ticketNo) {
        try {
            Map<String, Object> map = new HashMap<>();
            DETRHistoryResult historyResult = detr.getTicketHistoryByTktNo(ticketNo);
            map.put("issueDate", historyResult.getInfoItem(0).getOperTime());
            //从历史明细中获取pnr
            for (Object o : historyResult.getInfoItem()) {
                DETRHistoryInfoItem infoItem = (DETRHistoryInfoItem) o;
                if ("EOTU".equals(infoItem.getOperType()) && infoItem.getOperDesc().contains("CLEARED")) {
                    //
                    int position = infoItem.getOperDesc().indexOf(" RL ");
                    map.put("pnr", infoItem.getOperDesc().substring(position + 4, position + 10));
                    break;
                }
            }
            return map;
        } catch (Exception e) {
            //暂未确定是那种异常
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "获取历史客票信息失败", new RuntimeException("获取历史客票信息失败"));
        }
    }


    /**
     * 暂不支持返回历史明细
     *
     * @param result
     * @param detr
     * @param index
     * @param segInfo
     * @param needBookTime
     * @param needTicketChangeHis
     * @param cycleNum            历史客票最多递归次数（改期次数太多导致耗时太久无法返回数据）
     * @return
     * @throws Exception
     */
    @Override
    public List<AllChanTicketInfo> mapTicketInfoResult(String airline, Boolean filterOpenforUse, DETRTKTResult result, DETR2F detr2f, Integer index, String segInfo, String needBookTime, String needTicketChangeHis, int cycleNum, String secondFactorCode, String secondFactorValue) {
        long ticketStart = new Date().getTime();
        log.info("ticketNo:" + result.getTicketNo() + " mapAllChanTicketInfoResult start date:" + ticketStart);
        List<AllChanTicketInfo> ticket = new ArrayList<>();
        if (cycleNum++ == CYCLE_MAX_NUM) {
            return ticket;
        }

        for (int i = 0; i < result.getSegmentCount(); ++i) {
            DETRTKTSegment detrtktSegment = result.getSegment(i);
            //过滤o仓
            /*if (testIsOCabin(detrtktSegment)){
                continue;
            }*/
            if (StringUtils.isEmpty(airline)){
                airline = detrtktSegment.getAirline();
            }
            //支持根据客票过滤状态，暂不实现
            if (index == 1 && !(detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode()).equals(segInfo)) {
                continue;
            }
            AllChanTicketInfo seg = segInfoManager.getBaseAllChanTicketInfo(result, i, detrtktSegment, detr2f, secondFactorCode, secondFactorValue);
            segInfoManager.getOtherAllChanTicketInfo(result, detr2f, seg, secondFactorCode, secondFactorValue);
            //获取历史明细
            if (StringUtils.isNotEmpty(needTicketChangeHis)) {
                getHisTicketInfo(airline, result, detr2f, cycleNum, seg, secondFactorCode, secondFactorValue);
            }
            if (result.getETicketType() != 11 && result.getETicketType() != 13) { //国际票往返程是价格总和，不支持分开计算
                seg.setTicketType(0);//国内票标识
            } else {
                seg.setTicketType(1);//国内票标识
            }
            ticket.add(seg);
        }
        if(ticket.size() == 0 ){
            return ticket;
        }
        com.travelsky.ibe.client.pnr.RT rt = IbeCmd.getIbeClient(airline, com.travelsky.ibe.client.pnr.RT.class);
        com.travelsky.ibe.client.pnr.RTResult rtResult = null;//票号里的编码相同，rt一次即可
        boolean isCompletelyPnr = false;//提取的历史
        Date bookTime = null;
        String passengerId = "";
        Map<String, Date> arriveTimeMap = new HashMap<>();
        Map<String, String> pnrStatusMap = new HashMap<>();
        if (ticket.size() > 0) {
            AllChanTicketInfo seg = ticket.get(0);
            if (null != seg.getPnr() && !"".equals(seg.getPnr())) {
                //提取pnr状态信息
                try {
                    try {
                        rtResult = rt.retrieve(seg.getPnr());
                    } catch (Exception e) {
                        log.info(" rt pnr: {} , getTxnTraceKey: {}, message: {} ", seg.getPnr(), rt.getTxnTraceKey(), e.getMessage());
                    }
                    if (rtResult == null) {
                        //pnr编号无相应信息或已经删除,只能通过历史记录
                        try {
                            rtResult = rt.retrieveCompletely(seg.getPnr());
                        } catch (Throwable e) {
                            if (e.getMessage().contains("NoPNRException") || e instanceof RTNotAuthorizedException) {
                                //pnr编号无相应信息或已经删除,只能通过历史记录
                                DETRHistoryResult ticketHistoryByTktNo = detr2f.getTicketHistoryByTktNo2F(result.getTicketNo(), "N", secondFactorCode, secondFactorValue);
                                //提取预定时间
                                bookTime = ticketHistoryByTktNo.getIssueDate();
                                seg.setIssueDate(bookTime);
                                //如果是历史编码
                                for (AllChanTicketInfo ticketInfo : ticket) {
                                    ticketInfo.setBookTime(bookTime);
                                    setTicketInfo(ticketHistoryByTktNo, ticketInfo);
                                }
                                return ticket;
                            }
                            throw e;
                        }
                        //提取预定时间
                        if (org.springframework.util.StringUtils.hasText(needBookTime)) {
                            parsePnrHis(rtResult, seg);
                            bookTime = seg.getBookTime();
                        }
                        isCompletelyPnr = true;
                    }
                    //获取birthday
                    seg.setBirthDay(rtResultManager.getBirthday(rtResult, seg));
                    //rtResult存在
                    if (org.springframework.util.StringUtils.hasText(needBookTime) && !isCompletelyPnr) {
                        com.travelsky.ibe.client.pnr.RTResult rtResult1 = rt.retrieveCompletely(seg.getPnr());
                        //获取PNR预定时间
                        parsePnrHis(rtResult1, seg);
                        bookTime = seg.getBookTime();
                        isCompletelyPnr = true;
                    }

                    if (!isCompletelyPnr) {
                        notCompletelyPnrInfo(ticket, airline, rtResult, pnrStatusMap);
                        passengerId = getPassengerId(rtResult, seg);
                        //读取到达时间
                        for (int j = 0; j < rtResult.getAirSegsCount(); j++) {
                            PNRAirSeg pnrSeg = rtResult.getAirSegAt(j);
                            arriveTimeMap.put(pnrSeg.getOrgCity() + pnrSeg.getDesCity(), pnrSeg.getArrivalTime());
                        }
                        com.travelsky.ibe.client.pnr.PNRResp pnrResp = rtResult.getResp();
                        for (AllChanTicketInfo ticketInfo : ticket) {
                            ticketInfo.setBookTime(bookTime);
                            if(!StringUtils.isEmpty(passengerId)) {
                                ticketInfo.setPassengerID(passengerId);
                            }
                            ticketInfo.setArrTime(arriveTimeMap.get(ticketInfo.getDepCode() + ticketInfo.getArrCode()));
                            ticketInfo.setOffice(pnrResp.getOfficecode());
                            ticketInfo.setPnrStatus(pnrStatusMap.get(ticketInfo.getDepCode() + ticketInfo.getArrCode()));
                            //婴儿获取同行成人的信息，成人获取携带婴儿的信息(只拿一次)
                            if (cycleNum == 1) {
                                getAccompaniedPersonInfo(rtResult, ticketInfo, detr2f, secondFactorCode, secondFactorValue);
                            }

                        }
                    } else {
                        com.travelsky.ibe.client.pnr.PNRResp pnrResp = rtResult.getResp();
                        //如果是历史编码
                        for (AllChanTicketInfo ticketInfo : ticket) {
                            ticketInfo.setBookTime(bookTime);
                            ticketInfo.setOffice(pnrResp.getOfficecode());
                            if (rtResult != null) {
                                Vector vector = rtResult.getAirSegs();
                                Iterator<PNRAirSeg> it = vector.iterator();
                                while (it.hasNext()) {
                                    PNRAirSeg pnrAirSeg = it.next();
                                    String desCity = pnrAirSeg.getDesCity();
                                    String orgCity = pnrAirSeg.getOrgCity();
                                    if (ticketInfo.getArrCode().equals(desCity) || ticketInfo.getDepCode().equals(orgCity)) {
                                        ticketInfo.setPnrStatus(pnrAirSeg.getActionCode());
                                    }
                                }
                                if (cycleNum == 1) {
                                    getAccompaniedPersonInfo(rtResult, ticketInfo, detr2f, secondFactorCode, secondFactorValue);
                                }
                            } else {
                                ticketInfo.setPnrStatus("RR");
                            }
                            searchMessageFromOringinalRT(rtResult, ticketInfo);
                        }
                    }
                } catch (Exception e) {
                    log.error("mapAllChanTicketInfoResult" + seg.getTicketNo(), e);
                    throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "获取客票信息失败", new RuntimeException("获取客票信息失败"));
                }



            }
        }
        log.info("ticketNo:" + result.getTicketNo() + " mapAllChanTicketInfoResult end date:" + (new Date().getTime() - ticketStart));
        return ticket;
    }

    private void setTicketInfo(DETRHistoryResult ticketHistoryByTktNo, AllChanTicketInfo ticketInfo) {
        for (Object o : ticketHistoryByTktNo.getInfoItem()) {
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(o));
            String operType = jsonObject.getString("operType");
            if (operType.equals("TRMK")) {
                String operDesc = jsonObject.getString("operDesc");
                Pattern compile = Pattern.compile("\\w{3}\\d{3}");
                Matcher matcher = compile.matcher(operDesc);
                if (matcher.find()) {
                    ticketInfo.setOffice(matcher.group());
                }
            }
            if (operType.equals("EOTU")) {
                String flightNo = null;
                String operDesc = jsonObject.getString("operDesc");
                Pattern compile = Pattern.compile("\\w{2}\\d{4}");
                Matcher matcher = compile.matcher(operDesc);
                if (matcher.find()) {
                    flightNo = matcher.group();
                }
                if (flightNo == null) {
                    continue;
                }
                for (String s : operDesc.split("\\s")) {
                    if (s.contains(flightNo)) {
                        String[] split = s.split("/");
                        String deparr = split[split.length - 1];
                        if (!Objects.equals(deparr, String.join("", ticketInfo.getDepCode(), ticketInfo.getArrCode()))) {
                            continue;
                        }
                        ticketInfo.setFlightNo(flightNo);
                        for (int i = 0; i < split.length; i++) {
                            if (i == 0) {
                                continue;
                            }
                            if (i == 1) {
                                ticketInfo.setFlightDate(DateUtils.parseDate(split[i], new String[]{"ddMMMyy"}));
                                ticketInfo.setDepTime(DateUtils.parseDate(split[i], new String[]{"ddMMMyy"}));
                            }
                        }
                    }
                }
            }

        }

    }

    public void getHisTicketInfo(String airline, DETRTKTResult result, DETR2F detr2f, int cycleNum, AllChanTicketInfo seg, String secondFactorCode, String secondFactorValue) {
        //OI换开的
        String oldTicket = result.getExchangeInfo();
        String[] arr = null;
        if (null != result.getSigningInfo()) {
            arr = result.getSigningInfo().split(",");
        }
        //改升的
        if (null != oldTicket && "".equals(oldTicket)) {
            if (null != arr && arr.length > 1) {
                oldTicket = arr[1];
            }
        }
        Boolean checkResult = ticketCheck(oldTicket);
        log.info("oldTicket:" + oldTicket + ",checkres:" + checkResult);
        //提取到的客票号符合客票号的规则去提取历史信息
        if (null != oldTicket && !"".equals(oldTicket) && checkResult) {
            //历史信息
            try {
                DETRTKTResult hisresult = detr2f.getTicketInfoByTktNo2F(oldTicket, false, "", "N", secondFactorCode, secondFactorValue);
                Long startTime = new Date().getTime();
                log.info("oldTicket:" + oldTicket + " find history start date:" + new Date().getTime());
                //进入这里说明需要递归获取历史客票记录needTicketChangeHis默认传非空
                seg.setHistoryTicketInfo(mapTicketInfoResult(airline,false, hisresult, detr2f, 1, seg.getDepCode() + seg.getArrCode(), "1"/*改期需要查历史编码*/, "1", cycleNum, secondFactorCode, secondFactorValue));
                log.info("oldTicket:" + oldTicket + " find history end date:" + (new Date().getTime() - startTime));
            } catch (Exception e) {
                //失败的话就不再继续递归获取历史记录明细
                log.error("获取历史明细失败:" + seg, e);
            }
        }
    }

    public void getAccompaniedPersonInfo(RTResult rtResult, AllChanTicketInfo ticketInfo, DETR2F detr2f, String secondFactorCode, String secondFactorValue) throws Exception {
        //婴儿备注一下成人客票
        PNRTktNo accompaniedPersonTicketNo = null;
        Vector<PNRTktNo> ticketNos = rtResult.getTktnos();
        String psgrID = "";
        for (PNRTktNo o : ticketNos) {
            String tktNo = o.getTktNo().split("-")[1];
            if (ticketInfo.getTicketNo().equals(tktNo) || (ticketInfo.getIssCode() + "-" + ticketInfo.getTicketNo()).equals(tktNo)) {
                //取一下儿童所在的组
                psgrID = o.getPsgrID();
            }
        }
        for (PNRTktNo o : ticketNos) {
            String tktNo = o.getTktNo().split("-")[1];
            if (!ticketInfo.getTicketNo().equals(tktNo) && psgrID.equals(o.getPsgrid())) {
                //获取携带或被携带乘客
                accompaniedPersonTicketNo = o;
            }
        }
        if (accompaniedPersonTicketNo != null) {
            //detr获取携带者的客票信息()
            try {
                DETRTKTResult result = detrDoubleTime(accompaniedPersonTicketNo.getTktNo(), detr2f, secondFactorCode, secondFactorValue);
                AllChanTicketInfo accompany = null;
                for (int i = 0; i < result.getSegmentCount(); ++i) {
                    DETRTKTSegment detrtktSegment = result.getSegment(i);
                    if (segInfoManager.testTwoSegOnOneFligh(ticketInfo, detrtktSegment)) {
                        accompany = segInfoManager.getBaseAllChanTicketInfo(result, i, detrtktSegment, detr2f, secondFactorCode, secondFactorValue);
                        segInfoManager.getOtherAllChanTicketInfo(result, detr2f, accompany, secondFactorCode, secondFactorValue);
                        //这些信息和随行人一样，直接从随行人获取
                        accompany.setArrTime(ticketInfo.getArrTime());
                        accompany.setPnrStatus(ticketInfo.getPnrStatus());
                        accompany.setOffice(ticketInfo.getOffice());
                        accompany.setBookTime(ticketInfo.getBookTime());
                    }
                }
                if (ticketInfo.getPassengerType().equals(PassengerType.PASSENGER_3.getAlias())) {
                    ticketInfo.setAccompaniedAuditTicketNo(accompaniedPersonTicketNo.getTktNo().substring(4));//不要前三位结算码和－
                    ticketInfo.setAdtInfo(accompany);
                }
                //这里需要确定的是成年人是否会在同一组
                else if (ticketInfo.getPassengerType().equals(PassengerType.PASSENGER_0.getAlias())) {
                    ticketInfo.setAccompaniedInfantTicketNo(accompaniedPersonTicketNo.getTktNo().substring(4));//不要前三位结算码和－
                    //获取婴儿信息
                    ticketInfo.setInfantInfo(accompany);
                    //这里是获取同行人的生日，要传同行人的客票信息accompany
                    accompany.setBirthDay(rtResultManager.getBirthday(rtResult, accompany));
                }
            } catch (Exception e) {
                //失败不报错
                log.error("获取同行人信息失败", e);
            }
        }

    }

    /**
     * 从rt.retrieve结果获取passengerId
     *
     * @param rtResult
     * @param seg
     * @return
     * @throws Exception
     */
    public String getPassengerId(RTResult rtResult, AllChanTicketInfo seg) throws Exception {
        //读取旅客证件号
        String passengerId = null;
        int paxIndex = 0;
        for (int j = 0; j < rtResult.getPassengerNumber(); j++) {
            PNRPassenger p = rtResult.getPassengerAt(j);
            if (p.getName() != null && p.getName().equals(seg.getPassengerName())) {
                paxIndex = p.getIndex();
                break;
            }
        }
        for (int j = 0; j < rtResult.getSSRsCount(); j++) {
            PNRSSR ssr = rtResult.getSSRAt(j);
            if ("FOID".equals(ssr.getSSRType()) && ("P" + paxIndex).equals(ssr.getPsgrid())) {
                passengerId =  ((PNRSSR_FOID) ssr).getIdNo();
                break;
            }
        }

        if (!org.springframework.util.StringUtils.hasText(passengerId)) {
            //找护照号
            for(int j = 0; j< rtResult.getSSRsCount(); j++) {
                PNRSSR ssr = rtResult.getSSRAt(j);
                if ("DOCS".equals(ssr.getSSRType()) && ("P"+paxIndex).equals(ssr.getPsgrid())) {
                    passengerId = ((com.travelsky.ibe.client.pnr.PNRSSR_DOCS)ssr).getDoc_No();
                    break;
                }
            }
        }

        //没有获取到
        return passengerId;
    }

    /**
     * rt.retrieve成功的时候获取航段信息的逻辑rt.retrieve成功就不会再rt.retrieveCompletely
     *
     * @param ticket
     * @param airline
     * @param rtResult
     * @param pnrStatusMap
     */
    public void notCompletelyPnrInfo(List<AllChanTicketInfo> ticket, String airline, RTResult rtResult, Map<String, String> pnrStatusMap) {
        int segIndex = 0;
        for (Object obj : rtResult.getAirSegs()) {
            if (segIndex >= ticket.size()) {
                //往返程退了第二段，ticket只有一个，rtResult.getAirSegs()是2个
                break;
            }
            PNRAirSeg airSeg = (PNRAirSeg) obj;
            log.info("检查出的pnr是" + airline + "的航班" + airSeg.getAirNo());
            AllChanTicketInfo segTem = ticket.get(segIndex++);
            if (!segTem.getDepCode().equals(airSeg.getOrgCity())) {
                //往返程退了第一段时，ticket只有一个，rtResult.getAirSegs()是2个
                segIndex--;
                continue;
            }
            segTem.setFlightNo(airSeg.getAirNo());
            segTem.setArrTerm(airSeg.getArrivalTerm());
            segTem.setDepTerm(airSeg.getDepartureTerm());
            if (segTem.getDepTime() == null) segTem.setDepTime(airSeg.getDepartureTime());
            if (segTem.getFlightDate() == null) segTem.setFlightDate(airSeg.getDepartureTime());
            if (airSeg.getAirNo().substring(0, 2).equals(airline)) {
                pnrStatusMap.put(airSeg.getOrgCity() + airSeg.getDesCity(), airSeg.getActionCode());
            }
        }
    }

    private boolean testIsOCabin(DETRTKTSegment detrtktSegment){
        if("O".equals(detrtktSegment.getCabin()+"") || "o".equals(detrtktSegment.getCabin()+"")){
            return true;
        }
        return false;
    }

    public RTResult rt(String pnr, String airline) {
        RT rt = IbeCmd.getIbeClient(airline, com.travelsky.ibe.client.pnr.RT.class);
        RTResult rtResult = null;
        //提取pnr状态信息
        try {
            rtResult = rt.retrieve(pnr);
        } catch (Exception e) {
            log.info(" rt pnr: {} , getTxnTraceKey: {}, message: {} ", pnr, rt.getTxnTraceKey(), e.getMessage());
        }
        if (rtResult == null) {
            try {
                //pnr编号无相应信息或已经删除,只能通过历史记录
                rtResult = rt.retrieveCompletely(pnr);
            } catch (Exception e) {
                throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "通过历史记录rt失败", new RuntimeException("通过历史记录rt失败"));
            }
        }
        return rtResult;
    }

    /**
     * 获取换开信息，暂时没有使用到
     *
     * @param result
     * @return
     */
    @Override
    public String getExchangeInfo(DETRTKTResult result) {
        //OI换开的
        String oldTicket = result.getExchangeInfo();
        //历史记录信息
        String[] arr = null;
        if (null != result.getSigningInfo()) {
            arr = result.getSigningInfo().split(",");
        }
        //改升的
        if (null != oldTicket && "".equals(oldTicket)) {
            if (null != arr && arr.length > 1) {
                oldTicket = arr[1];
            }
        }
        return oldTicket;
    }

    //客票正则
    private boolean ticketCheck(String ticketNo) {
        String regEx = "[0-9]{3}(-)?[0-9]{10}";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(ticketNo);
        // 字符串是否与正则表达式相匹配
        return matcher.matches();
    }

    /**
     * 从编码里获取编码生成的月日时分
     *
     * @param rtResult
     * @param seg
     * @return
     * @throws ParseException
     */
    private void parsePnrHis(RTResult rtResult, AllChanTicketInfo seg) throws ParseException {
        String operateHis = rtResult.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        //读取pnr预定时间
        Map<String, String> map = new HashMap<String, String>();
        String pnrbookTime = "";
        for (String one : list) {
            one = one.replaceAll("\\s+", " ");
            if ((one.startsWith("001") || one.startsWith("002") || one.startsWith("003")) && (one.split(" ").length == 5 || one.split(" ").length == 6)) {
                log.info(one + ":" + one.split(" ").length);
                String[] details = one.split(" ");
                if (one.indexOf(" IK ") > 0) { //找到操作配置
                    pnrbookTime = map.get(details[1] + details[2]);
                    break;
                }
                map.put(details[1] + details[2], details[3] + details[4]);//key是配置 value是时间
            }
        }

        //格式化日期
        try {
            if (StringUtils.isNotEmpty(pnrbookTime)) {
                Calendar c = Calendar.getInstance();
                if (pnrbookTime.length() == 9) {//只有月日时分
                    Date bookTime = DateUtils.parseDate(pnrbookTime, new String[]{"HHmmddMMM"});
                    int year = c.get(Calendar.YEAR);
                    c.setTime(bookTime);
                    c.set(Calendar.YEAR, year);
                    if (bookTime.compareTo(seg.getIssueDate()) > 0) {//年份原因导致生编码时间大于出票时间
                        c.add(Calendar.YEAR, -1);
                    }
                    seg.setBookTime(c.getTime());

                } else {
                    Date bookTime = DateUtils.parseDate(pnrbookTime, new String[]{"HHmmddMMMyy"});
                    c.setTime(bookTime);
                    seg.setBookTime(bookTime);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void getArrTimeAndId(RTResult rtResult, AllChanTicketInfo ticketInfo) {
        String operateHis = rtResult.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        if (operateHis.contains(ticketInfo.getPassengerName()) && operateHis.contains(ticketInfo.getDepCode() + ticketInfo.getArrCode())) {

        }
    }

    /**
     * @param depTime 不能为空
     * @param depCode
     * @param arrCode
     * @param list    遍历operateHis.split("\r\n")
     * @return
     */
    public Date getArrTimeWithDepTime(Date depTime, String depCode, String arrCode, String[] list) {
        //1.1先知道起飞时间
        Calendar c = Calendar.getInstance();
        c.setTime(depTime);
        int hour = c.get(Calendar.HOUR_OF_DAY);
        int minutes = c.get(Calendar.MINUTE);
        String depTimeStr = " " + (hour > 9 ? hour : "0" + hour) + (minutes > 9 ? minutes : "0" + minutes) + " ";
        //1.2把内容解析出来
        String arrTime = "";
        for (String one : list) {
            one = one.replaceAll("\\s+", " ");
            int depTimeIndex = one.indexOf(depTimeStr);
            if (depTimeIndex > 0 && one.indexOf(depCode + arrCode) > -1) {
                //找到落地时间字符串
                arrTime = one.substring(depTimeIndex + depTimeStr.length(), depTimeIndex + depTimeStr.length() + 4);
                c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(arrTime.substring(0, 2)));
                c.set(Calendar.MINUTE, Integer.parseInt(arrTime.substring(2, 4)));
                return c.getTime();
            }
        }
        return null;
    }

    public Date getDepArrTimeWithoutDepTime(Calendar c, String[] timeArray, int index) {
        c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeArray[index].substring(0, 2)));
        c.set(Calendar.MINUTE, Integer.parseInt(timeArray[index].substring(2, 4)));
        return c.getTime();
    }

    public Date getFlightDate(String time, String fltDateStr) {
        Calendar c = Calendar.getInstance();
        Date fltDate = DateUtils.parseDate(fltDateStr.substring(3), new String[]{"ddMMM"});
        //TODO year，感觉没有实现，逻辑不完整
        int year = c.get(Calendar.YEAR);
        c.setTime(fltDate);
        //看不懂下面的逻辑
        c.set(Calendar.YEAR, year);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }


    public String getFlightNo(String one) {
        return REMatchUtil.searchkey("\\s*[A-Z]{2}\\d{3,4}\\s", one).trim();
    }


    //查找到达时间和证件号
    private void searchMessageFromOringinalRT(RTResult rtResult, AllChanTicketInfo ticketInfo) {
        String operateHis = rtResult.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        //判断编码是否和票中人一致
        if (operateHis.contains(ticketInfo.getPassengerName()) && operateHis.contains(ticketInfo.getDepCode() + ticketInfo.getArrCode())) {
            if (StringUtils.isEmpty(ticketInfo.getOffice())) {
                for (String one : list) {
                    String officeStr = REMatchUtil.searchkey("001\\s\\d{1,2}[.]\\w{3}\\d{3}", one);
                    if (StringUtils.isEmpty(officeStr)) {
                        continue;
                    }
                    String office = REMatchUtil.searchkey("\\w{3}\\d{3}", officeStr);
                    ticketInfo.setOffice(office);
                }
            }
            if (ticketInfo.getDepTime() != null) {
                ticketInfo.setArrTime(getArrTimeWithDepTime(ticketInfo.getDepTime(), ticketInfo.getDepCode(), ticketInfo.getArrCode(), list));
            } else {
                String arrTime = "";
                for (String one : list) {
                    String time = REMatchUtil.searchkey("\\s\\d{4}\\s\\d{4}", one); //特例  2.  JD5675 L   FR15FEB  PEKHGH NO1   2320 0140+1    E T1T3
                    String fltDateStr = REMatchUtil.searchkey("\\s[A-Z]{2}\\d{2}[A-Z]{3}\\s", one);
                    if (StringUtils.isNotEmpty(time) && StringUtils.isNotEmpty(fltDateStr)) {
                        if (!one.contains(String.join("", ticketInfo.getDepCode(), ticketInfo.getArrCode()))) {
                            continue;
                        }
                        //获取起飞时间，这里感觉之前的逻辑有点问题
                        Calendar c = Calendar.getInstance();
                        Date fltDate = DateUtils.parseDate(fltDateStr.substring(3), new String[]{"ddMMM"});
                        int year = c.get(Calendar.YEAR);
                        c.setTime(fltDate);
                        c.set(Calendar.YEAR, year);
                        c.set(Calendar.HOUR_OF_DAY, 0);
                        c.set(Calendar.MINUTE, 0);
                        c.set(Calendar.SECOND, 0);
                        ticketInfo.setFlightDate(c.getTime());
                        String[] timeArray = time.substring(0, 10).split("\\s");
                        //获取起飞时间
                        ticketInfo.setDepTime(getDepArrTimeWithoutDepTime(c, timeArray, 1));
                        //获取到达时间
                        ticketInfo.setArrTime(getDepArrTimeWithoutDepTime(c, timeArray, 2));
                        if (StringUtils.isEmpty(ticketInfo.getFlightNo())) {
                            //获取航班号
                            ticketInfo.setFlightNo(getFlightNo(one));
                        }
                        break;
                    }
                }

            }
            //2.1检索乘客的索引
            String passengerId = getPassengerID(ticketInfo.getIssCode()+"-"+ticketInfo.getTicketNo(), list);
            if(!StringUtils.isEmpty(passengerId)){
                ticketInfo.setPassengerID(passengerId);
            }
        }
    }

    /**
     * 从rt.retrieveCompletely内获取证件信息
     *
     * @param ticketNo
     * @param list
     * @return
     */
    public String getPassengerID(String ticketNo, String[] list) {
        String paxIndex = "";
        Map<String, String> paxIDMap = new HashMap<>();
        String paxTicket = "TN/" + ticketNo + "/P";
        for (String one : list) {
            one = one.replaceAll("\\s+", " ");
            int paxTicketIndex = one.indexOf(paxTicket);
            //SSR FOID JD HK1 NI330219197304216855/P1
            if (one.indexOf("FOID") > -1) {
                String paxId = REMatchUtil.searchkey("(PP|NI)\\d+[A-Z]{0,2}(/P)\\d", one);
                if (!"".equals(paxId)) {
                    //P1->NI330219197304216855/P1
                    paxIDMap.put(paxId.substring(paxId.length() - 2), paxId.substring(2, paxId.length() - 3));
                }
            }
            if (paxTicketIndex > 0) {
                paxIndex = "P" + one.substring(paxTicketIndex + paxTicket.length(), paxTicketIndex + paxTicket.length() + 1);
                break;
            }
        }
        return paxIDMap.get(paxIndex);
    }

    /**
     * 递归获取
     *
     * @param cycleNum
     * @return
     */
    private int testCycleNum(int cycleNum) {
        if (cycleNum++ > CYCLE_MAX_NUM) {
            throw new GlobalException(CodeIbe.TICKET_HIS_LIMIT_CYCLE_NUM, CodeIbe.TICKET_HIS_LIMIT_CYCLE_NUM.getName(), new RuntimeException(CodeIbe.TICKET_HIS_LIMIT_CYCLE_NUM.getName()));
        }
        return cycleNum;
    }

    public DETRTktResult detrFromTicketNo(DETRTKTResult result, String ticketNo) throws Exception {
        DETRTktResult ticket = new DETRTktResult();
        BeanUtils.copyProperties(result, ticket);
        for (int i = 0; i < result.getSegmentCount(); ++i) {
            DETRTKTSegment detrtktSegment = result.getSegment(i);
            DETRSeg seg = new DETRSeg();
            BeanUtils.copyProperties(detrtktSegment, seg);
            ticket.getSegs().add(seg);
        }
        mapTax(result, ticket);
        return ticket;
    }

    public void mapTax(DETRTKTResult result, DETRTktResult ticket) {
        for (int taxCount = 0; taxCount < result.getTaxLength(); taxCount++) {
            DETRTax tax = new DETRTax();
            tax.setTaxAmount((float) result.getTaxAmount(taxCount));
            tax.setTaxCode(result.getTaxCode(taxCount));
            tax.setTaxCurrency(result.getTaxCurrencyType(taxCount));
            ticket.getTaxs().add(tax);
        }
    }

}
