/*     */ package com.travelsky.hub.model.peentity.upgemds.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class OrderItem
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -799148260208815863L;
/*     */   private String travellerID;
/*     */   private String totalFltSegQty;
/*     */   private String ticketingModeCode;
/*     */   private String remark;
/*     */   private String specificData;
/*     */   private Service service;
/*     */   private Price price;
/*     */   private PaymentDetail paymentDetail;
/*     */ 
/*     */   public String getTravellerID()
/*     */   {
/*  62 */     return this.travellerID;
/*     */   }
/*     */ 
/*     */   public void setTravellerID(String travellerID)
/*     */   {
/*  69 */     this.travellerID = travellerID;
/*     */   }
/*     */ 
/*     */   public String getTotalFltSegQty()
/*     */   {
/*  76 */     return this.totalFltSegQty;
/*     */   }
/*     */ 
/*     */   public void setTotalFltSegQty(String totalFltSegQty)
/*     */   {
/*  83 */     this.totalFltSegQty = totalFltSegQty;
/*     */   }
/*     */ 
/*     */   public String getTicketingModeCode()
/*     */   {
/*  90 */     return this.ticketingModeCode;
/*     */   }
/*     */ 
/*     */   public void setTicketingModeCode(String ticketingModeCode)
/*     */   {
/*  97 */     this.ticketingModeCode = ticketingModeCode;
/*     */   }
/*     */ 
/*     */   public String getRemark()
/*     */   {
/* 104 */     return this.remark;
/*     */   }
/*     */ 
/*     */   public void setRemark(String remark)
/*     */   {
/* 111 */     this.remark = remark;
/*     */   }
/*     */ 
/*     */   public String getSpecificData()
/*     */   {
/* 118 */     return this.specificData;
/*     */   }
/*     */ 
/*     */   public void setSpecificData(String specificData)
/*     */   {
/* 125 */     this.specificData = specificData;
/*     */   }
/*     */ 
/*     */   public Service getService()
/*     */   {
/* 132 */     return this.service;
/*     */   }
/*     */ 
/*     */   public void setService(Service service)
/*     */   {
/* 139 */     this.service = service;
/*     */   }
/*     */ 
/*     */   public Price getPrice()
/*     */   {
/* 146 */     return this.price;
/*     */   }
/*     */ 
/*     */   public void setPrice(Price price)
/*     */   {
/* 153 */     this.price = price;
/*     */   }
/*     */ 
/*     */   public PaymentDetail getPaymentDetail()
/*     */   {
/* 160 */     return this.paymentDetail;
/*     */   }
/*     */ 
/*     */   public void setPaymentDetail(PaymentDetail paymentDetail)
/*     */   {
/* 167 */     this.paymentDetail = paymentDetail;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.OrderItem
 * JD-Core Version:    0.6.0
 */