/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatChartQueryBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String flightDate;
/*    */   private String flightNo;
/*    */   private String toCity;
/*    */   private String fromCity;
/* 21 */   private String flightClass = "*";
/*    */ 
/*    */   public String getFlightDate()
/*    */   {
/* 28 */     return this.flightDate;
/*    */   }
/*    */ 
/*    */   public void setFlightDate(String flightDate)
/*    */   {
/* 35 */     this.flightDate = flightDate;
/*    */   }
/*    */ 
/*    */   public String getFlightNo()
/*    */   {
/* 42 */     return this.flightNo;
/*    */   }
/*    */ 
/*    */   public void setFlightNo(String flightNo)
/*    */   {
/* 49 */     this.flightNo = flightNo;
/*    */   }
/*    */ 
/*    */   public String getToCity()
/*    */   {
/* 56 */     return this.toCity;
/*    */   }
/*    */ 
/*    */   public void setToCity(String toCity)
/*    */   {
/* 63 */     this.toCity = toCity;
/*    */   }
/*    */ 
/*    */   public String getFromCity()
/*    */   {
/* 70 */     return this.fromCity;
/*    */   }
/*    */ 
/*    */   public void setFromCity(String fromCity)
/*    */   {
/* 77 */     this.fromCity = fromCity;
/*    */   }
/*    */ 
/*    */   public String getFlightClass()
/*    */   {
/* 84 */     return this.flightClass;
/*    */   }
/*    */ 
/*    */   public void setFlightClass(String flightClass)
/*    */   {
/* 91 */     this.flightClass = flightClass;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.SeatChartQueryBean
 * JD-Core Version:    0.6.0
 */