package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class CancelPnrRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = 22986223924279831L;

	private String pnrNo;
	private Segment pnrSegment;
	private boolean xepnr;//no or xepnr
	
	@Data
	public static class Segment{
		private int index;
		private String airNo;
		private char fltClass;
		private String orgCity;
		private String desCity;
		private Date departureTime;
		private String actionCode;
		public Segment(String airNo, String orgCity, String desCity, Date departureTime) {
			super();
			this.airNo = airNo;
//			this.fltClass = fltClass;
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.departureTime = departureTime;
		}
		public Segment() {
			super();
		}
	}
	
	public void setSegment(String orgCity, String desCity,  Date departureTime, String airNo) {
		this.pnrSegment = new Segment(airNo, orgCity, desCity, departureTime);
	}
}
