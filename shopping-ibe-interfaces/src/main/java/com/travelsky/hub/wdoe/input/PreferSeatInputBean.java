/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PreferSeatInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String seatPrefer;
/*     */   private String flightNo;
/*     */   private String flightOriCity;
/*     */   private String flightDesCity;
/*     */   private String paxLevel;
/*     */ 
/*     */   public String getFlightOriCity()
/*     */   {
/*  43 */     return this.flightOriCity;
/*     */   }
/*     */ 
/*     */   public void setFlightOriCity(String flightOriCity)
/*     */   {
/*  50 */     this.flightOriCity = flightOriCity;
/*     */   }
/*     */ 
/*     */   public String getSeatPrefer()
/*     */   {
/*  58 */     return this.seatPrefer;
/*     */   }
/*     */ 
/*     */   public void setSeatPrefer(String seatPrefer)
/*     */   {
/*  65 */     this.seatPrefer = seatPrefer;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  72 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  79 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getPaxLevel()
/*     */   {
/*  87 */     return this.paxLevel;
/*     */   }
/*     */ 
/*     */   public void setPaxLevel(String paxLevel)
/*     */   {
/*  94 */     this.paxLevel = paxLevel;
/*     */   }
/*     */ 
/*     */   public String getFlightDesCity()
/*     */   {
/* 101 */     return this.flightDesCity;
/*     */   }
/*     */ 
/*     */   public void setFlightDesCity(String flightDesCity)
/*     */   {
/* 108 */     this.flightDesCity = flightDesCity;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PreferSeatInputBean
 * JD-Core Version:    0.6.0
 */