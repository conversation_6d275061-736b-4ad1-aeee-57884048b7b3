/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BaggageTag
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 5849155940172818005L;
/*    */   private String bagIndex;
/*    */   private String bagTagNo;
/*    */   private String bagArrivalAirport;
/*    */   private String bagStatus;
/*    */ 
/*    */   public String getBagIndex()
/*    */   {
/* 44 */     return this.bagIndex;
/*    */   }
/*    */ 
/*    */   public String getBagTagNo()
/*    */   {
/* 50 */     return this.bagTagNo;
/*    */   }
/*    */ 
/*    */   public void setBagTagNo(String bagTagNo)
/*    */   {
/* 57 */     this.bagTagNo = bagTagNo;
/*    */   }
/*    */ 
/*    */   public void setBagIndex(String bagIndex)
/*    */   {
/* 63 */     this.bagIndex = bagIndex;
/*    */   }
/*    */ 
/*    */   public String getBagStatus()
/*    */   {
/* 69 */     return this.bagStatus;
/*    */   }
/*    */ 
/*    */   public String getBagArrivalAirport()
/*    */   {
/* 75 */     return this.bagArrivalAirport;
/*    */   }
/*    */ 
/*    */   public void setBagArrivalAirport(String bagArrivalAirport)
/*    */   {
/* 82 */     this.bagArrivalAirport = bagArrivalAirport;
/*    */   }
/*    */ 
/*    */   public void setBagStatus(String bagStatus)
/*    */   {
/* 89 */     this.bagStatus = bagStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BaggageTag
 * JD-Core Version:    0.6.0
 */