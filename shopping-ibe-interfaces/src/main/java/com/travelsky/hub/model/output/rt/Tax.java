/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Tax
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4389372278555053105L;
/*    */   private String taxCurCode;
/*    */   private String taxCode;
/*    */   private String taxamount;
/*    */ 
/*    */   public String getTaxCurCode()
/*    */   {
/* 34 */     return this.taxCurCode;
/*    */   }
/*    */ 
/*    */   public void setTaxCurCode(String taxCurCode)
/*    */   {
/* 41 */     this.taxCurCode = taxCurCode;
/*    */   }
/*    */ 
/*    */   public String getTaxCode()
/*    */   {
/* 48 */     return this.taxCode;
/*    */   }
/*    */ 
/*    */   public void setTaxCode(String taxCode)
/*    */   {
/* 55 */     this.taxCode = taxCode;
/*    */   }
/*    */ 
/*    */   public String getTaxamount()
/*    */   {
/* 62 */     return this.taxamount;
/*    */   }
/*    */ 
/*    */   public void setTaxamount(String taxamount)
/*    */   {
/* 69 */     this.taxamount = taxamount;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Tax
 * JD-Core Version:    0.6.0
 */