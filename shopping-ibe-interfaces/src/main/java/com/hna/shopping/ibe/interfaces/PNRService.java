package com.hna.shopping.ibe.interfaces;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import com.hna.shopping.ibe.interfaces.dto.CancelPnrRequest;
import com.hna.shopping.ibe.interfaces.dto.DelPnrItemRequest;

/**
 * pnr操作业务
 */
public interface PNRService {

    /**
     * delPnrItem
     *
     * @param delPnrItemReq
     * @return
     */
    BaseResponse delPnrItem(DelPnrItemRequest delPnrItemReq);

    /**
     * cancelPnr
     *
     * @param cancelPnrReq
     * @return
     */
    BaseResponse cancelPnr(CancelPnrRequest cancelPnrReq);
}
