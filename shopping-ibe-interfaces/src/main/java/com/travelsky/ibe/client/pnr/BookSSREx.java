package com.travelsky.ibe.client.pnr;

import java.util.Date;

import com.travelsky.util.QDateTime;

//import com.travelsky.ibe.exceptions.QDateTime;

public class BookSSREx extends BookSSR {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7087602635376138729L;

	public BookSSREx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookSSREx(String serveCode, String airCode, String actionCode, int person, String cityPair, String airNo,
			char fltClass, Date departureTime, String serveInfo, String psgrName, String segidx) throws Exception {
		super(serveCode, airCode, actionCode, person, cityPair, airNo, fltClass, departureTime, serveInfo, psgrName, segidx);
		// TODO Auto-generated constructor stub
	}

	public BookSSREx(String serveCode, String airCode, String actionCode, int person, String cityPair, String airNo,
			char fltClass, Date departureTime, String serveInfo, String psgrName) throws Exception {
		super(serveCode, airCode, actionCode, person, cityPair, airNo, fltClass, departureTime, serveInfo, psgrName);
		// TODO Auto-generated constructor stub
	}

	public BookSSREx(String serveCode, String airCode, String actionCode, int person, String cityPair, String airNo,
			char fltClass, String departureTime, String serveInfo, String psgrName) throws Exception {
		super(serveCode, airCode, actionCode, person, cityPair, airNo, fltClass, departureTime, serveInfo, psgrName);
		// TODO Auto-generated constructor stub
	}

	public BookSSREx(String serveCode, String serveInfo) throws Exception {
		super(serveCode, serveInfo);
		// TODO Auto-generated constructor stub
	}

	public void setDepartureTime(String departureTime) throws Exception {
		// 可能是日期类型
		if(null==departureTime || departureTime.trim().equals(""))
			return;
		if(departureTime.length()==5||departureTime.length()==7||departureTime.length()==10) {
			super.setDepartureTime(departureTime);
		}else {
			super.setDepartureTime(new Date(Long.parseLong(departureTime)));
		}
			

	}
}
