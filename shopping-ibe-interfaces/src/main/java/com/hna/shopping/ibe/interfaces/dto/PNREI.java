package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNREI extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2190483162584399757L;
	private int index;                                                                                                                            
	                                                                    
	private String psgrid = null;                                                                                                                     
	                                                                                                                                          
	private String ei = null;                                                                                                                         
	                                                                                                                                          
	private boolean infant = false;                                                                                                                   
	                                                                                                                                          
	                                                                                                                                   
	                                                                                                                                          
	public String toString()                                                                                                                  
	{                                                                                                                                         
	  try                                                                                                                                     
	  {                                                                                                                                       
	    return String.valueOf(this.index) + ".EI " + ((this.infant) ? "IN/" : "") + this.ei + "  " + this.psgrid; } catch (Exception e) { 
	  }                                                                                                                                       
	  return super.toString();                                                                                                                
	}                                                                                                                                         
	                                                                                                                                          
	                                                                                                                               

}
