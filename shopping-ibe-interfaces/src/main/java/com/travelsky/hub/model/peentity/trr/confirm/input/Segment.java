/*    */ package com.travelsky.hub.model.peentity.trr.confirm.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Segment
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -799918427175523892L;
/*    */   private String tickteNo;
/*    */   private String couponNo;
/*    */   private PnrUpdtInfo sourceSegment;
/*    */   private PnrUpdtInfo newSegment;
/*    */ 
/*    */   public String getTickteNo()
/*    */   {
/* 42 */     return this.tickteNo;
/*    */   }
/*    */ 
/*    */   public void setTickteNo(String tickteNo)
/*    */   {
/* 49 */     this.tickteNo = tickteNo;
/*    */   }
/*    */ 
/*    */   public String getCouponNo()
/*    */   {
/* 56 */     return this.couponNo;
/*    */   }
/*    */ 
/*    */   public void setCouponNo(String couponNo)
/*    */   {
/* 63 */     this.couponNo = couponNo;
/*    */   }
/*    */ 
/*    */   public PnrUpdtInfo getSourceSegment()
/*    */   {
/* 70 */     return this.sourceSegment;
/*    */   }
/*    */ 
/*    */   public void setSourceSegment(PnrUpdtInfo sourceSegment)
/*    */   {
/* 77 */     this.sourceSegment = sourceSegment;
/*    */   }
/*    */ 
/*    */   public PnrUpdtInfo getNewSegment()
/*    */   {
/* 84 */     return this.newSegment;
/*    */   }
/*    */ 
/*    */   public void setNewSegment(PnrUpdtInfo newSegment)
/*    */   {
/* 91 */     this.newSegment = newSegment;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.confirm.input.Segment
 * JD-Core Version:    0.6.0
 */