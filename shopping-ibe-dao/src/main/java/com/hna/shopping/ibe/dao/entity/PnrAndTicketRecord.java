package com.hna.shopping.ibe.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@TableName("ibe_pnr_ticket_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PnrAndTicketRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("pnr_no")
    private String pnrNo;

    @TableField("flight_date")
    private String flightDate;

    @TableField("cabin")
    private String cabin;

    @TableField("dep_code")
    private String depCode;

    @TableField("arr_code")
    private String arrCode;

    @TableField("flight_no")
    private String flightNo;

    @TableField("ticket_no")
    private String ticketNo;

    @TableField("passenger_name")
    private String passengerName;

    @TableField("id_card")
    private String idCard;

    @TableField("pnr_time")
    private Date pnrCreateTime;

    @TableField("issue_ticket_time")
    private Date issueTicketTime;

    @TableField("clear_pnr_time")
    private Date clearPnrTime;

    @TableField("txnid")
    private String txnid;

}
