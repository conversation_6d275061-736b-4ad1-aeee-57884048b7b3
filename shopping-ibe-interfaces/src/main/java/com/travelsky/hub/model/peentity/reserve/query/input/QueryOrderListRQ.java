/*     */ package com.travelsky.hub.model.peentity.reserve.query.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class QueryOrderListRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7999463626356364489L;
/*     */   private String ocAirlineCode;
/*     */   private String ocFlightNumber;
/*     */   private String ocFlightSuffix;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String reverseNumber;
/*     */   private String ticketNumber;
/*     */   private String certType;
/*     */   private String certNo;
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/*  68 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/*  75 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/*  82 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/*  89 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/*  96 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 103 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 110 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 117 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 124 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 131 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 138 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 145 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getReverseNumber()
/*     */   {
/* 152 */     return this.reverseNumber;
/*     */   }
/*     */ 
/*     */   public void setReverseNumber(String reverseNumber)
/*     */   {
/* 159 */     this.reverseNumber = reverseNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 166 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 173 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 180 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 187 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 194 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 201 */     this.certNo = certNo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.input.QueryOrderListRQ
 * JD-Core Version:    0.6.0
 */