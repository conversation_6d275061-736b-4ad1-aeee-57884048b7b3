package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.hna.shopping.ibe.interfaces.dto.DETRCreResult;
import com.hna.shopping.ibe.interfaces.dto.DtoAllChanTicketInfoResponse;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPassengerNoRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPnrRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoTicketNoRequest;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import com.travelsky.ibe.client.pnr.DETRTKTSegment;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

/**
 * longx.yang 2019/12/17
 * 分离出来，根据特定的信息提取票面信息
 */
public interface SegInfoManager {

	/**
	 * 组装可以从detr获取的航段信息
	 * @param result
	 * @param segIndex
	 * @param detrtktSegment
	 * @param detr2f
	 * @return
	 */
	AllChanTicketInfo getBaseAllChanTicketInfo(DETRTKTResult result, Integer segIndex, DETRTKTSegment detrtktSegment, DETR2F detr2f, String secondFactorCode, String secondFactorValue);

	/**
	 * 获取从detr获取不到的其他票面信息
	 * @param result
	 * @param detr
	 * @param seg
	 * @return
	 */
	void getOtherAllChanTicketInfo(DETRTKTResult result, DETR2F detr2f, AllChanTicketInfo seg, String secondFactorCode, String secondFactorValue);

	Map<String, Object> getInfoFromHistory(DETR2F detr2f, DETRTKTResult detrtktResult, DETRTKTSegment detrtktSegment, String ticketNo,int segindex, String secondFactorCode, String secondFactorValue);

	DETRCreResult getCreResult(DETR2F detr2f, String ticketNo, String secondFactorCode, String secondFactorValue);

	public void setConponResult(AllChanTicketInfo seg, String conponResult);

	public String getConponResult(DETRTKTResult result);

	/**
	 * 判断两个航段是否在同一时刻，同一航班上，成人携带婴儿需要返回婴儿所在的航段
	 * @param seg
	 * @param detrtktSegment
	 * @return
	 */
	public boolean testTwoSegOnOneFligh(AllChanTicketInfo seg, DETRTKTSegment detrtktSegment);

}
