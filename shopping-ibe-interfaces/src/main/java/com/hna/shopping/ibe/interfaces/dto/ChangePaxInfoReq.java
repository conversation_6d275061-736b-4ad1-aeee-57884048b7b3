package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.BookPassengerEx;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class ChangePaxInfoReq extends BaseRequest {

    private static final long serialVersionUID = -4266752629041589073L;
    /**
     * pnr号
     */
    private String pnrNo;
    /**
     * 旧旅客信息
     */
    private BookPassengerEx oldPassenger;
    /**
     * 新旅客信息
     */
    private BookPassengerEx newPassenger;
    /**
     * 新证件号，必须包含证件类型
     */
    private String foid;

    /**
     * json ex.
         {
             "airlineCode":"JD",
             "pnrNo":"PLD88B",
             "oldPassenger":{
                 "name":"测试王五",
                 "age":"",
                 "type":""
             },
             "newPassenger":{
                 "name":"测试一",
                 "age":"",
                 "type":""
             },
            "foid":"NI12222244"
         }
     */
}
