/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import com.travelsky.hub.model.input.InfInfoBean;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PersonInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2863767123352544197L;
/*     */   private String tktNumber;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String passengerName;
/*     */   private String seatNumber;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String tourIndex;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String isGroup;
/*     */   private String sNoption;
/*     */   private String xbp;
/*     */   private String psm;
/*     */   private String chd;
/*     */   private String deptTime;
/*     */   private String gender;
/*     */   private String bagWeight;
/*     */   private String bagQuantity;
/*     */   private String bagArrivalAirport;
/*     */   private String hostNumber;
/*     */   private String phoneNumber;
/*     */   private String email;
/*     */   private InfInfoBean infInfo;
/*     */   private String ckinMessage;
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 138 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 145 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 153 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 161 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 169 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 177 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 184 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 192 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 199 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 206 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 213 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 221 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 228 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 235 */     this.ffpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 242 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 250 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 257 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 265 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getIsGroup()
/*     */   {
/* 274 */     return this.isGroup;
/*     */   }
/*     */ 
/*     */   public void setIsGroup(String isGroup)
/*     */   {
/* 281 */     this.isGroup = isGroup;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 288 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 296 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 303 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 310 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getPsm()
/*     */   {
/* 318 */     return this.psm;
/*     */   }
/*     */ 
/*     */   public void setPsm(String psm)
/*     */   {
/* 326 */     this.psm = psm;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 334 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 341 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getSNoption()
/*     */   {
/* 349 */     return this.sNoption;
/*     */   }
/*     */ 
/*     */   public void setSNoption(String noption)
/*     */   {
/* 357 */     this.sNoption = noption;
/*     */   }
/*     */ 
/*     */   public String getTKTNumber()
/*     */   {
/* 365 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTKTNumber(String number)
/*     */   {
/* 373 */     this.tktNumber = number;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 381 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 388 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 396 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 403 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getXbp()
/*     */   {
/* 411 */     return this.xbp;
/*     */   }
/*     */ 
/*     */   public void setXbp(String xbp)
/*     */   {
/* 419 */     this.xbp = xbp;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 426 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 433 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 440 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 447 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getBagArrivalAirport()
/*     */   {
/* 454 */     return this.bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagArrivalAirport(String bagArrivalAirport)
/*     */   {
/* 461 */     this.bagArrivalAirport = bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/* 468 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/* 475 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 482 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 489 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 496 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 503 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getPhoneNumber()
/*     */   {
/* 510 */     return this.phoneNumber;
/*     */   }
/*     */ 
/*     */   public void setPhoneNumber(String phoneNumber)
/*     */   {
/* 517 */     this.phoneNumber = phoneNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 525 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 533 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 540 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 547 */     this.infInfo = infInfo;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 554 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 561 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PersonInputBean
 * JD-Core Version:    0.6.0
 */