/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AsvcInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String emdNumber;
/*     */   private String couponNum;
/*     */   private String ssrCode;
/*     */   private String asvcStatus;
/*     */   private String comServiceName;
/*     */ 
/*     */   public AsvcInfo()
/*     */   {
/*  40 */     this.asvcStatus = "";
/*  41 */     this.couponNum = "";
/*  42 */     this.emdNumber = "";
/*  43 */     this.ssrCode = "";
/*  44 */     this.comServiceName = "";
/*     */   }
/*     */ 
/*     */   public String getEmdNumber()
/*     */   {
/*  51 */     return this.emdNumber;
/*     */   }
/*     */ 
/*     */   public void setEmdNumber(String emdNumber)
/*     */   {
/*  58 */     this.emdNumber = emdNumber;
/*     */   }
/*     */ 
/*     */   public String getCouponNum()
/*     */   {
/*  65 */     return this.couponNum;
/*     */   }
/*     */ 
/*     */   public void setCouponNum(String couponNum)
/*     */   {
/*  72 */     this.couponNum = couponNum;
/*     */   }
/*     */ 
/*     */   public String getSsrCode()
/*     */   {
/*  79 */     return this.ssrCode;
/*     */   }
/*     */ 
/*     */   public void setSsrCode(String ssrCode)
/*     */   {
/*  86 */     this.ssrCode = ssrCode;
/*     */   }
/*     */ 
/*     */   public String getAsvcStatus()
/*     */   {
/*  93 */     return this.asvcStatus;
/*     */   }
/*     */ 
/*     */   public void setAsvcStatus(String asvcStatus)
/*     */   {
/* 100 */     this.asvcStatus = asvcStatus;
/*     */   }
/*     */ 
/*     */   public String getComServiceName()
/*     */   {
/* 108 */     return this.comServiceName;
/*     */   }
/*     */ 
/*     */   public void setComServiceName(String comServiceName)
/*     */   {
/* 116 */     this.comServiceName = comServiceName;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AsvcInfo
 * JD-Core Version:    0.6.0
 */