/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgPwInfoBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String flightNo;
/*     */   private String certId;
/*  22 */   private String certType = "NI";
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String checkCode;
/*     */   private String outboundEtCode;
/*     */   private String forcedCancellation;
/*     */ 
/*     */   public String getCheckCode()
/*     */   {
/*  39 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */   public void setCheckCode(String checkCode)
/*     */   {
/*  47 */     this.checkCode = checkCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  54 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  62 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  69 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  77 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getCertId()
/*     */   {
/*  84 */     return this.certId;
/*     */   }
/*     */ 
/*     */   public void setCertId(String certId)
/*     */   {
/*  92 */     this.certId = certId;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/*  99 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 107 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 115 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 122 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 130 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 137 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public String getOutboundEtCode()
/*     */   {
/* 144 */     return this.outboundEtCode;
/*     */   }
/*     */ 
/*     */   public void setOutboundEtCode(String outboundEtCode)
/*     */   {
/* 151 */     this.outboundEtCode = outboundEtCode;
/*     */   }
/*     */ 
/*     */   public String getForcedCancellation()
/*     */   {
/* 159 */     return this.forcedCancellation;
/*     */   }
/*     */ 
/*     */   public void setForcedCancellation(String forcedCancellation)
/*     */   {
/* 167 */     this.forcedCancellation = forcedCancellation;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PsgPwInfoBean
 * JD-Core Version:    0.6.0
 */