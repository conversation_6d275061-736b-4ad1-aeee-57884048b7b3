/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class CheckInStatusQueryOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7083961131314455393L;
/*    */   private String checkInStatus;
/* 25 */   private List<OutBoundInfo> outBounds = new ArrayList();
/*    */ 
/* 29 */   private List<InBoundInfo> inBounds = new ArrayList();
/*    */   private String appStatus;
/*    */ 
/*    */   public List<OutBoundInfo> getOutBounds()
/*    */   {
/* 40 */     return this.outBounds;
/*    */   }
/*    */ 
/*    */   public void setOutBounds(List<OutBoundInfo> outBounds)
/*    */   {
/* 47 */     this.outBounds = outBounds;
/*    */   }
/*    */ 
/*    */   public String getCheckInStatus()
/*    */   {
/* 54 */     return this.checkInStatus;
/*    */   }
/*    */ 
/*    */   public void setCheckInStatus(String checkInStatus)
/*    */   {
/* 61 */     this.checkInStatus = checkInStatus;
/*    */   }
/*    */ 
/*    */   public List<InBoundInfo> getInBounds()
/*    */   {
/* 68 */     return this.inBounds;
/*    */   }
/*    */ 
/*    */   public void setInBounds(List<InBoundInfo> inBounds)
/*    */   {
/* 75 */     this.inBounds = inBounds;
/*    */   }
/*    */ 
/*    */   public String getAppStatus()
/*    */   {
/* 82 */     return this.appStatus;
/*    */   }
/*    */ 
/*    */   public void setAppStatus(String appStatus)
/*    */   {
/* 89 */     this.appStatus = appStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.CheckInStatusQueryOutputBean
 * JD-Core Version:    0.6.0
 */