/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class IdentityInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2998456357243438518L;
/*    */   private String foidType;
/*    */   private String foidNumber;
/*    */ 
/*    */   public String getFoidType()
/*    */   {
/* 39 */     return this.foidType;
/*    */   }
/*    */ 
/*    */   public void setFoidType(String foidType)
/*    */   {
/* 46 */     this.foidType = foidType;
/*    */   }
/*    */ 
/*    */   public String getFoidNumber()
/*    */   {
/* 53 */     return this.foidNumber;
/*    */   }
/*    */ 
/*    */   public void setFoidNumber(String foidNumber)
/*    */   {
/* 60 */     this.foidNumber = foidNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.IdentityInformation
 * JD-Core Version:    0.6.0
 */