/*     */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class BookingSeatRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7228739426518917463L;
/*     */   private OfficeInfo officeInfo;
/*     */   private String pnrCode;
/*     */   private PassengerInfo passenger;
/*     */   private FlightSegment flightSegment;
/*     */   private ServiceItem serviceItem;
/*     */ 
/*     */   public OfficeInfo getOfficeInfo()
/*     */   {
/*  48 */     return this.officeInfo;
/*     */   }
/*     */ 
/*     */   public void setOfficeInfo(OfficeInfo officeInfo)
/*     */   {
/*  55 */     this.officeInfo = officeInfo;
/*     */   }
/*     */ 
/*     */   public String getPnrCode()
/*     */   {
/*  62 */     return this.pnrCode;
/*     */   }
/*     */ 
/*     */   public void setPnrCode(String pnrCode)
/*     */   {
/*  69 */     this.pnrCode = pnrCode;
/*     */   }
/*     */ 
/*     */   public PassengerInfo getPassenger()
/*     */   {
/*  76 */     return this.passenger;
/*     */   }
/*     */ 
/*     */   public void setPassenger(PassengerInfo passenger)
/*     */   {
/*  83 */     this.passenger = passenger;
/*     */   }
/*     */ 
/*     */   public FlightSegment getFlightSegment()
/*     */   {
/*  90 */     return this.flightSegment;
/*     */   }
/*     */ 
/*     */   public void setFlightSegment(FlightSegment flightSegment)
/*     */   {
/*  97 */     this.flightSegment = flightSegment;
/*     */   }
/*     */ 
/*     */   public ServiceItem getServiceItem()
/*     */   {
/* 104 */     return this.serviceItem;
/*     */   }
/*     */ 
/*     */   public void setServiceItem(ServiceItem serviceItem)
/*     */   {
/* 111 */     this.serviceItem = serviceItem;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.BookingSeatRQ
 * JD-Core Version:    0.6.0
 */