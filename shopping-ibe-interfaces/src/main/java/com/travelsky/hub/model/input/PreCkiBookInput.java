/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PreCkiBookInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4832325616537860007L;
/*     */   private String passengerName;
/*     */   private String flightNumber;
/*     */   private String seatNumber;
/*     */   private String departureTime;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String flightClass;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String airlineCode;
/*     */   private String ticketID;
/*     */   private String sequenceNumber;
/*     */   private String flightDate;
/*     */   private String pnr;
/*     */   private String phoneNumber;
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/*  51 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  58 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  66 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  73 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  81 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  88 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  96 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 103 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 110 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 117 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 124 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 131 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 138 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 145 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 152 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 159 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/* 166 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 174 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setPhoneNumber(String phoneNumber)
/*     */   {
/* 182 */     this.phoneNumber = phoneNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 189 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 196 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 203 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 210 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 217 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 224 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 231 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/* 238 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/* 245 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getPhoneNumber()
/*     */   {
/* 252 */     return this.phoneNumber;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 259 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PreCkiBookInput
 * JD-Core Version:    0.6.0
 */