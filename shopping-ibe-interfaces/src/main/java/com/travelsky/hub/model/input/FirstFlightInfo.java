/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FirstFlightInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String deptCity;
/*    */   private String airlineCode;
/*    */   private String destCity;
/*    */   private String flightDate;
/*    */   private String flightNum;
/*    */ 
/*    */   public String getFlightNum()
/*    */   {
/* 21 */     return this.flightNum;
/*    */   }
/*    */ 
/*    */   public String getDestCity()
/*    */   {
/* 28 */     return this.destCity;
/*    */   }
/*    */ 
/*    */   public void setAirlineCode(String airlineCode)
/*    */   {
/* 35 */     this.airlineCode = airlineCode;
/*    */   }
/*    */ 
/*    */   public String getAirlineCode()
/*    */   {
/* 42 */     return this.airlineCode;
/*    */   }
/*    */ 
/*    */   public String getFlightDate()
/*    */   {
/* 51 */     return this.flightDate;
/*    */   }
/*    */ 
/*    */   public void setFlightNum(String flightNum)
/*    */   {
/* 58 */     this.flightNum = flightNum;
/*    */   }
/*    */ 
/*    */   public void setFlightDate(String flightDate)
/*    */   {
/* 65 */     this.flightDate = flightDate;
/*    */   }
/*    */ 
/*    */   public void setDestCity(String destCity)
/*    */   {
/* 74 */     this.destCity = destCity;
/*    */   }
/*    */ 
/*    */   public String getDeptCity()
/*    */   {
/* 81 */     return this.deptCity;
/*    */   }
/*    */ 
/*    */   public void setDeptCity(String deptCity)
/*    */   {
/* 88 */     this.deptCity = deptCity;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FirstFlightInfo
 * JD-Core Version:    0.6.0
 */