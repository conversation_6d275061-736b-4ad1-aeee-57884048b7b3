/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ResultBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4506350319283975763L;
/*    */   private long errorCode;
/*    */   private String errorMsg;
/*    */   private String outputxml;
/*    */ 
/*    */   public ResultBean()
/*    */   {
/* 31 */     this.errorCode = 0L;
/* 32 */     this.errorMsg = "";
/*    */   }
/*    */ 
/*    */   public String getOutputxml()
/*    */   {
/* 40 */     return this.outputxml;
/*    */   }
/*    */ 
/*    */   public void setOutputxml(String outputxml)
/*    */   {
/* 47 */     this.outputxml = outputxml;
/*    */   }
/*    */ 
/*    */   public long getErrorCode()
/*    */   {
/* 55 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(long errorCode)
/*    */   {
/* 62 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 69 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 76 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String toString()
/*    */   {
/* 84 */     return "errorCode= " + this.errorCode + " errorMsg=" + this.errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.ResultBean
 * JD-Core Version:    0.6.0
 */