/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatReserveBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String certId;
/*     */   private String flightDate;
/*     */   private String deptAirport;
/*     */   private String flightNo;
/*  53 */   private String certType = "NI";
/*     */   private String seatNo;
/*     */ 
/*     */   public void setCertId(String certId)
/*     */   {
/*  22 */     this.certId = certId;
/*     */   }
/*     */ 
/*     */   public String getCertId()
/*     */   {
/*  29 */     return this.certId;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  38 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  45 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/*  61 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/*  68 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  76 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  83 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getDeptAirport()
/*     */   {
/*  92 */     return this.deptAirport;
/*     */   }
/*     */ 
/*     */   public void setDeptAirport(String deptAirport)
/*     */   {
/*  99 */     this.deptAirport = deptAirport;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 107 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 114 */     this.certType = certType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.SeatReserveBean
 * JD-Core Version:    0.6.0
 */