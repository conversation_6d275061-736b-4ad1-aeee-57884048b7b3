/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PreCkiDelBookRequest
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String ticketID;
/*     */   private String sequenceNumber;
/*     */   private String flightNumber;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  22 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  29 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/*  42 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/*  49 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/*  60 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/*  67 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  76 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  83 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  94 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 101 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 110 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 117 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 124 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 131 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PreCkiDelBookRequest
 * JD-Core Version:    0.6.0
 */