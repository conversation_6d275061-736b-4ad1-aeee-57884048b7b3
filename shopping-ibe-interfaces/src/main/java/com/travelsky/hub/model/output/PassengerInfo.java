/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class PassengerInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String urp;
/*    */   private List<TravelSegment> travelSegments;
/*    */   private AdcError adcError;
/*    */ 
/*    */   public String getUrp()
/*    */   {
/* 33 */     return this.urp;
/*    */   }
/*    */ 
/*    */   public void setUrp(String urp)
/*    */   {
/* 42 */     this.urp = urp;
/*    */   }
/*    */ 
/*    */   public List<TravelSegment> getTravelSegments()
/*    */   {
/* 51 */     return this.travelSegments;
/*    */   }
/*    */ 
/*    */   public void setTravelSegments(List<TravelSegment> travelSegments)
/*    */   {
/* 60 */     this.travelSegments = travelSegments;
/*    */   }
/*    */ 
/*    */   public AdcError getAdcError()
/*    */   {
/* 69 */     return this.adcError;
/*    */   }
/*    */ 
/*    */   public void setAdcError(AdcError adcError)
/*    */   {
/* 78 */     this.adcError = adcError;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerInfo
 * JD-Core Version:    0.6.0
 */