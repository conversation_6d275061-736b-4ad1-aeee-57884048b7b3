/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatAttr
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String seatNumber;
/*    */   private String gender;
/*    */   private String passengerType;
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 42 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 53 */     this.seatNumber = seatNumber;
/*    */   }
/*    */ 
/*    */   public String getGender()
/*    */   {
/* 62 */     return this.gender;
/*    */   }
/*    */ 
/*    */   public void setGender(String gender)
/*    */   {
/* 73 */     this.gender = gender;
/*    */   }
/*    */ 
/*    */   public String getPassengerType()
/*    */   {
/* 82 */     return this.passengerType;
/*    */   }
/*    */ 
/*    */   public void setPassengerType(String passengerType)
/*    */   {
/* 93 */     this.passengerType = passengerType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatAttr
 * JD-Core Version:    0.6.0
 */