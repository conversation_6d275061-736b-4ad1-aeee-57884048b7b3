/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EDIHbpuoInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private FlightInfo flightInfo;
/*    */   private PassengerInfo psgInfo;
/*    */   private String psgName;
/*    */   private String hostNum;
/*    */ 
/*    */   public FlightInfo getFlightInfo()
/*    */   {
/* 36 */     return this.flightInfo;
/*    */   }
/*    */ 
/*    */   public void setFlightInfo(FlightInfo flightInfo)
/*    */   {
/* 43 */     this.flightInfo = flightInfo;
/*    */   }
/*    */ 
/*    */   public PassengerInfo getPsgInfo()
/*    */   {
/* 50 */     return this.psgInfo;
/*    */   }
/*    */ 
/*    */   public void setPsgInfo(PassengerInfo psgInfo)
/*    */   {
/* 57 */     this.psgInfo = psgInfo;
/*    */   }
/*    */ 
/*    */   public String getPsgName()
/*    */   {
/* 64 */     return this.psgName;
/*    */   }
/*    */ 
/*    */   public void setPsgName(String psgName)
/*    */   {
/* 71 */     this.psgName = psgName;
/*    */   }
/*    */ 
/*    */   public String getHostNum()
/*    */   {
/* 78 */     return this.hostNum;
/*    */   }
/*    */ 
/*    */   public void setHostNum(String hostNum)
/*    */   {
/* 85 */     this.hostNum = hostNum;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.EDIHbpuoInfo
 * JD-Core Version:    0.6.0
 */