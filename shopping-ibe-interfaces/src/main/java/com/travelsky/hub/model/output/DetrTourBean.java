/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class DetrTourBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1759831441443603464L;
/*  18 */   private String tourIndex = "";
/*     */ 
/*  22 */   private String fromCity = "";
/*     */ 
/*  26 */   private String toCity = "";
/*     */ 
/*  30 */   private String pnr = "";
/*     */ 
/*  34 */   private String airlineCode = "";
/*     */ 
/*  38 */   private String flightNumber = "";
/*     */ 
/*  42 */   private String tourDate = "";
/*     */ 
/*  46 */   private String tourTime = "";
/*     */ 
/*  50 */   private String tourClass = "";
/*     */ 
/*  54 */   private String status = "";
/*     */ 
/*  58 */   private String carrAirlineCode = "";
/*     */ 
/*  62 */   private String fromCityStatus = "";
/*     */ 
/*  66 */   private String toCityStatus = "";
/*     */ 
/*  70 */   private String arrivalTerminalName = "";
/*     */ 
/*  74 */   private String departureTerminalName = "";
/*     */ 
/*  78 */   private String issueAirline = "";
/*     */ 
/*  83 */   private String msPnr = "";
/*     */ 
/*  87 */   private String msSystemID = "";
/*     */ 
/*  91 */   private String fareBasicCode = "";
/*     */ 
/*  95 */   private String firstTicketDate = "";
/*     */ 
/*  99 */   private String lastTicketDate = "";
/*     */ 
/* 103 */   private String reservationStatus = "";
/*     */ 
/* 107 */   private String fba = "";
/*     */ 
/* 111 */   private String baggagePiece = "";
/*     */ 
/* 115 */   private String baggageWeight = "";
/*     */ 
/*     */   public String getIssueAirline()
/*     */   {
/* 121 */     return this.issueAirline;
/*     */   }
/*     */ 
/*     */   public void setIssueAirline(String issueAirline)
/*     */   {
/* 128 */     this.issueAirline = issueAirline;
/*     */   }
/*     */ 
/*     */   public String getMsPnr()
/*     */   {
/* 135 */     return this.msPnr;
/*     */   }
/*     */ 
/*     */   public void setMsPnr(String msPnr)
/*     */   {
/* 142 */     this.msPnr = msPnr;
/*     */   }
/*     */ 
/*     */   public String getMsSystemID()
/*     */   {
/* 149 */     return this.msSystemID;
/*     */   }
/*     */ 
/*     */   public void setMsSystemID(String msSystemID)
/*     */   {
/* 156 */     this.msSystemID = msSystemID;
/*     */   }
/*     */ 
/*     */   public String getFareBasicCode()
/*     */   {
/* 163 */     return this.fareBasicCode;
/*     */   }
/*     */ 
/*     */   public void setFareBasicCode(String fareBasicCode)
/*     */   {
/* 170 */     this.fareBasicCode = fareBasicCode;
/*     */   }
/*     */ 
/*     */   public String getFirstTicketDate()
/*     */   {
/* 177 */     return this.firstTicketDate;
/*     */   }
/*     */ 
/*     */   public void setFirstTicketDate(String firstTicketDate)
/*     */   {
/* 184 */     this.firstTicketDate = firstTicketDate;
/*     */   }
/*     */ 
/*     */   public String getLastTicketDate()
/*     */   {
/* 191 */     return this.lastTicketDate;
/*     */   }
/*     */ 
/*     */   public void setLastTicketDate(String lastTicketDate)
/*     */   {
/* 198 */     this.lastTicketDate = lastTicketDate;
/*     */   }
/*     */ 
/*     */   public String getReservationStatus()
/*     */   {
/* 205 */     return this.reservationStatus;
/*     */   }
/*     */ 
/*     */   public void setReservationStatus(String reservationStatus)
/*     */   {
/* 212 */     this.reservationStatus = reservationStatus;
/*     */   }
/*     */ 
/*     */   public String getFba()
/*     */   {
/* 219 */     return this.fba;
/*     */   }
/*     */ 
/*     */   public void setFba(String fba)
/*     */   {
/* 226 */     this.fba = fba;
/*     */   }
/*     */ 
/*     */   public String getBaggagePiece()
/*     */   {
/* 233 */     return this.baggagePiece;
/*     */   }
/*     */ 
/*     */   public void setBaggagePiece(String baggagePiece)
/*     */   {
/* 240 */     this.baggagePiece = baggagePiece;
/*     */   }
/*     */ 
/*     */   public String getBaggageWeight()
/*     */   {
/* 247 */     return this.baggageWeight;
/*     */   }
/*     */ 
/*     */   public void setBaggageWeight(String baggageWeight)
/*     */   {
/* 254 */     this.baggageWeight = baggageWeight;
/*     */   }
/*     */ 
/*     */   public String getArrivalTerminalName()
/*     */   {
/* 262 */     return this.arrivalTerminalName;
/*     */   }
/*     */ 
/*     */   public void setArrivalTerminalName(String arrivalTerminalName)
/*     */   {
/* 270 */     this.arrivalTerminalName = arrivalTerminalName;
/*     */   }
/*     */ 
/*     */   public String getDepartureTerminalName()
/*     */   {
/* 278 */     return this.departureTerminalName;
/*     */   }
/*     */ 
/*     */   public void setDepartureTerminalName(String departureTerminalName)
/*     */   {
/* 286 */     this.departureTerminalName = departureTerminalName;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 294 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String i)
/*     */   {
/* 301 */     this.tourIndex = i;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 308 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String i)
/*     */   {
/* 315 */     this.fromCity = i;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 322 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String i)
/*     */   {
/* 329 */     this.toCity = i;
/*     */   }
/*     */ 
/*     */   public String getPNR()
/*     */   {
/* 336 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPNR(String i)
/*     */   {
/* 343 */     this.pnr = i;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 350 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String i)
/*     */   {
/* 357 */     this.airlineCode = i;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 364 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String i)
/*     */   {
/* 371 */     this.flightNumber = i;
/*     */   }
/*     */ 
/*     */   public String getTourDate()
/*     */   {
/* 378 */     return this.tourDate;
/*     */   }
/*     */ 
/*     */   public void setTourDate(String i)
/*     */   {
/* 385 */     this.tourDate = i;
/*     */   }
/*     */ 
/*     */   public String getTourTime()
/*     */   {
/* 392 */     return this.tourTime;
/*     */   }
/*     */ 
/*     */   public void setTourTime(String i)
/*     */   {
/* 399 */     this.tourTime = i;
/*     */   }
/*     */ 
/*     */   public String getTourClass()
/*     */   {
/* 406 */     return this.tourClass;
/*     */   }
/*     */ 
/*     */   public void setTourClass(String i)
/*     */   {
/* 413 */     this.tourClass = i;
/*     */   }
/*     */ 
/*     */   public String getStatus()
/*     */   {
/* 420 */     return this.status;
/*     */   }
/*     */ 
/*     */   public void setStatus(String i)
/*     */   {
/* 427 */     this.status = i;
/*     */   }
/*     */ 
/*     */   public void setCarrAirlineCode(String carrAirlineCode)
/*     */   {
/* 434 */     this.carrAirlineCode = carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getCarrAirlineCode()
/*     */   {
/* 441 */     return this.carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getFromCityStatus()
/*     */   {
/* 448 */     return this.fromCityStatus;
/*     */   }
/*     */ 
/*     */   public void setFromCityStatus(String fromCityStatus)
/*     */   {
/* 455 */     this.fromCityStatus = fromCityStatus;
/*     */   }
/*     */ 
/*     */   public String getToCityStatus()
/*     */   {
/* 462 */     return this.toCityStatus;
/*     */   }
/*     */ 
/*     */   public void setToCityStatus(String toCityStatus)
/*     */   {
/* 469 */     this.toCityStatus = toCityStatus;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 475 */     this.tourIndex = "";
/* 476 */     this.fromCity = "";
/* 477 */     this.toCity = "";
/* 478 */     this.pnr = "";
/* 479 */     this.airlineCode = "";
/* 480 */     this.flightNumber = "";
/* 481 */     this.tourDate = "";
/* 482 */     this.tourTime = "";
/* 483 */     this.tourClass = "";
/* 484 */     this.status = "";
/* 485 */     this.carrAirlineCode = "";
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DetrTourBean
 * JD-Core Version:    0.6.0
 */