/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryPassengerInfoOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4502089521028619765L;
/*    */   private FlightInfo flightInfo;
/*    */   private PrPassengerInfo prPassengerInfo;
/*    */ 
/*    */   public FlightInfo getFlightInfo()
/*    */   {
/* 36 */     return this.flightInfo;
/*    */   }
/*    */ 
/*    */   public void setFlightInfo(FlightInfo flightInfo)
/*    */   {
/* 43 */     this.flightInfo = flightInfo;
/*    */   }
/*    */ 
/*    */   public PrPassengerInfo getPrPassengerInfo()
/*    */   {
/* 50 */     return this.prPassengerInfo;
/*    */   }
/*    */ 
/*    */   public void setPrPassengerInfo(PrPassengerInfo prPassengerInfo)
/*    */   {
/* 57 */     this.prPassengerInfo = prPassengerInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.QueryPassengerInfoOutputBean
 * JD-Core Version:    0.6.0
 */