/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PrApiInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4895052086565133710L;
/*     */   private String surName;
/*     */   private String gender;
/*     */   private String docID;
/*     */   private String docType;
/*     */   private String givenName;
/*     */   private String birthDate;
/*     */   private String docHolderNationality;
/*     */   private String expireDate;
/*     */   private String transferInd;
/*     */   private String primaryHolderInd;
/*     */   private String docIssueStateProv;
/*     */   private String docIssueCountry;
/*     */   private String middleName;
/*     */   private String birthLocation;
/*     */   private String birthCountry;
/*     */   private String infantInd;
/*     */   private String effectiveDate;
/*     */   private String residenceCountry;
/*     */   private String apiInfoSource;
/*     */   private VisaInfo visaInfo;
/*     */   private ContactAddress homeAddress;
/*     */   private ContactAddress destAddress;
/*     */   private OtherDocInfo otherDocInfo;
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 121 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 128 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getGivenName()
/*     */   {
/* 135 */     return this.givenName;
/*     */   }
/*     */ 
/*     */   public void setGivenName(String givenName)
/*     */   {
/* 142 */     this.givenName = givenName;
/*     */   }
/*     */ 
/*     */   public String getBirthDate()
/*     */   {
/* 149 */     return this.birthDate;
/*     */   }
/*     */ 
/*     */   public void setBirthDate(String birthDate)
/*     */   {
/* 156 */     this.birthDate = birthDate;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 163 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 170 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getTransferInd()
/*     */   {
/* 177 */     return this.transferInd;
/*     */   }
/*     */ 
/*     */   public void setTransferInd(String transferInd)
/*     */   {
/* 184 */     this.transferInd = transferInd;
/*     */   }
/*     */ 
/*     */   public String getPrimaryHolderInd()
/*     */   {
/* 191 */     return this.primaryHolderInd;
/*     */   }
/*     */ 
/*     */   public void setPrimaryHolderInd(String primaryHolderInd)
/*     */   {
/* 198 */     this.primaryHolderInd = primaryHolderInd;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 205 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 212 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 219 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 226 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocHolderNationality()
/*     */   {
/* 233 */     return this.docHolderNationality;
/*     */   }
/*     */ 
/*     */   public void setDocHolderNationality(String docHolderNationality)
/*     */   {
/* 240 */     this.docHolderNationality = docHolderNationality;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/* 247 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/* 254 */     this.expireDate = expireDate;
/*     */   }
/*     */ 
/*     */   public String getDocIssueStateProv()
/*     */   {
/* 261 */     return this.docIssueStateProv;
/*     */   }
/*     */ 
/*     */   public void setDocIssueStateProv(String docIssueStateProv)
/*     */   {
/* 268 */     this.docIssueStateProv = docIssueStateProv;
/*     */   }
/*     */ 
/*     */   public String getDocIssueCountry()
/*     */   {
/* 275 */     return this.docIssueCountry;
/*     */   }
/*     */ 
/*     */   public void setDocIssueCountry(String docIssueCountry)
/*     */   {
/* 282 */     this.docIssueCountry = docIssueCountry;
/*     */   }
/*     */ 
/*     */   public String getMiddleName()
/*     */   {
/* 289 */     return this.middleName;
/*     */   }
/*     */ 
/*     */   public void setMiddleName(String middleName)
/*     */   {
/* 296 */     this.middleName = middleName;
/*     */   }
/*     */ 
/*     */   public String getBirthLocation()
/*     */   {
/* 303 */     return this.birthLocation;
/*     */   }
/*     */ 
/*     */   public void setBirthLocation(String birthLocation)
/*     */   {
/* 310 */     this.birthLocation = birthLocation;
/*     */   }
/*     */ 
/*     */   public String getBirthCountry()
/*     */   {
/* 317 */     return this.birthCountry;
/*     */   }
/*     */ 
/*     */   public void setBirthCountry(String birthCountry)
/*     */   {
/* 324 */     this.birthCountry = birthCountry;
/*     */   }
/*     */ 
/*     */   public String getInfantInd()
/*     */   {
/* 331 */     return this.infantInd;
/*     */   }
/*     */ 
/*     */   public void setInfantInd(String infantInd)
/*     */   {
/* 338 */     this.infantInd = infantInd;
/*     */   }
/*     */ 
/*     */   public String getEffectiveDate()
/*     */   {
/* 345 */     return this.effectiveDate;
/*     */   }
/*     */ 
/*     */   public void setEffectiveDate(String effectiveDate)
/*     */   {
/* 352 */     this.effectiveDate = effectiveDate;
/*     */   }
/*     */ 
/*     */   public String getResidenceCountry()
/*     */   {
/* 359 */     return this.residenceCountry;
/*     */   }
/*     */ 
/*     */   public void setResidenceCountry(String residenceCountry)
/*     */   {
/* 366 */     this.residenceCountry = residenceCountry;
/*     */   }
/*     */ 
/*     */   public String getApiInfoSource()
/*     */   {
/* 373 */     return this.apiInfoSource;
/*     */   }
/*     */ 
/*     */   public void setApiInfoSource(String apiInfoSource)
/*     */   {
/* 380 */     this.apiInfoSource = apiInfoSource;
/*     */   }
/*     */ 
/*     */   public VisaInfo getVisaInfo()
/*     */   {
/* 387 */     return this.visaInfo;
/*     */   }
/*     */ 
/*     */   public void setVisaInfo(VisaInfo visaInfo)
/*     */   {
/* 394 */     this.visaInfo = visaInfo;
/*     */   }
/*     */ 
/*     */   public ContactAddress getHomeAddress()
/*     */   {
/* 401 */     return this.homeAddress;
/*     */   }
/*     */ 
/*     */   public void setHomeAddress(ContactAddress homeAddress)
/*     */   {
/* 408 */     this.homeAddress = homeAddress;
/*     */   }
/*     */ 
/*     */   public ContactAddress getDestAddress()
/*     */   {
/* 415 */     return this.destAddress;
/*     */   }
/*     */ 
/*     */   public void setDestAddress(ContactAddress destAddress)
/*     */   {
/* 422 */     this.destAddress = destAddress;
/*     */   }
/*     */ 
/*     */   public OtherDocInfo getOtherDocInfo()
/*     */   {
/* 429 */     return this.otherDocInfo;
/*     */   }
/*     */ 
/*     */   public void setOtherDocInfo(OtherDocInfo otherDocInfo)
/*     */   {
/* 436 */     this.otherDocInfo = otherDocInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PrApiInfo
 * JD-Core Version:    0.6.0
 */