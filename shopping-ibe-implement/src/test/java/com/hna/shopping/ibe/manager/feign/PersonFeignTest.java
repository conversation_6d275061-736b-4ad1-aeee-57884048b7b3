package com.hna.shopping.ibe.manager.feign;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import com.hna.shopping.ibe.ImplementApplication;

import lombok.extern.slf4j.Slf4j;


//@RunWith(SpringRunner.class)
//@ActiveProfiles("sit,local-zf")
//@SpringBootTest(classes = ImplementApplication.class)
@Slf4j
public class PersonFeignTest {
    @Autowired
    private PersonFeign personFeign;

//    @Test
    public void allPersonTest() {
//        RestResponse<List<PersonDTO>> response = personFeign.allPerson();
//        log.info("PersonDtos: {}", response);
    }
}
