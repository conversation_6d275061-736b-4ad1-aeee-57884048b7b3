/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ContactAddress
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8925657776366413180L;
/*     */   private String streetNmbr;
/*     */   private String cityName;
/*     */   private String stateProv;
/*     */   private String stateCode;
/*     */   private String countryName;
/*     */   private String postalCode;
/*     */   private String telephoner;
/*     */ 
/*     */   public String getStreetNmbr()
/*     */   {
/*  56 */     return this.streetNmbr;
/*     */   }
/*     */ 
/*     */   public void setStreetNmbr(String streetNmbr)
/*     */   {
/*  63 */     this.streetNmbr = streetNmbr;
/*     */   }
/*     */ 
/*     */   public String getCityName()
/*     */   {
/*  70 */     return this.cityName;
/*     */   }
/*     */ 
/*     */   public void setCityName(String cityName)
/*     */   {
/*  77 */     this.cityName = cityName;
/*     */   }
/*     */ 
/*     */   public String getStateProv()
/*     */   {
/*  84 */     return this.stateProv;
/*     */   }
/*     */ 
/*     */   public void setStateProv(String stateProv)
/*     */   {
/*  91 */     this.stateProv = stateProv;
/*     */   }
/*     */ 
/*     */   public String getStateCode()
/*     */   {
/*  98 */     return this.stateCode;
/*     */   }
/*     */ 
/*     */   public void setStateCode(String stateCode)
/*     */   {
/* 105 */     this.stateCode = stateCode;
/*     */   }
/*     */ 
/*     */   public String getCountryName()
/*     */   {
/* 112 */     return this.countryName;
/*     */   }
/*     */ 
/*     */   public void setCountryName(String countryName)
/*     */   {
/* 119 */     this.countryName = countryName;
/*     */   }
/*     */ 
/*     */   public String getPostalCode()
/*     */   {
/* 126 */     return this.postalCode;
/*     */   }
/*     */ 
/*     */   public void setPostalCode(String postalCode)
/*     */   {
/* 133 */     this.postalCode = postalCode;
/*     */   }
/*     */ 
/*     */   public String getTelephoner()
/*     */   {
/* 140 */     return this.telephoner;
/*     */   }
/*     */ 
/*     */   public void setTelephoner(String telephoner)
/*     */   {
/* 147 */     this.telephoner = telephoner;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ContactAddress
 * JD-Core Version:    0.6.0
 */