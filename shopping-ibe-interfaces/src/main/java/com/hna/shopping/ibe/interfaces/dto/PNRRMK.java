package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNRRMK extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5868476927016425958L;
	 private String rmktype;                                                                                                             
	 private String rmkinfo;                                                                                                             
	 private String psgrID;                                                                                                              
	private  int index;                                                                                                                      
	                                                                                                                                     
	 public PNRRMK()                                                                                                                     
	   throws Exception                                                                                                                  
	 {                                                                                                                                   
	 }                                                                                                                                   
	                                                                                                                                     
	 public PNRRMK(String rmktype, String rmkinfo, String psgrID)                                                                        
	   throws Exception                                                                                                                  
	 {                                                                                                                                   
	   this.rmktype = rmktype;                                                                                                           
	   this.rmkinfo = rmkinfo;                                                                                                           
	   this.psgrID = psgrID;                                                                                                             
	 }                                                                                                                                   
	                                                                                                                                      
	                                                                                                                                     
//	 public String getPsgrid()                                                                                                           
//	  // throws Exception, UnsupportedOperationException                                                                                   
//	 {                                                                                                                                   
////	   return this.getPsgrID();                                                                                                               
//	 }                                                                                                                                   
	                                                                                                                                             

}
