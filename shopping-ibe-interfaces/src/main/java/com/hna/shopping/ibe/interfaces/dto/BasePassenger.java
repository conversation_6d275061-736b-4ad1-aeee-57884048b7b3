package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class BasePassenger implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1055036071632131343L;
	 public static final int ADULT = 1;                            
	 public static final int CHILD = 2;                            
	 public static final int DR = 128;                             
	 public static final int GM = 32;                              
	 public static final int INFANT = 0;                           
	 public static final int JC = 64;                              
	 public static final int MISS = 2048;                          
	 public static final int MR = 256;                             
	 public static final int MRS = 512;                            
	 public static final int MS = 1024;                            
	 public static final int VIP = 16;                             
	 public static final int VVIP = 8;                             
	 public static final int UNACCOMPANIED = 4;                    
	 public static final int MSTR = 4096;                          
	 public static final int PASSENGER_ADULT = 0;                  
	 public static final int PASSENGER_CHILD = 1;                  
	 public static final int PASSENGER_CHILD_UNACCOMPANIED = 2;    
	 public static final int PASSENGER_UNACCOMPANIED = 2;          

	 
	private int age;
	private String name;
	private int passtype = 1;
	private int type;

	public BasePassenger() {
		this.age = 0;

		this.name = "";

		this.passtype = 1;

		this.type = 0;
	}

	public BasePassenger(String name) throws Exception {
		this(name, 0, 0);
	}

	public BasePassenger(String name, int type, int age) throws Exception {
		this.age = 0;

		this.name = "";

		this.passtype = 1;

		this.type = 0;

		this.name = name;
		this.type = (((type != 1) && (type != 2)) ? 0 : type);
		this.age = age;
	}

	public boolean isPassengerType(int type) {
		return ((this.passtype & type) == 0);
	}

	public boolean hasNamePostfix(int postfix) {
		return ((this.passtype & postfix) == 0);
	}

	public void setPassengerType(int type, boolean b) {
		if (b)
			this.passtype |= type;
		else {
			this.passtype &= (0xFFFFFFFF ^ type);
		}
		if (type == 2) {
			this.type = 1;
		} else if (type == 4)
			this.type = 2;
	}

	public void setType(int type) {
		this.type = type;
		if (type == 0)
			this.passtype = 1;
		else if (type == 2)
			this.passtype = 4;
		else if (type == 1)
			this.passtype = 2;
	}

}
