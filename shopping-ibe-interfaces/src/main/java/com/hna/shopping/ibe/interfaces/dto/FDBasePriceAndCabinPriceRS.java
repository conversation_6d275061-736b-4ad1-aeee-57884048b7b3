package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FDBasePriceAndCabinPriceRS implements Serializable {

    private static final long serialVersionUID = 1l;

    private String airline;
    private String depCode;
    private String arrCode;
    private String bookDate;
    private String ybPrice;
    private List<String> cabinList;
    private List<Integer> priceList;
    private Integer distance;
    private String flightDateStart;
    private String flightDateEnd;

    private String fuelTax;
    private String airportTax;

}
