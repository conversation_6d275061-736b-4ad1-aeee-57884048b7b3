/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class RetrievePassengerInfoOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6907825537463879865L;
/*    */   private List<PdPassengerInfo> pdPassengerInfos;
/*    */ 
/*    */   public List<PdPassengerInfo> getPdPassengerInfos()
/*    */   {
/* 31 */     return this.pdPassengerInfos;
/*    */   }
/*    */ 
/*    */   public void setPdPassengerInfos(List<PdPassengerInfo> pdPassengerInfos)
/*    */   {
/* 39 */     this.pdPassengerInfos = pdPassengerInfos;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.RetrievePassengerInfoOutputBean
 * JD-Core Version:    0.6.0
 */