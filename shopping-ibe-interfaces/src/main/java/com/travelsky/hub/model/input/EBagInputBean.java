/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class EBagInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String psrName;
/*     */   private String flightNumber;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String hostNumber;
/*     */   private String airlineCode;
/*     */   private String ticketNumber;
/*     */   private String boardingNumber;
/*     */   private String cabin;
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  34 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  42 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/*  50 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/*  58 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  66 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  74 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 115 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 123 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 131 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 139 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 147 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 155 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 164 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 172 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 179 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 187 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 196 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 204 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 212 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 220 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.EBagInputBean
 * JD-Core Version:    0.6.0
 */