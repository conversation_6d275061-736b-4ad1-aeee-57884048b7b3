/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EDISeResOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String seatMap;
/*    */   private String planeClass;
/*    */   private String planeType;
/*    */ 
/*    */   public String getSeatMap()
/*    */   {
/* 34 */     return this.seatMap;
/*    */   }
/*    */ 
/*    */   public void setSeatMap(String seatMap)
/*    */   {
/* 41 */     this.seatMap = seatMap;
/*    */   }
/*    */ 
/*    */   public String getPlaneClass()
/*    */   {
/* 48 */     return this.planeClass;
/*    */   }
/*    */ 
/*    */   public void setPlaneClass(String planeClass)
/*    */   {
/* 55 */     this.planeClass = planeClass;
/*    */   }
/*    */ 
/*    */   public String getPlaneType()
/*    */   {
/* 62 */     return this.planeType;
/*    */   }
/*    */ 
/*    */   public void setPlaneType(String planeType)
/*    */   {
/* 69 */     this.planeType = planeType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EDISeResOutput
 * JD-Core Version:    0.6.0
 */