/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import com.travelsky.hub.wdoe.output.PAAcceptedPsrBean;
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class UpdateOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  28 */   private String bordingTime = "";
/*     */ 
/*  33 */   private String departureTime = "";
/*     */ 
/*  39 */   private String arrivalTime = "";
/*     */ 
/*  45 */   private String boardingGateNumber = "";
/*     */ 
/*  49 */   private String marketingAirline = "";
/*     */ 
/*  53 */   private String marketingFlightNumber = "";
/*     */ 
/*  57 */   private String fareClass = "";
/*     */   private List<BoundInfo> boundInfos;
/*  66 */   private String docType = "";
/*     */ 
/*  70 */   private String docID = "";
/*     */ 
/*  74 */   private String passengerStatus = "";
/*     */ 
/*  78 */   private String hostNumber = "";
/*     */ 
/*  82 */   private String cabinType = "";
/*     */ 
/*  86 */   private String ticketID = "";
/*     */ 
/*  90 */   private String groupCode = "";
/*     */ 
/*  94 */   private String groupNumber = "";
/*     */ 
/*  98 */   private String contactText = "";
/*     */   private List<TextMsg> textMsgList;
/* 106 */   private String surName = "";
/*     */ 
/* 113 */   private List<PAAcceptedPsrBean> passengers = new ArrayList();
/*     */ 
/*     */   public List<PAAcceptedPsrBean> getPassengers()
/*     */   {
/* 120 */     return this.passengers;
/*     */   }
/*     */ 
/*     */   public void setPassengers(List<PAAcceptedPsrBean> passengers)
/*     */   {
/* 127 */     this.passengers = passengers;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 136 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String i)
/*     */   {
/* 143 */     this.arrivalTime = i;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 150 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String i)
/*     */   {
/* 157 */     this.bordingTime = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 164 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String i)
/*     */   {
/* 171 */     this.boardingGateNumber = i;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 178 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String i)
/*     */   {
/* 185 */     this.departureTime = i;
/*     */   }
/*     */ 
/*     */   public int getPassengersSize()
/*     */   {
/* 192 */     return this.passengers.size();
/*     */   }
/*     */ 
/*     */   public String getMarketingAirline()
/*     */   {
/* 200 */     return this.marketingAirline;
/*     */   }
/*     */ 
/*     */   public void setMarketingAirline(String marketingAirline)
/*     */   {
/* 207 */     this.marketingAirline = marketingAirline;
/*     */   }
/*     */ 
/*     */   public void clearPassengers()
/*     */   {
/* 214 */     this.passengers.clear();
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 220 */     clearPassengers();
/* 221 */     this.bordingTime = "";
/* 222 */     this.boardingGateNumber = "";
/* 223 */     this.departureTime = "";
/* 224 */     this.arrivalTime = "";
/*     */   }
/*     */ 
/*     */   public String getFareClass()
/*     */   {
/* 231 */     return this.fareClass;
/*     */   }
/*     */ 
/*     */   public void setFareClass(String fareClass)
/*     */   {
/* 238 */     this.fareClass = fareClass;
/*     */   }
/*     */ 
/*     */   public List<BoundInfo> getBoundInfos()
/*     */   {
/* 245 */     return this.boundInfos;
/*     */   }
/*     */ 
/*     */   public void setBoundInfos(List<BoundInfo> boundInfos)
/*     */   {
/* 252 */     this.boundInfos = boundInfos;
/*     */   }
/*     */ 
/*     */   public String getMarketingFlightNumber()
/*     */   {
/* 259 */     return this.marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMarketingFlightNumber(String marketingFlightNumber)
/*     */   {
/* 266 */     this.marketingFlightNumber = marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 272 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 279 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 287 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 294 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 301 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 308 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 315 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 322 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getContactText()
/*     */   {
/* 328 */     return this.contactText;
/*     */   }
/*     */ 
/*     */   public void setContactText(String contactText)
/*     */   {
/* 335 */     this.contactText = contactText;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 342 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 349 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 356 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 363 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getGroupCode()
/*     */   {
/* 369 */     return this.groupCode;
/*     */   }
/*     */ 
/*     */   public void setGroupCode(String groupCode)
/*     */   {
/* 376 */     this.groupCode = groupCode;
/*     */   }
/*     */ 
/*     */   public List<TextMsg> getTextMsgList()
/*     */   {
/* 383 */     return this.textMsgList;
/*     */   }
/*     */ 
/*     */   public void setTextMsgList(List<TextMsg> textMsgList)
/*     */   {
/* 390 */     this.textMsgList = textMsgList;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 397 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String groupNumber)
/*     */   {
/* 404 */     this.groupNumber = groupNumber;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 410 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 417 */     this.surName = surName;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.UpdateOutputBean
 * JD-Core Version:    0.6.0
 */