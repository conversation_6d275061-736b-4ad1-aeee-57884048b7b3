/*    */ package com.travelsky.hub.model.input.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryOrderInput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7771339655282350278L;
/*    */   private String bookingId;
/*    */ 
/*    */   public String getBookingId()
/*    */   {
/* 28 */     return this.bookingId;
/*    */   }
/*    */ 
/*    */   public void setBookingId(String bookingId)
/*    */   {
/* 35 */     this.bookingId = bookingId;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.rt.QueryOrderInput
 * JD-Core Version:    0.6.0
 */