# 系统端口
spring.application.name=jd-ibe

# 引入 ODS API 配置文件
spring.config.import=optional:classpath:odsapi.properties

# logback 配置，如果不显式指定，而是按 Spring boot 官方建议使用默认名字 logback-spring.xml
# 启动时会在当前目录默认生成一个文件夹 LOG_PATH_IS_UNDEFINED，然后两个文件夹都同时写入
# 初步判断是因为 springBoot 初始化的时候，还不能及时把 logging.path 注入到 logback 里的 LOG_PATH 变量导致的
# 根本原因是因为引入 SpringCloud 模块导致的，暂时这么解决吧
logging.config=classpath:log-back.xml

# eureka 注册中心配置
eureka.instance.preferIpAddress=true
eureka.instance.instance-id=${spring.cloud.client.ipAddress}:${server.port}


#server.tomcat.max-http-post-size=2048000
#server.tomcat.max-connections=600
#server.tomcat.max-threads=600
#server.tomcat.min-spare-threads=100
#server.max-http-header-size=2048000
# hystrix 超时时间
#hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=20000
#feign.hystrix.enabled=false
#feign.okhttp.enabled=true
# 默认 200
#feign.max-connections=600
# 默认值 50
#feign.max-connections-per-route=100

# Hystrix 线程池配置
###hystrix.threadpool.default.allowMaximumSizeToDivergeFromCoreSize=true
###hystrix.threadpool.default.coreSize=100
###hystrix.threadpool.default.maximumSize=200
###hystrix.threadpool.default.maxQueueSize=300
###hystrix.threadpool.default.queueSizeRejectionThreshold=100

jasypt.encryptor.algorithm=PBEWithMD5AndDES
jasypt.encryptor.iv-generator-classname=org.jasypt.iv.NoIvGenerator

# ODS API 调试日志
logging.level.com.hnair.opcnet=DEBUG
logging.level.com.hna.shopping.ibe.manager.esb=DEBUG