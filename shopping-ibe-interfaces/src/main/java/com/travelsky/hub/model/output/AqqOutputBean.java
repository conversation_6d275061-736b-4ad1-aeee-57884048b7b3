/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AqqOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String result;
/*    */   private String msgType;
/*    */ 
/*    */   public String getResult()
/*    */   {
/* 23 */     return this.result;
/*    */   }
/*    */ 
/*    */   public void setResult(String result)
/*    */   {
/* 30 */     this.result = result;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 38 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 46 */     this.msgType = msgType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AqqOutputBean
 * JD-Core Version:    0.6.0
 */