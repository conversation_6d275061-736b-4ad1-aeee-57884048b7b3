/*     */ package com.travelsky.hub.model.output.poolingbaggageallowance;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PassengerList
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8304625759880149765L;
/*     */   private String index;
/*     */   private String nameOfPassenger;
/*     */   private String groupNumber;
/*     */   private String groupName;
/*     */   private String boardingNumber;
/*     */   private String seatNumber;
/*     */   private String subClass;
/*     */   private String freeBaggage;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String passengerDestination;
/*     */   private String baggageDestination;
/*     */   private String remark;
/*     */   private String gfbaCode;
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  79 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  86 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getNameOfPassenger()
/*     */   {
/*  93 */     return this.nameOfPassenger;
/*     */   }
/*     */ 
/*     */   public void setNameOfPassenger(String nameOfPassenger)
/*     */   {
/* 100 */     this.nameOfPassenger = nameOfPassenger;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 107 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String groupNumber)
/*     */   {
/* 114 */     this.groupNumber = groupNumber;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/* 121 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/* 128 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 135 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 142 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 149 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 156 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getSubClass()
/*     */   {
/* 163 */     return this.subClass;
/*     */   }
/*     */ 
/*     */   public void setSubClass(String subClass)
/*     */   {
/* 170 */     this.subClass = subClass;
/*     */   }
/*     */ 
/*     */   public String getFreeBaggage()
/*     */   {
/* 177 */     return this.freeBaggage;
/*     */   }
/*     */ 
/*     */   public void setFreeBaggage(String freeBaggage)
/*     */   {
/* 184 */     this.freeBaggage = freeBaggage;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 191 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 198 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 205 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 212 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getPassengerDestination()
/*     */   {
/* 219 */     return this.passengerDestination;
/*     */   }
/*     */ 
/*     */   public void setPassengerDestination(String passengerDestination)
/*     */   {
/* 226 */     this.passengerDestination = passengerDestination;
/*     */   }
/*     */ 
/*     */   public String getBaggageDestination()
/*     */   {
/* 233 */     return this.baggageDestination;
/*     */   }
/*     */ 
/*     */   public void setBaggageDestination(String baggageDestination)
/*     */   {
/* 240 */     this.baggageDestination = baggageDestination;
/*     */   }
/*     */ 
/*     */   public String getRemark()
/*     */   {
/* 247 */     return this.remark;
/*     */   }
/*     */ 
/*     */   public void setRemark(String remark)
/*     */   {
/* 254 */     this.remark = remark;
/*     */   }
/*     */ 
/*     */   public String getGfbaCode()
/*     */   {
/* 261 */     return this.gfbaCode;
/*     */   }
/*     */ 
/*     */   public void setGfbaCode(String gfbaCode)
/*     */   {
/* 268 */     this.gfbaCode = gfbaCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.poolingbaggageallowance.PassengerList
 * JD-Core Version:    0.6.0
 */