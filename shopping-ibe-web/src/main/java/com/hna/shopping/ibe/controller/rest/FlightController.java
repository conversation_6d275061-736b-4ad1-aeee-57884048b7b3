/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import java.util.UUID;


import com.hna.shopping.ibe.interfaces.dto.pricing.request.PricingRequest;
import com.hna.shopping.ibe.interfaces.dto.pricing.response.PricingResponse;
import com.hna.shopping.ibe.interfaces.dto.searchone.request.SearchOneRequest;
import com.hna.shopping.ibe.interfaces.dto.searchone.response.SearchOneResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSON;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.dto.AVRequest;
import com.hna.shopping.ibe.interfaces.dto.AVResponse;
import com.hna.shopping.ibe.interfaces.dto.FFRequest;
import com.hna.shopping.ibe.interfaces.dto.FFResponse;
import com.hna.shopping.ibe.manager.FlightManager;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

@Api("航班查询接口")
@RestController
@RequestMapping("/api/flight")
@Slf4j
public class FlightController {

    @Autowired
    private FlightManager flightManager;


    @ApiOperation(value = "AV 查询")
    @RequestMapping(value = "av", method = RequestMethod.POST)
    RestResponse<AVResponse> av(@ApiParam(value = "航班查询参数对象", required = true)
    @RequestBody AVRequest request) {
    	String uuid = UUID.randomUUID().toString();

        log.info("Start uuid: {} , av: {}", uuid, JSON.toJSONString(request));
        AVResponse avResponse = flightManager.av(request);

        log.info("End uuid: {} , av: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }
    
    @ApiOperation(value = "FF 查询")
    @RequestMapping(value = "ff", method = RequestMethod.POST)
    RestResponse<FFResponse> ff(@ApiParam(value = "航班时刻查询参数对象", required = true)
    @RequestBody FFRequest request) {
    	String uuid = UUID.randomUUID().toString();
    	
    	log.info("Start uuid: {} , ff: {}", uuid, JSON.toJSONString(request));
    	FFResponse avResponse = flightManager.ff(request);
    	
    	log.info("End uuid: {} , ff: {}", uuid, JSON.toJSONString(avResponse));
    	return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "国际票searchone查询")
    @RequestMapping(value = "searchOne", method = RequestMethod.POST)
    RestResponse<SearchOneResponse> searchOne(@ApiParam(value = "国际航班查询参数对象", required = true)@RequestBody SearchOneRequest request,
                                              @ApiParam(value = "航司二字码", required = true)@RequestParam String airline) {
        String uuid = UUID.randomUUID().toString();

        log.info("Start uuid: {} , searchOne: {}", uuid, JSON.toJSONString(request));

        SearchOneResponse response = flightManager.searchOne(request,airline);

        log.info("End uuid: {} , searchOne: {}", uuid, JSON.toJSONString(response));
        return RestResponse.ok(response);
    }

    @ApiOperation(value = "国际票Q税")
    @RequestMapping(value = "pricing", method = RequestMethod.POST)
    RestResponse<PricingResponse> pricing(@ApiParam(value = "国际航班查询参数对象", required = true)@RequestBody PricingRequest request,
                                              @ApiParam(value = "航司二字码", required = true)@RequestParam String airline) {
        String uuid = UUID.randomUUID().toString();

        log.info("Start uuid: {} , pricing: {}", uuid, JSON.toJSONString(request));

        PricingResponse response = flightManager.pricing(request,airline);

        log.info("End uuid: {} , pricing: {}", uuid, JSON.toJSONString(response));
        return RestResponse.ok(response);
    }

}
