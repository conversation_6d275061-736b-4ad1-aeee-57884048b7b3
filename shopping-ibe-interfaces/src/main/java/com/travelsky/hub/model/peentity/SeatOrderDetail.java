/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatOrderDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7297580140789425610L;
/*     */   private String orderNum;
/*     */   private String orderStatus;
/*     */   private String orderStatusDesc;
/*     */   private String flightNumber;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String psrName;
/*     */   private String pnr;
/*     */   private String ticketNumber;
/*     */   private String tourIndex;
/*     */   private String certType;
/*     */   private String certNo;
/*     */   private String cabinType;
/*     */   private String seatNo;
/*     */   private String hostNo;
/*     */   private String price;
/*     */   private String payType;
/*     */   private String emdNo;
/*     */   private String validTime;
/*     */   private String currencyCode;
/*     */   private String errorMsg;
/*     */   private String errorCode;
/*     */   private String refundTktNum;
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  67 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  74 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getOrderNum()
/*     */   {
/*  83 */     return this.orderNum;
/*     */   }
/*     */ 
/*     */   public void setOrderNum(String orderNum)
/*     */   {
/*  90 */     this.orderNum = orderNum;
/*     */   }
/*     */ 
/*     */   public String getOrderStatus()
/*     */   {
/*  97 */     return this.orderStatus;
/*     */   }
/*     */ 
/*     */   public void setOrderStatus(String orderStatus)
/*     */   {
/* 104 */     this.orderStatus = orderStatus;
/*     */   }
/*     */ 
/*     */   public String getOrderStatusDesc()
/*     */   {
/* 112 */     return this.orderStatusDesc;
/*     */   }
/*     */ 
/*     */   public void setOrderStatusDesc(String orderStatusDesc)
/*     */   {
/* 119 */     this.orderStatusDesc = orderStatusDesc;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 128 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 135 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 142 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 149 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 157 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 164 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 171 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 178 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/* 185 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/* 192 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 200 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 207 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 214 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 221 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 228 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 235 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 243 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 250 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 257 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 264 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getHostNo()
/*     */   {
/* 272 */     return this.hostNo;
/*     */   }
/*     */ 
/*     */   public void setHostNo(String hostNo)
/*     */   {
/* 279 */     this.hostNo = hostNo;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 286 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 293 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getPrice()
/*     */   {
/* 300 */     return this.price;
/*     */   }
/*     */ 
/*     */   public void setPayType(String payType)
/*     */   {
/* 308 */     this.payType = payType;
/*     */   }
/*     */ 
/*     */   public String getErrorMsg()
/*     */   {
/* 315 */     return this.errorMsg;
/*     */   }
/*     */ 
/*     */   public void setErrorMsg(String errorMsg)
/*     */   {
/* 322 */     this.errorMsg = errorMsg;
/*     */   }
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/* 329 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setPrice(String price)
/*     */   {
/* 336 */     this.price = price;
/*     */   }
/*     */ 
/*     */   public String getPayType()
/*     */   {
/* 343 */     return this.payType;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/* 350 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public String getEmdNo()
/*     */   {
/* 357 */     return this.emdNo;
/*     */   }
/*     */ 
/*     */   public void setEmdNo(String emdNo)
/*     */   {
/* 364 */     this.emdNo = emdNo;
/*     */   }
/*     */ 
/*     */   public String getValidTime()
/*     */   {
/* 371 */     return this.validTime;
/*     */   }
/*     */ 
/*     */   public void setValidTime(String validTime)
/*     */   {
/* 378 */     this.validTime = validTime;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/* 385 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/* 392 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ 
/*     */   public String getRefundTktNum()
/*     */   {
/* 399 */     return this.refundTktNum;
/*     */   }
/*     */ 
/*     */   public void setRefundTktNum(String refundTktNum)
/*     */   {
/* 405 */     this.refundTktNum = refundTktNum;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatOrderDetail
 * JD-Core Version:    0.6.0
 */