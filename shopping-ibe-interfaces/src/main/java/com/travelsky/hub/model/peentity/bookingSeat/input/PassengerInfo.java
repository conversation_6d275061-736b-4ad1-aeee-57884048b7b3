/*    */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2614733913332846392L;
/*    */   private String passengerID;
/*    */   private String passengerName;
/*    */   private String passengerType;
/*    */ 
/*    */   public String getPassengerID()
/*    */   {
/* 42 */     return this.passengerID;
/*    */   }
/*    */ 
/*    */   public void setPassengerID(String passengerID)
/*    */   {
/* 49 */     this.passengerID = passengerID;
/*    */   }
/*    */ 
/*    */   public String getPassengerName()
/*    */   {
/* 56 */     return this.passengerName;
/*    */   }
/*    */ 
/*    */   public void setPassengerName(String passengerName)
/*    */   {
/* 63 */     this.passengerName = passengerName;
/*    */   }
/*    */ 
/*    */   public String getPassengerType()
/*    */   {
/* 70 */     return this.passengerType;
/*    */   }
/*    */ 
/*    */   public void setPassengerType(String passengerType)
/*    */   {
/* 77 */     this.passengerType = passengerType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.PassengerInfo
 * JD-Core Version:    0.6.0
 */