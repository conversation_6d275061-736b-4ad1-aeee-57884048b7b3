/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatOrderCanceled
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6948069616575340156L;
/*    */   private String isSuccess;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String orderNum;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 22 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 29 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 38 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 45 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 54 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 61 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 70 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 77 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 86 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 93 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatOrderCanceled
 * JD-Core Version:    0.6.0
 */