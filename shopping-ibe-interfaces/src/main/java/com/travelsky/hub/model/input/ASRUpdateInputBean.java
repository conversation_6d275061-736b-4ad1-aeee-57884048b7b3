/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ASRUpdateInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8881137663235029893L;
/*     */   private String hostNumber;
/*     */   private String cabin;
/*     */   private String destCity;
/*     */   private String flightNumber;
/*     */   private String deptCity;
/*     */   private String airlineCode;
/*     */   private String seatNo;
/*     */   private String isSNR;
/*     */   private String ckinMessage;
/*     */   private String tktNumber;
/*     */   private String flightDate;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  24 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  31 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  38 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  45 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  52 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  59 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  66 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  73 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/*  80 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/*  87 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  94 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 101 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 108 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 115 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getIsSNR()
/*     */   {
/* 122 */     return this.isSNR;
/*     */   }
/*     */ 
/*     */   public void setIsSNR(String isSNR)
/*     */   {
/* 129 */     this.isSNR = isSNR;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 136 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 143 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 150 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 157 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 165 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 172 */     this.cabin = cabin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ASRUpdateInputBean
 * JD-Core Version:    0.6.0
 */