/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class IdentityInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7199262405334466069L;
/*    */   private String foidType;
/*    */   private String foidNumber;
/*    */ 
/*    */   public String getFoidType()
/*    */   {
/* 34 */     return this.foidType;
/*    */   }
/*    */ 
/*    */   public String getFoidNumber()
/*    */   {
/* 43 */     return this.foidNumber;
/*    */   }
/*    */ 
/*    */   public void setFoidNumber(String foidNumber)
/*    */   {
/* 49 */     this.foidNumber = foidNumber;
/*    */   }
/*    */ 
/*    */   public void setFoidType(String foidType)
/*    */   {
/* 55 */     this.foidType = foidType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.IdentityInformation
 * JD-Core Version:    0.6.0
 */