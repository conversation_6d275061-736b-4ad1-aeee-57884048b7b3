/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class BaggageInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1278749290492974072L;
/*    */   private String baggageType;
/*    */   private String baggageCount;
/*    */   private String baggageWeight;
/*    */   private List<BagTagsInfo> bagTagsInfo;
/*    */ 
/*    */   public String getBaggageType()
/*    */   {
/* 42 */     return this.baggageType;
/*    */   }
/*    */ 
/*    */   public void setBaggageType(String baggageType)
/*    */   {
/* 49 */     this.baggageType = baggageType;
/*    */   }
/*    */ 
/*    */   public String getBaggageCount()
/*    */   {
/* 56 */     return this.baggageCount;
/*    */   }
/*    */ 
/*    */   public void setBaggageCount(String baggageCount)
/*    */   {
/* 63 */     this.baggageCount = baggageCount;
/*    */   }
/*    */ 
/*    */   public String getBaggageWeight()
/*    */   {
/* 70 */     return this.baggageWeight;
/*    */   }
/*    */ 
/*    */   public void setBaggageWeight(String baggageWeight)
/*    */   {
/* 77 */     this.baggageWeight = baggageWeight;
/*    */   }
/*    */ 
/*    */   public List<BagTagsInfo> getBagTagsInfo()
/*    */   {
/* 84 */     return this.bagTagsInfo;
/*    */   }
/*    */ 
/*    */   public void setBagTagsInfo(List<BagTagsInfo> bagTagsInfo)
/*    */   {
/* 91 */     this.bagTagsInfo = bagTagsInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BaggageInfo
 * JD-Core Version:    0.6.0
 */