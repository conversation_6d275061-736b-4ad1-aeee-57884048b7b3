/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightControlInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String channel;
/*    */   private String optService;
/*    */ 
/*    */   public String getChannel()
/*    */   {
/* 41 */     return this.channel;
/*    */   }
/*    */ 
/*    */   public void setChannel(String channel)
/*    */   {
/* 49 */     this.channel = channel;
/*    */   }
/*    */ 
/*    */   public String getOptService()
/*    */   {
/* 56 */     return this.optService;
/*    */   }
/*    */ 
/*    */   public void setOptService(String optService)
/*    */   {
/* 63 */     this.optService = optService;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.FlightControlInputBean
 * JD-Core Version:    0.6.0
 */