/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class DelExpressBagsOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8655025618592516089L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String scheduledDepartureTime;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String bagRemark;
/*     */   private String isManualBag;
/*     */   private String bagDestination;
/*     */   private String bagAirlineCode;
/*     */   private List<String> bagTagNumbers;
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  77 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  85 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  98 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 108 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 116 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 123 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 130 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 138 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 145 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 153 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getScheduledDepartureTime()
/*     */   {
/* 161 */     return this.scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 168 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setScheduledDepartureTime(String scheduledDepartureTime)
/*     */   {
/* 175 */     this.scheduledDepartureTime = scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 182 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 189 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 197 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getBagRemark()
/*     */   {
/* 205 */     return this.bagRemark;
/*     */   }
/*     */ 
/*     */   public void setBagRemark(String bagRemark)
/*     */   {
/* 213 */     this.bagRemark = bagRemark;
/*     */   }
/*     */ 
/*     */   public String getBagDestination()
/*     */   {
/* 221 */     return this.bagDestination;
/*     */   }
/*     */ 
/*     */   public void setBagDestination(String bagDestination)
/*     */   {
/* 229 */     this.bagDestination = bagDestination;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 236 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getBagAirlineCode()
/*     */   {
/* 242 */     return this.bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setBagAirlineCode(String bagAirlineCode)
/*     */   {
/* 250 */     this.bagAirlineCode = bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTagNumbers()
/*     */   {
/* 258 */     return this.bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 265 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumbers(List<String> bagTagNumbers)
/*     */   {
/* 273 */     this.bagTagNumbers = bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public String getIsManualBag()
/*     */   {
/* 281 */     return this.isManualBag;
/*     */   }
/*     */ 
/*     */   public void setIsManualBag(String isManualBag)
/*     */   {
/* 289 */     this.isManualBag = isManualBag;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 297 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 305 */     this.flightClass = flightClass;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DelExpressBagsOutputBean
 * JD-Core Version:    0.6.0
 */