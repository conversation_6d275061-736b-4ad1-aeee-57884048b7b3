/*
 * Copyright (C) 2017 eKing Technology, Inc. All Rights Reserved.
 */

package com.hna.shopping.ibe;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 这个 ImplementApplication 放这里，是为了能把 单元测试 run 起来
 * 这里没法引用到 WebApplication
 */
@SpringBootApplication
@EnableScheduling                       // 开启定时任务
@EnableDiscoveryClient                  // 启动 eureka
@EnableFeignClients                     // 启用 feign 进行远程调用
public class ManagerApplication {
    public static void main(String[] args) {
        SpringApplication.run(ManagerApplication.class, args);
    }
}
