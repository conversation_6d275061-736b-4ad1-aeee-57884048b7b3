package com.hna.shopping.ibe.interfaces.dto.checkin; /**
 * <AUTHOR>
 * @date 2020/10/29
 */

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import com.travelsky.hub.model.input.InfInfoBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SingleCheckInRQ extends BaseRequest implements Serializable {
    @ApiModelProperty("起飞时间")
    private String flightDate;
    @ApiModelProperty("航班号")
    private String flightNo;
    @ApiModelProperty("座位号")
    private String seatNo;
    @ApiModelProperty("到达城市")
    private String fromCity;
    @ApiModelProperty("起飞城市")
    private String toCity;

    private String etCode;//票号
    @ApiModelProperty("票号")
    private String tkNo;

    @ApiModelProperty("下标")
    private String tourIndex;

    //下面字段可以不传
    @ApiModelProperty("是否强制预留，默认false，航空公司根据业务需求，判定白金卡旅客需要强制预占C座位时则传入true")
    private boolean isSNR;
    @ApiModelProperty("常客卡号")
    private String cardID;
    @ApiModelProperty("常客卡航空公司")
    private String cardAirLine;
    @ApiModelProperty("儿童标示，如果有值则为“CHD”，否则为空")
    private String chd;
    @ApiModelProperty("未知 可以为空")
    private InfInfoBean infInfo;
    @ApiModelProperty("未知 可以为空")
    private String ckinMessage;
    @ApiModelProperty("ASR座位号 预选座位用，单人值机可忽略")
    private String asrSeatNo;
    @ApiModelProperty("性别 (港航航班，必须输入)")
    private String gender;
    @ApiModelProperty("旅客手机号，用于发送值机通知短信")
    private String phoneNumber;
    @ApiModelProperty("旅客邮箱，用于发送值机通知邮件")
    private String email;

}
