/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightHoldType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8739639664712134453L;
/*    */   private String hoType;
/*    */   private String operationType;
/*    */ 
/*    */   public String getHoType()
/*    */   {
/* 22 */     return this.hoType;
/*    */   }
/*    */ 
/*    */   public void setHoType(String hoType)
/*    */   {
/* 30 */     this.hoType = hoType;
/*    */   }
/*    */ 
/*    */   public String getOperationType()
/*    */   {
/* 38 */     return this.operationType;
/*    */   }
/*    */ 
/*    */   public void setOperationType(String operationType)
/*    */   {
/* 46 */     this.operationType = operationType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.FlightHoldType
 * JD-Core Version:    0.6.0
 */