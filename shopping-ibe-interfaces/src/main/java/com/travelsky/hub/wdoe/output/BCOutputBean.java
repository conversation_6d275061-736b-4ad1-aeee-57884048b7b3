/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.LinkedList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class BCOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8520659773801158137L;
/* 28 */   private List dataFlows = new LinkedList();
/*    */ 
/*    */   public List getDataFlows()
/*    */   {
/* 34 */     return this.dataFlows;
/*    */   }
/*    */ 
/*    */   public void setDataFlows(List dataFlows)
/*    */   {
/* 41 */     this.dataFlows = dataFlows;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 47 */     this.dataFlows.clear();
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.BCOutputBean
 * JD-Core Version:    0.6.0
 */