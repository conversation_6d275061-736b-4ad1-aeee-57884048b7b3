/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PreCkiBookInfoInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String sequenceNumber;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String flightNumber;
/*     */   private String arrivalAirport;
/*     */   private String ticketID;
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  19 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  27 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  34 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  41 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  49 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  62 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  72 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/*  80 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  87 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  95 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 102 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 109 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 117 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 125 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PreCkiBookInfoInput
 * JD-Core Version:    0.6.0
 */