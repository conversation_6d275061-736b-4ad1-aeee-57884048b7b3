/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SyDBOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6921143964878366889L;
/*    */   private String flightStatus;
/*    */ 
/*    */   public String getFlightStatus()
/*    */   {
/* 25 */     return this.flightStatus;
/*    */   }
/*    */ 
/*    */   public void setFlightStatus(String flightStatus)
/*    */   {
/* 33 */     this.flightStatus = flightStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.SyDBOutputBean
 * JD-Core Version:    0.6.0
 */