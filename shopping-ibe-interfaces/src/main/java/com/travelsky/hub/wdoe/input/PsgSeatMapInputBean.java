/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgSeatMapInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -210973172698421662L;
/*     */   private String flightOriCity;
/*     */   private String flightDesCity;
/*     */   private String airPort;
/*     */   private String paxOriCity;
/*     */   private String paxDesCity;
/*     */   private String paxLevel;
/*     */   private String paxNum;
/*     */   private String isVerticalMode;
/*     */ 
/*     */   public String getPaxLevel()
/*     */   {
/*  56 */     return this.paxLevel;
/*     */   }
/*     */ 
/*     */   public void setPaxLevel(String paxLevel)
/*     */   {
/*  63 */     this.paxLevel = paxLevel;
/*     */   }
/*     */ 
/*     */   public String getFlightOriCity()
/*     */   {
/*  72 */     return this.flightOriCity;
/*     */   }
/*     */ 
/*     */   public void setFlightOriCity(String flightOriCity)
/*     */   {
/*  79 */     this.flightOriCity = flightOriCity;
/*     */   }
/*     */ 
/*     */   public String getAirPort()
/*     */   {
/*  90 */     return this.airPort;
/*     */   }
/*     */ 
/*     */   public void setAirPort(String airPort)
/*     */   {
/*  97 */     this.airPort = airPort;
/*     */   }
/*     */ 
/*     */   public String getPaxOriCity()
/*     */   {
/* 104 */     return this.paxOriCity;
/*     */   }
/*     */ 
/*     */   public void setPaxOriCity(String paxOriCity)
/*     */   {
/* 111 */     this.paxOriCity = paxOriCity;
/*     */   }
/*     */ 
/*     */   public String getFlightDesCity()
/*     */   {
/* 118 */     return this.flightDesCity;
/*     */   }
/*     */ 
/*     */   public void setFlightDesCity(String flightDesCity)
/*     */   {
/* 125 */     this.flightDesCity = flightDesCity;
/*     */   }
/*     */ 
/*     */   public String getPaxDesCity()
/*     */   {
/* 132 */     return this.paxDesCity;
/*     */   }
/*     */ 
/*     */   public void setPaxDesCity(String paxDesCity)
/*     */   {
/* 139 */     this.paxDesCity = paxDesCity;
/*     */   }
/*     */ 
/*     */   public String getPaxNum()
/*     */   {
/* 146 */     return this.paxNum;
/*     */   }
/*     */ 
/*     */   public void setPaxNum(String paxNum)
/*     */   {
/* 153 */     this.paxNum = paxNum;
/*     */   }
/*     */ 
/*     */   public String getIsVerticalMode()
/*     */   {
/* 160 */     return this.isVerticalMode;
/*     */   }
/*     */ 
/*     */   public void setIsVerticalMode(String isVerticalMode)
/*     */   {
/* 167 */     this.isVerticalMode = isVerticalMode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PsgSeatMapInputBean
 * JD-Core Version:    0.6.0
 */