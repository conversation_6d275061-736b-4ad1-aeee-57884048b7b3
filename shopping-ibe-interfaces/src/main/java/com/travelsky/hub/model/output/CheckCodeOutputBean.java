/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CheckCodeOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String checkCode;
/*    */   private String telephone;
/*    */ 
/*    */   public String getCheckCode()
/*    */   {
/* 30 */     return this.checkCode;
/*    */   }
/*    */ 
/*    */   public void setCheckCode(String checkCode)
/*    */   {
/* 37 */     this.checkCode = checkCode;
/*    */   }
/*    */ 
/*    */   public String getTelephone()
/*    */   {
/* 44 */     return this.telephone;
/*    */   }
/*    */ 
/*    */   public void setTelephone(String telephone)
/*    */   {
/* 51 */     this.telephone = telephone;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.CheckCodeOutputBean
 * JD-Core Version:    0.6.0
 */