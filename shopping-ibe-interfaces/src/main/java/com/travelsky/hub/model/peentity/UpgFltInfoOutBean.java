/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class UpgFltInfoOutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4225495529713168780L;
/*    */   private String isSuccess;
/*    */   private List<UpgFltInfo> upgFltInfos;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 30 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 37 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public List<UpgFltInfo> getUpgFltInfos()
/*    */   {
/* 44 */     return this.upgFltInfos;
/*    */   }
/*    */ 
/*    */   public void setUpgFltInfos(List<UpgFltInfo> upgFltInfos)
/*    */   {
/* 51 */     this.upgFltInfos = upgFltInfos;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 58 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 65 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 72 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 79 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgFltInfoOutBean
 * JD-Core Version:    0.6.0
 */