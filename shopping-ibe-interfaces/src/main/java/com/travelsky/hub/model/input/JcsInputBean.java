/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class JcsInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3901180340154913065L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String flightDate;
/*     */   private String seatNumber;
/*     */   private String flightClass;
/*     */   private String jcsType;
/*     */   private String hostNumber;
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  48 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  55 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  65 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  72 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  87 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  94 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 101 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 108 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 114 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 121 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 128 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 135 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getJcsType()
/*     */   {
/* 143 */     return this.jcsType;
/*     */   }
/*     */ 
/*     */   public void setJcsType(String jcsType)
/*     */   {
/* 150 */     this.jcsType = jcsType;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 157 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 164 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 171 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 178 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.JcsInputBean
 * JD-Core Version:    0.6.0
 */