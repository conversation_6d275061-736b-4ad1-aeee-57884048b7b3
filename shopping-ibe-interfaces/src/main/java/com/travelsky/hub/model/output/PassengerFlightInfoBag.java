/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerFlightInfoBag
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5011228444955182935L;
/*    */   private String cabinType;
/*    */   private String gender;
/*    */   private String hostNumber;
/*    */   private String ticketID;
/*    */ 
/*    */   public String getCabinType()
/*    */   {
/* 39 */     return this.cabinType;
/*    */   }
/*    */ 
/*    */   public void setCabinType(String cabinType)
/*    */   {
/* 46 */     this.cabinType = cabinType;
/*    */   }
/*    */ 
/*    */   public String getGender()
/*    */   {
/* 53 */     return this.gender;
/*    */   }
/*    */ 
/*    */   public void setGender(String gender)
/*    */   {
/* 60 */     this.gender = gender;
/*    */   }
/*    */ 
/*    */   public String getHostNumber()
/*    */   {
/* 67 */     return this.hostNumber;
/*    */   }
/*    */ 
/*    */   public void setHostNumber(String hostNumber)
/*    */   {
/* 74 */     this.hostNumber = hostNumber;
/*    */   }
/*    */ 
/*    */   public String getTicketID()
/*    */   {
/* 81 */     return this.ticketID;
/*    */   }
/*    */ 
/*    */   public void setTicketID(String ticketID)
/*    */   {
/* 88 */     this.ticketID = ticketID;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerFlightInfoBag
 * JD-Core Version:    0.6.0
 */