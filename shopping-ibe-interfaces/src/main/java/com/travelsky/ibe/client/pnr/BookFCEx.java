package com.travelsky.ibe.client.pnr;

import java.util.*;

import com.hna.shopping.ibe.interfaces.dto.FareCalculation;

public class BookFCEx extends FareCalculation {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2790792658112555046L;

	private List<String> psgrname = new ArrayList<String>();                                    
     
	 private String xtCurrency = "";                                    
	                                                                    
	 private String inputCode = null;                                   
	                                                                    
	 public BookFCEx()                                                    
	 {                                                                  
	 }                                                                  
	                                                                    
	 public BookFCEx(String fc)                                           
	 {                                                                  
	   setFc(fc);                                                       
	 }                                                                  
	                                                                    
	 public BookFCEx(String fc, String name)                              
	 {                                                                  
	   setFc(fc);                                                       
	   if ((name != null) && (name.length() > 0))                       
	     this.psgrname.add(name);                                
	 }                                                                  
	                                                                    
	 public BookFCEx(String fc, String name, boolean inf)                 
	 {                                                                  
	   setFc(fc);                                                       
	   if ((name != null) && (name.length() > 0))                       
	     this.psgrname.add(name);                                
	   this.infant = inf;                                               
	 }                                                                  
	                                                                    
	 public void addPsgrname(String newPsgrname)                        
	 {                                                                  
	   if ((newPsgrname != null) && (newPsgrname.length() > 0))         
	     this.psgrname.add(newPsgrname);                         
	 }                                                                  
	                                                                    
	 public String getFc()                                              
	 {                                                                  
	   return this.fc;                                                  
	 }                                                                  
	                                                                    
                                                            
	                                                                    
	 public boolean isInfant()                                          
	 {                                                                  
	   return this.infant;                                              
	 }                                                                  
	                                                                    
	 public void setFc(String newFc)                                    
	 {                                                                  
	   String newFc1 = newFc.trim().toUpperCase(Locale.ENGLISH);
	   if ((newFc1.startsWith("FC/")) || (newFc1.startsWith("FC:")) ||  
	     (newFc1.startsWith("FC ")))                                    
	     newFc1 = newFc1.substring(3);                                  
	   this.fc = newFc1;                                                
	 }                                                                  
	                                                                    
	 public void setInfant(boolean newInfant)                           
	 {                                                                  
	   this.infant = newInfant;                                         
	 }                                                                  
	 public void setExtraCurrency(String currency)                                                 
	 {                                                                                             
	   super.setExtraTaxCurrency(currency);                                                        
	   if ((currency == null) || (currency.trim().length() != 3))                                  
	     return;                                                                                   
	   this.xtCurrency = currency;                                                                 
	 }                                                                                             
	                                                                                               
	 public String getXtCurrency()                                                                 
	 {                                                                                             
	   return this.xtCurrency;                                                                     
	 }                                                                                             
	                                                                                               
	 public void setXtCurrency(String xtCurrency)                                                  
	 {                                                                                             
	   if ((xtCurrency == null) || (xtCurrency.trim().length() != 3))                              
	     return;                                                                                   
	   this.xtCurrency = xtCurrency;                                                               
	 }                                                                                             
	                                                                                               
	 public void setAssociatedPassengerName(String name) throws UnsupportedOperationException      
	 {                                                                                             
	   this.psgrname.clear();                                                                      
	   addPsgrname(name);                                                                          
	 }                                                                                             
	                                                                                               
	 public void setAssociatedPassengerName(Collection<String> names)                              
	   throws UnsupportedOperationException, NullPointerException                                  
	 {                                                                                             
	   this.psgrname.clear();                                                                      
	   this.psgrname.addAll(names);                                                                
	 }                                                                                             
	                                                                                               
	 public void setAppliedForInfant(boolean infant) throws UnsupportedOperationException {        
	   setInfant(infant);                                                                          
	 }                                                                                             
	                                                                                               
	 public String getInputCode() {                                                                
	   return this.inputCode; }                                                                    
	                                                                                               
	 public void setInputCode(String inputCode) {                                                  
	   this.inputCode = inputCode;                                                                 
	 }



	public List<String> getPsgrname() {
		return psgrname;
	}

	public void setPsgrname(List<String> psgrname) {
		this.psgrname = psgrname;
	}                                                                                             

}
