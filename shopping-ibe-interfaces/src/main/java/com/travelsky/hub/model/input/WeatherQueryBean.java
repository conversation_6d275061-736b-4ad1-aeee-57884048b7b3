/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class WeatherQueryBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String airportCode;
/* 27 */   private String lang = "CHS";
/*    */ 
/*    */   public String getAirportCode()
/*    */   {
/* 33 */     return this.airportCode;
/*    */   }
/*    */ 
/*    */   public void setAirportCode(String airportCode)
/*    */   {
/* 40 */     this.airportCode = airportCode;
/*    */   }
/*    */ 
/*    */   public String getLang()
/*    */   {
/* 47 */     return this.lang;
/*    */   }
/*    */ 
/*    */   public void setLang(String lang)
/*    */   {
/* 54 */     this.lang = lang;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.WeatherQueryBean
 * JD-Core Version:    0.6.0
 */