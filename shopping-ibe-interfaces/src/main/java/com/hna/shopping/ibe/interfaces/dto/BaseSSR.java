package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
@ApiModel
public class BaseSSR implements Serializable {/**
	 * 
	 */
	private static final long serialVersionUID = 9040695364818129823L;
	 private String serveCode = "";             
	 private String airCode = "";               
	 private String actionCode = "";            
//	 private int person = 0;                    
	 private String cityPair = "";              
	 private String airNo = "";                 
	 private char fltClass = '$';               
//	 private Date departureTime = new Date(1L); 
	 private String serveInfo = "";             

	
}
