/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import com.travelsky.hub.model.input.TxnInfo;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PsrCheckInRequestBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 778938541119720555L;
/*    */   private TxnInfo txnInfo;
/*    */   private PsrCheckInInput psrCheckInRequestBean;
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 33 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 40 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ 
/*    */   public PsrCheckInInput getPsrCheckInRequestBean()
/*    */   {
/* 48 */     return this.psrCheckInRequestBean;
/*    */   }
/*    */ 
/*    */   public void setPsrCheckInRequestBean(PsrCheckInInput psrCheckInRequestBean)
/*    */   {
/* 55 */     this.psrCheckInRequestBean = psrCheckInRequestBean;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.PsrCheckInRequestBean
 * JD-Core Version:    0.6.0
 */