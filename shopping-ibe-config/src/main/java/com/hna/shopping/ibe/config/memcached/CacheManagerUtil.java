/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.memcached;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.Cache.ValueWrapper;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

/**
 * 简单封装一下, 只是为了能够加上 prefix
 *
 * <AUTHOR>
 */
//@Component
public class CacheManagerUtil {
    private static CacheManager cacheManager;

    @Autowired
    public CacheManagerUtil(CacheManager cacheManager) {
        CacheManagerUtil.cacheManager = cacheManager;
    }

    /**
     * 放一个 key、value
     *
     * @param cacheName
     * @param key
     * @param value
     */
    public static void put(String cacheName, Object key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        cache.put(CustomerKeyGenerator.generateKey(key), value);
    }

    /**
     * 放一个 key、value
     *
     * @param cacheName
     * @param key
     * @param value
     */
    public static void putIfAbsent(String cacheName, Object key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        cache.putIfAbsent(CustomerKeyGenerator.generateKey(key), value);
    }

    /**
     * 获取一个 key
     *
     * @param cacheName
     * @param key
     *
     * @return
     */
    public static Object get(String cacheName, Object key) {
        Cache cache = cacheManager.getCache(cacheName);
        ValueWrapper valueWrapper = cache.get(CustomerKeyGenerator.generateKey(key));
        return valueWrapper == null ? null : valueWrapper.get();
    }
}
