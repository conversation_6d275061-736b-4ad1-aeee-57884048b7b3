package com.hna.shopping.ibe.manager.util;

import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.config.ApplicationContextProvider;
import com.hna.shopping.ibe.config.ibe.IBEConfig;
import com.hna.shopping.ibe.config.ibe.IBEConfigFU;
import com.travelsky.ibe.client.IBEClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * ibe 相关操作
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IbeCmd {

    public static <T extends IBEClient> T getIbeClient(String airline, Class<T> clazz) {
        // 根据 airline 获取配置
        IBEConfig ibeConfig = ApplicationContextProvider.getBean(String.format("IBEConfig%s",
                StringUtils.upperCase(airline)), IBEConfig.class);

        // 写入配置
        IBEClient ibeClient;
        try {
            // 这里传进来的 clazz，肯定可以转成 IBEClient 的
            ibeClient = clazz.newInstance();

            ibeClient.setAgentInfo(ibeConfig.getClient().getOffice(), ibeConfig.getClient().getCustomNo(),
                    ibeConfig.getClient().getValidationNo());
            ibeClient.setAppName(ibeConfig.getClient().getApp());

            // 随机使用一个 ip
            String ip = (new Date().getTime() % 2 == 0) ? ibeConfig.getServer().getIp() : ibeConfig.getServer().getBackupIp();
            ibeClient.setConnectionInfo(ip, ibeConfig.getServer().getPort());

            return (T) ibeClient;
        } catch (InstantiationException | IllegalAccessException e) {
            throw new GlobalException(CodeDefault.ILLEGAL_ARGUMENT, e);
        }
    }
}
