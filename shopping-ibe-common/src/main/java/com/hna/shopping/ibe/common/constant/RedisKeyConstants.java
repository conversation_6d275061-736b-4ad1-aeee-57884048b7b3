/**
 * Copyright (C) 2006-2012 Tuniu All rights reserved
 * Author: gaoxin
 * Date: 2016/6/22
 * Description:TrainConfigConsts
 */
package com.hna.shopping.ibe.common.constant;

/**
 * redis key常量
 */
public class RedisKeyConstants {
    /**
     * 默认系统前缀
     */
    public static final String DEFAULT_PREFIX = "DEFAULT_";

    /**
     * uni-ibe系统前缀
     */
    public static final String UNI_IBE_PREFIX = "UNI_IBE_";

    /**
     * 操作pnr加锁前缀
     */
    public static final String LOCK_PNR_PREFIX = DEFAULT_PREFIX + "LOCK_PNR_";


}
