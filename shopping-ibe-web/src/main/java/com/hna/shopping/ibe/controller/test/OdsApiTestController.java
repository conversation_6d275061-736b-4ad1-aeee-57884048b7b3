package com.hna.shopping.ibe.controller.test;

import com.hna.shopping.ibe.manager.esb.ESBService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * ODS API 测试控制器
 * 用于验证 @EnableOdsApi 和 @OdsReference 是否正常工作
 */
@RestController
@RequestMapping("/test/odsapi")
@Slf4j
public class OdsApiTestController {
    
    @Autowired
    private ESBService esbService;
    
    /**
     * 测试 ODS API 注入是否成功
     */
    @GetMapping("/check")
    public String checkOdsApiInjection() {
        try {
            // 检查 ESBService 是否被正确注入
            if (esbService == null) {
                return "ESBService 注入失败";
            }
            
            // 通过反射检查 ietsQueryApi 字段是否被注入
            java.lang.reflect.Field field = ESBService.class.getDeclaredField("ietsQueryApi");
            field.setAccessible(true);
            Object ietsQueryApi = field.get(esbService);
            
            if (ietsQueryApi == null) {
                return "@OdsReference 注入失败 - ietsQueryApi 为 null";
            } else {
                return "@OdsReference 注入成功 - ietsQueryApi 类型: " + ietsQueryApi.getClass().getName();
            }
            
        } catch (Exception e) {
            log.error("检查 ODS API 注入时发生错误", e);
            return "检查失败: " + e.getMessage();
        }
    }
    
    /**
     * 测试调用 ODS API
     */
    @GetMapping("/test-call")
    public String testOdsApiCall() {
        try {
            // 这里可以添加实际的 ODS API 调用测试
            // 例如：esbService.queryIETSByIcsnoAndFltDate("test", "20250101");
            return "ODS API 调用测试 - 请在日志中查看详细信息";
        } catch (Exception e) {
            log.error("测试 ODS API 调用时发生错误", e);
            return "调用失败: " + e.getMessage();
        }
    }
}
