/*     */ package com.travelsky.hub.model.peentity.trr.confirm.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PnrUpdtInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1000436503648343084L;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */ 
///*     */   @JSONField(name="arrivalDate")
/*     */   private String arrivalDateTime;
/*     */   private String flightNo;
/*     */ 
///*     */   @J<PERSON>NField(name="flightDate")
/*     */   private String flightDateTime;
/*     */   private String cabin;
/*     */   private String isVVIP;
/*     */ 
/*     */   public static long getSerialVersionUID()
/*     */   {
/*  58 */     return -1000436503648343084L;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  65 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  72 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  79 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  86 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalDateTime()
/*     */   {
/*  93 */     return this.arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalDateTime(String arrivalDateTime)
/*     */   {
/* 100 */     this.arrivalDateTime = arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 107 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 114 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDateTime()
/*     */   {
/* 121 */     return this.flightDateTime;
/*     */   }
/*     */ 
/*     */   public void setFlightDateTime(String flightDateTime)
/*     */   {
/* 128 */     this.flightDateTime = flightDateTime;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 135 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 142 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getIsVVIP()
/*     */   {
/* 149 */     return this.isVVIP;
/*     */   }
/*     */ 
/*     */   public void setIsVVIP(String isVVIP)
/*     */   {
/* 156 */     this.isVVIP = isVVIP;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.confirm.input.PnrUpdtInfo
 * JD-Core Version:    0.6.0
 */