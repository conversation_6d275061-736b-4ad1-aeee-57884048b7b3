/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Delivery
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2082290794512630255L;
/*     */   private String xres;
/*     */   private String rw;
/*     */   private String hostNumber;
/*     */   private String freeBagageAllowance;
/*     */   private String infantFreeBagageAllowance;
/*     */ 
/*     */   public String getXres()
/*     */   {
/*  39 */     return this.xres;
/*     */   }
/*     */ 
/*     */   public void setXres(String xres)
/*     */   {
/*  46 */     this.xres = xres;
/*     */   }
/*     */ 
/*     */   public String getRw()
/*     */   {
/*  53 */     return this.rw;
/*     */   }
/*     */ 
/*     */   public void setRw(String rw)
/*     */   {
/*  60 */     this.rw = rw;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  67 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  74 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getFreeBagageAllowance()
/*     */   {
/*  81 */     return this.freeBagageAllowance;
/*     */   }
/*     */ 
/*     */   public void setFreeBagageAllowance(String freeBagageAllowance)
/*     */   {
/*  88 */     this.freeBagageAllowance = freeBagageAllowance;
/*     */   }
/*     */ 
/*     */   public String getInfantFreeBagageAllowance()
/*     */   {
/*  95 */     return this.infantFreeBagageAllowance;
/*     */   }
/*     */ 
/*     */   public void setInfantFreeBagageAllowance(String infantFreeBagageAllowance)
/*     */   {
/* 102 */     this.infantFreeBagageAllowance = infantFreeBagageAllowance;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Delivery
 * JD-Core Version:    0.6.0
 */