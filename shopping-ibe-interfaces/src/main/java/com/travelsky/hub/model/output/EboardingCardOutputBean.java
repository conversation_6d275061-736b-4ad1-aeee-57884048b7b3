/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class EboardingCardOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<String> ebpStr;
/*    */ 
/*    */   public List<String> getEbpStr()
/*    */   {
/* 28 */     return this.ebpStr;
/*    */   }
/*    */ 
/*    */   public void setEbpStr(List<String> ebpStr)
/*    */   {
/* 36 */     this.ebpStr = ebpStr;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EboardingCardOutputBean
 * JD-Core Version:    0.6.0
 */