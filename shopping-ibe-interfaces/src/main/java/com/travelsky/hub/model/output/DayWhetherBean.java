/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class DayWhetherBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String date;
/*     */   private String index;
/*     */   private String l_Temperature;
/*     */   private String h_Temperature;
/*     */   private String description;
/*     */   private String wind;
/*     */   private String day;
/*     */ 
/*     */   public String getDescription()
/*     */   {
/*  50 */     return this.description;
/*     */   }
/*     */ 
/*     */   public void setDescription(String description)
/*     */   {
/*  58 */     this.description = description.trim();
/*     */   }
/*     */ 
/*     */   public String getH_Temperature()
/*     */   {
/*  66 */     return this.h_Temperature;
/*     */   }
/*     */ 
/*     */   public void setH_Temperature(String temperature)
/*     */   {
/*  74 */     String temp = temperature.trim();
/*  75 */     temp = temp.substring(temp.indexOf(' '), temp.length());
/*  76 */     this.h_Temperature = temp;
/*     */   }
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  84 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  91 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getL_Temperature()
/*     */   {
/*  99 */     return this.l_Temperature;
/*     */   }
/*     */ 
/*     */   public void setL_Temperature(String temperature)
/*     */   {
/* 107 */     String temp = temperature.trim();
/* 108 */     temp = temp.substring(temp.indexOf(' '), temp.length());
/* 109 */     this.l_Temperature = temp;
/*     */   }
/*     */ 
/*     */   public String getWind()
/*     */   {
/* 117 */     return this.wind;
/*     */   }
/*     */ 
/*     */   public void setWind(String wind)
/*     */   {
/* 125 */     this.wind = wind.trim();
/*     */   }
/*     */ 
/*     */   public String getDate()
/*     */   {
/* 132 */     return this.date;
/*     */   }
/*     */ 
/*     */   public void setDate(String date)
/*     */   {
/* 139 */     this.date = date;
/*     */   }
/*     */ 
/*     */   public String getDay()
/*     */   {
/* 145 */     return this.day;
/*     */   }
/*     */ 
/*     */   public void setDay(String day)
/*     */   {
/* 151 */     this.day = day;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DayWhetherBean
 * JD-Core Version:    0.6.0
 */