/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AdcRequestBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6952475338490540004L;
/*     */   private String hostNum;
/*     */   private String fromCity;
/*     */   private String flightDate;
/*     */   private String ticketNumber;
/*     */   private String flightNumber;
/*     */   private String cabin;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  38 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  47 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  55 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  63 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/*  71 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/*  79 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  86 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  93 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 100 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 108 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 116 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 124 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 132 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 141 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AdcRequestBean
 * JD-Core Version:    0.6.0
 */