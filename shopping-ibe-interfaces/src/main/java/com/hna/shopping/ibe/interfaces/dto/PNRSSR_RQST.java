package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRSSR_RQST extends PNRSSR {
	/**
	 * 
	 */
	private static final long serialVersionUID = -2001820060582260041L;
	char cabin = ' ';                            
	String citypair = "";                        
	String date = "";                            
	String flight = "";                          
	int personNum = 0;                           
	String seatNo = "";                          
	                                             
	public char getCabin()                       
	{                                            
	  return this.cabin;                         
	}                                            
	                                             
	public String getCitypair()                  
	{                                            
	  return this.citypair;                      
	}                                            
	                                             
	public String getDate()                      
	{                                            
	  return this.date;                          
	}                                            
	                                             
	public String getFlight()                    
	{                                            
	  return this.flight;                        
	}                                            
	                                             
	public int getPersonNum()                    
	{                                            
	  return this.personNum;                     
	}                                            
	                                             
	public String getSeatNo()                    
	{                                            
	  return this.seatNo;                        
	}                                            
	                                             
	public void setCabin(char c)                 
	{                                            
	  this.cabin = c;                            
	}                                            
	                                             
	public void setCitypair(String string)       
	{                                            
	  this.citypair = string;                    
	}                                            
	                                             
	public void setDate(String string)           
	{                                            
	  this.date = string;                        
	}                                            
	                                             
	public void setFlight(String string)         
	{                                            
	  this.flight = string;                      
	}                                            
	                                             
	public void setPersonNum(int i)              
	{                                            
	  this.personNum = i;                        
	}                                            
	                                             
	public void setSeatNo(String string)         
	{                                            
	  this.seatNo = string;                      
	}                                            
	                                             
	public String toString() {                   
	  try {                                      
	    return this.index + ".SSR  " +       
	      this.SSRType +                         
	      "  " +                                 
	      this.airline +                         
	      "  " +                                 
	      this.actionCode +                      
	      " " +                                  
	      this.personNum +                       
	      "  城市对:" +                          
	      this.citypair +                        
	      "  航班号:" +                          
	      this.flight +                          
	      "  舱位:" +                            
	      this.cabin +                           
	      " 日期:" +                             
	      this.date +                            
	      " 座位:" +                             
	      this.seatNo; } catch (Exception e) {   
	  }                                          
	  return super.toString();                   
	}                                            

}
