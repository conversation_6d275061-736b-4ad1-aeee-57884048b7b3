/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AcceptMutiPsrOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3751444118331567333L;
/*  19 */   private String departureTime = "";
/*     */ 
/*  23 */   private String bordingTime = "";
/*     */ 
/*  28 */   private String boardingGateNumber = "";
/*     */ 
/*  32 */   private List passengers = new ArrayList();
/*     */ 
/*  37 */   private String arrivalTime = "";
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/*  44 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String i)
/*     */   {
/*  51 */     this.departureTime = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/*  58 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String i)
/*     */   {
/*  65 */     this.boardingGateNumber = i;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/*  72 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String i)
/*     */   {
/*  79 */     this.bordingTime = i;
/*     */   }
/*     */ 
/*     */   public PAAcceptedMutiPsrBean getPassenger(int i)
/*     */   {
/*  88 */     return (PAAcceptedMutiPsrBean)this.passengers.get(i);
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/*  95 */     clearPassengers();
/*  96 */     this.bordingTime = "";
/*  97 */     this.boardingGateNumber = "";
/*  98 */     this.departureTime = "";
/*  99 */     this.arrivalTime = "";
/*     */   }
/*     */ 
/*     */   public void clearPassengers()
/*     */   {
/* 106 */     this.passengers.clear();
/*     */   }
/*     */ 
/*     */   public void addPassenger(PAAcceptedMutiPsrBean i)
/*     */   {
/* 113 */     this.passengers.add(i);
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 120 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String i)
/*     */   {
/* 127 */     this.arrivalTime = i;
/*     */   }
/*     */ 
/*     */   public int getPassengersSize()
/*     */   {
/* 134 */     return this.passengers.size();
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.AcceptMutiPsrOutputBean
 * JD-Core Version:    0.6.0
 */