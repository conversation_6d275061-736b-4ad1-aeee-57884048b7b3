package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class DelPnrItemRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2507858944539441796L;

	private String pnrNo;
	private int[] indexes;

	/**
	 * 删除内容项
	 */
	private List<String> contents;
}
