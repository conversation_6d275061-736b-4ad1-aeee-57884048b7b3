/*    */ package com.travelsky.hub.model.input.poolingbaggageallowance;
/*    */ 
/*    */ import com.travelsky.hub.model.input.TxnInfo;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PoolingBaggageAllowanceRequest
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -654545241096976361L;
/*    */   private TxnInfo txnInfo;
/*    */   private PoolingBaggageAllowanceInput poolingBaggageAllowanceRq;
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 32 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 39 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ 
/*    */   public PoolingBaggageAllowanceInput getPoolingBaggageAllowanceRq()
/*    */   {
/* 46 */     return this.poolingBaggageAllowanceRq;
/*    */   }
/*    */ 
/*    */   public void setPoolingBaggageAllowanceRq(PoolingBaggageAllowanceInput poolingBaggageAllowanceRq)
/*    */   {
/* 53 */     this.poolingBaggageAllowanceRq = poolingBaggageAllowanceRq;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.poolingbaggageallowance.PoolingBaggageAllowanceRequest
 * JD-Core Version:    0.6.0
 */