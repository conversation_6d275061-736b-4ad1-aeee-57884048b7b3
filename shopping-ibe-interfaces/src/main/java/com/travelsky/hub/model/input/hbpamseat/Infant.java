/*    */ package com.travelsky.hub.model.input.hbpamseat;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Infant
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3524588555762416422L;
/*    */   private String infInfo;
/*    */   private String infEtNo;
/*    */   private String infEtSequence;
/*    */ 
/*    */   public String getInfInfo()
/*    */   {
/* 34 */     return this.infInfo;
/*    */   }
/*    */ 
/*    */   public void setInfInfo(String infInfo)
/*    */   {
/* 41 */     this.infInfo = infInfo;
/*    */   }
/*    */ 
/*    */   public String getInfEtNo()
/*    */   {
/* 48 */     return this.infEtNo;
/*    */   }
/*    */ 
/*    */   public void setInfEtNo(String infEtNo)
/*    */   {
/* 55 */     this.infEtNo = infEtNo;
/*    */   }
/*    */ 
/*    */   public String getInfEtSequence()
/*    */   {
/* 62 */     return this.infEtSequence;
/*    */   }
/*    */ 
/*    */   public void setInfEtSequence(String infEtSequence)
/*    */   {
/* 69 */     this.infEtSequence = infEtSequence;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.Infant
 * JD-Core Version:    0.6.0
 */