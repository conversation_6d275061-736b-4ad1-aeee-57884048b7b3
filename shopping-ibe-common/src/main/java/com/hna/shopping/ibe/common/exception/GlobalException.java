package com.hna.shopping.ibe.common.exception;

import com.hna.shopping.ibe.common.responsecode.CodeEnum;
import lombok.Data;

/**
 * 全局 exception, 本系统中的所有自定义 exception 都应该由此派生, 以便 GlobalExceptionHandler 进行捕获
 *
 * <AUTHOR>
 */
@Data
public class GlobalException extends RuntimeException {
    private CodeEnum codeEnum;
    private String message;

    private GlobalException() {
        super();
    }

    /**
     * 使用 codeEnum 的 defaultMessage 异常信息
     * 一定要带上 cause，才能 log 出最原始的堆栈
     *
     * @param codeEnum
     */
    public GlobalException(CodeEnum codeEnum, Throwable cause) {
        super(codeEnum.toString(), cause);
        this.codeEnum = codeEnum;
    }

    /**
     * 使用 defaultMessage + message 作为异常信息
     * 一定要带上 cause，才能 log 出最原始的堆栈
     *
     * @param codeEnum
     * @param message
     */
    public GlobalException(CodeEnum codeEnum, String message, Throwable cause) {
        super(String.format("%s\t%s", codeEnum.toString(), message), cause);
        this.codeEnum = codeEnum;
    }

    public GlobalException(CodeEnum codeEnum, String message) {
        this.message = message;
        this.codeEnum = codeEnum;
    }
}

