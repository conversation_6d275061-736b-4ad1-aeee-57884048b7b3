package com.hna.shopping.ibe.common.enums;

import javax.swing.text.html.CSS;

public enum SeatCharEnum {
    IRS("重要旅客预留座位"),ERS("ETRIP旅客留座"),AIR("右边有过道座位"),UMA("无人陪伴儿童座位"),NMV("不可观影"),RRS("靠背不可转动"),
    BAS("婴儿摇篮座位"),LTS("最后可利用座位"),PUG("登机口付费升舱座位"),ELR("腿部宽敞座位"),OSS("其他航段保留座位"),
    BLS("不可用（被锁定）座位"),SES("为本段保留座位"),ORS("离港普通旅客预留座位"),GRS("RS 预留团队座位"),VRS("VIP 旅客预留座位"),
    ARS("ASR 预留座位"),ABR("ABP 预留"),NTS("厕所行的座位"),IPS("婴儿优先行座位"),QES("安静座位"),EES("出口和紧急出口座位"),
    UDK("上层甲板座位"),WIN("靠窗座位"),CES("中间座位"),AIS("过道座位"),AIL("左边有过道座位"),ACL("飞机左侧座位"),ACR("飞机右侧座位"),
    CSS("中间区域座位"),OWI("机翼座位"),DAS("可用座位"),APS("已分配的付费座位,$座位的购买旅客留座"),TBS("转港保留座位"),SRS("订座特殊旅客过来离港后预留"),
    OCC("已值机"),TPS("在本航站转港旅客占座"),PWI("此座位旅客为带婴儿旅客"),DCH("收费座位"),LRI("左侧为婴儿预留座位/婴儿留座INF"),RRI("右侧为婴儿预留座位/婴儿留座INF"),
    LRK("左侧为携带狗屋旅客预留座位/手提狗笼PETC"),RRK("右侧为携带狗屋旅客预留座位/手提狗笼PETC"),LRB("左侧为大件行李预留座位/额外占座ADSR"),
    RRB("右侧为大件行李预留座位/额外占座ADSR"),

    ASR("JCS预留"),ABP("预留"),RES("订座预留"),CRS("PA/PU离港预留"),XASR("取消预留"),NML("离港普通预留"),
    SPC("特殊旅客预留"),VIP("VIP旅客预留"),IMP("重要旅客预留"),

    //FIXME 中文
    BZS(""),ISS(""),

    ;

    private String alias;
    SeatCharEnum(String alias){
        this.alias = alias;
    }

    public String getAlias(){
        return this.alias;
    }
}
