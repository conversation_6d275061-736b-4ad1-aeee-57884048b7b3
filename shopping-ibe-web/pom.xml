<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hna.shopping.ibe</groupId>
        <artifactId>shopping-ibe-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shopping-ibe-web</artifactId>
    <repositories>
        <!-- 配置nexus远程仓库 -->
        <repository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <!-- 配置从哪个仓库中下载构件，即jar包 -->
    <pluginRepositories>
        <pluginRepository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    
    <dependencies>
        <!--以后发布远程接口, 也是只需要引用这两个 jar 包即可; 后续考虑处理 common-->
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-interfaces</artifactId>
            <version>0.1.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-manager</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <!--下面两个, 为了今后能无缝拆分成远程调用, 下面两个, 必须指定为 runtime-->
        <dependency>
            <!--必须有这个, 否则启动找不到配置文件-->
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-config</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <!--对于远程调用, 才是引用 interface-->
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-implement</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-config</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>4.9</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.12</version>
        </dependency>
    </dependencies>


    <!--这个 build, 不能放在 parent, 否则每个 module 都会生成一个可执行的 jar 包; 整体包就会特别大: 400+mb-->
    <!--放这里, 就只有 web 可以执行了-->
    <build>
        <!-- 为jar包取名 -->
        <!--<finalName>shopping-ibe-start</finalName>-->
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.hna.shopping.ibe.WebApplication</mainClass>
                    <layout>ZIP</layout>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>haihangyun-spirit</id>
            <name>Releases</name>
            <url>http://maven.haihangyun.com/content/repositories/Spirit-Central</url>
        </repository>
    </distributionManagement>
</project>