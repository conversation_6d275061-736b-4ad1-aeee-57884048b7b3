/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Order
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2462101444116936286L;
/*    */   private String orderType;
/*    */   private String orderNum;
/*    */   private String orderStatus;
/*    */ 
/*    */   public String getOrderType()
/*    */   {
/* 27 */     return this.orderType;
/*    */   }
/*    */ 
/*    */   public void setOrderType(String orderType)
/*    */   {
/* 34 */     this.orderType = orderType;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 41 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 48 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 55 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 62 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.Order
 * JD-Core Version:    0.6.0
 */