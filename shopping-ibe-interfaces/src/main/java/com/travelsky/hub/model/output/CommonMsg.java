/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CommonMsg
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String ruleText;
/*    */   private String referenceCode;
/*    */ 
/*    */   public String getRuleText()
/*    */   {
/* 27 */     return this.ruleText;
/*    */   }
/*    */ 
/*    */   public void setRuleText(String ruleText)
/*    */   {
/* 36 */     this.ruleText = ruleText;
/*    */   }
/*    */ 
/*    */   public String getReferenceCode()
/*    */   {
/* 45 */     return this.referenceCode;
/*    */   }
/*    */ 
/*    */   public void setReferenceCode(String referenceCode)
/*    */   {
/* 54 */     this.referenceCode = referenceCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.CommonMsg
 * JD-Core Version:    0.6.0
 */