/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import com.travelsky.hub.model.output.ApiInfo;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ApiUpdInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6588322167668167148L;
/*    */   private ApiInfo apiInfo;
/*    */   private String ticketNumber;
/*    */ 
/*    */   public ApiInfo getApiInfo()
/*    */   {
/* 35 */     return this.apiInfo;
/*    */   }
/*    */ 
/*    */   public void setApiInfo(ApiInfo apiInfo)
/*    */   {
/* 42 */     this.apiInfo = apiInfo;
/*    */   }
/*    */ 
/*    */   public String getTicketNumber()
/*    */   {
/* 50 */     return this.ticketNumber;
/*    */   }
/*    */ 
/*    */   public void setTicketNumber(String ticketNumber)
/*    */   {
/* 58 */     this.ticketNumber = ticketNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.ApiUpdInputBean
 * JD-Core Version:    0.6.0
 */