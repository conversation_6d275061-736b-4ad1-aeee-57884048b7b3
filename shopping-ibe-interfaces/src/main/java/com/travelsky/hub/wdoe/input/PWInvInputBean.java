/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PWInvInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String boardingNumber;
/*     */   private String tourIndex;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private String passengerName;
/*     */   private String seatNumber;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String telephoneNumber;
/*     */   private String email;
/*     */   private String isNotified;
/*     */   private String isBppPrinted;
/*     */   private String psrComment;
/*     */   private String checkCode;
/*     */   private String outboundEtCode;
/*     */   private String forcedCancellation;
/*     */ 
/*     */   public String getCheckCode()
/*     */   {
/*  87 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */   public void setCheckCode(String checkCode)
/*     */   {
/*  95 */     this.checkCode = checkCode;
/*     */   }
/*     */ 
/*     */   public static long getSerialVersionUID()
/*     */   {
/* 103 */     return 1L;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 110 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 118 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 126 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 134 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 141 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 149 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 156 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 164 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 171 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 179 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 186 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 194 */     this.ffpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFFPCardPrior()
/*     */   {
/* 201 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFFPCardPrior(String cardPrior)
/*     */   {
/* 209 */     this.ffpCardPrior = cardPrior;
/*     */   }
/*     */ 
/*     */   public String getIsNotified()
/*     */   {
/* 216 */     return this.isNotified;
/*     */   }
/*     */ 
/*     */   public void setIsNotified(String isNotified)
/*     */   {
/* 224 */     this.isNotified = isNotified;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 232 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 240 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getIsBppPrinted()
/*     */   {
/* 247 */     return this.isBppPrinted;
/*     */   }
/*     */ 
/*     */   public void setIsBppPrinted(String isBppPrinted)
/*     */   {
/* 255 */     this.isBppPrinted = isBppPrinted;
/*     */   }
/*     */ 
/*     */   public String getPsrComment()
/*     */   {
/* 263 */     return this.psrComment;
/*     */   }
/*     */ 
/*     */   public void setPsrComment(String psrComment)
/*     */   {
/* 271 */     this.psrComment = psrComment;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 279 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 287 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getTelephoneNumber()
/*     */   {
/* 295 */     return this.telephoneNumber;
/*     */   }
/*     */ 
/*     */   public void setTelephoneNumber(String telephoneNumber)
/*     */   {
/* 303 */     this.telephoneNumber = telephoneNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 312 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 320 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getOutboundEtCode()
/*     */   {
/* 327 */     return this.outboundEtCode;
/*     */   }
/*     */ 
/*     */   public void setOutboundEtCode(String outboundEtCode)
/*     */   {
/* 334 */     this.outboundEtCode = outboundEtCode;
/*     */   }
/*     */ 
/*     */   public String getForcedCancellation()
/*     */   {
/* 342 */     return this.forcedCancellation;
/*     */   }
/*     */ 
/*     */   public void setForcedCancellation(String forcedCancellation)
/*     */   {
/* 350 */     this.forcedCancellation = forcedCancellation;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PWInvInputBean
 * JD-Core Version:    0.6.0
 */