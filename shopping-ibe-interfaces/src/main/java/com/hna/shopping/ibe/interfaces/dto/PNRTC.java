package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRTC extends PNRObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5935051423078614296L;
	private int index;                                                                                                  
	private String psgrid = null;                                                                                           
	                                                                                                                
	private String tourcode = null;                                                                                         
	                                                                                                                
	private boolean infant = false;                                                                                         
	                                                                                                                
                                                                                                             
	public String getPsgrid()                                                                                       
	{                                                                                                               
	  return this.psgrid;                                                                                           
	}                                                                                                               
	                                                                                                                
	public String getTourcode()                                                                                     
	{                                                                                                               
	  return this.tourcode;                                                                                         
	}                                                                                                               
	                                                                                                                
	void setPsgrid(String newPsgrid)                                                                                
	{                                                                                                               
	  this.psgrid = newPsgrid;                                                                                      
	}                                                                                                               
	                                                                                                                
	void setTourcode(String newTourcode)                                                                            
	{                                                                                                               
	  this.tourcode = newTourcode;                                                                                  
	}                                                                                                               
	                                                                                                                
	public String toString()                                                                                        
	{                                                                                                               
	  try                                                                                                           
	  {                                                                                                             
	    return String.valueOf(this.index) + ".TC " + ((this.infant) ? "IN/" : "") + this.tourcode + "  " +      
	      this.psgrid; } catch (Exception e) {                                                                      
	  }                                                                                                             
	  return super.toString();                                                                                      
	}                                                                                                               
	                                                                                                                
	public boolean isInfant()                                                                                       
	{                                                                                                               
	  return this.infant;                                                                                           
	}                                                                                                               
	                                                                                                                
	public void setInfant(boolean infant)                                                                           
	{                                                                                                               
	  this.infant = infant;                                                                                         
	}                                                                                                               

}
