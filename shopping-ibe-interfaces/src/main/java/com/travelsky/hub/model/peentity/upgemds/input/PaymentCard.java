/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PaymentCard
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4418843707507319207L;
/*    */   private String cardCode;
/*    */   private String cardNumber;
/*    */   private String text;
/*    */ 
/*    */   public String getCardCode()
/*    */   {
/* 43 */     return this.cardCode;
/*    */   }
/*    */ 
/*    */   public void setCardCode(String cardCode)
/*    */   {
/* 50 */     this.cardCode = cardCode;
/*    */   }
/*    */ 
/*    */   public String getCardNumber()
/*    */   {
/* 57 */     return this.cardNumber;
/*    */   }
/*    */ 
/*    */   public void setCardNumber(String cardNumber)
/*    */   {
/* 64 */     this.cardNumber = cardNumber;
/*    */   }
/*    */ 
/*    */   public String getText()
/*    */   {
/* 71 */     return this.text;
/*    */   }
/*    */ 
/*    */   public void setText(String text)
/*    */   {
/* 78 */     this.text = text;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.PaymentCard
 * JD-Core Version:    0.6.0
 */