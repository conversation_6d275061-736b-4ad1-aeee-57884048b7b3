package com.hna.shopping.ibe.common.util;

public enum PassengerType
{
	PASSENGER_0("ADT"), PASSENGER_1("CHD"),PASSENGER_2("CHD"), PASSENGER_3("INF"), PASSENGER_4("OTHER");

	private final String alias;

	private PassengerType(String alias) {
		this.alias = alias;
	}

	public String getAlias() {
		return alias;
	}

	public static String convertString(String in[])
	{

		if (in!=null && in.length >0)
		{
			String out = "";
			for (String s  : in)
			{
				out = out + s + ",";
			}
			// remove the last ','
			if(out.endsWith(",")){out = out.substring(0, out.length()-1);}
			return out;
		}
		return null;
	}
}
