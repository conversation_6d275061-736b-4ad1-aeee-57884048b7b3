/*     */ package com.travelsky.hub.model.output.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class StopOverInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4345669412936293017L;
/*     */   private String cabinType;
/*     */   private String departureAirport;
/*     */   private String boardingTime;
/*     */   private String boardingGateNumber;
/*     */   private String seatNumber;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  48 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  55 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/*  61 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/*  68 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  75 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  82 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  89 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  96 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 102 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 109 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 115 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 122 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpamseat.StopOverInfo
 * JD-Core Version:    0.6.0
 */