package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class AVFlightInfo extends FlightInfo implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 8529902126819247366L;
//	private AVCabinInfo maxOpenCabin;
	private List<AVCabinInfo> cabins= new ArrayList<AVCabinInfo>();

	
}
