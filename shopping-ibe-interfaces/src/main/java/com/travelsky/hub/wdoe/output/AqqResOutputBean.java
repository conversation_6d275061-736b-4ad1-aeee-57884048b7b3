/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import com.travelsky.hub.model.output.govsecuritycheck.GovSecurityInformation;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AqqResOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4465252122507495567L;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String deptCity;
/*     */   private String aqqFlag;
/*     */   private String estaFlag;
/*     */   private String hostNBR;
/*     */   private List<GovSecurityInformation> govSecurityInformations;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  60 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  67 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public List<GovSecurityInformation> getGovSecurityInformations()
/*     */   {
/*  73 */     return this.govSecurityInformations;
/*     */   }
/*     */ 
/*     */   public void setGovSecurityInformations(List<GovSecurityInformation> govSecurityInformations)
/*     */   {
/*  80 */     this.govSecurityInformations = govSecurityInformations;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  88 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getHostNBR()
/*     */   {
/*  95 */     return this.hostNBR;
/*     */   }
/*     */ 
/*     */   public void setHostNBR(String hostNBR)
/*     */   {
/* 102 */     this.hostNBR = hostNBR;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 109 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/* 116 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public String getAqqFlag()
/*     */   {
/* 123 */     return this.aqqFlag;
/*     */   }
/*     */ 
/*     */   public void setAqqFlag(String aqqFlag)
/*     */   {
/* 130 */     this.aqqFlag = aqqFlag;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 137 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getEstaFlag()
/*     */   {
/* 145 */     return this.estaFlag;
/*     */   }
/*     */ 
/*     */   public void setEstaFlag(String estaFlag)
/*     */   {
/* 152 */     this.estaFlag = estaFlag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.AqqResOutputBean
 * JD-Core Version:    0.6.0
 */