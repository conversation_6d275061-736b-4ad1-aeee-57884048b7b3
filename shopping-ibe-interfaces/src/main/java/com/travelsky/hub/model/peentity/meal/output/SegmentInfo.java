/*    */ package com.travelsky.hub.model.peentity.meal.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SegmentInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2053960016064786030L;
/*    */   private String departureAirportCode;
/*    */   private String arrivalAirportCode;
/*    */   private List<MealCodeInfo> mealCodeInfos;
/*    */   private List<ClassStatusInfo> classStatusInfos;
/*    */ 
/*    */   public String getDepartureAirportCode()
/*    */   {
/* 32 */     return this.departureAirportCode;
/*    */   }
/*    */ 
/*    */   public void setDepartureAirportCode(String departureAirportCode)
/*    */   {
/* 41 */     this.departureAirportCode = departureAirportCode;
/*    */   }
/*    */ 
/*    */   public String getArrivalAirportCode()
/*    */   {
/* 50 */     return this.arrivalAirportCode;
/*    */   }
/*    */ 
/*    */   public void setArrivalAirportCode(String arrivalAirportCode)
/*    */   {
/* 59 */     this.arrivalAirportCode = arrivalAirportCode;
/*    */   }
/*    */ 
/*    */   public List<MealCodeInfo> getMealCodeInfos()
/*    */   {
/* 68 */     return this.mealCodeInfos;
/*    */   }
/*    */ 
/*    */   public void setMealCodeInfos(List<MealCodeInfo> mealCodeInfos)
/*    */   {
/* 77 */     this.mealCodeInfos = mealCodeInfos;
/*    */   }
/*    */ 
/*    */   public List<ClassStatusInfo> getClassStatusInfos()
/*    */   {
/* 84 */     return this.classStatusInfos;
/*    */   }
/*    */ 
/*    */   public void setClassStatusInfos(List<ClassStatusInfo> classStatusInfos)
/*    */   {
/* 91 */     this.classStatusInfos = classStatusInfos;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.SegmentInfo
 * JD-Core Version:    0.6.0
 */