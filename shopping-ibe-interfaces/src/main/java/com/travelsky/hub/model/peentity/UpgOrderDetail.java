/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class UpgOrderDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6350956087872645523L;
/*     */   private String isSuccess;
/*     */   private String orderNum;
/*     */   private String flightNumber;
/*     */   private String arrivalAirport;
/*     */   private String psrName;
/*     */   private String pnr;
/*     */   private String certType;
/*     */   private String contactInfo;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String psrLevel;
/*     */   private String certNo;
/*     */   private String originalCabin;
/*     */   private String seniorCabin;
/*     */   private String originalSeat;
/*     */   private String seniorSest;
/*     */   private String upPrice;
/*     */   private String ticketNumber;
/*     */   private String tourIndex;
/*     */   private String currencyCode;
/*     */   private String PayType;
/*     */   private String OrderStatus;
/*     */   private String validTime;
/*     */   private String emdNo;
/*     */   private String errorMsg;
/*     */   private String errorCode;
/*     */   private String refundTktNum;
/*     */ 
/*     */   public String getRefundTktNum()
/*     */   {
/*  77 */     return this.refundTktNum;
/*     */   }
/*     */ 
/*     */   public void setRefundTktNum(String refundTktNum)
/*     */   {
/*  84 */     this.refundTktNum = refundTktNum;
/*     */   }
/*     */ 
/*     */   public String getOrderNum()
/*     */   {
/*  92 */     return this.orderNum;
/*     */   }
/*     */ 
/*     */   public void setOrderNum(String orderNum)
/*     */   {
/*  99 */     this.orderNum = orderNum;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 106 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 113 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 120 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 127 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 134 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 141 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 148 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 155 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 162 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 169 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 176 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 183 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 190 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 197 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 204 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 211 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 219 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 226 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/* 233 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/* 240 */     this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/* 247 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/* 254 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/* 261 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/* 268 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ 
/*     */   public String getOriginalCabin()
/*     */   {
/* 275 */     return this.originalCabin;
/*     */   }
/*     */ 
/*     */   public void setOriginalCabin(String originalCabin)
/*     */   {
/* 282 */     this.originalCabin = originalCabin;
/*     */   }
/*     */ 
/*     */   public String getSeniorCabin()
/*     */   {
/* 289 */     return this.seniorCabin;
/*     */   }
/*     */ 
/*     */   public void setSeniorCabin(String seniorCabin)
/*     */   {
/* 296 */     this.seniorCabin = seniorCabin;
/*     */   }
/*     */ 
/*     */   public String getIsSuccess()
/*     */   {
/* 303 */     return this.isSuccess;
/*     */   }
/*     */ 
/*     */   public void setIsSuccess(String isSuccess)
/*     */   {
/* 310 */     this.isSuccess = isSuccess;
/*     */   }
/*     */ 
/*     */   public String getOriginalSeat()
/*     */   {
/* 317 */     return this.originalSeat;
/*     */   }
/*     */ 
/*     */   public void setOriginalSeat(String originalSeat)
/*     */   {
/* 324 */     this.originalSeat = originalSeat;
/*     */   }
/*     */ 
/*     */   public String getSeniorSest()
/*     */   {
/* 331 */     return this.seniorSest;
/*     */   }
/*     */ 
/*     */   public void setSeniorSest(String seniorSest)
/*     */   {
/* 338 */     this.seniorSest = seniorSest;
/*     */   }
/*     */ 
/*     */   public String getUpPrice()
/*     */   {
/* 345 */     return this.upPrice;
/*     */   }
/*     */ 
/*     */   public void setUpPrice(String upPrice)
/*     */   {
/* 352 */     this.upPrice = upPrice;
/*     */   }
/*     */ 
/*     */   public String getPayType()
/*     */   {
/* 359 */     return this.PayType;
/*     */   }
/*     */ 
/*     */   public void setPayType(String payType)
/*     */   {
/* 366 */     this.PayType = payType;
/*     */   }
/*     */ 
/*     */   public String getValidTime()
/*     */   {
/* 373 */     return this.validTime;
/*     */   }
/*     */ 
/*     */   public void setValidTime(String validTime)
/*     */   {
/* 380 */     this.validTime = validTime;
/*     */   }
/*     */ 
/*     */   public String getOrderStatus()
/*     */   {
/* 387 */     return this.OrderStatus;
/*     */   }
/*     */ 
/*     */   public void setOrderStatus(String orderStatus)
/*     */   {
/* 394 */     this.OrderStatus = orderStatus;
/*     */   }
/*     */ 
/*     */   public String getErrorMsg()
/*     */   {
/* 401 */     return this.errorMsg;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/* 409 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public String getEmdNo()
/*     */   {
/* 416 */     return this.emdNo;
/*     */   }
/*     */ 
/*     */   public void setEmdNo(String emdNo)
/*     */   {
/* 423 */     this.emdNo = emdNo;
/*     */   }
/*     */ 
/*     */   public void setErrorMsg(String errorMsg)
/*     */   {
/* 430 */     this.errorMsg = errorMsg;
/*     */   }
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/* 437 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/* 444 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/* 451 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgOrderDetail
 * JD-Core Version:    0.6.0
 */