/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ElectronicTicketInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1835114716891515792L;
/*    */   private String electronicTicketCoupon;
/*    */   private String electronicTicketType;
/*    */   private String electronicTicketNumber;
/*    */ 
/*    */   public String getElectronicTicketCoupon()
/*    */   {
/* 29 */     return this.electronicTicketCoupon;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketCoupon(String electronicTicketCoupon)
/*    */   {
/* 36 */     this.electronicTicketCoupon = electronicTicketCoupon;
/*    */   }
/*    */ 
/*    */   public String getElectronicTicketType()
/*    */   {
/* 45 */     return this.electronicTicketType;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketType(String electronicTicketType)
/*    */   {
/* 52 */     this.electronicTicketType = electronicTicketType;
/*    */   }
/*    */ 
/*    */   public String getElectronicTicketNumber()
/*    */   {
/* 60 */     return this.electronicTicketNumber;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketNumber(String electronicTicketNumber)
/*    */   {
/* 67 */     this.electronicTicketNumber = electronicTicketNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.ElectronicTicketInformation
 * JD-Core Version:    0.6.0
 */