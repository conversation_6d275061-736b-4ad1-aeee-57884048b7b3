package com.hna.shopping.ibe.interfaces.dto.ticketinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class DtoTicketInfoPassengerNoRequest implements Serializable {

	private static final long serialVersionUID = -1L;

	@ApiModelProperty(value = "旅客证件类型")
	@NotEmpty(message = "passengerType不能为空")
	private String passengerType;

	@ApiModelProperty(value = "旅客证件号")
	@NotEmpty(message = "passengerNo不能为空")
	private String passengerNo;

	@ApiModelProperty(value = "指定航空公司配置，必填")
	@NotEmpty(message = "airlineCode不能为空")
	private String airlineCode;

	private String depCode;

	@ApiModelProperty(value = "是否需要获取编码预定时间，不为空时多RT一次，获取预定时间")
	private String needPnrBookTime;

	@ApiModelProperty(value = "是否需要获取客票改期历史，不为空时获取")
	private String needTicketChangeHis;

	/**
	 * pnr。双因素
	 */
	private String pnrNo;
	/**
	 * 旅客姓名。 双因素
	 */
	private String passengerName;

}
