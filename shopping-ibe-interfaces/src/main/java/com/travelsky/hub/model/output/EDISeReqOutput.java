/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EDISeReqOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String resultCode;
/*    */   private String resultMsg;
/*    */ 
/*    */   public String getResultMsg()
/*    */   {
/* 19 */     return this.resultMsg;
/*    */   }
/*    */ 
/*    */   public void setResultMsg(String resultMsg)
/*    */   {
/* 26 */     this.resultMsg = resultMsg;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 39 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 46 */     this.resultCode = resultCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EDISeReqOutput
 * JD-Core Version:    0.6.0
 */