/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsptInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6592488411146157296L;
/*     */   private String psptNumber;
/*     */   private String countryName;
/*     */   private String birthDate;
/*     */   private String gender;
/*     */   private String multiPassenger;
/*     */ 
/*     */   public String getPsptNumber()
/*     */   {
/*  48 */     return this.psptNumber;
/*     */   }
/*     */ 
/*     */   public void setPsptNumber(String psptNumber)
/*     */   {
/*  55 */     this.psptNumber = psptNumber;
/*     */   }
/*     */ 
/*     */   public String getCountryName()
/*     */   {
/*  62 */     return this.countryName;
/*     */   }
/*     */ 
/*     */   public void setCountryName(String countryName)
/*     */   {
/*  69 */     this.countryName = countryName;
/*     */   }
/*     */ 
/*     */   public String getBirthDate()
/*     */   {
/*  76 */     return this.birthDate;
/*     */   }
/*     */ 
/*     */   public void setBirthDate(String birthDate)
/*     */   {
/*  83 */     this.birthDate = birthDate;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/*  90 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/*  97 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getMultiPassenger()
/*     */   {
/* 104 */     return this.multiPassenger;
/*     */   }
/*     */ 
/*     */   public void setMultiPassenger(String multiPassenger)
/*     */   {
/* 111 */     this.multiPassenger = multiPassenger;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PsptInfo
 * JD-Core Version:    0.6.0
 */