/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Traveller
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1268043857500461774L;
/*    */   private String travellerID;
/*    */   private String travellerName;
/*    */   private String passengerTypeCode;
/*    */ 
/*    */   public String getTravellerID()
/*    */   {
/* 41 */     return this.travellerID;
/*    */   }
/*    */ 
/*    */   public void setTravellerID(String travellerID)
/*    */   {
/* 48 */     this.travellerID = travellerID;
/*    */   }
/*    */ 
/*    */   public String getTravellerName()
/*    */   {
/* 55 */     return this.travellerName;
/*    */   }
/*    */ 
/*    */   public void setTravellerName(String travellerName)
/*    */   {
/* 62 */     this.travellerName = travellerName;
/*    */   }
/*    */ 
/*    */   public String getPassengerTypeCode()
/*    */   {
/* 69 */     return this.passengerTypeCode;
/*    */   }
/*    */ 
/*    */   public void setPassengerTypeCode(String passengerTypeCode)
/*    */   {
/* 76 */     this.passengerTypeCode = passengerTypeCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Traveller
 * JD-Core Version:    0.6.0
 */