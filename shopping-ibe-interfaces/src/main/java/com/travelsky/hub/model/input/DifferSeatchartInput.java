/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class DifferSeatchartInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String ticketNo;
/*     */   private String flightSuffix;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String flightNum;
/*     */   private String cabin;
/*     */   private String hostNum;
/*     */   private String flightDate;
/*     */   private String priority;
/*     */   private String optionType;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  68 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  76 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  84 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  92 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 100 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 108 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getFlightNum()
/*     */   {
/* 116 */     return this.flightNum;
/*     */   }
/*     */ 
/*     */   public void setFlightNum(String flightNum)
/*     */   {
/* 124 */     this.flightNum = flightNum;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 132 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 140 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 148 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 156 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 164 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 172 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getPriority()
/*     */   {
/* 180 */     return this.priority;
/*     */   }
/*     */ 
/*     */   public void setPriority(String priority)
/*     */   {
/* 188 */     this.priority = priority;
/*     */   }
/*     */ 
/*     */   public String getOptionType()
/*     */   {
/* 196 */     return this.optionType;
/*     */   }
/*     */ 
/*     */   public void setOptionType(String optionType)
/*     */   {
/* 204 */     this.optionType = optionType;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 212 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 220 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getTicketNo()
/*     */   {
/* 228 */     return this.ticketNo;
/*     */   }
/*     */ 
/*     */   public void setTicketNo(String ticketNo)
/*     */   {
/* 236 */     this.ticketNo = ticketNo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.DifferSeatchartInput
 * JD-Core Version:    0.6.0
 */