/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Message
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1071933513389379660L;
/*     */   private String serviceType;
/*     */   private String messageGUID;
/*     */   private String messageStatus;
/*     */   private String messageCode;
/*     */   private String ruleID;
/*     */   private String message;
/*     */   private String documentGUID;
/*     */   private String segmentGUID;
/*     */   private List<SuggestedDocument> suggestedDocuments;
/*     */ 
/*     */   public String getServiceType()
/*     */   {
/*  67 */     return this.serviceType;
/*     */   }
/*     */ 
/*     */   public String getMessageGUID()
/*     */   {
/*  74 */     return this.messageGUID;
/*     */   }
/*     */ 
/*     */   public String getMessageStatus()
/*     */   {
/*  81 */     return this.messageStatus;
/*     */   }
/*     */ 
/*     */   public String getMessageCode()
/*     */   {
/*  88 */     return this.messageCode;
/*     */   }
/*     */ 
/*     */   public String getRuleID()
/*     */   {
/*  95 */     return this.ruleID;
/*     */   }
/*     */ 
/*     */   public String getMessage()
/*     */   {
/* 102 */     return this.message;
/*     */   }
/*     */ 
/*     */   public String getDocumentGUID()
/*     */   {
/* 109 */     return this.documentGUID;
/*     */   }
/*     */ 
/*     */   public String getSegmentGUID()
/*     */   {
/* 116 */     return this.segmentGUID;
/*     */   }
/*     */ 
/*     */   public List<SuggestedDocument> getSuggestedDocuments()
/*     */   {
/* 123 */     return this.suggestedDocuments;
/*     */   }
/*     */ 
/*     */   public void setServiceType(String serviceType)
/*     */   {
/* 130 */     this.serviceType = serviceType;
/*     */   }
/*     */ 
/*     */   public void setMessageGUID(String messageGUID)
/*     */   {
/* 137 */     this.messageGUID = messageGUID;
/*     */   }
/*     */ 
/*     */   public void setMessageStatus(String messageStatus)
/*     */   {
/* 144 */     this.messageStatus = messageStatus;
/*     */   }
/*     */ 
/*     */   public void setMessageCode(String messageCode)
/*     */   {
/* 151 */     this.messageCode = messageCode;
/*     */   }
/*     */ 
/*     */   public void setRuleID(String ruleID)
/*     */   {
/* 158 */     this.ruleID = ruleID;
/*     */   }
/*     */ 
/*     */   public void setMessage(String message)
/*     */   {
/* 165 */     this.message = message;
/*     */   }
/*     */ 
/*     */   public void setDocumentGUID(String documentGUID)
/*     */   {
/* 172 */     this.documentGUID = documentGUID;
/*     */   }
/*     */ 
/*     */   public void setSegmentGUID(String segmentGUID)
/*     */   {
/* 179 */     this.segmentGUID = segmentGUID;
/*     */   }
/*     */ 
/*     */   public void setSuggestedDocuments(List<SuggestedDocument> suggestedDocuments)
/*     */   {
/* 186 */     this.suggestedDocuments = suggestedDocuments;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Message
 * JD-Core Version:    0.6.0
 */