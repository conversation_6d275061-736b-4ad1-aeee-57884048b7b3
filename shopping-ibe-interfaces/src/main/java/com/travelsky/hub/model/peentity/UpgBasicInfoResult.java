/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class UpgBasicInfoResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String isSuccess;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */   private String validStartTime;
/*    */   private String validEndTime;
/*    */   private List<CabinInfo> availableCabins;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 29 */     return this.isSuccess;
/*    */   }
/*    */   public String getErrorMsg() {
/* 32 */     return this.errorMsg;
/*    */   }
/*    */   public String getErrorCode() {
/* 35 */     return this.errorCode;
/*    */   }
/*    */   public String getValidStartTime() {
/* 38 */     return this.validStartTime;
/*    */   }
/*    */   public String getValidEndTime() {
/* 41 */     return this.validEndTime;
/*    */   }
/*    */   public List<CabinInfo> getAvailableCabins() {
/* 44 */     return this.availableCabins;
/*    */   }
/*    */   public void setIsSuccess(String isSuccess) {
/* 47 */     this.isSuccess = isSuccess;
/*    */   }
/*    */   public void setErrorMsg(String errorMsg) {
/* 50 */     this.errorMsg = errorMsg;
/*    */   }
/*    */   public void setErrorCode(String errorCode) {
/* 53 */     this.errorCode = errorCode;
/*    */   }
/*    */   public void setValidStartTime(String validStartTime) {
/* 56 */     this.validStartTime = validStartTime;
/*    */   }
/*    */   public void setValidEndTime(String validEndTime) {
/* 59 */     this.validEndTime = validEndTime;
/*    */   }
/*    */   public void setAvailableCabins(List<CabinInfo> availableCabins) {
/* 62 */     this.availableCabins = availableCabins;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgBasicInfoResult
 * JD-Core Version:    0.6.0
 */