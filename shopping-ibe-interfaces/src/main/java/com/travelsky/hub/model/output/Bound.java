/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Bound
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5053581282063474446L;
/*     */   private String boundType;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String cabinType;
/*     */   private String arrivalAirport;
/*     */   private String boundStatus;
/*     */   private String brdInfo;
/*     */   private String seatInfo;
/*     */   private String edi;
/*     */   private String flightSuffix;
/*     */   private String departureAirport;
/*     */ 
/*     */   public String getBoundType()
/*     */   {
/*  74 */     return this.boundType;
/*     */   }
/*     */ 
/*     */   public void setBoundType(String boundType)
/*     */   {
/*  81 */     this.boundType = boundType;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  88 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  95 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 104 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 111 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 118 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBoundStatus()
/*     */   {
/* 124 */     return this.boundStatus;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 130 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setBoundStatus(String boundStatus)
/*     */   {
/* 136 */     this.boundStatus = boundStatus;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 142 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getBrdInfo()
/*     */   {
/* 148 */     return this.brdInfo;
/*     */   }
/*     */ 
/*     */   public void setBrdInfo(String brdInfo)
/*     */   {
/* 155 */     this.brdInfo = brdInfo;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 161 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getSeatInfo()
/*     */   {
/* 167 */     return this.seatInfo;
/*     */   }
/*     */ 
/*     */   public void setSeatInfo(String seatInfo)
/*     */   {
/* 174 */     this.seatInfo = seatInfo;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 180 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getEdi()
/*     */   {
/* 187 */     return this.edi;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 193 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setEdi(String edi)
/*     */   {
/* 199 */     this.edi = edi;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 206 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 213 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 220 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 227 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Bound
 * JD-Core Version:    0.6.0
 */