/*     */ package com.travelsky.hub.model.output.poolingbaggageallowance;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PoolingTotal
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3630851649660801975L;
/*     */   private String gfbaCode;
/*     */   private String totalPassenger;
/*     */   private String freeBaggage;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String infantFreeBaggage;
/*     */   private String baggageDestination;
/*     */ 
/*     */   public String getGfbaCode()
/*     */   {
/*  50 */     return this.gfbaCode;
/*     */   }
/*     */ 
/*     */   public void setGfbaCode(String gfbaCode)
/*     */   {
/*  57 */     this.gfbaCode = gfbaCode;
/*     */   }
/*     */ 
/*     */   public String getTotalPassenger()
/*     */   {
/*  64 */     return this.totalPassenger;
/*     */   }
/*     */ 
/*     */   public void setTotalPassenger(String totalPassenger)
/*     */   {
/*  71 */     this.totalPassenger = totalPassenger;
/*     */   }
/*     */ 
/*     */   public String getFreeBaggage()
/*     */   {
/*  78 */     return this.freeBaggage;
/*     */   }
/*     */ 
/*     */   public void setFreeBaggage(String freeBaggage)
/*     */   {
/*  85 */     this.freeBaggage = freeBaggage;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/*  92 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/*  99 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 106 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 113 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getInfantFreeBaggage()
/*     */   {
/* 120 */     return this.infantFreeBaggage;
/*     */   }
/*     */ 
/*     */   public void setInfantFreeBaggage(String infantFreeBaggage)
/*     */   {
/* 127 */     this.infantFreeBaggage = infantFreeBaggage;
/*     */   }
/*     */ 
/*     */   public String getBaggageDestination()
/*     */   {
/* 134 */     return this.baggageDestination;
/*     */   }
/*     */ 
/*     */   public void setBaggageDestination(String baggageDestination)
/*     */   {
/* 141 */     this.baggageDestination = baggageDestination;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.poolingbaggageallowance.PoolingTotal
 * JD-Core Version:    0.6.0
 */