/*     */ package com.travelsky.hub.model.peentity.meal.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightSegment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7221055218744654575L;
/*     */   private String departureDate;
/*     */   private String departureTime;
/*     */   private String segmentID;
/*     */   private String departureAirport;
/*     */   private String mcRBD;
/*     */   private String mcFlightSuffix;
/*     */   private String arrivalAirport;
/*     */   private String mcFlightNumber;
/*     */   private String ocFlightNumber;
/*     */   private String mcAirlineID;
/*     */   private String ocAirlineID;
/*     */   private String arrivalDate;
/*     */   private String ocFlightSuffix;
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  45 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  53 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getSegmentID()
/*     */   {
/*  60 */     return this.segmentID;
/*     */   }
/*     */ 
/*     */   public void setSegmentID(String segmentID)
/*     */   {
/*  68 */     this.segmentID = segmentID;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/*  75 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/*  83 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  93 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 101 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getMcRBD()
/*     */   {
/* 109 */     return this.mcRBD;
/*     */   }
/*     */ 
/*     */   public void setMcRBD(String mcRBD)
/*     */   {
/* 117 */     this.mcRBD = mcRBD;
/*     */   }
/*     */ 
/*     */   public String getMcFlightSuffix()
/*     */   {
/* 125 */     return this.mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setMcFlightSuffix(String mcFlightSuffix)
/*     */   {
/* 133 */     this.mcFlightSuffix = mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 140 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 148 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 156 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 164 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 174 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalDate()
/*     */   {
/* 181 */     return this.arrivalDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalDate(String arrivalDate)
/*     */   {
/* 189 */     this.arrivalDate = arrivalDate;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 196 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineID()
/*     */   {
/* 204 */     return this.ocAirlineID;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineID(String ocAirlineID)
/*     */   {
/* 212 */     this.ocAirlineID = ocAirlineID;
/*     */   }
/*     */ 
/*     */   public String getMcAirlineID()
/*     */   {
/* 219 */     return this.mcAirlineID;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineID(String mcAirlineID)
/*     */   {
/* 227 */     this.mcAirlineID = mcAirlineID;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 234 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 242 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.FlightSegment
 * JD-Core Version:    0.6.0
 */