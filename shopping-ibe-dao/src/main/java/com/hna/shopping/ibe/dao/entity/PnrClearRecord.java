package com.hna.shopping.ibe.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@TableName("ibe_pnr_clear_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class PnrClearRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("pnr_no")
    private String pnrNo;

    @TableField("flight_date")
    private String flightDate;

    @TableField("dep_code")
    private String depCode;

    @TableField("arr_code")
    private String arrCode;

    @TableField("flight_no")
    private String flightNo;

    @TableField("passenger_name")
    private String passengerName;

    @TableField("status")
    private int status;

    @TableField("create_time")
    private Date createTime;

    @TableField("clear_pnr_time")
    private Date clearPnrTime;

    /**
     * 业务渠道：
     * 1: 国际改期取消订单清位
     * 2: 国际退票清位
     * 3：海外债退票清位
     * 4：特殊退票清位
     */
    @TableField("channel")
    private Integer channel;
}
