/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FcTax
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2682768217998068322L;
/*    */   private String amount;
/*    */   private String taxCode;
/*    */ 
/*    */   public String getAmount()
/*    */   {
/* 30 */     return this.amount;
/*    */   }
/*    */ 
/*    */   public void setAmount(String amount)
/*    */   {
/* 37 */     this.amount = amount;
/*    */   }
/*    */ 
/*    */   public String getTaxCode()
/*    */   {
/* 44 */     return this.taxCode;
/*    */   }
/*    */ 
/*    */   public void setTaxCode(String taxCode)
/*    */   {
/* 51 */     this.taxCode = taxCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FcTax
 * JD-Core Version:    0.6.0
 */