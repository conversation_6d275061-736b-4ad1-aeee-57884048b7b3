package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:06
 * @Version 1.0
 */
@Data
public class RetrieveRS extends BaseResponse {
    @ApiModelProperty("多票号/证件 查询 组装的key certificateType+certifycateNumber")
    private String key;
    @ApiModelProperty("票号")
    private String tktNumber;
    @ApiModelProperty("乘机人姓名")
    private String pName;
    @ApiModelProperty("城市是否可以值机")
    private boolean cityIsCheckIn;
    @ApiModelProperty("行程序号")
    private String tourIndex;
    @ApiModelProperty("出发城市三字码")
    private String fromCity;
    @ApiModelProperty("到达城市三字码")
    private String toCity;
    @ApiModelProperty("PNR编号")
    private String pnr;
    @ApiModelProperty("航空公司代码")
    private String airlineCode;
    @ApiModelProperty("航班号")
    private String flightNumber;
    @ApiModelProperty("出发日期")
    private String tourDate;
    @ApiModelProperty("出发时间")
    private String tourTime;
    @ApiModelProperty("舱位")
    private String tourClass;
    @ApiModelProperty("票面状态信息 OPEN FOR USE：可用 CHECKED IN：已值机 USED/FLOWN：已飞 LIFT/BOARDED：已起飞/降落 VOID：作废")
    private String status;
    @ApiModelProperty("承运公司代码")
    private String carrAirlineCode;
    @ApiModelProperty("始发航站国际属性 I：国际航站 D：国内航站")
    private String fromCityStatus;
    @ApiModelProperty("到达航站国际属性 I：国际航站 D：国内航站")
    private String toCityStatus;
    @ApiModelProperty("true：北美航线 false：非北美航线")
    private boolean northAmerica;
    @ApiModelProperty("旅客类型 ADT：成人 CHD：儿童")
    private String pType;
}
