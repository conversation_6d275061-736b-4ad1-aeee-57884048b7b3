/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class CheckedDocBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private Integer docType;
/*     */   private String docFormat;
/*     */   private String docIssueCountry;
/*     */   private String docID;
/*     */   private String docHolderNationality;
/*     */   private String expireDate;
/*     */ 
/*     */   public Integer getDocType()
/*     */   {
/*  44 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(Integer docType)
/*     */   {
/*  51 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocFormat()
/*     */   {
/*  58 */     return this.docFormat;
/*     */   }
/*     */ 
/*     */   public void setDocFormat(String docFormat)
/*     */   {
/*  65 */     this.docFormat = docFormat;
/*     */   }
/*     */ 
/*     */   public String getDocIssueCountry()
/*     */   {
/*  72 */     return this.docIssueCountry;
/*     */   }
/*     */ 
/*     */   public void setDocIssueCountry(String docIssueCountry)
/*     */   {
/*  79 */     this.docIssueCountry = docIssueCountry;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/*  86 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/*  93 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getDocHolderNationality()
/*     */   {
/* 100 */     return this.docHolderNationality;
/*     */   }
/*     */ 
/*     */   public void setDocHolderNationality(String docHolderNationality)
/*     */   {
/* 107 */     this.docHolderNationality = docHolderNationality;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/* 114 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/* 121 */     this.expireDate = expireDate;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckedDocBean
 * JD-Core Version:    0.6.0
 */