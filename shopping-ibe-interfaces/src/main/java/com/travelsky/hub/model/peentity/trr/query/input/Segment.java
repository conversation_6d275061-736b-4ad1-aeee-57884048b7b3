/*     */ package com.travelsky.hub.model.peentity.trr.query.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Segment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1216631645873717400L;
/*     */   private String mcAirlineCode;
/*     */   private String ocAirlineCode;
/*     */   private String mcFlightNumber;
/*     */   private String ocFlightNumber;
/*     */   private String cabin;
/*     */   private String arrivalAirport;
/*     */   private String departureAirport;
/*     */   private String departureDateTime;
/*     */   private String arrivalDateTime;
/*     */   private String isChanged;
/*     */   private String isInbound;
/*     */   private String queryDeptDate;
/*     */ 
/*     */   public String getMcAirlineCode()
/*     */   {
/*  78 */     return this.mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineCode(String mcAirlineCode)
/*     */   {
/*  85 */     this.mcAirlineCode = mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/*  92 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/*  99 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 106 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 113 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 120 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 127 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 134 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 141 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 148 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 155 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 162 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 169 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDateTime()
/*     */   {
/* 176 */     return this.departureDateTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureDateTime(String departureDateTime)
/*     */   {
/* 183 */     this.departureDateTime = departureDateTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalDateTime()
/*     */   {
/* 190 */     return this.arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalDateTime(String arrivalDateTime)
/*     */   {
/* 197 */     this.arrivalDateTime = arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public String getIsChanged()
/*     */   {
/* 204 */     return this.isChanged;
/*     */   }
/*     */ 
/*     */   public void setIsChanged(String isChanged)
/*     */   {
/* 211 */     this.isChanged = isChanged;
/*     */   }
/*     */ 
/*     */   public String getIsInbound()
/*     */   {
/* 218 */     return this.isInbound;
/*     */   }
/*     */ 
/*     */   public void setIsInbound(String isInbound)
/*     */   {
/* 225 */     this.isInbound = isInbound;
/*     */   }
/*     */ 
/*     */   public String getQueryDeptDate()
/*     */   {
/* 232 */     return this.queryDeptDate;
/*     */   }
/*     */ 
/*     */   public void setQueryDeptDate(String queryDeptDate)
/*     */   {
/* 239 */     this.queryDeptDate = queryDeptDate;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.input.Segment
 * JD-Core Version:    0.6.0
 */