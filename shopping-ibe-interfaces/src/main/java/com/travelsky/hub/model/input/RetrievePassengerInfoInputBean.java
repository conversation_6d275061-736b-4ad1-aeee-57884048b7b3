/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class RetrievePassengerInfoInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4249669264851404689L;
/*     */   private String airlineCode;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String queryParameterName;
/*     */   private String queryParameterValue;
/*     */ 
/*     */   public String getQueryParameterName()
/*     */   {
/*  46 */     return this.queryParameterName;
/*     */   }
/*     */ 
/*     */   public void setQueryParameterName(String queryParameterName) {
/*  50 */     this.queryParameterName = queryParameterName;
/*     */   }
/*     */ 
/*     */   public String getQueryParameterValue() {
/*  54 */     return this.queryParameterValue;
/*     */   }
/*     */ 
/*     */   public void setQueryParameterValue(String queryParameterValue) {
/*  58 */     this.queryParameterValue = queryParameterValue;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  66 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  74 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  82 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  90 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  98 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 106 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.RetrievePassengerInfoInputBean
 * JD-Core Version:    0.6.0
 */