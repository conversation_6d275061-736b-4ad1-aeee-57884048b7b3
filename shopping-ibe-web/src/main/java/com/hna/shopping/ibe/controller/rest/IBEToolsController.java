package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.dto.ibetools.*;
import com.hna.shopping.ibe.manager.IBEToolsManager;
import com.travelsky.ibe.client.pnr.DETRHistoryResult;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import com.travelsky.ibe.client.pnr.RTResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping("/api/ibetool")
@Slf4j
public class IBEToolsController {

    @Autowired
    private IBEToolsManager ibeToolsManager;


    /**
     * 航信免流量费--预定编码、出票
     * @param req
     * @return
     */
    @RequestMapping(value = "issueForTravelSky", method = RequestMethod.POST)
    RestResponse<IssueForTravelSkyRes> issueForTravelSky(@RequestBody IssueForTravelSkyReq req) {
        IssueForTravelSkyRes res = ibeToolsManager.issueForTravelSky(req);
        return RestResponse.ok(res);
    }

    /**
     * 航信免流量费--RT获取结果
     * @param req
     * @return
     */
    @RequestMapping(value = "rt", method = RequestMethod.POST)
    RestResponse<String> rt(@RequestBody RtReq req) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start rt uuid: {} , rt: {}", uuid, JSON.toJSONString(req));
        RTResult rtRes = ibeToolsManager.rt(req);
        return RestResponse.ok(rtRes.getOringinalRT());
    }


    /**
     * 航信免流量费--退票、清位
     * @param req
     * @return
     */
    @RequestMapping(value = "refundForTravelSky", method = RequestMethod.POST)
    RestResponse<Boolean> refundForTravelSky(@RequestBody RefundForTravelSkyReq req) {
        Boolean res = ibeToolsManager.refundForTravelSky(req);
        return RestResponse.ok(res);
    }

    /**
     * IBE小工具--RT历史获取结果
     * @param req
     * @return
     */
    @RequestMapping(value = "rtHistory", method = RequestMethod.POST)
    RestResponse<String> rtHistory(@RequestBody RtReq req) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start rtHistory uuid: {} , rt: {}", uuid, JSON.toJSONString(req));
        RTResult rtRes = ibeToolsManager.rtHistory(req);
        return RestResponse.ok(rtRes.getOringinalRT());
    }

    /**
     * IBE小工具--DETR 获取结果
     * @param req
     * @return
     */
    @RequestMapping(value = "detr", method = RequestMethod.POST)
    RestResponse<String> detr(@RequestBody DETRReq req) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start detr uuid: {} , rt: {}", uuid, JSON.toJSONString(req));
        DETRTKTResult detrRes = ibeToolsManager.detr(req);
        return RestResponse.ok(detrRes.getRemark().trim());
    }

    /**
     * IBE小工具--DETR 历史获取结果
     * @param req
     * @return
     */
    @RequestMapping(value = "detrHistory", method = RequestMethod.POST)
    RestResponse<String> detrHistory(@RequestBody DETRReq req) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start detrHistory uuid: {} , rt: {}", uuid, JSON.toJSONString(req));
        DETRHistoryResult detrHistoryRes = ibeToolsManager.detrHistory(req);
        return RestResponse.ok(detrHistoryRes.getRemark());
    }


}
