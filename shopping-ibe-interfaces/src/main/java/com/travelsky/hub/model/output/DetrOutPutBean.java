/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class DetrOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5963010048982520439L;
/* 18 */   private List tickets = new ArrayList();
/*    */ 
/*    */   public List getTickets()
/*    */   {
/* 25 */     return this.tickets;
/*    */   }
/*    */ 
/*    */   public void setTickets(List tickets)
/*    */   {
/* 33 */     this.tickets = tickets;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DetrOutPutBean
 * JD-Core Version:    0.6.0
 */