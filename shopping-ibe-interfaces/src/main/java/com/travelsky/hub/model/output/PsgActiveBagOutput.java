/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PsgActiveBagOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2141758119292232011L;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String depCity;
/*     */   private String arrCity;
/*     */   private String etNo;
/*     */   private String cabin;
/*     */   private String psrName;
/*     */   private String psrCkiStatus;
/*     */   private String seatNo;
/*     */   private String boardingNumber;
/*     */   private String hostNumber;
/*     */   private String activeBagQuantity;
/*     */   private String activeBagWeight;
/*     */   private String activeBagWeightUnit;
/*     */   private List<BagTag> bagTags;
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  97 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 105 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 113 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 120 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 128 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getPsrCkiStatus()
/*     */   {
/* 135 */     return this.psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getDepCity()
/*     */   {
/* 142 */     return this.depCity;
/*     */   }
/*     */ 
/*     */   public void setDepCity(String depCity)
/*     */   {
/* 150 */     this.depCity = depCity;
/*     */   }
/*     */ 
/*     */   public void setArrCity(String arrCity)
/*     */   {
/* 158 */     this.arrCity = arrCity;
/*     */   }
/*     */ 
/*     */   public String getEtNo()
/*     */   {
/* 166 */     return this.etNo;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 173 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setEtNo(String etNo)
/*     */   {
/* 181 */     this.etNo = etNo;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 189 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public String getArrCity()
/*     */   {
/* 196 */     return this.arrCity;
/*     */   }
/*     */ 
/*     */   public String getActiveBagQuantity()
/*     */   {
/* 203 */     return this.activeBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setActiveBagQuantity(String activeBagQuantity)
/*     */   {
/* 211 */     this.activeBagQuantity = activeBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 218 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 226 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 234 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrCkiStatus(String psrCkiStatus)
/*     */   {
/* 244 */     this.psrCkiStatus = psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 252 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 260 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 268 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 276 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getActiveBagWeightUnit()
/*     */   {
/* 284 */     return this.activeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public void setActiveBagWeightUnit(String activeBagWeightUnit)
/*     */   {
/* 292 */     this.activeBagWeightUnit = activeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 299 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 307 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getActiveBagWeight()
/*     */   {
/* 315 */     return this.activeBagWeight;
/*     */   }
/*     */ 
/*     */   public void setActiveBagWeight(String activeBagWeight)
/*     */   {
/* 323 */     this.activeBagWeight = activeBagWeight;
/*     */   }
/*     */ 
/*     */   public List<BagTag> getBagTags()
/*     */   {
/* 331 */     return this.bagTags;
/*     */   }
/*     */ 
/*     */   public void setBagTags(List<BagTag> bagTags)
/*     */   {
/* 339 */     this.bagTags = bagTags;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PsgActiveBagOutput
 * JD-Core Version:    0.6.0
 */