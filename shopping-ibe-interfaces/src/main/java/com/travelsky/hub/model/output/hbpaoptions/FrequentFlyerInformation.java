/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FrequentFlyerInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6214722420329894175L;
/*    */   private String frequentFlyerType;
/*    */   private String frequentFlyerAirline;
/*    */   private String frequentFlyerCard;
/*    */   private String frequentFlyerLevel;
/*    */   private String frequentUnionLevel;
/*    */ 
/*    */   public String getFrequentFlyerType()
/*    */   {
/* 37 */     return this.frequentFlyerType;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerAirline(String frequentFlyerAirline)
/*    */   {
/* 46 */     this.frequentFlyerAirline = frequentFlyerAirline;
/*    */   }
/*    */ 
/*    */   public String getFrequentFlyerCard()
/*    */   {
/* 53 */     return this.frequentFlyerCard;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerType(String frequentFlyerType)
/*    */   {
/* 59 */     this.frequentFlyerType = frequentFlyerType;
/*    */   }
/*    */ 
/*    */   public String getFrequentFlyerAirline()
/*    */   {
/* 66 */     return this.frequentFlyerAirline;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerLevel(String frequentFlyerLevel)
/*    */   {
/* 73 */     this.frequentFlyerLevel = frequentFlyerLevel;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerCard(String frequentFlyerCard)
/*    */   {
/* 79 */     this.frequentFlyerCard = frequentFlyerCard;
/*    */   }
/*    */ 
/*    */   public String getFrequentFlyerLevel()
/*    */   {
/* 86 */     return this.frequentFlyerLevel;
/*    */   }
/*    */ 
/*    */   public void setFrequentUnionLevel(String frequentUnionLevel)
/*    */   {
/* 93 */     this.frequentUnionLevel = frequentUnionLevel;
/*    */   }
/*    */ 
/*    */   public String getFrequentUnionLevel()
/*    */   {
/* 99 */     return this.frequentUnionLevel;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.FrequentFlyerInformation
 * JD-Core Version:    0.6.0
 */