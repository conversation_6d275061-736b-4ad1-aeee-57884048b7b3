package com.travelsky.hub.wdoe.util;

public final class AdcConstr
{
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String ADCINFO_ELEMENT = "AdcInfo";
  public static final String ADC_AIRLINECODE = "AirlineCode";
  public static final String ADC_FLIGHTNUMBER = "FlightNumber";
  public static final String ADC_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String ADC_DEPARTUREDATE = "DepartureDate";
  public static final String ADC_HOSTNUMBER = "HostNumber";
  public static final String ADC_TICKETID = "TicketID";
  public static final String ADC_CABIN = "Cabin";
  public static final String ADC_ADCRESULT = "AdcResult";
  public static final String ADC_PASSENGERINFOS = "PassengerInfos";
  public static final String ADC_PASSENGERINFO = "PassengerInfo";
  public static final String ADC_URP = "URP";
  public static final String ADC_TRAVELSEGMENTS = "TravelSegments";
  public static final String ADC_TRAVELSEGMENT = "TravelSegment";
  public static final String ADC_CARRIERSEGMENTID = "CarrierSegmentID";
  public static final String ADC_DOCCHECKDECISION = "DocCheckDecision";
  public static final String ADC_ADCERROR = "AdcError";
  public static final String ADC_ERRORCODE = "ErrorCode";
  public static final String ADC_QUALIFIER = "Qualifier";
  public static final String ADC_ERROEDESCRIPTION = "ErrorDescription";
  public static final String ADC_DECISIONCODE = "DecisionCode";
  public static final String ADC_DECISIONMESSAGES = "DecisionMessages";
  public static final String ADC_CONDITIONS = "Conditions";
  public static final String ADC_WARNINGS = "Warnings";
  public static final String ADC_DECISIONMESSAGE = "DecisionMessage";
  public static final String ADC_CONDITION = "Condition";
  public static final String ADC_WARNING = "Warning";
  public static final String ADC_RULETEXT = "RuleText";
  public static final String ADC_REFERENCECODE = "ReferenceCode";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.AdcConstr
 * JD-Core Version:    0.6.0
 */