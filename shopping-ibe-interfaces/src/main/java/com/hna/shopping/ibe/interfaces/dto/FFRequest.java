package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class FFRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1089678918768414356L;
	@DateTimeFormat( pattern = "yyyy-MM-dd" )
	private Date departDate;
	private String flightNo;
	
}
