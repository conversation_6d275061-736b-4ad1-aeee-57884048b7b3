/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class MarketingCarrieFlightInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4903748012069466154L;
/*    */   private String airline;
/*    */   private String flightSuffix;
/*    */   private String flightNumber;
/*    */   private String subClass;
/*    */ 
/*    */   public String getAirline()
/*    */   {
/* 41 */     return this.airline;
/*    */   }
/*    */ 
/*    */   public void setAirline(String airline)
/*    */   {
/* 48 */     this.airline = airline;
/*    */   }
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 55 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 62 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ 
/*    */   public String getFlightSuffix()
/*    */   {
/* 69 */     return this.flightSuffix;
/*    */   }
/*    */ 
/*    */   public void setFlightSuffix(String flightSuffix)
/*    */   {
/* 76 */     this.flightSuffix = flightSuffix;
/*    */   }
/*    */ 
/*    */   public String getSubClass()
/*    */   {
/* 83 */     return this.subClass;
/*    */   }
/*    */ 
/*    */   public void setSubClass(String subClass)
/*    */   {
/* 90 */     this.subClass = subClass;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.MarketingCarrieFlightInformation
 * JD-Core Version:    0.6.0
 */