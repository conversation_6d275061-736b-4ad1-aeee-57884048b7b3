/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PUOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2789768010623094536L;
/*  22 */   private String bordingTime = "";
/*     */ 
/*  27 */   private String departureTime = "";
/*     */ 
/*  33 */   private String arrivalTime = "";
/*     */ 
/*  39 */   private String boardingGateNumber = "";
/*     */ 
/*  44 */   private List<PAAcceptedPsrBean> Passengers = new ArrayList();
/*     */ 
/*     */   public List<PAAcceptedPsrBean> getPassengers()
/*     */   {
/*  51 */     return this.Passengers;
/*     */   }
/*     */ 
/*     */   public void setPassengers(List<PAAcceptedPsrBean> passengers)
/*     */   {
/*  58 */     this.Passengers = passengers;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/*  66 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String i)
/*     */   {
/*  73 */     this.arrivalTime = i;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/*  80 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String i)
/*     */   {
/*  87 */     this.departureTime = i;
/*     */   }
/*     */ 
/*     */   public PAAcceptedPsrBean getPassenger(int i)
/*     */   {
/*  95 */     return (PAAcceptedPsrBean)this.Passengers.get(i);
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 102 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String i)
/*     */   {
/* 109 */     this.bordingTime = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 116 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String i)
/*     */   {
/* 123 */     this.boardingGateNumber = i;
/*     */   }
/*     */ 
/*     */   public void addPassenger(PAAcceptedPsrBean i)
/*     */   {
/* 130 */     this.Passengers.add(i);
/*     */   }
/*     */ 
/*     */   public int getPassengersSize()
/*     */   {
/* 137 */     return this.Passengers.size();
/*     */   }
/*     */ 
/*     */   public void clearPassengers()
/*     */   {
/* 143 */     this.Passengers.clear();
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 149 */     clearPassengers();
/* 150 */     this.bordingTime = "";
/* 151 */     this.boardingGateNumber = "";
/* 152 */     this.departureTime = "";
/* 153 */     this.arrivalTime = "";
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PUOutputBean
 * JD-Core Version:    0.6.0
 */