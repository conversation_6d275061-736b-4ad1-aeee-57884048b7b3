/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.memcached;


import java.lang.reflect.Field;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.Map;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;
import net.rubyeye.xmemcached.auth.AuthInfo;
import net.rubyeye.xmemcached.utils.AddrUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.CacheResolver;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import com.google.code.ssm.Cache;
import com.google.code.ssm.CacheFactory;
import com.google.code.ssm.config.DefaultAddressProvider;
import com.google.code.ssm.providers.xmemcached.MemcacheClientFactoryImpl;
import com.google.code.ssm.providers.xmemcached.XMemcachedConfiguration;
import com.google.code.ssm.spring.ExtendedSSMCacheManager;
import com.google.code.ssm.spring.SSMCache;
import com.google.common.collect.Lists;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.transcoders.SerializingTranscoder;

/**
 * memcached 配置
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
@EnableCaching
@EnableAspectJAutoProxy
public class MemcachedConfig extends CachingConfigurerSupport {
    @Autowired
    private MemcachedConfigInfo memcachedConfigInfo;
//
//    @Override
//    @Bean
//    public CacheManager cacheManager() {
//        ExtendedSSMCacheManager cacheManager = new ExtendedSSMCacheManager();
//        try {
//            List<SSMCache> caches = Lists.newArrayList();
//
//            for (String cacheName : getAllCacheNames()) {
//                Validate.notNull(CacheName.CACHE_NAME2_EXPIRATION_SECONDS.get(cacheName),
//                        cacheName + " Must set expiration within com.eking.config.memcached.CacheName.");
//
//                SSMCache cache = new SSMCache(defaultCache(Joiner.on(',').join(memcachedConfigInfo.serverHost), cacheName),
//                        CacheName.CACHE_NAME2_EXPIRATION_SECONDS.get(cacheName), false);
//                caches.add(cache);
//            }
//            cacheManager.setCaches(caches);
//        } catch (Exception e) {
//            log.info("cacheManager error: {}", e);
//        }
//        return cacheManager;
//    }
//
//    /**
//     * 获取所有的 cacheName, 即在 CacheName 里定义的 public static String 变量
//     *
//     * @return
//     */
//    private List<String> getAllCacheNames() throws IllegalAccessException {
//        List<String> cacheNames = Lists.newArrayList();
//
//        Field[] declaredFields = CacheName.class.getDeclaredFields();
//        for (Field field : declaredFields) {
//            // 只取 static 的 String 变量
//            if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) &&
//                    String.class.isAssignableFrom(field.getType())) {
//                field.setAccessible(true);
//                if (field.isAccessible()) {
//                    cacheNames.add((String) field.get(null));
//                }
//            }
//        }
//
//        return cacheNames;
//    }
//
//    @Override
//    @Bean
//    public KeyGenerator keyGenerator() {
//        return new CustomerKeyGenerator(memcachedConfigInfo);
//        //        return new SimpleKeyGenerator();
//    }
//
//    @Override
//    public CacheResolver cacheResolver() {
//        return null;
//    }
//
//    @Override
//    public CacheErrorHandler errorHandler() {
//        return null;
//    }
//
//    /**
//     * 默认 cache 配置
//     *
//     * @param cacheName
//     * @return
//     * @throws Exception
//     */
//    private Cache defaultCache(String serverHost, String cacheName) throws Exception {
//        CacheFactory cacheFactory = new CacheFactory();
//        cacheFactory.setCacheName(cacheName);
//        cacheFactory.setCacheClientFactory(new MemcacheClientFactoryImpl());
//
//        cacheFactory.setAddressProvider(new DefaultAddressProvider(Joiner.on(',').join(memcachedConfigInfo.serverHost)));
//        cacheFactory.setConfiguration(cacheConfiguration());
//        return cacheFactory.getObject();
//    }
//
//    /**
//     * xmemcached 配置
//     */
//    @Bean
//    public XMemcachedConfiguration cacheConfiguration() {
//        XMemcachedConfiguration configuration = new XMemcachedConfiguration();
//
//        // 默认使用一致性 hash
//        configuration.setConsistentHashing(true);
//        configuration.setUseBinaryProtocol(true);
//        // 单位应该是 ms
//        configuration.setOperationTimeout(6000);
//        configuration.setKeyPrefixSeparator(".");
//        // 在登录的情况下, 自动用这个来区分不同系统的 prefix; 现在 memcached 不需要登录, 所以只能自己实现 keyGenerator 了
//        configuration.setUseNameAsKeyPrefix(true);
//        // 单位应该是 ms
//        configuration.setConnectionTimeout(6000L);
//        configuration.setDefaultTranscoder(new SerializingTranscoder());
//
//        Map<InetSocketAddress, AuthInfo> authInfoMap = Maps.newHashMap();
//
//        for (int i = 0; i < memcachedConfigInfo.serverHost.length; i++) {
//            if(!StringUtils.isEmpty(memcachedConfigInfo.userName[i])) {
//                authInfoMap.put(AddrUtil.getOneAddress(memcachedConfigInfo.serverHost[i]),
//                        AuthInfo.plain(memcachedConfigInfo.userName[i], memcachedConfigInfo.password[i]));
//            }
//        }
//        if(!authInfoMap.isEmpty()) {
//            configuration.setAuthInfoMap(authInfoMap);
//        }
//        return configuration;
//    }

    @Configuration
//    @ConfigurationProperties(prefix = "memcached")
    @Data
    public class MemcachedConfigInfo {
        String[] serverHost;
        String[] userName;
        String[] password;
        String prefix;
    }
}
