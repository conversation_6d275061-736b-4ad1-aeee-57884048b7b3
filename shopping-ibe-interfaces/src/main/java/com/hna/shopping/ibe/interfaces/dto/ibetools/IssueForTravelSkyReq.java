package com.hna.shopping.ibe.interfaces.dto.ibetools;

import lombok.Data;

import java.io.Serializable;

/**
 * 航信免流量费--出票
 */
@Data
public class IssueForTravelSkyReq implements Serializable {

    /**
     * 出发地三字码
     */
    private String depCode;

    /**
     * 目的地三字码
     */
    private String arrCode;

    /**
     * 舱位
     */
    private String cabin;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期 2025-01-20
     */
    private String flightDate;

    /**
     * IBE office号。xiy255、xiy258
     */
    private String ibeOfficeNo;

    /**
     * 旅客姓名
     */
    private String passengerName;

    /**
     * 证件号
     */
    private String certNo;

    /**
     * 联系人电话号码
     */
    private String contactNo;


}
