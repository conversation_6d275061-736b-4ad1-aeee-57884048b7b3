/*     */ package com.travelsky.hub.model.peentity.meal.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class BookingMealRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2004131761529050991L;
/*     */   private OfficeInfo officeInfo;
/*     */   private String passengerName;
/*     */   private String passengerType;
/*     */   private String certificationType;
/*     */   private String certificationNo;
/*     */   private String pnrNo;
/*     */   private List<FlightSegment> flightSegments;
/*     */   private List<ServiceItem> serviceItems;
/*     */ 
/*     */   public OfficeInfo getOfficeInfo()
/*     */   {
/*  36 */     return this.officeInfo;
/*     */   }
/*     */ 
/*     */   public void setOfficeInfo(OfficeInfo officeInfo)
/*     */   {
/*  44 */     this.officeInfo = officeInfo;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/*  52 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/*  60 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getPassengerType()
/*     */   {
/*  68 */     return this.passengerType;
/*     */   }
/*     */ 
/*     */   public void setPassengerType(String passengerType)
/*     */   {
/*  76 */     this.passengerType = passengerType;
/*     */   }
/*     */ 
/*     */   public String getCertificationType()
/*     */   {
/*  84 */     return this.certificationType;
/*     */   }
/*     */ 
/*     */   public void setCertificationType(String certificationType)
/*     */   {
/*  92 */     this.certificationType = certificationType;
/*     */   }
/*     */ 
/*     */   public String getCertificationNo()
/*     */   {
/* 100 */     return this.certificationNo;
/*     */   }
/*     */ 
/*     */   public void setCertificationNo(String certificationNo)
/*     */   {
/* 108 */     this.certificationNo = certificationNo;
/*     */   }
/*     */ 
/*     */   public String getPnrNo()
/*     */   {
/* 116 */     return this.pnrNo;
/*     */   }
/*     */ 
/*     */   public void setPnrNo(String pnrNo)
/*     */   {
/* 124 */     this.pnrNo = pnrNo;
/*     */   }
/*     */ 
/*     */   public List<FlightSegment> getFlightSegments()
/*     */   {
/* 132 */     return this.flightSegments;
/*     */   }
/*     */ 
/*     */   public void setFlightSegments(List<FlightSegment> flightSegments)
/*     */   {
/* 140 */     this.flightSegments = flightSegments;
/*     */   }
/*     */ 
/*     */   public List<ServiceItem> getServiceItems()
/*     */   {
/* 148 */     return this.serviceItems;
/*     */   }
/*     */ 
/*     */   public void setServiceItems(List<ServiceItem> serviceItems)
/*     */   {
/* 156 */     this.serviceItems = serviceItems;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.BookingMealRQ
 * JD-Core Version:    0.6.0
 */