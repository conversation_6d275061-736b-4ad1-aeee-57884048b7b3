/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.Iterator;
/*     */ import java.util.List;
/*     */ 
/*     */ public class MultiPsrInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private PsrInputBean psrInput;
/*     */   private String hostNumber;
/*     */   private List<Partner> partners;
/*     */ 
/*     */   public PsrInputBean getPsrInput()
/*     */   {
/*  34 */     return this.psrInput;
/*     */   }
/*     */ 
/*     */   public List<Partner> getPartners()
/*     */   {
/*  42 */     return this.partners;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  49 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public void setPsrInput(PsrInputBean psrInput)
/*     */   {
/*  56 */     this.psrInput = psrInput;
/*     */   }
/*     */ 
/*     */   public void setPartners(List<Partner> partners)
/*     */   {
/*  63 */     this.partners = partners;
/*     */   }
/*     */ 
/*     */   public boolean validatePartnerInfo()
/*     */   {
/*  72 */     if ((this.partners == null) || (this.partners.isEmpty())) {
/*  73 */       return false;
/*     */     }
/*  75 */     Iterator iter = this.partners.iterator();
/*  76 */     while (iter.hasNext()) {
/*  77 */       Partner info = (Partner)iter.next();
/*  78 */       if ((info.getIndex() == null) || (info.getIndex().trim().equals(""))) {
/*  79 */         return false;
/*     */       }
/*  81 */       if ((info.getHostNumber() == null) || 
/*  82 */         (info
/*  82 */         .getHostNumber().trim().equals(""))) {
/*  83 */         return false;
/*     */       }
/*  85 */       if ((info.getTicketID() == null) || 
/*  86 */         (info
/*  86 */         .getTicketID().trim().equals(""))) {
/*  87 */         return false;
/*     */       }
/*  89 */       if ((info.getSequenceNumber() == null) || 
/*  90 */         (info
/*  90 */         .getSequenceNumber().trim().equals(""))) {
/*  91 */         return false;
/*     */       }
/*     */     }
/*  94 */     return true;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 103 */     return this.hostNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.MultiPsrInputBean
 * JD-Core Version:    0.6.0
 */