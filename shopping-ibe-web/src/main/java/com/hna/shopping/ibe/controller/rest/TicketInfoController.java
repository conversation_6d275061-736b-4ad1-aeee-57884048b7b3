/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.dto.DtoAllChanTicketInfoResponse;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPassengerNoRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoPnrRequest;
import com.hna.shopping.ibe.interfaces.dto.ticketinfo.DtoTicketInfoTicketNoRequest;
import com.hna.shopping.ibe.manager.TicketInfoManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.UUID;

/**
 * created by longx.yang 2019/12/17
 */

@Api("全渠道客票操作接口")
@RestController
@RequestMapping("/api/allchanticket")
@Slf4j
public class TicketInfoController {

    @Autowired
    private TicketInfoManager ticketInfoManager;

    /**
     * 错误会直接当异常抛出，不需要code码了
     * @param request
     * @return
     */
    @ApiOperation(value = "根据pnr查询客票信息")
    @RequestMapping(value = "getTicketInfoByPnr", method = RequestMethod.POST)
    RestResponse<DtoAllChanTicketInfoResponse> getTicketInfoByPnr(@ApiParam(value = "ticketInfo参数对象", required = true)
                                                      @RequestBody @Valid DtoTicketInfoPnrRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , detr: {}", uuid, JSON.toJSONString(request));
        DtoAllChanTicketInfoResponse avResponse = ticketInfoManager.getAllChanTicketInfoByPnr(request);
        avResponse.setSuccess(true);
        log.info(" End uuid: {} , detr: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "根据票号提取客票信息")
    @RequestMapping(value = "getTicketInfoByTicketNo", method = RequestMethod.POST)
    RestResponse<DtoAllChanTicketInfoResponse> getTicketInfoByTicketNo(@ApiParam(value = "ticketInfo参数对象", required = true)
                                                           @RequestBody @Valid DtoTicketInfoTicketNoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , detr: {}", uuid, JSON.toJSONString(request));
        DtoAllChanTicketInfoResponse avResponse = ticketInfoManager.getAllChanTicketInfoByTicketNo(request);
        avResponse.setSuccess(true);
        log.info(" End uuid: {} , detr: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "根据证件号和证件类型提取客票信息")
    @RequestMapping(value = "getTicketInfoByPassengerNo", method = RequestMethod.POST)
    RestResponse<DtoAllChanTicketInfoResponse> getTicketInfoByPassengerNo(@ApiParam(value = "ticketInfo参数对象", required = true)
                                                                @RequestBody @Valid DtoTicketInfoPassengerNoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , detr: {}", uuid, JSON.toJSONString(request));
        DtoAllChanTicketInfoResponse avResponse = ticketInfoManager.getAllChanTicketInfoByPassengerNo(request);
        avResponse.setSuccess(true);
        log.info(" End uuid: {} , detr: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }


}