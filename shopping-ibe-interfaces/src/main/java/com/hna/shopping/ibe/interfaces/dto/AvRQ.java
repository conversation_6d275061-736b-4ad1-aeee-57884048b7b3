package com.hna.shopping.ibe.interfaces.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AvRQ {
	private static final Long serialVersionUID = 1L;
	private String supplier;
	private String airline;
	private String orgCity;
	private String dstCity;
	@JsonFormat(pattern="yyyyMMdd",timezone = "GMT+8")
	private Date depDate;
	private boolean isDirect;
	private String sellerChannels;
	private String allAirline;
	
}
