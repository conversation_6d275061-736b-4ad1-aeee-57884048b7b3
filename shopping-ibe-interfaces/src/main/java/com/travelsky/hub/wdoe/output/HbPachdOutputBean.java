/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class HbPachdOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6819501278491392746L;
/*    */   private List<ChdCheckinPsgInfo> chdCheckinPsgList;
/* 20 */   private String checkCode = "";
/*    */ 
/*    */   public List<ChdCheckinPsgInfo> getChdCheckinPsgList()
/*    */   {
/* 27 */     return this.chdCheckinPsgList;
/*    */   }
/*    */ 
/*    */   public void setChdCheckinPsgList(List<ChdCheckinPsgInfo> chdCheckinPsgList)
/*    */   {
/* 34 */     this.chdCheckinPsgList = chdCheckinPsgList;
/*    */   }
/*    */ 
/*    */   public String getCheckCode()
/*    */   {
/* 41 */     return this.checkCode;
/*    */   }
/*    */ 
/*    */   public void setCheckCode(String checkCode)
/*    */   {
/* 48 */     this.checkCode = checkCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.HbPachdOutputBean
 * JD-Core Version:    0.6.0
 */