server.port=8086

# 引入公共配置
spring.profiles.include=common

# spring-boot-starter-actuator 生产环境访问需要鉴权
management.security.enabled=true

# 每个系统都应该不一样
memcached.prefix=jdtest


memcached.serverHost[0]=*************:11211
memcached.userName[0]=memcached
memcached.password[0]=ENC(yawm/celfy5VE3Qdx+dDaroo5NzNptdG)

# logback 配置
logging.path=/app/logs/jd-ibe
logging.level.com.hna.eking.basedemo=INFO
logging.level.root=INFO
logging.level.com.google.code.ssm=WARN


# eureka 注册中心地址
eureka.client.serviceUrl.defaultZone=http://************:8761/eureka/,http://************:8761/eureka/

# redis配置
redisson.address=redis://***********:6379
redisson.password=ENC(dg0jrcwpRZs3tNH0OmUZaR9Odg4qng63)
redisson.config.open=true

# jd 配置
ibe.jd.server.ip=*************
ibe.jd.server.backupIp=*************
ibe.jd.server.port=6891
ibe.jd.client.app=jdcrm
ibe.jd.client.office=xiy258
ibe.jd.client.customNo=0
ibe.jd.client.validationNo=64
ibe.jd.client.printNos=1,2,3,4,6,7,8


ibe.jd.gw.server.ip=***********
ibe.jd.gw.server.backupIp=***********
ibe.jd.gw.server.port=6891
ibe.jd.gw.client.app=jdibe
ibe.jd.gw.client.office=xiy210
ibe.jd.gw.client.customNo=0
ibe.jd.gw.client.validationNo=64
ibe.jd.gw.client.printNos=1,2,3,4,6,7,8

# jd 票务处理
ibe.jd.pw.server.ip=*************
ibe.jd.pw.server.backupIp=*************
ibe.jd.pw.server.port=6891
ibe.jd.pw.client.app=jdcrm
ibe.jd.pw.client.office=xiy258
ibe.jd.pw.client.customNo=0
ibe.jd.pw.client.validationNo=64
ibe.jd.pw.client.printNos=1,2,3,4,6,7,8

# jd IBE小工具 xiy255配置
ibe.jd.xiy255.server.ip=***********
ibe.jd.xiy255.server.backupIp=***********
ibe.jd.xiy255.server.port=6891
ibe.jd.xiy255.client.app=jdhaiwai
ibe.jd.xiy255.client.office=xiy255
ibe.jd.xiy255.client.customNo=0
ibe.jd.xiy255.client.validationNo=64
ibe.jd.xiy255.client.printNos=1,2,3,4,6,7,8

# jd IBE小工具 xiy258配置
ibe.jd.xiy258.server.ip=*************
ibe.jd.xiy258.server.backupIp=*************
ibe.jd.xiy258.server.port=6891
ibe.jd.xiy258.client.app=jdcrm
ibe.jd.xiy258.client.office=xiy258
ibe.jd.xiy258.client.customNo=0
ibe.jd.xiy258.client.validationNo=64
ibe.jd.xiy258.client.printNos=1,2,3,4,6,7,8

# jd 配置
ibe.jd.k.server.ip=***********
ibe.jd.k.server.backupIp=***********
ibe.jd.k.server.port=6891
ibe.jd.k.client.app=jdair
ibe.jd.k.client.office=xiy201
ibe.jd.k.client.customNo=0
ibe.jd.k.client.validationNo=64
ibe.jd.k.client.printNos=1,2,3,4,6,7,8



travelsky.service.url=

# 股份 esb 接口配置信息
odsapi.url=https://odsapi-bs.hnair.net/api
odsapi.serialization=fastjson2
odsapi.timeout=600000


spring.redis.database=1
# Redis服务器地址
spring.redis.host=************
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=ENC(TtqYrprrK9bAGNMGtO60OEx4XRyhYLFkO2HTpO5M2ws=)
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=20
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=3000

spring.datasource.dynamic.primary=master
#type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.master.url=*******************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=jdmanager
spring.datasource.dynamic.datasource.master.password=ENC(qwcGa3fiBPVj+Bjm3U0UBxVWmT0Ri8CRs8nlGUXEbxU=)
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver