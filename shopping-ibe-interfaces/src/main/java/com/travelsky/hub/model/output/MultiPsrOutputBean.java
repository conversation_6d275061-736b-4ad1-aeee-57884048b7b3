/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class MultiPsrOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<PassengerInfoMult> passengerInfoList;
/*    */ 
/*    */   public void setPassengerInfoList(List<PassengerInfoMult> passengerInfoList)
/*    */   {
/* 21 */     this.passengerInfoList = passengerInfoList;
/*    */   }
/*    */ 
/*    */   public List<PassengerInfoMult> getPassengerInfoList()
/*    */   {
/* 28 */     return this.passengerInfoList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.MultiPsrOutputBean
 * JD-Core Version:    0.6.0
 */