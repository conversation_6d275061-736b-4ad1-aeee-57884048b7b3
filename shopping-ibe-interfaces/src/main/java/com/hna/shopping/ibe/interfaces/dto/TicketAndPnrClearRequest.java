package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

/**
 * [
 *     {
 *         "issCode": "989",
 *         "ticketNo": "1111111111",
 *         "segList": [
 *             {
 *                 "depCode": "PKX",
 *                 "arrCode": "LJG"
 *             },
 *             {
 *                 "depCode": "LJG",
 *                 "arrCode": "PKX"
 *             }
 *         ]
 *     },
 *     {
 *         "issCode": "989",
 *         "ticketNo": "2222222222",
 *         "segList": [
 *             {
 *                 "depCode": "PKX",
 *                 "arrCode": "LJG"
 *             },
 *             {
 *                 "depCode": "LJG",
 *                 "arrCode": "PKX"
 *             }
 *         ]
 *     }
 * ]
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class TicketAndPnrClearRequest implements Serializable {

	private static final long serialVersionUID = -876864144246838384L;

	private List<TicketInfo> ticketInfoList;

	@Data
	public static class TicketInfo {
		private String issCode;
		private String ticketNo;
		private String passengerName;  // 双因素
		private String certNo;   // 双因素
		private String certType;  // 双因素
		private String pnrNo;      // 双因素
		private List<Seg> segList;
	}

	@Data
	public static class Seg {
		private String depCode;
		private String arrCode;
	}

}
