package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.travelsky.util.QDateTime;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
@ApiModel
public class DETRHistoryResult implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2214752319641918332L;
	public static final int PASSENGER_ADULT = 0;
	public static final int PASSENGER_CHILD = 1;
	public static final int PASSENGER_CHILD_UNACCOMPANIED = 2;
	public static final int PASSENGER_INFANT = 3;
	private String ticketNo;
	private String passengerName;
	private int passengerType;
	private int unaccompaniedChildAge;
	private Date infantBirthday;
	private String iataNo;
	private Date issueDate;
	private List<DETRHistoryInfoItem> infoItems = new ArrayList<DETRHistoryInfoItem>();
	private String remark;

	public DETRHistoryResult() {

	}


	public DETRHistoryInfoItem getInfoItem(int i) {
		if ((i < 0) || (i > this.infoItems.size()))
			return null;
		return ((DETRHistoryInfoItem) this.infoItems.get(i));
	}

	

	

	public String toString() {
		try {
			String type;
			StringBuffer buffer = new StringBuffer();

			buffer.append("TKTN: " + this.ticketNo);
			buffer.append("\t\tNAME: ");
			buffer.append(getPassengerName() + "\t");

			if (getPassengerType() == 1)
				type = "CHILD";
			else if (getPassengerType() == 2)
				type = "UNCOMPANY CHILD";
			else if (getPassengerType() == 3)
				type = "INFANT";
			else
				type = "ADULT";
			buffer.append("\t" + type + "\t");
			if (getPassengerType() == 2)
				buffer.append("AGE: " + getUnaccompaniedChildAge() + "\t");
			if (getPassengerType() == 3) {
				String birthday;
				try {
					birthday = QDateTime.dateToString(getInfantBirthday(), "MMMYY");
				} catch (Exception e) {
					birthday = null;
				}
				buffer.append("BIRTHDAY: " + birthday + "\t");
			}
			buffer.append("\r\n");
			buffer.append("IATA OFFC: " + this.iataNo);
			buffer.append("\t\tISSUED: " + QDateTime.dateToString(this.issueDate, "DDMMMYY"));
			buffer.append("\r\n");

			for (int i = 0; i < this.infoItems.size(); ++i) {
				buffer.append(((DETRHistoryInfoItem) this.infoItems.get(i)).toString());
			}
			return buffer.toString();
		} catch (Exception e) {
		}
		return null;
	}

	public int getInfoItemNum() {
		if (this.infoItems != null)
			return this.infoItems.size();
		return 0;
	}
}