/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class DetrTicketBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6650668274297579553L;
/*  20 */   private String passengerName = "";
/*     */ 
/*  25 */   private String tktNumber = "";
/*     */ 
/*  29 */   private String isReceiptPrinted = "";
/*     */ 
/*  36 */   private String chineseEnglishNameInd = "";
/*     */ 
/*  40 */   private String conjTicket = "";
/*     */ 
/*  44 */   private String ticketType = "";
/*     */ 
/*  48 */   private String totalType = "";
/*     */ 
/*  52 */   private String total = "";
/*     */ 
/*  56 */   private String eqviuCurrencyType = "";
/*     */ 
/*  60 */   private String eqviuFare = "";
/*     */ 
/*  64 */   private String currencyType = "";
/*     */ 
/*  68 */   private String fare = "";
/*     */   private List<String> taxCurrencyType;
/*  76 */   private String tax = "";
/*     */ 
/*  80 */   private String formOfPayment = "";
/*     */ 
/*  84 */   private String exchangeInfo = "";
/*     */ 
/*  88 */   private String tourCode = "";
/*     */ 
/*  92 */   private String endorsement = "";
/*     */ 
/*  96 */   private String fareCalcInfo = "";
/*     */ 
/* 100 */   private String originalIssue = "";
/*     */ 
/* 104 */   private String originalIssueDate = "";
/*     */ 
/* 109 */   private List tours = new ArrayList();
/*     */ 
/*     */   public String getIsReceiptPrinted()
/*     */   {
/* 116 */     return this.isReceiptPrinted;
/*     */   }
/*     */ 
/*     */   public void setIsReceiptPrinted(String isReceiptPrinted)
/*     */   {
/* 126 */     this.isReceiptPrinted = isReceiptPrinted;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 135 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 145 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getTKTNumber()
/*     */   {
/* 153 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTKTNumber(String number)
/*     */   {
/* 162 */     this.tktNumber = number;
/*     */   }
/*     */ 
/*     */   public List getTours()
/*     */   {
/* 171 */     return this.tours;
/*     */   }
/*     */ 
/*     */   public void setTours(List tours)
/*     */   {
/* 179 */     this.tours = tours;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 185 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 192 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getChineseEnglishNameInd()
/*     */   {
/* 199 */     return this.chineseEnglishNameInd;
/*     */   }
/*     */ 
/*     */   public void setChineseEnglishNameInd(String chineseEnglishNameInd)
/*     */   {
/* 206 */     this.chineseEnglishNameInd = chineseEnglishNameInd;
/*     */   }
/*     */ 
/*     */   public String getConjTicket()
/*     */   {
/* 213 */     return this.conjTicket;
/*     */   }
/*     */ 
/*     */   public void setConjTicket(String conjTicket)
/*     */   {
/* 220 */     this.conjTicket = conjTicket;
/*     */   }
/*     */ 
/*     */   public String getTicketType()
/*     */   {
/* 227 */     return this.ticketType;
/*     */   }
/*     */ 
/*     */   public void setTicketType(String ticketType)
/*     */   {
/* 234 */     this.ticketType = ticketType;
/*     */   }
/*     */ 
/*     */   public String getTotalType()
/*     */   {
/* 241 */     return this.totalType;
/*     */   }
/*     */ 
/*     */   public void setTotalType(String totalType)
/*     */   {
/* 248 */     this.totalType = totalType;
/*     */   }
/*     */ 
/*     */   public String getTotal()
/*     */   {
/* 255 */     return this.total;
/*     */   }
/*     */ 
/*     */   public void setTotal(String total)
/*     */   {
/* 262 */     this.total = total;
/*     */   }
/*     */ 
/*     */   public String getEqviuCurrencyType()
/*     */   {
/* 269 */     return this.eqviuCurrencyType;
/*     */   }
/*     */ 
/*     */   public void setEqviuCurrencyType(String eqviuCurrencyType)
/*     */   {
/* 276 */     this.eqviuCurrencyType = eqviuCurrencyType;
/*     */   }
/*     */ 
/*     */   public String getEqviuFare()
/*     */   {
/* 283 */     return this.eqviuFare;
/*     */   }
/*     */ 
/*     */   public void setEqviuFare(String eqviuFare)
/*     */   {
/* 290 */     this.eqviuFare = eqviuFare;
/*     */   }
/*     */ 
/*     */   public String getCurrencyType()
/*     */   {
/* 297 */     return this.currencyType;
/*     */   }
/*     */ 
/*     */   public void setCurrencyType(String currencyType)
/*     */   {
/* 304 */     this.currencyType = currencyType;
/*     */   }
/*     */ 
/*     */   public String getFare()
/*     */   {
/* 311 */     return this.fare;
/*     */   }
/*     */ 
/*     */   public void setFare(String fare)
/*     */   {
/* 318 */     this.fare = fare;
/*     */   }
/*     */ 
/*     */   public List<String> getTaxCurrencyType()
/*     */   {
/* 325 */     return this.taxCurrencyType;
/*     */   }
/*     */ 
/*     */   public void setTaxCurrencyType(List<String> taxCurrencyType)
/*     */   {
/* 332 */     this.taxCurrencyType = taxCurrencyType;
/*     */   }
/*     */ 
/*     */   public String getTax()
/*     */   {
/* 339 */     return this.tax;
/*     */   }
/*     */ 
/*     */   public void setTax(String tax)
/*     */   {
/* 346 */     this.tax = tax;
/*     */   }
/*     */ 
/*     */   public String getFormOfPayment()
/*     */   {
/* 353 */     return this.formOfPayment;
/*     */   }
/*     */ 
/*     */   public void setFormOfPayment(String formOfPayment)
/*     */   {
/* 360 */     this.formOfPayment = formOfPayment;
/*     */   }
/*     */ 
/*     */   public String getExchangeInfo()
/*     */   {
/* 367 */     return this.exchangeInfo;
/*     */   }
/*     */ 
/*     */   public void setExchangeInfo(String exchangeInfo)
/*     */   {
/* 374 */     this.exchangeInfo = exchangeInfo;
/*     */   }
/*     */ 
/*     */   public String getTourCode()
/*     */   {
/* 381 */     return this.tourCode;
/*     */   }
/*     */ 
/*     */   public void setTourCode(String tourCode)
/*     */   {
/* 388 */     this.tourCode = tourCode;
/*     */   }
/*     */ 
/*     */   public String getEndorsement()
/*     */   {
/* 395 */     return this.endorsement;
/*     */   }
/*     */ 
/*     */   public void setEndorsement(String endorsement)
/*     */   {
/* 402 */     this.endorsement = endorsement;
/*     */   }
/*     */ 
/*     */   public String getFareCalcInfo()
/*     */   {
/* 409 */     return this.fareCalcInfo;
/*     */   }
/*     */ 
/*     */   public void setFareCalcInfo(String fareCalcInfo)
/*     */   {
/* 416 */     this.fareCalcInfo = fareCalcInfo;
/*     */   }
/*     */ 
/*     */   public String getOriginalIssue()
/*     */   {
/* 423 */     return this.originalIssue;
/*     */   }
/*     */ 
/*     */   public void setOriginalIssue(String originalIssue)
/*     */   {
/* 430 */     this.originalIssue = originalIssue;
/*     */   }
/*     */ 
/*     */   public String getOriginalIssueDate()
/*     */   {
/* 437 */     return this.originalIssueDate;
/*     */   }
/*     */ 
/*     */   public void setOriginalIssueDate(String originalIssueDate)
/*     */   {
/* 444 */     this.originalIssueDate = originalIssueDate;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 451 */     this.passengerName = "";
/* 452 */     this.tktNumber = "";
/* 453 */     this.isReceiptPrinted = "";
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DetrTicketBean
 * JD-Core Version:    0.6.0
 */