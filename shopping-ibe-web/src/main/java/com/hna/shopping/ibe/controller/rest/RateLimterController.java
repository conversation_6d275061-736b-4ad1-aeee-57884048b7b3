/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.google.common.util.concurrent.RateLimiter;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(description = "限流功能示例")
@RestController
@RequestMapping("/api/ratelimter")
@Slf4j
public class RateLimterController {

    /**
     * 速率是每秒 5 个令牌
     */
    private static final RateLimiter RATE_LIMITER_5 = RateLimiter.create(5.0);
    /**
     * 速率是每秒 30 个令牌
     */
    private static final RateLimiter RATE_LIMITER_30 = RateLimiter.create(30.0);

    /**
     * 允许每秒 5 个并发
     *
     * @param name
     *
     * @return
     */
    @RequestMapping(value = "rate5", method = RequestMethod.GET)
    RestResponse<String> rate5(@RequestParam(value = "name") String name) {
        // acquire 阻塞，tryAcquire 不阻塞；acquire 限流，tryAcquire 降级

        // 也许需要等待
        RATE_LIMITER_5.acquire();

        // 这个不会等待，获取令牌失败，立即返回 false
//        RATE_LIMITER_5.tryAcquire();


        log.info("Rate5 Got it: {}", name);
        return RestResponse.ok(name);
    }

    /**
     * 允许每秒 30 个并发
     *
     * @param name
     *
     * @return
     */
    @RequestMapping(value = "rate30", method = RequestMethod.GET)
    RestResponse<String> rate30(@RequestParam(value = "name") String name) {
        // 也许需要等待
        RATE_LIMITER_30.acquire();

        log.info("Rate30 Got it: {}", name);
        return RestResponse.ok(name);
    }
}
