/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PreferSeatOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String seatNo;
/*    */ 
/*    */   public String getSeatNo()
/*    */   {
/* 25 */     return this.seatNo;
/*    */   }
/*    */ 
/*    */   public void setSeatNo(String SeatNo)
/*    */   {
/* 32 */     this.seatNo = SeatNo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PreferSeatOutputBean
 * JD-Core Version:    0.6.0
 */