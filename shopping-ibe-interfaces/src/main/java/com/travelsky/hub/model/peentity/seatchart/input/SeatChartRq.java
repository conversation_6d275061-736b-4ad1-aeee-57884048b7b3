/*     */ package com.travelsky.hub.model.peentity.seatchart.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatChartRq
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8914869122352837601L;
/*     */   private String ticketingDate;
/*     */   private Responsibility responsibility;
/*     */   private String vcAirline;
/*     */   private String groupInd;
/*     */   private RequestCriteria requestCriteria;
/*     */   private String currencyCode;
/*     */ 
///*     */   @JSONField(name="salesDate")
/*     */   public String getTicketingDate()
/*     */   {
/*  55 */     return this.ticketingDate;
/*     */   }
/*     */ 
/*     */   public void setTicketingDate(String ticketingDate)
/*     */   {
/*  62 */     this.ticketingDate = ticketingDate;
/*     */   }
/*     */ 
///*     */   @JSONField(name="pos")
/*     */   public Responsibility getResponsibility()
/*     */   {
/*  70 */     return this.responsibility;
/*     */   }
/*     */ 
/*     */   public void setResponsibility(Responsibility responsibility)
/*     */   {
/*  77 */     this.responsibility = responsibility;
/*     */   }
/*     */ 
///*     */   @JSONField(name="vcAirline")
/*     */   public String getVcAirline()
/*     */   {
/*  85 */     return this.vcAirline;
/*     */   }
/*     */ 
/*     */   public void setVcAirline(String vcAirline)
/*     */   {
/*  92 */     this.vcAirline = vcAirline;
/*     */   }
/*     */ 
///*     */   @JSONField(name="pnrIndicator")
/*     */   public String getGroupInd()
/*     */   {
/* 100 */     return this.groupInd;
/*     */   }
/*     */ 
/*     */   public void setGroupInd(String groupInd)
/*     */   {
/* 107 */     this.groupInd = groupInd;
/*     */   }
/*     */ 
/*     */   public RequestCriteria getRequestCriteria()
/*     */   {
/* 114 */     return this.requestCriteria;
/*     */   }
/*     */ 
/*     */   public void setRequestCriteria(RequestCriteria requestCriteria)
/*     */   {
/* 121 */     this.requestCriteria = requestCriteria;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/* 128 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/* 135 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.SeatChartRq
 * JD-Core Version:    0.6.0
 */