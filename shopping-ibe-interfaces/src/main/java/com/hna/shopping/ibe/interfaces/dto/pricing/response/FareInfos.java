package com.hna.shopping.ibe.interfaces.dto.pricing.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class FareInfos implements Serializable{

    private static final long serialVersionUID = 7376801915964444933L;
    private String arrivalAirport;
    private Constructedfare constructedfare;
    private String departureAirport;
    private String directionality;
    private String fareReference;
    private String fareruleid;
    private String filingAirline;
    private boolean isNegotiatedFare;
    private boolean negotiatedFare;
    private String ref1;
    private String ref2;
    private RuleRequest ruleRequest;


}