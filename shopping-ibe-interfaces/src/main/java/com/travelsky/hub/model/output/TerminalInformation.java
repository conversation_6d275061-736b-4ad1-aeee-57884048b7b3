/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class TerminalInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String departureAirportSegment;
/*     */   private String arrivalAirportSegment;
/*     */   private String departureTime;
/*     */   private String arrivalTime;
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  42 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  53 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  62 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  73 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirportSegment()
/*     */   {
/*  81 */     return this.departureAirportSegment;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirportSegment(String departureAirportSegment)
/*     */   {
/*  88 */     this.departureAirportSegment = departureAirportSegment;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirportSegment()
/*     */   {
/*  95 */     return this.arrivalAirportSegment;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirportSegment(String arrivalAirportSegment)
/*     */   {
/* 102 */     this.arrivalAirportSegment = arrivalAirportSegment;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 109 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/* 116 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 123 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String arrivalTime)
/*     */   {
/* 130 */     this.arrivalTime = arrivalTime;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TerminalInformation
 * JD-Core Version:    0.6.0
 */