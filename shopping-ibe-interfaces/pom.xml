<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <!--interface 就不需要设置 parent 了，以免引入太多没必要的 pom-->
    <groupId>com.hna.shopping.ibe</groupId>
    <artifactId>shopping-ibe-interfaces</artifactId>
    <version>0.1.6-SNAPSHOT</version>

    <repositories>
        <!-- 配置nexus远程仓库 -->
        <repository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <!-- 配置从哪个仓库中下载构件，即jar包 -->
    <pluginRepositories>
        <pluginRepository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Internal Repository</name>
            <url>http://maven.haihangyun.com/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.16.12</version>
        </dependency>

        <!--interface 里面引入 swagger 是否合适，这个有待进一步考虑-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.6.1</version>
        </dependency>

        <!-- 分页信息 pageable 需要这个包 -->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-commons</artifactId>
            <version>1.13.3.RELEASE</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>ebuildapi</groupId>-->
<!--            <artifactId>ebuildapi</artifactId>-->
<!--            <version>2.0.0</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>ebuildapi</groupId>
            <artifactId>ebuildapi-enc-2f</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fakepath</groupId>
            <artifactId>ebuild-axi</artifactId>
            <version>1.7.0</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>5.3.5.Final</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.46</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.6.1</version>
                <configuration>
                    <!--为了导出的接口，有更大的兼容性，尽量按 JDK1.6 进行导出-->
                    <source>1.6</source>
                    <target>1.6</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>