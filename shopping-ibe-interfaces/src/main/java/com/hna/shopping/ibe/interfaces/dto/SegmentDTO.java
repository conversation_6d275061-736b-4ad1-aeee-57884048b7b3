/*
 * create on 2008-3-11
 * Copy right (2008)
 * HNA System All rights reserved
 */
package com.hna.shopping.ibe.interfaces.dto;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class SegmentDTO implements Serializable {
    private static final long serialVersionUID = -630571892656906104L;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "承运航班号")
    private String carrierFlightNo;

    @ApiModelProperty(value = "共享航班号")
    private String shareFlightNo;

    @ApiModelProperty(value = "机型")
    private String planeStyle;

    @ApiModelProperty(value = "起飞日期")
    private Date departDate;

    @ApiModelProperty(value = "到达时间")
    private Date arriveDate;

    @ApiModelProperty(value = "出发城市")
    private String orgCity;

    @ApiModelProperty(value = "目的城市")
    private String dstCity;

    @ApiModelProperty(value = "经停数")
    private int stops;

    @ApiModelProperty(value = "是否代码共享航班")
    private boolean codeShare;

    @ApiModelProperty(value = "出发航站楼")
    private String depTerm;

    @ApiModelProperty(value = "抵达航站楼")
    private String arriTerm;

    @ApiModelProperty(value = "是否有餐食")
    private boolean hasMeal;

    @ApiModelProperty(value = "餐食代码 B: 早餐, S: 点心, L: 午餐, D: 晚餐, M: 早＋午餐")
    private String mealCode;

    @ApiModelProperty(value = "经停信息")
    List<StopPointDTO> stopPoints = Lists.newArrayList();

    @ApiModelProperty(value = "仓位信息")
    private List<CabinDTO> cabins = Lists.newArrayList();

    @ApiModelProperty(value = "航段序号")
    private String seq;//

}
