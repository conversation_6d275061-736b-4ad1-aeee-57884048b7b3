/*    */ package com.travelsky.hub.model.input.hbpamseat;
/*    */ 
/*    */ import com.travelsky.hub.model.input.TxnInfo;
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class MultiPsrCheckInSeatRequest
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3701193260111167006L;
/*    */   private FlightInfo flightInfo;
/*    */   private TxnInfo txnInfo;
/*    */   private List<PartnerInfo> partnerInfoList;
/*    */   private PassengerInfo passengerInfo;
/*    */ 
/*    */   public PassengerInfo getPassengerInfo()
/*    */   {
/* 42 */     return this.passengerInfo;
/*    */   }
/*    */ 
/*    */   public void setPassengerInfo(PassengerInfo passengerInfo)
/*    */   {
/* 49 */     this.passengerInfo = passengerInfo;
/*    */   }
/*    */ 
/*    */   public FlightInfo getFlightInfo()
/*    */   {
/* 55 */     return this.flightInfo;
/*    */   }
/*    */ 
/*    */   public void setFlightInfo(FlightInfo flightInfo)
/*    */   {
/* 62 */     this.flightInfo = flightInfo;
/*    */   }
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 68 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 75 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ 
/*    */   public List<PartnerInfo> getPartnerInfoList()
/*    */   {
/* 81 */     return this.partnerInfoList;
/*    */   }
/*    */ 
/*    */   public void setPartnerInfoList(List<PartnerInfo> partnerInfoList)
/*    */   {
/* 88 */     this.partnerInfoList = partnerInfoList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.MultiPsrCheckInSeatRequest
 * JD-Core Version:    0.6.0
 */