package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

import com.travelsky.util.QDateTime;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
@ApiModel
public class DETRHistoryInfoItem implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8776225544305278827L;
	private boolean isDeleted = false;
	private int operID;
	private int couponNo;
	private Date operTime;
	private String agentNo;
	private String operType;
	private String operDesc;

	
	public String toString() {
		try {
			StringBuffer buffer = new StringBuffer("                                            ");

			if (isDeleted())
				buffer.replace(0, 1, "X");
			else
				buffer.replace(0, 1, " ");
			buffer.replace(3, 6, String.valueOf(this.operID));
			if (this.couponNo != 0)
				buffer.replace(6, 9, String.valueOf(this.couponNo));
			buffer.replace(10, 22, QDateTime.dateToString(this.operTime, "DDMMMYY/HHMI"));
			buffer.replace(22, 23, "/");
			buffer.replace(23, 31, this.agentNo);
			buffer.replace(31, 35, this.operType);

			return new StringBuilder("*").append(buffer.toString()).toString().trim().substring(1) + " " + this.operDesc
					+ "\r\n";
		} catch (Exception e) {
		}
		return null;
	}

}