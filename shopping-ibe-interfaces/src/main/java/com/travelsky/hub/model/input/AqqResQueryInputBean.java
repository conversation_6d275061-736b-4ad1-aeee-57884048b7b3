/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AqqResQueryInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8708115171711075320L;
/*     */   private String hostNumber;
/*     */   private String flightDate;
/*     */   private String fromCity;
/*     */   private String tktNumber;
/*     */   private String flightNumber;
/*     */   private String msgType;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  35 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  42 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  49 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  55 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  62 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getMsgType()
/*     */   {
/*  69 */     return this.msgType;
/*     */   }
/*     */ 
/*     */   public void setMsgType(String msgType)
/*     */   {
/*  76 */     this.msgType = msgType;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  83 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  90 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  97 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 104 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 111 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 118 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 125 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AqqResQueryInputBean
 * JD-Core Version:    0.6.0
 */