/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AddBaggageInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7842808681571926931L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String cabinType;
/*     */   private String arrivalAirport;
/*     */   private String departureAirport;
/*     */   private String hostNumber;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String bagDestCity;
/*     */   private String bagTag;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  67 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  74 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  81 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  88 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  95 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 102 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 109 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 116 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 123 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 129 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 136 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 143 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getBagDestCity()
/*     */   {
/* 150 */     return this.bagDestCity;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 156 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 163 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setBagDestCity(String bagDestCity)
/*     */   {
/* 170 */     this.bagDestCity = bagDestCity;
/*     */   }
/*     */ 
/*     */   public String getBagTag()
/*     */   {
/* 177 */     return this.bagTag;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 183 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 190 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagTag(String bagTag)
/*     */   {
/* 197 */     this.bagTag = bagTag;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 204 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 211 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AddBaggageInputBean
 * JD-Core Version:    0.6.0
 */