package com.hna.shopping.ibe.manager.impl;

import com.google.common.collect.Lists;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.config.memcached.CacheManagerUtil;
import com.hna.shopping.ibe.config.memcached.CacheName;
import com.hna.shopping.ibe.interfaces.dto.AvCache;
import com.hna.shopping.ibe.interfaces.dto.CabinDTO;
import com.hna.shopping.ibe.interfaces.dto.SegmentDTO;
import com.hna.shopping.ibe.interfaces.dto.StopPointDTO;
import com.hna.shopping.ibe.manager.ibe.IbeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by chenjie on 2017/12/27.
 */
@Component
@Slf4j
public class AsyncTask {
    // 仓位状态
    private static final String ICS_OPEN = "OP";
    private static final String ICS_CLOSE = "CD";
    private static final String ICS_APPLY = "AP";

    private String getCacheKey(String airline, String orgCity, String dstCity, Date flightDate) {
        return String.format("%s%s%s%s", airline, orgCity, dstCity, DateUtil.toStringYMD(flightDate));
    }

//    @Async
//    public void avNow(String airline, String orgCity, String dstCity, Date flightDate, boolean isDirect, String sellerChannels, String allAirline) {
//        // 判断缓存有没有, 有就直接返回
//        String cacheKey = this.getCacheKey(airline, orgCity, dstCity, flightDate);
//        // 走一次 AV
//        List<SegmentDTO> avResult = IbeUtil.av(null, airline, orgCity, dstCity, flightDate, isDirect, sellerChannels, allAirline);
//
//        List<SegmentDTO> result = Lists.newArrayList();
//        // 按 ET 习惯转换 Cabin
//        for (SegmentDTO segmentDTO : avResult) {
//            // 出发、达到机场要完全一致
//            if (!(StringUtils.equals(orgCity, segmentDTO.getOrgCity())
//                    && StringUtils.equals(dstCity, segmentDTO.getDstCity()))) {
//                continue;
//            }
//
//            // 如果只要直达的，那就就过滤掉中转的；否则返回全部
//            if (isDirect && segmentDTO.getStops() > 0) {
//                continue;
//            }
//
//            for (CabinDTO cabinDTO : segmentDTO.getCabins()) {
//                this.convertCabin(cabinDTO);
//            }
//
//            result.add(segmentDTO);
//        }
//
//        // 存缓存
//        //CacheManagerUtil.put(CacheName.AV, cacheKey, result);
////        put2Cache(cacheKey, result);
//    }

    /**
     * 按 ET 的习惯转换 cabin
     */
    private void convertCabin(CabinDTO cabinDTO) {
        if (StringUtils.isNumeric(cabinDTO.getQuantity())) {
            cabinDTO.setInventory(Integer.parseInt(cabinDTO.getQuantity()));
            cabinDTO.setStatus(ICS_OPEN);
        } else {
            if ("A".equalsIgnoreCase(cabinDTO.getQuantity())) {
                cabinDTO.setInventory(10);
                cabinDTO.setStatus(ICS_OPEN);
            } else if ("Q".equalsIgnoreCase(cabinDTO.getQuantity()) || "S".equalsIgnoreCase(cabinDTO.getQuantity())) {
                cabinDTO.setInventory(0);
                cabinDTO.setStatus(ICS_APPLY);
            } else {
                cabinDTO.setInventory(0);
                cabinDTO.setStatus(ICS_CLOSE);
            }
        }
    }

//    @Async
//    public void put2Cache(String cacheKey, List<SegmentDTO> result) {
//        for (String avSpace : CacheName.AV_SPACES) {
//            int cacheLevel = Integer.parseInt(avSpace.split("\\_")[1]);
//            AvCache avCache = new AvCache();
//            avCache.setCacheLevel("" + cacheLevel);
//            avCache.setSegments(result);
//
//            avCache.setCacheTime(Calendar.getInstance().getTimeInMillis());
//            CacheManagerUtil.put(avSpace, cacheKey, avCache);
//            log.info("put2Cache cacheKey = {} avSpace = {} time = {} datetime = {}", cacheKey, avSpace, avCache.getCacheTime(), DateUtil.toStringYMDHMS(new Date(avCache.getCacheTime())));
//        }
//    }

//    @Async
//    public void clear2Cache(String cacheKey) {
//        for (String avSpace : CacheName.AV_SPACES) {
//            CacheManagerUtil.put(avSpace, cacheKey, null);
//        }
//
//    }

//    @Async
//    public void putStopCity2Cache(String cacheKey, List<StopPointDTO> os) {
//        CacheManagerUtil.put(CacheName.AV_STOP_CITY, cacheKey, os);
//    }
}
