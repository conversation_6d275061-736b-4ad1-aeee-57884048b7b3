/*     */ package com.travelsky.hub.model.peentity.seatchart.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightSegment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3491285456692813666L;
/*     */   private String segmentID;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String departureDateTime;
/*     */   private String arrivalDateTime;
/*     */   private String mcAirlineCode;
/*     */   private String mcFlightNumber;
/*     */   private String mcRBD;
/*     */   private String ocAirlineCode;
/*     */   private String ocFlightNumber;
/*     */   private String ocRBD;
/*     */   private String equipment;
/*     */   private String mcCabin;
/*     */ 
/*     */   public String getSegmentID()
/*     */   {
/*  90 */     return this.segmentID;
/*     */   }
/*     */ 
/*     */   public void setSegmentID(String segmentID)
/*     */   {
/*  98 */     this.segmentID = segmentID;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 105 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 112 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 119 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 126 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDateTime()
/*     */   {
/* 133 */     return this.departureDateTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureDateTime(String departureDateTime)
/*     */   {
/* 140 */     this.departureDateTime = departureDateTime;
/*     */   }
/*     */ 
///*     */   @JSONField(name="flightArriveDateTime")
/*     */   public String getArrivalDateTime()
/*     */   {
/* 148 */     return this.arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalDateTime(String arrivalDateTime)
/*     */   {
/* 155 */     this.arrivalDateTime = arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public String getMcAirlineCode()
/*     */   {
/* 162 */     return this.mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineCode(String mcAirlineCode)
/*     */   {
/* 169 */     this.mcAirlineCode = mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 176 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 183 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
///*     */   @JSONField(name="mcResBookDesigCode")
/*     */   public String getMcRBD()
/*     */   {
/* 191 */     return this.mcRBD;
/*     */   }
/*     */ 
/*     */   public void setMcRBD(String mcRBD)
/*     */   {
/* 198 */     this.mcRBD = mcRBD;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/* 205 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/* 212 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 219 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 226 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
///*     */   @JSONField(name="ocResBookDesigCode")
/*     */   public String getOcRBD()
/*     */   {
/* 234 */     return this.ocRBD;
/*     */   }
/*     */ 
/*     */   public void setOcRBD(String ocRBD)
/*     */   {
/* 241 */     this.ocRBD = ocRBD;
/*     */   }
/*     */ 
///*     */   @JSONField(name="equipmentCode")
/*     */   public String getEquipment()
/*     */   {
/* 249 */     return this.equipment;
/*     */   }
/*     */ 
/*     */   public void setEquipment(String equipment)
/*     */   {
/* 256 */     this.equipment = equipment;
/*     */   }
/*     */ 
///*     */   @JSONField(name="cabin")
/*     */   public String getMcCabin()
/*     */   {
/* 264 */     return this.mcCabin;
/*     */   }
/*     */ 
/*     */   public void setMcCabin(String mcCabin)
/*     */   {
/* 271 */     this.mcCabin = mcCabin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.FlightSegment
 * JD-Core Version:    0.6.0
 */