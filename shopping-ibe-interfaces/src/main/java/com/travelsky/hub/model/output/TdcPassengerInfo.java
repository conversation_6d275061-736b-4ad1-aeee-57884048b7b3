/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class TdcPassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7090977258410503785L;
/*     */   private PassengerBaseInfo passengerBaseInfo;
/*     */   private List<Document> documents;
/*     */   private List<Segment> segments;
/*     */   private List<Message> firstLevelMessages;
/*     */   private List<Message> secondLevelMessages;
/*     */ 
/*     */   public PassengerBaseInfo getPassengerBaseInfo()
/*     */   {
/*  50 */     return this.passengerBaseInfo;
/*     */   }
/*     */ 
/*     */   public void setPassengerBaseInfo(PassengerBaseInfo passengerBaseInfo)
/*     */   {
/*  57 */     this.passengerBaseInfo = passengerBaseInfo;
/*     */   }
/*     */ 
/*     */   public List<Document> getDocuments()
/*     */   {
/*  64 */     return this.documents;
/*     */   }
/*     */ 
/*     */   public void setDocuments(List<Document> documents)
/*     */   {
/*  71 */     this.documents = documents;
/*     */   }
/*     */ 
/*     */   public List<Segment> getSegments()
/*     */   {
/*  78 */     return this.segments;
/*     */   }
/*     */ 
/*     */   public void setSegments(List<Segment> segments)
/*     */   {
/*  85 */     this.segments = segments;
/*     */   }
/*     */ 
/*     */   public List<Message> getFirstLevelMessages()
/*     */   {
/*  92 */     return this.firstLevelMessages;
/*     */   }
/*     */ 
/*     */   public void setFirstLevelMessages(List<Message> firstLevelMessages)
/*     */   {
/*  99 */     this.firstLevelMessages = firstLevelMessages;
/*     */   }
/*     */ 
/*     */   public List<Message> getSecondLevelMessages()
/*     */   {
/* 106 */     return this.secondLevelMessages;
/*     */   }
/*     */ 
/*     */   public void setSecondLevelMessages(List<Message> secondLevelMessages)
/*     */   {
/* 113 */     this.secondLevelMessages = secondLevelMessages;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TdcPassengerInfo
 * JD-Core Version:    0.6.0
 */