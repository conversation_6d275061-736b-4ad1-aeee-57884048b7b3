/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EbpResultBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String ebpStr;
/*    */ 
/*    */   public String getEbpStr()
/*    */   {
/* 23 */     return this.ebpStr;
/*    */   }
/*    */ 
/*    */   public void setEbpStr(String ebpStr)
/*    */   {
/* 30 */     this.ebpStr = ebpStr;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.EbpResultBean
 * JD-Core Version:    0.6.0
 */