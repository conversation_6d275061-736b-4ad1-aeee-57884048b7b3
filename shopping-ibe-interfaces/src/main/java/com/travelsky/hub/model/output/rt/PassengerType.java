/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5590291782417055460L;
/*    */   private String deporteeInd;
/*    */   private String airSeaInd;
/*    */   private String ptc;
/*    */   private String unaccompaniedMinorInd;
/*    */ 
/*    */   public String getDeporteeInd()
/*    */   {
/* 38 */     return this.deporteeInd;
/*    */   }
/*    */ 
/*    */   public void setDeporteeInd(String deporteeInd)
/*    */   {
/* 45 */     this.deporteeInd = deporteeInd;
/*    */   }
/*    */ 
/*    */   public String getAirSeaInd()
/*    */   {
/* 52 */     return this.airSeaInd;
/*    */   }
/*    */ 
/*    */   public void setAirSeaInd(String airSeaInd)
/*    */   {
/* 59 */     this.airSeaInd = airSeaInd;
/*    */   }
/*    */ 
/*    */   public String getPtc()
/*    */   {
/* 66 */     return this.ptc;
/*    */   }
/*    */ 
/*    */   public void setPtc(String ptc)
/*    */   {
/* 73 */     this.ptc = ptc;
/*    */   }
/*    */ 
/*    */   public String getUnaccompaniedMinorInd()
/*    */   {
/* 80 */     return this.unaccompaniedMinorInd;
/*    */   }
/*    */ 
/*    */   public void setUnaccompaniedMinorInd(String unaccompaniedMinorInd)
/*    */   {
/* 87 */     this.unaccompaniedMinorInd = unaccompaniedMinorInd;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.PassengerType
 * JD-Core Version:    0.6.0
 */