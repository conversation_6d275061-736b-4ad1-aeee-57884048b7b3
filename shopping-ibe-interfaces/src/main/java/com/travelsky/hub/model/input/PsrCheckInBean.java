/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsrCheckInBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String seatNo;
/*     */   private boolean isSNR;
/*     */   private String cardID;
/*     */   private String cardAirLine;
/*     */   private String fromCity;
/*     */   private String etCode;
/*     */   private String tourIndex;
/*     */   private String asrSeatNo;
/*     */   private String toCity;
/*     */   private String gender;
/*     */   private String phoneNumber;
/*     */   private String email;
/*     */   private String flightNo;
/*     */   private String chd;
/*     */   private String option;
/*     */   private InfInfoBean infInfo;
/*     */   private Extraseat extraSeat;
/*     */ 
/*     */   public String getOption()
/*     */   {
/*  67 */     return this.option;
/*     */   }
/*     */ 
/*     */   public void setOption(String option)
/*     */   {
/*  74 */     this.option = option;
/*     */   }
/*     */ 
/*     */   public Extraseat getExtraSeat()
/*     */   {
/*  82 */     return this.extraSeat;
/*     */   }
/*     */ 
/*     */   public void setExtraSeat(Extraseat extraSeat)
/*     */   {
/*  90 */     this.extraSeat = extraSeat;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/*  98 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 106 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getPhoneNumber()
/*     */   {
/* 114 */     return this.phoneNumber;
/*     */   }
/*     */ 
/*     */   public void setPhoneNumber(String phoneNumber)
/*     */   {
/* 121 */     this.phoneNumber = phoneNumber;
/*     */   }
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 128 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 135 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 142 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 149 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getAsrSeatNo()
/*     */   {
/* 156 */     return this.asrSeatNo;
/*     */   }
/*     */ 
/*     */   public void setAsrSeatNo(String asrSeatNo)
/*     */   {
/* 163 */     this.asrSeatNo = asrSeatNo;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 170 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 177 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 185 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 192 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 199 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 206 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 213 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 220 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public boolean isSNR()
/*     */   {
/* 227 */     return this.isSNR;
/*     */   }
/*     */ 
/*     */   public void setSNR(boolean isSNR)
/*     */   {
/* 234 */     this.isSNR = isSNR;
/*     */   }
/*     */ 
/*     */   public String getCardID()
/*     */   {
/* 261 */     return this.cardID;
/*     */   }
/*     */ 
/*     */   public void setCardID(String cardID)
/*     */   {
/* 268 */     this.cardID = cardID;
/*     */   }
/*     */ 
/*     */   public String getCardAirLine()
/*     */   {
/* 275 */     return this.cardAirLine;
/*     */   }
/*     */ 
/*     */   public void setCardAirLine(String cardAirLine)
/*     */   {
/* 282 */     this.cardAirLine = cardAirLine;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 289 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 296 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/* 303 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/* 310 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 318 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 325 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 332 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 340 */     this.infInfo = infInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PsrCheckInBean
 * JD-Core Version:    0.6.0
 */