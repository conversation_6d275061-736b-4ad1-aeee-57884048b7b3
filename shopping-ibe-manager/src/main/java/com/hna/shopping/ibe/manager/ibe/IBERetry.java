package com.hna.shopping.ibe.manager.ibe;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.travelsky.ibe.client.IBEClient;
import com.travelsky.ibe.exceptions.IBEException;
import org.springframework.util.ReflectionUtils;


public class IBERetry
{

    private int maxTry = 3;

    private long retryInterval = 1000;

    private List<String> exceptionKeyList;


    private final Logger log = LoggerFactory.getLogger(IBERetry.class);

    public IBERetry()
    {
        this.exceptionKeyList = new ArrayList<String>();

        //		exceptionKeyList.add("NoAvailableResourceException");
        exceptionKeyList.add(
            "Operation timed out: connect:could be due to invalid address");
        exceptionKeyList.add("Connection timed out: connect");
//        exceptionKeyList.add("huairb failure");

        		exceptionKeyList.add("NoAvailableResourceException");
        exceptionKeyList.add("NetworkConnectionException");
        exceptionKeyList.add("ConnectionRefuseException");

        //		exceptionKeyList.add("NoAvailableResourceException");
        exceptionKeyList.add(
            "com.travelsky.ibe.exceptions.DisconnectionException");
        exceptionKeyList.add("com.travelsky.ibe.exceptions.NoResponseException");
        exceptionKeyList.add("com.travelsky.ibe.exceptions.NoDeviceException");
        exceptionKeyList.add("Connection reset");

        //Connection reset
    }

    /**
     * @param exceptionKeyList
     *            the exceptionKeyList to set
     */
    public void setExceptionKeyList(List<String> exceptionKeyList)
    {
        this.exceptionKeyList = exceptionKeyList;
    }

    /**
     * @param maxTry
     *            the maxTry to set
     */
    public void setMaxTry(int maxTry)
    {
        this.maxTry = maxTry;
    }

    /**
     *
     *
     * @param retryInterval c
     */
    public void setRetryInterval(long retryInterval)
    {
        this.retryInterval = retryInterval;
    }

    
    public Object execute(Object obj, String method, Class[] parameterTypes,
        Object[] args, String msg) throws Exception
    {
       
    	   /* 重试时间*/
        int retryTimes = 0;

        while (retryTimes++ < maxTry)
        {
            try
            {
                //            	 TODO Auto-generated catch block
                Class c = obj.getClass();
                Method m = c.getDeclaredMethod(method, parameterTypes);

                /* return*/
                return m.invoke(obj, args);
            }
            catch (Exception e)
            {
                //log.error(msg + "，详细信息 :" + e.toString());		
                //System.out.println(e.getCause());
                if (!(e instanceof InvocationTargetException))
                {
                    //    				log.error(msg + "，详细信息 :" + e.toString());	
                    throw new Exception("方法调用出错！" + e);
                }

                //				e instanceof SecurityException
                //				|| e instanceof NoSuchMethodException
                //				|| e instanceof IllegalArgumentException
                //				|| e instanceof IllegalAccessException				
                //|| e instanceof InvocationTargetException
                String errMsg = e.getCause().toString();

                // e.printStackTrace();
                // 抛出IBEException
                // 判断是否重试
                Iterator iter = exceptionKeyList.iterator();
                boolean isExpectedException = false;

                //				boolean isConnectionReset = false;
                String s = null;

                while (iter.hasNext())
                {
                    String exceptionKey = (String) iter.next();

                    //System.out.println(e.getMessage());
                    if (errMsg.indexOf(exceptionKey) > -1)
                    {
                        isExpectedException = true;
                        s = exceptionKey;

                        //						if (exceptionKey.equals("Connection reset"))
                        //							isConnectionReset = true;
                        break;
                    }
                }

                if (isExpectedException)
                {
                    //   TODO Auto-generated catch block
//                    if (s.equals("Connection reset"))
//                    {
//                        maxTry = 10;
//                        retryInterval = 300;
//                    }

                    if (retryTimes >= maxTry)
                    {
                        // 放弃重试
                    	if (obj instanceof IBEClient){
                    		IBEClient ibe = (IBEClient)obj;
                    		log.error("重试" + String.valueOf(maxTry) + "次后放弃。"+ibe.getConnectionIP()+"/"+ibe.getOfficeCode());
                    	}else
                    		log.error("重试" + String.valueOf(maxTry) + "次后放弃。");
                    	if (null!=e.getCause() && e.getCause() instanceof IBEException){
    						Class c = e.getCause().getClass();
    						Constructor c1 = c
    								.getDeclaredConstructor(new Class[] { String.class });
                            ReflectionUtils.makeAccessible(c1);
    						throw (Exception) c1.newInstance(new Object[] { msg
    								+ "失败：" + e.getCause().getMessage() });
                    	}else
                        throw new Exception(msg + "失败，详细信息 :" +
                            errMsg);
                    }
                    else
                    {
                        // 重试
                    	if (obj instanceof IBEClient){
                    		IBEClient ibe = (IBEClient)obj;
                    		log.error(s + "。等待" + String.valueOf(retryInterval) +
                            "毫秒后第" + retryTimes + "次重试。"+ibe.getConnectionIP()+"/"+ibe.getOfficeCode());
                    	}else
                    		log.error(s + "。等待" + String.valueOf(retryInterval) +
                            "毫秒后第" + retryTimes + "次重试。");

                        try
                        {
                            Thread.sleep(retryInterval);
                        }
                        catch (InterruptedException e1)
                        {
                            // TODO Auto-generated catch block
//                            e1.printStackTrace();
                        }
                    }
                }
                else
                {
                    // 无需重试
//                    throw new Exception(msg + "失败，详细信息 :" + errMsg);
//                	throw new Exception(e.getCause());
//                	e.getCause().
                	if (e.getCause() instanceof IBEException){
						Class c = e.getCause().getClass();
						Constructor c1 = c
								.getDeclaredConstructor(new Class[] { String.class });
                        ReflectionUtils.makeAccessible(c1);
						throw (Exception) c1.newInstance(new Object[] { msg
								+ "失败：" + e.getCause().getMessage() });
                	}else
                		throw new Exception(msg + "失败，详细信息 :" + errMsg);
//                	try {
//						throw e.getCause();
//					} catch (Throwable e1) {
//						// TODO Auto-generated catch block
//						e1.printStackTrace();
//					}

                    // 抛出异常
                }
            }
        }

        //      返回
        return null;
    }
}
