/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class LoyaltyRedemption
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7472921189394920898L;
/*    */   private String certificateNumber;
/*    */   private String memberNumber;
/*    */ 
/*    */   public String getCertificateNumber()
/*    */   {
/* 38 */     return this.certificateNumber;
/*    */   }
/*    */ 
/*    */   public void setCertificateNumber(String certificateNumber)
/*    */   {
/* 45 */     this.certificateNumber = certificateNumber;
/*    */   }
/*    */ 
/*    */   public String getMemberNumber()
/*    */   {
/* 52 */     return this.memberNumber;
/*    */   }
/*    */ 
/*    */   public void setMemberNumber(String memberNumber)
/*    */   {
/* 59 */     this.memberNumber = memberNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.LoyaltyRedemption
 * JD-Core Version:    0.6.0
 */