/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import com.travelsky.hub.model.input.Extraseat;
/*     */ import com.travelsky.hub.model.input.InfInfoBean;
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class SYPRResultBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5286702255111745277L;
/*     */   private String psrName;
/*     */   private String psrEnName;
/*     */   private String pstCkiStatus;
/*     */   private String ffLevel;
/*     */   private String asrSeat;
/*     */   private String cardLevel;
/*     */   private String cardID;
/*     */   private String cardAirline;
/*     */   private String cabinType;
/*     */   private String speicialSvc;
/*     */   private String schDeptTime;
/*     */   private String deptAirport;
/*     */   private String arriveAirport;
/*     */   private String expDeptTime;
/*     */   private String isFlightOpened;
/*     */   private String bordingTime;
/*     */   private String boardingGateNumber;
/*     */   private String planeType;
/*     */   private String groupCode;
/*     */   private String groupNumber;
/*     */   private String sequenceNumber;
/*     */   private String carrFlightNo;
/*     */   private String ckiInChannel;
/*     */   private List asvcInfo;
/*     */   private String asrStatus;
/*     */   private String hostNum;
/*     */   private String chdFlag;
/*     */   private String chd;
/*     */   private InfInfoBean infInfo;
/*     */   private String spmlMessage;
/*     */   private String parentCabinType;
/*     */   private String boardingNumber;
/*     */   private Extraseat extraseat;
/*     */   private String checkinChannel;
/*     */ 
/*     */   public SYPRResultBean()
/*     */   {
/* 184 */     this.psrName = "";
/* 185 */     this.psrEnName = "";
/* 186 */     this.pstCkiStatus = "";
/* 187 */     this.ffLevel = "";
/* 188 */     this.asrSeat = "";
/* 189 */     this.cardLevel = "";
/* 190 */     this.cardID = "";
/* 191 */     this.cardAirline = "";
/* 192 */     this.cabinType = "";
/* 193 */     this.speicialSvc = "";
/* 194 */     this.schDeptTime = "";
/* 195 */     this.deptAirport = "";
/* 196 */     this.arriveAirport = "";
/* 197 */     this.expDeptTime = "";
/* 198 */     this.isFlightOpened = "";
/* 199 */     this.bordingTime = "";
/* 200 */     this.boardingGateNumber = "";
/* 201 */     this.planeType = "";
/* 202 */     this.groupCode = "";
/* 203 */     this.groupNumber = "";
/* 204 */     this.sequenceNumber = "";
/* 205 */     this.carrFlightNo = "";
/* 206 */     this.ckiInChannel = "";
/* 207 */     this.asvcInfo = new ArrayList();
/* 208 */     this.asrStatus = "";
/* 209 */     this.chdFlag = "";
/* 210 */     this.chd = "";
/* 211 */     this.parentCabinType = "";
/* 212 */     this.boardingNumber = "";
/* 213 */     this.checkinChannel = "";
/*     */   }
/*     */ 
/*     */   public String getGroupCode()
/*     */   {
/* 221 */     return this.groupCode;
/*     */   }
/*     */ 
/*     */   public void setGroupCode(String groupCode)
/*     */   {
/* 229 */     this.groupCode = groupCode;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 237 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String groupNumber)
/*     */   {
/* 245 */     this.groupNumber = groupNumber;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 253 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 261 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 269 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 277 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getPsrEnName()
/*     */   {
/* 285 */     return this.psrEnName;
/*     */   }
/*     */ 
/*     */   public void setPsrEnName(String psrEnName)
/*     */   {
/* 293 */     this.psrEnName = psrEnName;
/*     */   }
/*     */ 
/*     */   public String getPstCkiStatus()
/*     */   {
/* 301 */     return this.pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPstCkiStatus(String pstCkiStatus)
/*     */   {
/* 309 */     this.pstCkiStatus = pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getCardLevel()
/*     */   {
/* 316 */     return this.cardLevel;
/*     */   }
/*     */ 
/*     */   public void setCardLevel(String cardLevel)
/*     */   {
/* 324 */     this.cardLevel = cardLevel;
/*     */   }
/*     */ 
/*     */   public String getCardID()
/*     */   {
/* 332 */     return this.cardID;
/*     */   }
/*     */ 
/*     */   public String getFfLevel()
/*     */   {
/* 339 */     return this.ffLevel;
/*     */   }
/*     */ 
/*     */   public void setFfLevel(String ffLevel)
/*     */   {
/* 347 */     this.ffLevel = ffLevel;
/*     */   }
/*     */ 
/*     */   public void setCardID(String cardID)
/*     */   {
/* 355 */     this.cardID = cardID;
/*     */   }
/*     */ 
/*     */   public String getCardAirline()
/*     */   {
/* 363 */     return this.cardAirline;
/*     */   }
/*     */ 
/*     */   public void setCardAirline(String cardAirline)
/*     */   {
/* 371 */     this.cardAirline = cardAirline;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 379 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 387 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getSpeicialSvc()
/*     */   {
/* 395 */     return this.speicialSvc;
/*     */   }
/*     */ 
/*     */   public String getAsrSeat()
/*     */   {
/* 404 */     return this.asrSeat;
/*     */   }
/*     */ 
/*     */   public void setAsrSeat(String asrSeat)
/*     */   {
/* 412 */     this.asrSeat = asrSeat;
/*     */   }
/*     */ 
/*     */   public void setSpeicialSvc(String speicialSvc)
/*     */   {
/* 420 */     this.speicialSvc = speicialSvc;
/*     */   }
/*     */ 
/*     */   public String getSchDeptTime()
/*     */   {
/* 428 */     return this.schDeptTime;
/*     */   }
/*     */ 
/*     */   public void setSchDeptTime(String schDeptTime)
/*     */   {
/* 436 */     this.schDeptTime = schDeptTime;
/*     */   }
/*     */ 
/*     */   public String getDeptAirport()
/*     */   {
/* 444 */     return this.deptAirport;
/*     */   }
/*     */ 
/*     */   public void setDeptAirport(String deptAirport)
/*     */   {
/* 452 */     this.deptAirport = deptAirport;
/*     */   }
/*     */ 
/*     */   public String getArriveAirport()
/*     */   {
/* 460 */     return this.arriveAirport;
/*     */   }
/*     */ 
/*     */   public void setArriveAirport(String arriveAirport)
/*     */   {
/* 468 */     this.arriveAirport = arriveAirport;
/*     */   }
/*     */ 
/*     */   public String getExpDeptTime()
/*     */   {
/* 476 */     return this.expDeptTime;
/*     */   }
/*     */ 
/*     */   public void setExpDeptTime(String expDeptTime)
/*     */   {
/* 484 */     this.expDeptTime = expDeptTime;
/*     */   }
/*     */ 
/*     */   public String getIsFlightOpened()
/*     */   {
/* 492 */     return this.isFlightOpened;
/*     */   }
/*     */ 
/*     */   public void setIsFlightOpened(String isFlightOpened)
/*     */   {
/* 500 */     this.isFlightOpened = isFlightOpened;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 508 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String bordingTime)
/*     */   {
/* 516 */     this.bordingTime = bordingTime;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 524 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 532 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/* 540 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/* 548 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getCarrFlightNo()
/*     */   {
/* 555 */     return this.carrFlightNo;
/*     */   }
/*     */ 
/*     */   public void setCarrFlightNo(String carrFlightNo)
/*     */   {
/* 562 */     this.carrFlightNo = carrFlightNo;
/*     */   }
/*     */ 
/*     */   public String getCkiInChannel()
/*     */   {
/* 569 */     return this.ckiInChannel;
/*     */   }
/*     */ 
/*     */   public void setCkiInChannel(String ckiInChannel)
/*     */   {
/* 576 */     this.ckiInChannel = ckiInChannel;
/*     */   }
/*     */ 
/*     */   public List getAsvcInfo()
/*     */   {
/* 583 */     return this.asvcInfo;
/*     */   }
/*     */ 
/*     */   public void setAsvcInfo(List asvcInfo)
/*     */   {
/* 590 */     this.asvcInfo = asvcInfo;
/*     */   }
/*     */ 
/*     */   public String getAsrStatus()
/*     */   {
/* 597 */     return this.asrStatus;
/*     */   }
/*     */ 
/*     */   public void setAsrStatus(String asrStatus)
/*     */   {
/* 604 */     this.asrStatus = asrStatus;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 611 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 619 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getChdFlag()
/*     */   {
/* 626 */     return this.chdFlag;
/*     */   }
/*     */ 
/*     */   public void setChdFlag(String chdFlag)
/*     */   {
/* 633 */     this.chdFlag = chdFlag;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 641 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 649 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 657 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 665 */     this.infInfo = infInfo;
/*     */   }
/*     */ 
/*     */   public void setSpmlMessage(String spmlMessage)
/*     */   {
/* 674 */     this.spmlMessage = spmlMessage;
/*     */   }
/*     */ 
/*     */   public String getSpmlMessage()
/*     */   {
/* 682 */     return this.spmlMessage;
/*     */   }
/*     */ 
/*     */   public String getParentCabinType()
/*     */   {
/* 690 */     return this.parentCabinType;
/*     */   }
/*     */ 
/*     */   public void setParentCabinType(String parentCabinType)
/*     */   {
/* 698 */     this.parentCabinType = parentCabinType;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 705 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 712 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getCheckinChannel()
/*     */   {
/* 720 */     return this.checkinChannel;
/*     */   }
/*     */ 
/*     */   public void setCheckinChannel(String checkinChannel)
/*     */   {
/* 727 */     this.checkinChannel = checkinChannel;
/*     */   }
/*     */ 
/*     */   public Extraseat getExtraseat() {
/* 731 */     return this.extraseat;
/*     */   }
/*     */ 
/*     */   public void setExtraseat(Extraseat extraseat) {
/* 735 */     this.extraseat = extraseat;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SYPRResultBean
 * JD-Core Version:    0.6.0
 */