/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AdcResQueryInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String cabin;
/*     */   private String hostNum;
/*     */   private String fromCity;
/*     */   private String flightNumber;
/*     */   private String ticketNumber;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  40 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  48 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  55 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  63 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  71 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  80 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  88 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/*  97 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 105 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 113 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 121 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 129 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 138 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 146 */     this.cabin = cabin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AdcResQueryInputBean
 * JD-Core Version:    0.6.0
 */