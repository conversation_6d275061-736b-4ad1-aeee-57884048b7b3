package com.hna.shopping.ibe.manager.util;

import com.hna.shopping.ibe.interfaces.dto.DelPnrItemRequest;
import com.travelsky.ibe.client.IBEClient;
import com.travelsky.ibe.client.pnr.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Vector;
import java.util.stream.Collectors;


@Slf4j
public class IBEUtils {

    public static PNRSSR getSSRFirst(RTResult rtResult, String ssrType, String actionCode, String passengerName, String cityPair, List<String[]> containsTexts, List<Integer> excludeSSRIndexes){
        List<PNRSSR> list = getSSRs(rtResult, ssrType, actionCode, passengerName, cityPair, containsTexts, excludeSSRIndexes);
        if(list != null && list.size() > 0){
            return list.get(0);
        }
        return null;
    }

    public static List<PNRSSR> getSSRs(RTResult rtResult, String ssrType, String actionCode, String passengerName, String cityPair, List<String[]> containsTexts, List<Integer> excludeSSRIndexes){
        List<PNRSSR> list = new ArrayList<>();
        String passengerId = getPassengerIdInPnr(rtResult, passengerName);
        if(StringUtils.isBlank(passengerId)){
            return list;
        }
        Vector<PNRSSR> ssrs = rtResult.getSsrs();
        for (PNRSSR ssr : ssrs) {
            if (ssr.getSSRType().equals(ssrType) && ssr.getActionCode().equals(actionCode)) {
                if(ssr.getPsgrID().equals(passengerId) && ssr.getCitypair().equals(cityPair)){
                    String text = ssr.getTextInPNR();
                    boolean containsAll = StringHelper.isContainsAll(text, containsTexts);
                    if(containsAll){
                        if(excludeSSRIndexes == null || !excludeSSRIndexes.contains(ssr.getIndex())){
                            list.add(ssr);
                        }
                    }
                }
            }
        }
        return list;
    }

    /**
     *该接口多航段时，如果存在已使用航段，则可能返回的AirSegs缺失，造成航段序号错误
     * 如果存在票号，从票号提取航段序号会更准确些
     */
    @Deprecated
    public static int getCouponNumber(RTResult rr, String cityPair) {
        int couponNumber = -1;
        for (int i = 0; i < rr.getAirSegsCount(); i++) {
            PNRAirSeg airSeg = rr.getAirSegAt(i);
            if(cityPair.equals(airSeg.getOrgCity() + airSeg.getDesCity())) {
                // 出票的 couponNumber 为 ET 客票的航段序号
                couponNumber = (i + 1);
                break;
            }
        }
        if(couponNumber < 0){
            log.error("ssr has no cityPair:{},rt:{}", cityPair, rr.getOringinalRT());
            throw new RuntimeException("ssr has no cityPair");
        }
        return couponNumber;
    }

    /**
     * 获取PNR中旅客编号
     * @param rtResult
     * @param passengerName
     * @return
     */
    public static String getPassengerIdInPnr(RTResult rtResult, String passengerName) {
        int index = getPassengerIndexInPnr(rtResult, passengerName);
        if(index == -1){
            return null;
        }
        return "P" + index;
    }

    /**
     * 获取旅客在pnr中的序号
     * @param rtResult
     * @param passengerName
     * @return
     */
    public static int getPassengerIndexInPnr(RTResult rtResult, String passengerName) {
        for (Object obj : rtResult.getPassengers()) {
            PNRPassenger pnrPassenger = (PNRPassenger)obj;
            if (pnrPassenger.getName().equals(passengerName)) {
                return pnrPassenger.getIndex();
            }
        }
        log.warn("ssr has no passengerName:{},rt:{}", passengerName, rtResult.getOringinalRT());
        return -1;
    }

    public static PNRRMK getRmk(String rmktype, String rmkinfo, String passengerName, RTResult rr){
        if(rmktype != null){
            rmktype = rmktype.toUpperCase(Locale.ENGLISH);
        }
        if(rmkinfo != null){
            rmkinfo = rmkinfo.toUpperCase(Locale.ENGLISH);
        }
        String passengerId = getPassengerIdInPnr(rr, passengerName);
        if(passengerId == null){
            return null;
        }
        for (Object os : rr.getRmks()) {
            PNRRMK rmk = (PNRRMK) os;
            if(passengerId.equals(rmk.getPsgrID())){
                if(StringUtils.isNotBlank(rmk.getRmktype())){
                    if (rmktype.equals(rmk.getRmktype())) {
                        if(rmk.getRmkinfo() != null && rmk.getRmkinfo().equals(rmkinfo)){
                            return rmk;
                        }
                    }
                }else{
                    if(rmk.getRmkinfo() != null && rmk.getRmkinfo().contains(rmktype+" "+rmkinfo+"/")){
                        return rmk;
                    }
                }
            }
        }
        return null;
    }

    /**
     * @param ibeClient
     * @return 获取航信返回的txnid
     */
    public static String getTxnid(IBEClient ibeClient) {
        return "txnid:" + ibeClient.getTxnTraceKey();
    }

    public static DelPnrItemRequest buildDelPnrItemReq(String pnrNo, String airlineCode, List<String> contents){
        DelPnrItemRequest req = new DelPnrItemRequest();
        req.setPnrNo(pnrNo);
        req.setAirlineCode(airlineCode);
        req.setContents(contents);
        return req;
    }

    public static List<String> getTextInPnr(List<Integer> indexes, RTResult rr){
        if(indexes == null || indexes.size() == 0){
            return null;
        }
        //去重index，当删除pnr内容是，避免多删
        indexes = indexes.stream().distinct().collect(Collectors.toList());
        log.info("pnr:{}, indexes:{}", rr.getPnrcode(), StringUtils.join(indexes, ","));
        List<String> contents = new ArrayList<>();
        List<PNRObjectInterface> list = rr.getObjectList();
        int size = list == null?0:list.size();
        if(size > 0){
            for(Integer index:indexes){
                if(index != null && index >= 0 && index < size){
                    PNRObjectInterface obj = list.get(index);
                    if(obj != null && StringUtils.isNotBlank(obj.getTextInPNR())){
                        contents.add(obj.getTextInPNR());
                    }
                }
            }
        }
        log.info("pnr:{}, items:{}", rr.getPnrcode(), StringUtils.join(contents, ","));
        return contents;
    }
}
