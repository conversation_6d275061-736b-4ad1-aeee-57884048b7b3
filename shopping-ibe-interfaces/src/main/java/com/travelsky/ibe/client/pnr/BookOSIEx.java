package com.travelsky.ibe.client.pnr;

public class BookOSIEx extends BookOSI {

	/**
	 * 
	 */
	private static final long serialVersionUID = -4218745497674608516L;

	public BookOSIEx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookOSIEx(String airCode, String osi, String psgrName) throws Exception {
		super(airCode, osi, psgrName);
		// TODO Auto-generated constructor stub
	}

	public BookOSIEx(String airCode, String osi) throws Exception {
		super(airCode, osi);
		// TODO Auto-generated constructor stub
	}
	/*    */ public  String getAirCode() {
		/* 55 */     return super.getAirCode();//.airCode;
		/*    */   }
	/*    */ public  String getOsi() {
		/* 66 */     return super.getOsi();//.osi;
		/*    */   }
	/*    */  public String getpsgrName() {
		/* 77 */     return super.getpsgrName();//.psgrName;
		/*    */   }
}
