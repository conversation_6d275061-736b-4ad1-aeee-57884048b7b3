/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EbpOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8030830405634528734L;
/*    */   private String ebpImgByteStr;
/*    */   private String ebpStr;
/*    */ 
/*    */   public String getEbpImgByteStr()
/*    */   {
/* 25 */     return this.ebpImgByteStr;
/*    */   }
/*    */ 
/*    */   public void setEbpImgByteStr(String ebpImgByteStr)
/*    */   {
/* 32 */     this.ebpImgByteStr = ebpImgByteStr;
/*    */   }
/*    */ 
/*    */   public String getEbpStr()
/*    */   {
/* 40 */     return this.ebpStr;
/*    */   }
/*    */ 
/*    */   public void setEbpStr(String ebpStr)
/*    */   {
/* 47 */     this.ebpStr = ebpStr;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EbpOutputBean
 * JD-Core Version:    0.6.0
 */