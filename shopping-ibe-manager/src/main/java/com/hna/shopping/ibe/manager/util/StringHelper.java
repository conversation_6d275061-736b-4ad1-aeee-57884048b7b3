package com.hna.shopping.ibe.manager.util;

import java.util.List;

/**
 * Zero
 * 2020/3/23 16:29
 **/
public class StringHelper {

    public static boolean isContainsAll(String target, String[] substrings){
        if(target == null){
            return false;
        }
        if(substrings != null && substrings.length > 0){
            for(String sub:substrings){
                if(!target.contains(sub)){
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isContainsAll(String target, List<String[]> substrings){
        if(target == null){
            return false;
        }
        if(substrings != null && substrings.size() > 0){
            for(String[] ors:substrings){
                boolean containsAny = isContainsAny(target, ors);
                if(!containsAny){
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isContainsAny(String target, String[] ors){
        if(target == null){
            return false;
        }
        if(ors != null && ors.length > 0){
            for(String or:ors){
                if(target.contains(or)){
                    return true;
                }
            }
            return false;
        }
        return true;
    }

}
