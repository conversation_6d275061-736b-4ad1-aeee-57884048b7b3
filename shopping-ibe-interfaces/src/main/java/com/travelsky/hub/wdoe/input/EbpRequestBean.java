/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class EbpRequestBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String checkInSrc;
/*     */   private String printSrc;
/*     */   private String bppType;
/*     */   private String ebpType;
/*     */   private String bppStream;
/*     */ 
/*     */   public String getCheckInSrc()
/*     */   {
/*  42 */     return this.checkInSrc;
/*     */   }
/*     */ 
/*     */   public void setCheckInSrc(String checkInSrc)
/*     */   {
/*  49 */     this.checkInSrc = checkInSrc;
/*     */   }
/*     */ 
/*     */   public String getPrintSrc()
/*     */   {
/*  56 */     return this.printSrc;
/*     */   }
/*     */ 
/*     */   public void setPrintSrc(String printSrc)
/*     */   {
/*  63 */     this.printSrc = printSrc;
/*     */   }
/*     */ 
/*     */   public String getBppType()
/*     */   {
/*  70 */     return this.bppType;
/*     */   }
/*     */ 
/*     */   public void setBppType(String bppType)
/*     */   {
/*  77 */     this.bppType = bppType;
/*     */   }
/*     */ 
/*     */   public String getEbpType()
/*     */   {
/*  84 */     return this.ebpType;
/*     */   }
/*     */ 
/*     */   public void setEbpType(String ebpType)
/*     */   {
/*  91 */     this.ebpType = ebpType;
/*     */   }
/*     */ 
/*     */   public String getBppStream()
/*     */   {
/*  98 */     return this.bppStream;
/*     */   }
/*     */ 
/*     */   public void setBppStream(String bppStream)
/*     */   {
/* 105 */     this.bppStream = bppStream;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.EbpRequestBean
 * JD-Core Version:    0.6.0
 */