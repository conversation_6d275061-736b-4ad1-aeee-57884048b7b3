package com.travelsky.hub.wdoe.util;

public final class AiritineraryConStr
{
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String ERROR_ELEMENT = "Error";
  public static final String CODE_ERROR = "Code";
  public static final String MSG_ERROR = "Msg";
  public static final String DETR_FUNCTION = "Function";
  public static final String DETR_MSGFUN = "MsgFun";
  public static final String DETR_CUSTOMER = "Customer";
  public static final String DETR_DOCUMENT = "Document";
  public static final String DETR_TICKETNUM = "TicketNumber";
  public static final String DETR_FLTINFO = "FlightInfo";
  public static final String DETR_AIRITINERARY = "AirItinerary";
  public static final String DETR_SURNAME = "SurName";
  public static final String DETR_DOCTYPE = "DocType";
  public static final String DETR_DOCID = "DocID";
  public static final String DETR_TICKETID = "TicketID";
  public static final String DETR_PNR = "PNR";
  public static final String DETR_ID = "ID";
  public static final String DETR_PNRNUMBER = "PNRNumber";
  public static final String DETR_PNRID = "PNRID";
  public static final String DETR_SHAREAIRLINECODE = "ShareAirlineCode";
  public static final String DETR_FLTNUMBER = "FlightNumber";
  public static final String DETR_DEPTDATE = "DepartureDate";
  public static final String DETR_DEPTAIRPORT = "DepartureAirport";
  public static final String DETR_ARRIAIRPORT = "ArrivalAirport";
  public static final String DETR_CABTYPE = "CabinType";
  public static final String DETR_AIRCODE = "AirlineCode";
  public static final String DETR_ALLOWMUL = "AllowMultiPsr";
  public static final String DETR_ISOPEN = "IsOpen";
  public static final String DETR_TICKETS = "Tickets";
  public static final String DETR_TICKET = "Ticket";
  public static final String DETR_ISPRINTED = "IsReceiptPrinted";
  public static final String DETR_TOURS = "Tours";
  public static final String DETR_TOUR = "Tour";
  public static final String SURNAME_PASSENGERINFO = "Surname";
  public static final String CHINESE_ENGLISH_NAMEIND = "ChineseEnglishNameInd";
  public static final String CONJ_TICKET = "ConjTicket";
  public static final String TICKET_TYPE = "TicketType";
  public static final String TOTAL_TYPE = "TotalType";
  public static final String TOTAL = "Total";
  public static final String EQVIU_CURRENCY_TYPE = "EqviuCurrencyType";
  public static final String EQVIU_FARE = "EqviuFare";
  public static final String CURRENCY_TYPE = "CurrencyType";
  public static final String FORM_OF_PAYMENT = "FormOfPayment";
  public static final String EXCHANGE_INFO = "ExchangeInfo";
  public static final String TOUR_CODE = "TourCode";
  public static final String FARE = "Fare";
  public static final String END_OR_SEMENT_TEXT = "EndorsementText";
  public static final String FARE_CALC_INFO = "FareCalcInfo";
  public static final String ORIGINAL_ISSUE = "OriginalIssue";
  public static final String ORIGINAL_ISSUE_DATE = "OriginalIssueDate";
  public static final String TAX = "Tax";
  public static final String TAX_CURRENCY_TYPES = "TaxCurrencyTypes";
  public static final String TAX_CURRENCY_TYPE = "TaxCurrencyType";
  public static final String DETR_SEQNUM = "SequenceNumber";
  public static final String DEPARTUREAIRPORT_FLIGHTINFO = "DepartureAirport";
  public static final String ARRIVALAIRPORT_FLIGHTINFO = "ArrivalAirport";
  public static final String DETR_DEPTDATETIME = "DepartureDateTime";
  public static final String DETR_TKESTATUS = "TicketingStatus";
  public static final String DETR_CARRAIRCODE = "CarrAirlineCode";
  public static final String DETR_DEPTAIRPORTSTATUS = "DeptAirportStatus";
  public static final String DETR_DESAIRPORTSTATUS = "DestAirportStatus";
  public static final String ISSUE_AIRLINE = "IssueAirline";
  public static final String MSPNR = "MSPNR";
  public static final String MSSYSTEM_ID = "MSSystemID";
  public static final String FARE_BASIC_CODE = "FareBasicCode";
  public static final String FIRST_TICKET_DATE = "FirstTicketDate";
  public static final String LAST_TICKET_DATE = "LastTicketDate";
  public static final String BAGGAGE_PIECE = "BaggagePiece";
  public static final String BAGGAGE_WEIGHT = "BaggageWeight";
  public static final String RESERVATION_STATUS = "ReservationStatus";
  public static final String FBA = "Fba";
  public static final String DETR_DEPARTURETERMINALNAME = "DepartureTerminalName";
  public static final String DETR_ARRIVALTERMINALNAME = "ArrivalTerminalName";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.AiritineraryConStr
 * JD-Core Version:    0.6.0
 */