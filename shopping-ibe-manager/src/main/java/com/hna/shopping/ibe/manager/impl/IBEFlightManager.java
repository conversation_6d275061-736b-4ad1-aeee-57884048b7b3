package com.hna.shopping.ibe.manager.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.config.ApplicationContextProvider;
import com.hna.shopping.ibe.config.ibe.IBEConfig;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.interfaces.dto.pricing.request.PIFlightSegment;
import com.hna.shopping.ibe.interfaces.dto.pricing.request.PricingRequest;
import com.hna.shopping.ibe.interfaces.dto.pricing.response.PricingResponse;
import com.hna.shopping.ibe.interfaces.dto.searchone.request.*;
import com.hna.shopping.ibe.interfaces.dto.searchone.response.SearchOneResponse;
import com.hna.shopping.ibe.manager.FlightManager;
import com.hna.shopping.ibe.manager.ibe.IBERetry;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ebuild.clientapi.axi.*;
import com.travelsky.ibe.client.*;
import com.travelsky.ibe.client.FFSegment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Vector;

@Component("ibeFlightManager")
@Slf4j
public class IBEFlightManager implements FlightManager {

    @Autowired(required = false)
    private IBERetry ibeRetry;

    @Override
    public AVResponse av(AVRequest avRequest) {
        // TODO Auto-generated method stub
//		return null;
        String airline = avRequest.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        AV av = IbeCmd.getIbeClient(airline, AV.class);
        AVResponse avResponse = new AVResponse();
        AvResult avResult;
        try {
            if (null == this.ibeRetry) {
                avResult = av.getAvailability(avRequest.getOrigin(), avRequest.getDestination(), avRequest.getDepart(), StringUtils.isEmpty(avRequest.getCarrier()) ? "ALL" : avRequest.getCarrier(), avRequest.isDirect());
            } else {
            /* 方法 */
                String method = "getAvailability";

			/* 参数 */
                Class[] parameterTypes = {String.class, String.class, Date.class,
                        String.class, boolean.class, boolean.class};

			/* 条件 */
                Object[] args = {avRequest.getOrigin(), avRequest.getDestination(), avRequest.getDepart(), airline, new Boolean(avRequest.isDirect()),
                        new Boolean(true)};
                String msg = "查询航班：" + "" + avRequest.getOrigin() + avRequest.getDestination() + "/" + DateUtil.toStringYMD(avRequest.getDepart());

			/* 执行指令 */
                avResult = (AvResult) this.ibeRetry.execute(av, method, parameterTypes,
                        args, msg);
            }

            for (int i = 0; i < avResult.getItemamount(); i++) {
                AvItem avItem = avResult.getItem(i);
                AVSegment avSegment0 = new AVSegment();
                avSegment0.setSegmentNumber(avItem.getSegmentnumber());
                avResponse.getSegments().add(avSegment0);
                for (int j = 0; j < avItem.getSegmentnumber(); j++) {
                    AvSegment avSegment = avItem.getSegment(j);// .get
                    AVFlightInfo flightInfo = new AVFlightInfo();
                    flightInfo.setOrigin(avSegment.getOrgcity());
                    flightInfo.setDestination(avSegment.getDstcity());
                    flightInfo.setDepartDate(avSegment.getDepdate());
                    flightInfo.setFlightNo(avSegment.getAirline());
                    flightInfo.setArriveDate(avSegment.getArridate());
                    flightInfo.setShare(avSegment.isCodeShare());
                    flightInfo.setStop(avSegment.getStopnumber());
                    flightInfo.setMeal(avSegment.getMealCode());
                    flightInfo.setPlaneStyle(avSegment.getPlanestyle());
                    flightInfo.setTerminal1(avSegment.getDepTerm());
                    flightInfo.setTerminal2(avSegment.getArriTerm());
                    flightInfo.setShareFlightNo(avSegment.getCarrier());
                    flightInfo.setArriTimeModify(avSegment.getArritimemodify());
                    avSegment0.getFlights().add(flightInfo);
                    // for(avSegment.get)
                    int k = 0;
                    while (k < 26) {
                        char cangwei = avSegment.getCangweiCodeSort(k);
                        if (cangwei == '-')
                            break;
                        if (cangwei == ' ') {
                            // strtmp.append("\n\t");
                            ++k;
                        } else {
                            AVCabinInfo avCabinInfo = new AVCabinInfo();
                            avCabinInfo.setCabin(String.valueOf(cangwei));
                            char c = avSegment.getCangweiinfoOfSort(cangwei);
                            avCabinInfo.setQuantity(String.valueOf(c));
                            if (c >= '1' && c <= '9') {
                                avCabinInfo.setInventory(Integer.parseInt(String.valueOf(c)));
                            } else if (c == 'A') {
                                avCabinInfo.setInventory(10);
                            }
//                            log.info("av result info:"+avSegment.getAirline() + avCabinInfo.getCabin()+":"+avCabinInfo.getInventory());
                            flightInfo.getCabins().add(avCabinInfo);
//							if (null == flightInfo.getMaxOpenCabin())
//								flightInfo.setMaxOpenCabin(avCabinInfo);
//							else if (flightInfo.getMaxOpenCabin().getNum() < avCabinInfo.getNum())
//								flightInfo.setMaxOpenCabin(avCabinInfo);
                            // 查找子舱位
//		                    String[] subCabins = avSegment.getSubClassList(cangwei);
//		                    if (subCabins != null) {
//		                        for (String subCabin : subCabins) {
//		                            String classInfo = avSegment.getSubClassInfoOf(cangwei, subCabin);
////		                            CabinDTO cabinSub = new CabinDTO();
//		                            avCabinInfo = new AVCabinInfo();
//		                            avCabinInfo.setCabin(subCabin);
//		                            avCabinInfo.setQuantity(classInfo);//.setQuantity(classInfo);
////		                            addCabin(segment.getCabins(), cabinSub);
//		                             c = classInfo!=null && classInfo.length()>0?classInfo.charAt(0):'_';
//		                             if (c >= '1' && c <= '9') {
//		 								avCabinInfo.setInventory(Integer.parseInt(String.valueOf(c)));
//		 							} else if (c == 'A') {
//		 								avCabinInfo.setInventory(10);
//		 							}
//		                            flightInfo.getCabins().add(avCabinInfo);
//		                        }
//		                    }
                            ++k;
                        }
                    }
//					break;
                }
            }
            avResponse.setSuccess(true);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            // e.printStackTrace();
            avResponse.setErrorCode(e.getClass().getSimpleName());
            avResponse.setErrorInfo(e.getMessage());
        }
        return avResponse;
    }

    @Override
    public FFResponse ff(FFRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getFlightNo().substring(0, 2);
//		int config = this.ibeConfigManager.getConfig(carrier);

        // return null;
        FFResponse response = new FFResponse();
        try {
            FF ff = IbeCmd.getIbeClient(airline, FF.class);
//			ff.setCurConfig(config);
            FFResult result = ff.flightTime(request.getFlightNo(), request.getDepartDate());
            response.setDepartDate(result.getFlightDate());
            response.setFlightNo(result.getAirNo());
            response.setPlaneModel(result.getPlaneModel());
            for (int i = 0; i < result.getSegmentCount(); i++) {
                FFSegment ffSegment = result.getSegmentAt(i);
                com.hna.shopping.ibe.interfaces.dto.FFSegment segment = new com.hna.shopping.ibe.interfaces.dto.FFSegment();
                BeanUtils.copyProperties(ffSegment, segment, "cabins", "cabinSeats");
                if (null != ffSegment.getCabins()) {
                    for (int j = 0; j < ffSegment.getCabins().size(); j++) {
                        FFCabinSeat cabinSeat = new FFCabinSeat();
                        cabinSeat.setCabins(ffSegment.getCabins().get(j).toString());
                        cabinSeat.setSeat(Integer.parseInt(ffSegment.getCabinSeats().get(j).toString()));
                        segment.getCabinSeats().add(cabinSeat);
                    }
                }
                response.getSegments().add(segment);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            // e.printStackTrace();
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        response.setSuccess(true);
        return response;
    }

    @Override
    public SearchOneResponse searchOne(SearchOneRequest request, String airline) {
        //处理默认入参参数
        initSearchOneReq(request, airline);
        return this.s1(request);
    }

    private SearchOneResponse s1(SearchOneRequest request) {
        String airline = request.getJourneyPreferences().getPlatingCarrier();
        SearchOneResponse response = new SearchOneResponse();
        SearchOne searchOne = IbeCmd.getIbeClient(airline + "GJP", SearchOne.class);
        String reqJson = "";
        String responeJson = "";
        long starttime = System.currentTimeMillis();
        try {
            reqJson = JSONObject.toJSONString(request);
//            responeJson = searchOne.getSearchOneStringRes(reqJson);
            response = JSONObject.parseObject(responeJson, SearchOneResponse.class);
        } catch (Exception e) {
            log.error(" searchOne.getSearchOneStringRes error, getTxnTraceKey: {} ", searchOne.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        } finally {
            log.info("searchOne.getSearchOneStringRes TxnTraceKey="
                    + searchOne.getTxnTraceKey() + "\n request = "
                    + reqJson + " \n respone = "
                    + responeJson);
            log.info("searchOne.getSearchOneStringRes time="
                    + (System.currentTimeMillis() - starttime));
        }
        return response;
    }

    private void initSearchOneReq(SearchOneRequest request, String airline) {
        IBEConfig ibeConfig = ApplicationContextProvider.getBean(String.format("IBEConfig%s", org.apache.commons.lang3.StringUtils.upperCase(airline + "GJP")), IBEConfig.class);
        //初始化-->agencies
        List<Agencies> agencies = JSONObject.parseArray(ibeConfig.getAgencies(), Agencies.class);
        if (!StringUtils.isEmpty(agencies)) {
            for (Agencies agency : agencies) {
                if (ibeConfig.getSystem() != null) {
                    agency.setSystem(ibeConfig.getSystem());
                }
                agency.setOfficeIdOwnedByCarrier(airline);
            }
            request.setAgencies(agencies);
        }
        //初始化-->journeyPreferences
        JourneyPreferences journeyPreferences = new JourneyPreferences();
        journeyPreferences.setPlatingCarrier(airline);
        request.setJourneyPreferences(journeyPreferences);

        //初始化-->preferences
        Preferences preferences = new Preferences();
        //带中转航班不返回
        //preferences.setAllowedStops("0");
        List<String> faresAllowedCarrierList = new LinkedList<String>();
        faresAllowedCarrierList.add(airline);
        preferences.setFaresAllowedCarriers(faresAllowedCarrierList);
        request.setPreferences(preferences);

        //判断是否是全舱位查询
        if (request.getSegments() != null) {
            for (Segments tmpSeg : request.getSegments()) {
                if (tmpSeg.getFlights() != null && tmpSeg.getFlights().size() > 0) {
                    request.getPreferences().setLowestFareMode("ALLCABIN");
                    break;
                }
            }
        }
    }


    @Override
    public PricingResponse pricing(PricingRequest request, String airline) {
        com.travelsky.ebuild.clientapi.axi.PricingRequest req = initPricingReq(request, airline);
        return this.pricingResult(req);
    }

    private com.travelsky.ebuild.clientapi.axi.PricingRequest initPricingReq(PricingRequest request, String airline) {
        com.travelsky.ebuild.clientapi.axi.PricingRequest req = new com.travelsky.ebuild.clientapi.axi.PricingRequest();
        IBEConfig ibeConfig = ApplicationContextProvider.getBean(String.format("IBEConfig%s", org.apache.commons.lang3.StringUtils.upperCase(airline + "GJP")), IBEConfig.class);
        JSONArray array = JSONArray.parseArray(ibeConfig.getAgencies());
        JSONObject object = array.getJSONObject(0);
        POSType pos = new POSType(object.getString("channel"), null, object.getString("pos"));
        // 添加IATA Number 信息
        if (object.getString("IATA_Number") != null) {
            SourceType source = new SourceType("13", object.getString("IATA_Number"), "IATA_Number");
            pos.addSource(source);
        }
        if (object.getString("iataNumber") != null) {
            SourceType source = new SourceType("13",object.getString("iataNumber"), "iataNumber");
            pos.addSource(source);
        }
        // 部门编码
        String departmentCode = object.getString("departmentCode");
        if (!StringUtils.isEmpty(departmentCode)) {
            SourceType departSource = new SourceType("6", departmentCode,"departmentCode");
            pos.addSource(departSource);
        }
        //标记是否有使用过航段
        boolean isUsed = false;
        Date nowDate = new Date();
        Date firstDepartureDateTime = new Date();
        // 添加OFFICECODE 信息
        SourceType officesource = new SourceType();
        officesource.setPseudoCityCode(ibeConfig.getClient().getOffice());
        pos.addSource(officesource);
        //赋值pos
        req.setPos(pos);
        //赋值Q税航段
        Vector<com.travelsky.ebuild.clientapi.axi.PIFlightSegment> flightSegments = new Vector<com.travelsky.ebuild.clientapi.axi.PIFlightSegment>();
        req.setFlightSegments(flightSegments);
        if (request.getFlightSegments() != null && request.getFlightSegments().size() > 0) {
            for (int i = 0; i < request.getFlightSegments().size(); i++) {

                PIFlightSegment fltSeg = request.getFlightSegments().get(i);
                com.travelsky.ebuild.clientapi.axi.PIFlightSegment piFlightSegment = new com.travelsky.ebuild.clientapi.axi.PIFlightSegment();
                piFlightSegment.setDepartureAirport(fltSeg.getDepartureAirport());
                piFlightSegment.setArrivalAirport(fltSeg.getArrivalAirport());
                piFlightSegment.setFlightNumber(fltSeg.getFlightNumber());
                piFlightSegment.setMarketingAirline(fltSeg.getAirline());
                piFlightSegment.setOperatingAirline(fltSeg.getAirline());
                piFlightSegment.setResBookDesigCode(fltSeg.getCabin());
                piFlightSegment.setDepartureDateTime(fltSeg.getDepartureDateTime());
                piFlightSegment.setArrivalDateTime(fltSeg.getArrivalDateTime());
                flightSegments.add(piFlightSegment);
                //只需看首航段是否使用过即可
                if (i == 0 && fltSeg.getDepartureDateTime().before(nowDate)){
                    isUsed = true;
                    firstDepartureDateTime = fltSeg.getDepartureDateTime();
                }
            }
        }
        // 旅行编码
        if (!StringUtils.isEmpty(request.getTourCode())) {
            req.setNegotiatedFareCode(request.getTourCode().trim());
        }
        //如果存在使用过航段，则查询历史运价
        if (isUsed){
            req.setAltTicketingDateTime(firstDepartureDateTime);
        }

        req.setTicketingCarrier(airline);
        req.setCurrencyCode(request.getCurrency());
        req.setPricingSource(com.travelsky.ebuild.clientapi.axi.PricingRequest.BOTH);
        if (request.getPrivatePrice() != null){
            if (request.getPrivatePrice()){
                req.setPricingSource(com.travelsky.ebuild.clientapi.axi.PricingRequest.PRIVATE);
            }else {
                req.setPricingSource(com.travelsky.ebuild.clientapi.axi.PricingRequest.PUBLISHED);
            }
        }

        req.setMaximumResponses(1);
        //赋值旅客信息
        PITravelerInfo piTravelerInfo = new PITravelerInfo();
        Vector<PIPassenger> passengers = new Vector<PIPassenger>();
        piTravelerInfo.setPassengers(passengers);
        req.setAirTravelerAvail(piTravelerInfo);
        if (request.getPassengers() != null && request.getPassengers().size() > 0) {
            for (PIPassenger pas : request.getPassengers()) {
                passengers.add(pas);
            }
        }
        return req;
    }

    private PricingResponse pricingResult(com.travelsky.ebuild.clientapi.axi.PricingRequest request) {
        String airline = request.getTicketingCarrier();
        PricingResponse response = new PricingResponse();
        Pricing pricing = IbeCmd.getIbeClient(airline + "GJP", Pricing.class);
        long starttime = System.currentTimeMillis();
        try {
            PricingResult respObj = pricing.doPricing(request);
            BeanUtils.copyProperties(respObj, response);

        } catch (Exception e) {
            log.error(" pricing.doPricing error, getTxnTraceKey: {} ", pricing.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        } finally {
            log.info("pricing.doPricing TxnTraceKey="
                    + pricing.getTxnTraceKey() + "\n request = "
                    + JSONObject.toJSONString(request) + " \n respone = "
                    +  JSONObject.toJSONString(response));
            log.info("pricing.doPricing time="
                    + (System.currentTimeMillis() - starttime));
        }

        return response;
    }

}
