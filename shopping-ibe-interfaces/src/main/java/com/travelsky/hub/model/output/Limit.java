/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Limit
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1558090237066554719L;
/*    */   private String name;
/*    */   private String limitDesc;
/*    */ 
/*    */   public String getName()
/*    */   {
/* 36 */     return this.name;
/*    */   }
/*    */ 
/*    */   public void setName(String name)
/*    */   {
/* 43 */     this.name = name;
/*    */   }
/*    */ 
/*    */   public String getLimitDesc()
/*    */   {
/* 50 */     return this.limitDesc;
/*    */   }
/*    */ 
/*    */   public void setLimitDesc(String limitDesc)
/*    */   {
/* 57 */     this.limitDesc = limitDesc;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Limit
 * JD-Core Version:    0.6.0
 */