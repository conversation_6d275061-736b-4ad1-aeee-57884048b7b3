/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Commission
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4165922024456596639L;
/*    */   private String percent;
/*    */   private String type;
/*    */ 
/*    */   public String getPercent()
/*    */   {
/* 36 */     return this.percent;
/*    */   }
/*    */ 
/*    */   public void setPercent(String percent)
/*    */   {
/* 43 */     this.percent = percent;
/*    */   }
/*    */ 
/*    */   public String getType()
/*    */   {
/* 50 */     return this.type;
/*    */   }
/*    */ 
/*    */   public void setType(String type)
/*    */   {
/* 57 */     this.type = type;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Commission
 * JD-Core Version:    0.6.0
 */