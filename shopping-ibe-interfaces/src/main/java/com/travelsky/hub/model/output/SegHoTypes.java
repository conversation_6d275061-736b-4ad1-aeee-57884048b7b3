/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SegHoTypes
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8739639664732134451L;
/*    */   private String holdType;
/*    */   private String arrivalStation;
/*    */   private String depatureStation;
/*    */   private String operationType;
/*    */ 
/*    */   public String getHoldType()
/*    */   {
/* 32 */     return this.holdType;
/*    */   }
/*    */ 
/*    */   public void setHoldType(String holdType)
/*    */   {
/* 39 */     this.holdType = holdType;
/*    */   }
/*    */ 
/*    */   public String getArrivalStation()
/*    */   {
/* 46 */     return this.arrivalStation;
/*    */   }
/*    */ 
/*    */   public void setArrivalStation(String arrivalStation)
/*    */   {
/* 53 */     this.arrivalStation = arrivalStation;
/*    */   }
/*    */ 
/*    */   public String getDepatureStation()
/*    */   {
/* 60 */     return this.depatureStation;
/*    */   }
/*    */ 
/*    */   public void setDepatureStation(String depatureStation)
/*    */   {
/* 67 */     this.depatureStation = depatureStation;
/*    */   }
/*    */ 
/*    */   public String getOperationType()
/*    */   {
/* 74 */     return this.operationType;
/*    */   }
/*    */ 
/*    */   public void setOperationType(String operationType)
/*    */   {
/* 81 */     this.operationType = operationType;
/*    */   }
/*    */ }

/* Location:           D:\海航科技文档\一步就坐\2022-01首都航网上值机生产包5.8.56\客户端需引入的jar\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SegHoTypes
 * JD-Core Version:    0.6.0
 */