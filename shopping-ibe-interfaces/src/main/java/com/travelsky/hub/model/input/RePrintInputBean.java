/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class RePrintInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String reissue;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String fromCity;
/*     */   private String boardingNumber;
/*     */   private String groupName;
/*     */   private String tourIndex;
/*     */   private String local;
/*     */   private String toCity;
/*     */   private String flightNumber;
/*     */   private String flightClass;
/*     */   private String passengerName;
/*     */   private String tktNumber;
/*     */ 
/*     */   public String getReissue()
/*     */   {
/*  27 */     return this.reissue;
/*     */   }
/*     */ 
/*     */   public void setReissue(String reissue)
/*     */   {
/*  34 */     this.reissue = reissue;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  47 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  54 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getLocal()
/*     */   {
/*  74 */     return this.local;
/*     */   }
/*     */ 
/*     */   public void setLocal(String local)
/*     */   {
/*  81 */     this.local = local;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  90 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  97 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 104 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 111 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 118 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 125 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 135 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 142 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 149 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 156 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 165 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 172 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/* 179 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/* 186 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 197 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 204 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 213 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 220 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 227 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 234 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.RePrintInputBean
 * JD-Core Version:    0.6.0
 */