/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class TransferOrRecheckInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1705586625668371931L;
/*     */   private String transferOrRecheckType;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String currentSubClass;
/*     */   private String arrivalAirport;
/*     */   private String passengerStatus;
/*     */   private String boardingNumber;
/*     */   private List<SeatInformation> seatInformations;
/*     */   private String ediType;
/*     */   private String departureAirport;
/*     */   private String flightSuffix;
/*     */ 
/*     */   public String getTransferOrRecheckType()
/*     */   {
/*  74 */     return this.transferOrRecheckType;
/*     */   }
/*     */ 
/*     */   public void setTransferOrRecheckType(String transferOrRecheckType)
/*     */   {
/*  81 */     this.transferOrRecheckType = transferOrRecheckType;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  88 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  95 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getAirline()
/*     */   {
/* 102 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/* 109 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 116 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 123 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 130 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 137 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getCurrentSubClass()
/*     */   {
/* 144 */     return this.currentSubClass;
/*     */   }
/*     */ 
/*     */   public void setCurrentSubClass(String currentSubClass)
/*     */   {
/* 151 */     this.currentSubClass = currentSubClass;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 158 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 165 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 172 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 179 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 186 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 193 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public List<SeatInformation> getSeatInformations()
/*     */   {
/* 200 */     return this.seatInformations;
/*     */   }
/*     */ 
/*     */   public void setSeatInformations(List<SeatInformation> seatInformations)
/*     */   {
/* 207 */     this.seatInformations = seatInformations;
/*     */   }
/*     */ 
/*     */   public String getEdiType()
/*     */   {
/* 214 */     return this.ediType;
/*     */   }
/*     */ 
/*     */   public void setEdiType(String ediType)
/*     */   {
/* 221 */     this.ediType = ediType;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 228 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 235 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TransferOrRecheckInformation
 * JD-Core Version:    0.6.0
 */