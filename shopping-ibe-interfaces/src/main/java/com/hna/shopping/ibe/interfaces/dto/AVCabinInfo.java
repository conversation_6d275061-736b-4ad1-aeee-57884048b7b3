package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
//@EqualsAndHashCode(callSuper=true)
@ApiModel
public class AVCabinInfo  implements Serializable {
	/**
	 * 
	 */
	
	private static final long serialVersionUID = -4211677687553181110L;
	    
    @ApiModelProperty(value = "舱位代码")
	private String cabin;
    @ApiModelProperty(value = "舱位开放数量, 原始数据")
    private String quantity;

    @ApiModelProperty(value = "舱位数量, 按 ET 习惯转换")
    private Integer inventory;

//    @ApiModelProperty(value = "舱位状态")
//    private String status;
	
}
