package com.hna.shopping.ibe.interfaces.dto.pricing.response;
import java.io.Serializable;
import java.util.List;

import lombok.Data;
@Data
public class TaxBreakDowns implements Serializable {

    private static final long serialVersionUID = -1386219171275146875L;
    private int amount;
    private String countryCode;
    private String currencyCode;
    private boolean eXEMPT;
    private boolean isEXEMPT;
    private String taxCode;
    private String taxName;
    private int taxableLocCount;
    private List<String> taxableLocations;

}