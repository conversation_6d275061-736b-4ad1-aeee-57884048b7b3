/*    */ package com.travelsky.hub.model.peentity.seatchart.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5442123377955816244L;
/*    */   private String seatNumber;
/*    */   private String deckCode;
/*    */   private String seatValue;
/*    */   private String seatValueEx;
/*    */   private String seatChar;
/*    */   private String fareAmount;
/*    */   private String fareCurrency;
/*    */   private String andOrIndicator;
/*    */   private String mileageFee;
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 12 */     this.seatNumber = seatNumber; } 
/* 12 */   public void setDeckCode(String deckCode) { this.deckCode = deckCode; } 
/* 12 */   public void setSeatValue(String seatValue) { this.seatValue = seatValue; } 
/* 12 */   public void setSeatValueEx(String seatValueEx) { this.seatValueEx = seatValueEx; } 
/* 12 */   public void setSeatChar(String seatChar) { this.seatChar = seatChar; } 
/* 12 */   public void setFareAmount(String fareAmount) { this.fareAmount = fareAmount; } 
/* 12 */   public void setFareCurrency(String fareCurrency) { this.fareCurrency = fareCurrency; } 
/* 12 */   public void setAndOrIndicator(String andOrIndicator) { this.andOrIndicator = andOrIndicator; } 
/* 12 */   public void setMileageFee(String mileageFee) { this.mileageFee = mileageFee;
/*    */   }
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 24 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public String getDeckCode()
/*    */   {
/* 29 */     return this.deckCode;
/*    */   }
/*    */ 
/*    */   public String getSeatValue()
/*    */   {
/* 34 */     return this.seatValue;
/*    */   }
/*    */ 
/*    */   public String getSeatValueEx()
/*    */   {
/* 39 */     return this.seatValueEx;
/*    */   }
/*    */ 
/*    */   public String getSeatChar()
/*    */   {
/* 44 */     return this.seatChar;
/*    */   }
/*    */ 
/*    */   public String getFareAmount()
/*    */   {
/* 49 */     return this.fareAmount;
/*    */   }
/*    */ 
/*    */   public String getFareCurrency()
/*    */   {
/* 54 */     return this.fareCurrency;
/*    */   }
/*    */ 
/*    */   public String getAndOrIndicator()
/*    */   {
/* 59 */     return this.andOrIndicator;
/*    */   }
/*    */ 
/*    */   public String getMileageFee()
/*    */   {
/* 64 */     return this.mileageFee;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.output.SeatInfo
 * JD-Core Version:    0.6.0
 */