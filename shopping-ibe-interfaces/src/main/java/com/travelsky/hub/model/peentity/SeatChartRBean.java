/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class SeatChartRBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5641924439420845122L;
/*     */   private String seatMap;
/*     */   private String planeType;
/*     */   private List<Rules> seatRules;
/*     */   private String airlineCode;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getSeatMap()
/*     */   {
/*  34 */     return this.seatMap;
/*     */   }
/*     */ 
/*     */   public void setSeatMap(String seatMap)
/*     */   {
/*  41 */     this.seatMap = seatMap;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  48 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  55 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/*  62 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/*  69 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  77 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  84 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public List<Rules> getSeatRules()
/*     */   {
/*  91 */     return this.seatRules;
/*     */   }
/*     */ 
/*     */   public void setSeatRules(List<Rules> seatRules)
/*     */   {
/*  98 */     this.seatRules = seatRules;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 105 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 112 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatChartRBean
 * JD-Core Version:    0.6.0
 */