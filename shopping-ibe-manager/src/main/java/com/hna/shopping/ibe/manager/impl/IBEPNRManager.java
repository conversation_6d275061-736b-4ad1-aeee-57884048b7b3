package com.hna.shopping.ibe.manager.impl;

import com.google.common.collect.Lists;
import com.hna.shopping.ibe.common.util.ConvertUtil;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.interfaces.dto.FareBox;
import com.hna.shopping.ibe.interfaces.dto.FareCalculation;
import com.hna.shopping.ibe.interfaces.dto.PNRFC;
import com.hna.shopping.ibe.interfaces.dto.PNRPassenger;
import com.hna.shopping.ibe.interfaces.dto.RTResult;
import com.hna.shopping.ibe.interfaces.dto.SSResult;
import com.hna.shopping.ibe.interfaces.dto.SellSeat;
import com.hna.shopping.ibe.manager.PNRManager;
import com.hna.shopping.ibe.manager.ibe.IBERetry;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ibe.client.ML;
import com.travelsky.ibe.client.MultiItem;
import com.travelsky.ibe.client.MultiResult;
import com.travelsky.ibe.client.MultiSegment;
import com.travelsky.ibe.client.FD;
import com.travelsky.ibe.client.FDItem;
import com.travelsky.ibe.client.FDResult;
import com.travelsky.ibe.client.Fares;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.client.pnr.SSSegment;
import com.travelsky.ibe.exceptions.*;
import com.travelsky.ibe.exceptions.IBEException;
import com.travelsky.util.QDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.Date;


@Component("ibePNRManager")
@Slf4j
public class IBEPNRManager implements PNRManager {

    @Autowired(required = false)
    private IBERetry ibeRetry;

    @Override
    public SplitPNRResponse splitPnr(SplitPnrRequest request) {
        // TODO Auto-generated method stub
        SplitPNRResponse response = new SplitPNRResponse();
        Vector<BookPassenger> passengers = new Vector<BookPassenger>();
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            if (null != request.getPassengers() && request.getPassengers().size() > 0) {
                for (PNRPassenger passenger : request.getPassengers()) {
                    BookPassenger bookPassenger = new BookPassenger();
                    ConvertUtil.map(passenger, bookPassenger);
                    passengers.add(bookPassenger);
                }
            }
            String pnrNo = pnrManage.splitPNR(request.getPnrNo(), passengers, request.getSplitNum());
            log.info(" splitPnr pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.info(" split PNR success ,new PNR: {} ", pnrNo);
            response.setSuccess(true);
            response.setPnrNo(pnrNo);
        } catch (Exception e) {

            log.error(" split PNR error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public SSResult ss(SellSeat sellSeat) {
        // TODO Auto-generated method stub
        SSResult response = new SSResult();
        String airline = sellSeat.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        com.travelsky.ibe.client.pnr.SellSeat ss = IbeCmd.getIbeClient(airline, com.travelsky.ibe.client.pnr.SellSeat.class);
        ss.setBif(sellSeat.getBif());
        ss.setMillisecondsBeforeEnvelop(sellSeat.getMillisecondsBeforeEnvelop());
        try {
            com.travelsky.ibe.client.pnr.SSResult result = ss.commit1();
            ConvertUtil.map(result, response);
            for (int i = 0; i < result.getSegmentsCount(); i++) {
                SSSegment ssSegment = result.getSegmentAt(i);
                com.hna.shopping.ibe.interfaces.dto.SSSegment ssSegment2 = new com.hna.shopping.ibe.interfaces.dto.SSSegment();
                ConvertUtil.map(ssSegment, ssSegment2);
                response.addSegment(ssSegment2);
            }
            log.info(" ss pnrNo: {} , getTxnTraceKey: {} ", result.getPnrno(), ss.getTxnTraceKey());

            response.setSuccess(true);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" ss error,sellSeat: {}, getTxnTraceKey: {} ", sellSeat.toString(), ss.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse addPNRInfo(AddPnrInfoRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String ok = pnrManage.addPnrInfo(request.getPnrNo(), request.getBookInfomation());
            log.info(" addPNRInfo pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
        } catch (PMSegmentContinuityException e) {
            //如果航变，要判断是否航变，封口，然后再写
            log.info("addPNRInfo error,  pnrNo: {} ,error:{}", request.getPnrNo(), e);
            String flightNo = ((BookSSR) request.getBookInfomation().getSsrs().get(0)).getSegidx();
            if (isSegmentUN(request.getAirlineCode(), request.getPnrNo(), flightNo)) {
                processClose(pnrManage, request.getPnrNo());//封口
                reAddPNRInfo(pnrManage, request.getPnrNo(), request.getBookInfomation(), response);
            } else {
                response.setErrorInfo("非航变航段,添加失败!");
            }

        } catch (Exception e) {
            log.info("addPNRInfo error,attempt force close and retry,getTxnTraceKey:{},error:{}", pnrManage.getTxnTraceKey(), e.getMessage());
            boolean closed = processClose(pnrManage, request.getPnrNo());//封口
            if (closed) {
                reAddPNRInfo(pnrManage, request.getPnrNo(), request.getBookInfomation(), response);
                return response;
            }
            log.error(" addPNRInfo error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    //重新添加pnr信息
    private void reAddPNRInfo(PnrManage pnrManage, String pnrNo, BookInformation bookInformation, BaseResponse response) {
        try {
            String ok = pnrManage.addPnrInfo(pnrNo, bookInformation);
            log.info(" reAddPNRInfo pnrNo: {}, getTxnTraceKey: {} ", pnrNo, pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
        } catch (Exception e) {
            log.error(" reAddPNRInfo pnrNo: {} , getTxnTraceKey: {} error:{}", pnrNo, pnrManage.getTxnTraceKey(), e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
    }

    //封口
    private boolean processClose(PnrManage pnrManage, String pnrNo) {
        BookInfomation bki = new BookInfomation();
        for (int i = 0; i < 3; i++) {
            try {
                log.info("processClose envelopType start ! pnr {}", pnrNo);
                //封口
                bki.setEnvelopType("K");
                pnrManage.addPnrInfo(pnrNo, bki);
                log.info("processClose envelopType end ! pnr {}", pnrNo);
                //RR
                log.info("processClose reconfirmAirSeg start ! pnr {}", pnrNo);
                pnrManage.reconfirmAirSeg(pnrNo, null, 0);
                log.info("processClose reconfirmAirSeg end ! pnr {}", pnrNo);
                return true;
            } catch (Exception e) {
                log.info("processClose fail ,retry! pnr {},error:{}", pnrNo, e);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e1) {
                    log.info("processClose InterruptedException fail,pnr {},error:{}", pnrNo, e1);
                }
            }
        }
        return false;
    }

    //判断是否有UN航变，是返回true，不是返回false
    private boolean isSegmentUN(String airline, String pnrNo, String flightNo) {
        boolean result = false;
        for (int i = 0; i < 3; i++) {
            try {
                log.info("isSegmentUN rt pnr {},airline:{},flightNo:{}", pnrNo, airline, flightNo);
                RT rt = IbeCmd.getIbeClient(airline, RT.class);
                com.travelsky.ibe.client.pnr.RTResult rtResult = rt.retrieve(pnrNo);
                if (rtResult == null) {
                    result = false;
                    Thread.sleep(10);
                    continue;
                }

                if (rtResult.getAirSegsCount() == 0) return false;//没找到返回false

                for (int y = 0; y < rtResult.getAirSegsCount(); y++) {
                    log.info("isSegmentUN flightNo:{}", rtResult.getAirSegAt(y).getAirNo());
                    if ("UN".equalsIgnoreCase(rtResult.getAirSegAt(y).getActionCode()) &&
                            rtResult.getAirSegAt(y).getAirNo().equalsIgnoreCase(flightNo)) {
                        log.info("isSegmentUN airline:{},pnr {},result:{}", airline, pnrNo, "true");
                        return true;
                    }
                }
                log.info("isSegmentUN airline:{},pnr {},result:{}", airline, pnrNo, "false");
                return false;
            } catch (Exception e) {
                result = false;
                log.info("isSegmentUN fail ,retry! airline:{},pnr {},error:{}", airline, pnrNo, e);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e1) {
                    log.info("isSegmentUN InterruptedException fail ,airline:{},pnr {},error:{}", airline, pnrNo, e1);
                }
            }
        }
        return false;
    }

    @Override
    public BaseResponse delPnrItem(DelPnrItemRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String ok = pnrManage.deleteItem(request.getPnrNo(), request.getIndexes());
            log.info(" delPnrItem pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());

            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" delPnrItem error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public RTResult rt(RTRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        RTResult response = new RTResult();
        RT rt = IbeCmd.getIbeClient(airline, RT.class);
        try {
            com.travelsky.ibe.client.pnr.RTResult rtResult = null;
            try {
                rtResult = rt.retrieve(request.getPnrNo());
            } catch (Exception e) {
                rtResult = rt.retrieve(request.getPnrNo());
            }
            log.info(" rt pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), rt.getTxnTraceKey());
            ConvertUtil.map(rtResult, response);
            for (int i = 0; i < rtResult.getFCsCount(); i++) {
                com.travelsky.ibe.client.pnr.PNRFC fc = rtResult.getFCAt(i);
                PNRFC pnrfc = response.getFCAt(i);
                for (int j = 0; j < fc.getSegmentCnt(); j++) {
                    com.travelsky.ibe.client.pnr.FareCalculation.fcitem fcitem = fc.getSegment(j);
                    FareCalculation.fcitem segment = pnrfc.newFcItem();
                    ConvertUtil.map(fcitem, segment);
                    pnrfc.insertSegment(j, segment);
//					pnrfc.insertSegment(j, fcitem.getOrg(), fcitem.getDst(), fcitem.getAircorp(), fcitem.getFltclass(), fcitem.getAmount(), f, NVB, NVA, E, stopOver, traveldirect, milesurcharge, msseg, q, qseg, branchStart, branchEnd, mileIntermediatePoint, fareseg);
                }
                for (int j = 0; j < fc.getExtraTaxCnt(); j++) {
                    pnrfc.insertTax(fc.getExtraTaxCurrency(j), fc.getExtraTaxAmount(j), fc.getExtraTaxCode(j));
                }
                for (int j = 0; j < fc.getRefundExtraTaxCnt(); j++) {
                    pnrfc.insertRTax(fc.getRefundExtraTaxCurrency(j), fc.getRefundExtraTaxAmount(j), fc.getRefundExtraTaxCode(j));
                }
                for (int j = 0; j < fc.getOriginalExtraTaxCnt(); j++) {
                    pnrfc.insertOTax(fc.getOriginalExtraTaxCurrency(j), fc.getOriginalExtraTaxAmount(j), fc.getOriginalExtraTaxCode(j));
                }
            }
            int[] types = new int[]{FareBox.F, FareBox.S, FareBox.A, FareBox.E, FareBox.R, FareBox.X};
            for (int i = 0; i < rtResult.getFNsCount(); i++) {
                com.travelsky.ibe.client.pnr.PNRFN fn = rtResult.getFNAt(i);
                com.hna.shopping.ibe.interfaces.dto.PNRFN pnrfc = response.getFNAt(i);
                int para = FareBox.T;
                for (int j = 0; j < fn.getTaxCnt(); j++) {
                    pnrfc.addTax(para, fn.getTaxCurrency(para, j), fn.getTaxAmount(para, j), fn.getTaxCode(para, j));
                }
                para = FareBox.O;
                for (int j = 0; j < fn.getOTaxCnt(); j++) {
                    pnrfc.addTax(para, fn.getTaxCurrency(para, j), fn.getTaxAmount(para, j), fn.getTaxCode(para, j));
                }
                for (int type : types) {
                    pnrfc.setAmount(type, fn.getCurrency(type), fn.getAmount(type));
                }
            }
            response.setSuccess(true);
        } catch (Exception e) {
            log.error(" rt error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), rt.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
            if (e instanceof RTException) {
                if (e.getMessage().contains("RTPNRCancelledException")){
                    response.setErrorCode(RTPNRCancelledException.class.getSimpleName());
                }
                if (e.getMessage().contains("RTNoPNRException")){
                    response.setErrorCode(RTNoPNRException.class.getSimpleName());
                }
                if (e.getMessage().contains("RTNotAuthorizedException")){
                    response.setErrorCode(RTNotAuthorizedException.class.getSimpleName());
                }
            }
        }
        return response;
    }

    @Override
    public boolean checkAllSegmentHK(RTRequest request) throws Exception {
        String airline = request.getAirlineCode();
        boolean result = false;
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        for (int i = 0; i < 3; i++) {
            try {
                log.info("checkAllSegmentHK rt pnr {}", request.getPnrNo());

                RT rt = IbeCmd.getIbeClient(airline, RT.class);

                com.travelsky.ibe.client.pnr.RTResult rtResult = rt.retrieve(request.getPnrNo());
                //log.info("checkAllSegmentHK rt pnr {} ,reResult = {}",request.getPnrNo(),rtResult);
                //所有航段都是HK
                //检查编码所有航段是否都为HK
                if (rtResult == null) {
                    result = false;
                    Thread.sleep(10);
                    continue;
                }
                log.info("checkAllSegmentHK rt pnr {} ,txnKey = {} , reResult = {}", request.getPnrNo(), rt.getTxnTraceKey(), rtResult.getOringinalRT());
                if (rtResult.getAirSegsCount() == 0) return false;

                for (int y = 0; y < rtResult.getAirSegsCount(); y++) {
                    if (!"HK".equalsIgnoreCase(rtResult.getAirSegAt(y).getActionCode())) {
                        log.info("checkAllSegmentHK rt pnr {} false", request.getPnrNo());
                        return false;
                    }
                }
                log.info("checkAllSegmentHK rt pnr {} true", request.getPnrNo());

                return true;

            } catch (Exception e) {
                result = false;
                log.info("checkAllSegmentHK fail ,retry! pnr:{},error:{}", request.getPnrNo(), e);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e1) {
                    log.info("checkAllSegmentHK fail ,pnr:{},error:{}", request.getPnrNo(), e1);
                }
            }
        }

        return result;
    }

    @Override
    public BaseResponse cancelPnr(CancelPnrRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String ok = cancelAirSeg(request, pnrManage);
            log.info(" cancelPnr pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());

            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" cancelPnr error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse cancelPnrK(CancelPnrRequest request) {
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline + "K", PnrManage.class);
        try {
            String ok = cancelAirSeg(request, pnrManage);
            log.info(" cancelPnrK pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());

            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" cancelPnrK error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    private String cancelAirSeg(CancelPnrRequest request, PnrManage pnrManage) throws Exception {
        String ok;
        if (request.isXepnr()) {
            ok = pnrManage.deletePnr(request.getPnrNo());
        } else {
            PNRAirSeg pnrAirSeg = null;
            if (null != request.getPnrSegment()) {
                pnrAirSeg = new PNRAirSeg();
                ConvertUtil.map(request.getPnrSegment(), pnrAirSeg);
            }
            ok = pnrManage.cancelAirSeg(request.getPnrNo(), pnrAirSeg);
        }
        return ok;
    }

    @Override
    public RTResult rtHistory(RTRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        RTResult response = new RTResult();
        RT rt = IbeCmd.getIbeClient(airline, RT.class);
        try {
            com.travelsky.ibe.client.pnr.RTResult rtResult = rt.retrieveCompletely(request.getPnrNo());
            log.info(" rtHistrory pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), rt.getTxnTraceKey());
            ConvertUtil.map(rtResult, response);
            response.setSuccess(true);
        } catch (Exception e) {
            log.error(" rtHistrory error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), rt.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse changePaxInfo(ChangePaxInfoReq changePaxInfoReq) {
        String airline = changePaxInfoReq.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String result = pnrManage.changePaxId(changePaxInfoReq.getPnrNo(), changePaxInfoReq.getOldPassenger(), changePaxInfoReq.getNewPassenger(), changePaxInfoReq.getFoid());
            log.info(" changePaxInfo  pnrNo: {}, getTxnTraceKey: {}", changePaxInfoReq.getPnrNo(), pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(result)) {
                response.setSuccess(true);

            } else {
                response.setErrorInfo(result);
            }
        } catch (Exception e) {
            log.error("", e);
            log.error(" changePaxInfo error pnrNo: {}, getTxnTraceKey: {}", changePaxInfoReq.getPnrNo(), pnrManage.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse updatePnr(UpdatePnrReq updatePnrReq) {
        String airline = updatePnrReq.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            int[] delIndexes = com.google.common.primitives.Ints.toArray(updatePnrReq.getDelIndexes());
            String result = pnrManage.updatePnr(updatePnrReq.getPnr(), updatePnrReq.getBookInformation(), delIndexes);
            log.info("Manager.updatePnr airline:{}, pnrNo: {}, getTxnTraceKey: {}", updatePnrReq.getAirlineCode(), updatePnrReq.getPnr(), pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(result)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(result);
            }
        } catch (Exception e) {
            log.error("Manager.updatePnr error pnrNo: {}, getTxnTraceKey: {},error:{}", updatePnrReq.getPnr(), pnrManage.getTxnTraceKey(), e.getMessage(), e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse reconfirmAirSeg(ReconfirmAirSegReq reconfirmAirSegReq) {
        String airline = reconfirmAirSegReq.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String result = pnrManage.reconfirmAirSeg(reconfirmAirSegReq.getPnrNo(), reconfirmAirSegReq.getPnrAirSeg(), PnrManage.RECONFIRM_ALL_POSSIBLE_ACTION);
            log.info("Manager.reconfirmAirSeg airline:{}, pnrNo: {}, getTxnTraceKey: {}", reconfirmAirSegReq.getAirlineCode(), reconfirmAirSegReq.getPnrNo(), pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(result)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(result);
            }
        } catch (Exception e) {
            log.error("Manager.reconfirmAirSeg error pnrNo: {}, getTxnTraceKey: {},error:{}", reconfirmAirSegReq.getPnrNo(), pnrManage.getTxnTraceKey(), e.getMessage(), e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse confirmAirSeg(ReconfirmAirSegRQ reconfirmAirSegReq) {
        String airline = reconfirmAirSegReq.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        PnrManage pnrManage = IbeCmd.getIbeClient(airline + "K", PnrManage.class);
        try {
            String result = pnrManage.confirmAirSeg(reconfirmAirSegReq.getPnrNo(), ConvertUtil.map(reconfirmAirSegReq.getPnrAirSeg(), PNRAirSeg.class));
            log.info("Manager.confirmAirSeg airline:{}, pnrNo: {}, getTxnTraceKey: {}", reconfirmAirSegReq.getAirlineCode(), reconfirmAirSegReq.getPnrNo(), pnrManage.getTxnTraceKey());
            if ("OK".equalsIgnoreCase(result)) {
                response.setSuccess(true);

            } else {
                response.setErrorInfo(result);
            }
        } catch (Exception e) {
            log.error("Manager.confirmAirSeg error pnrNo: {}, getTxnTraceKey: {},error:{}", reconfirmAirSegReq.getPnrNo(), pnrManage.getTxnTraceKey(), e.getMessage(), e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse addPnrTKNE(AddPnrInfoRequest request) throws Exception {
        BaseResponse response = new BaseResponse();


        PnrManage pnrManage = IbeCmd.getIbeClient(request.getAirlineCode(), PnrManage.class);
        BookInfomation bki = request.getBookInfomation();
        // 添加新TKNE

        bki.setEnvelopType("K");
        String result = pnrManage.addPnrInfo(request.getPnrNo(), bki);
        log.info("Manager.addPnr SSR_TKNE airline:{}, pnrNo: {}", request.getAirlineCode(), request.getPnrNo());
        if ("OK".equalsIgnoreCase(result)) {

            response.setSuccess(true);

        } else {
            response.setErrorInfo(result);
        }

        return response;
    }

    @Override
    public MLResponse ml(MLRequest request) {
        ML ml = IbeCmd.getIbeClient(request.getAirlineCode(), ML.class);
        MLResponse resp = null;
        long time = 0;
        try {
            long starttime = System.currentTimeMillis();
            Date flightDate = com.hna.eking.util.date.DateUtil.StringToDate(request.getFlightDate(), "yyyy-MM-dd HH:mm");
            MultiResult result = ml.multi(null, request.getFlightNo(), flightDate);
            time = System.currentTimeMillis() - starttime;
            resp = new MLResponse();
            buildMultiResp(result, resp, request, ml.getOfficeCode());


        } catch (Exception e) {
            log.error("ml.multi error getTxnTraceKey: {},error:{}", ml.getTxnTraceKey(), e.getMessage(), e);
        } finally {
            log.info("ml.multi getTxnTraceKey: {}, times :{} ,flightNo: {},flightDate:{},", ml.getTxnTraceKey(), time, request.getFlightNo(), request.getFlightDate());
        }
        log.info("ml.multi  getTxnTraceKey: {},resp :{}", ml.getTxnTraceKey(), resp);
        return resp;
    }

    private void buildMultiResp(MultiResult result, MLResponse resp, MLRequest request, String officeCode) {
        log.info("Multi result segment = " + result.getSegmentNumber() + " ,flightNo = " + request.getFlightNo() + " ,flightDate = " + request.getFlightDate());
        List<MLSegmentItem> items = Lists.newArrayList();
        resp.setItems(items);
        for (int i = 0; i < result.getSegmentNumber(); i++) {
            MultiSegment multiSegment = null;
            try {
                multiSegment = result.getSegmentAt(i);
                //入参起飞落地不为空，根据起飞落地筛选指令航段
                if ((!TextUtils.isEmpty(request.getDepCode()) && !TextUtils.isEmpty(request.getArrCode()))
                        && (!request.getDepCode().equals(multiSegment.getOrg()) || !request.getArrCode().equals(multiSegment.getDst()))) {
                    continue;
                }
            } catch (Exception e) {
                log.error("build multi segment ,error:{}", e.getMessage(), e);
            }
            int itemNum = multiSegment.getItemNumber();
            log.info("each multiSegment index = " + i + ";item num = " + itemNum);
            for (int j = 0; j < itemNum; j++) {
                try {
                    MultiItem multiItem = multiSegment.getItemAt(j);
                    //过滤不是本系统office号的编码
                    if (!multiItem.getOfficeCode().equalsIgnoreCase(officeCode)) {
                        continue;
                    }
                    //非指定编码状态过滤
                    if (!TextUtils.isEmpty(request.getActionCode()) && !multiItem.getActioncode().equalsIgnoreCase(request.getActionCode())) {
                        continue;
                    }
                    MLSegmentItem item = new MLSegmentItem();
                    item.setActionCode(multiItem.getActioncode());
                    String createDate = multiItem.getDateCreate().trim();
                    if (createDate.length() == 7)
                        createDate = com.travelsky.util.QDateTime.dateToString(com.travelsky.util.QDateTime.stringToDate(createDate, "ddmmmyy"), "yyyy-mm-dd");
                    else if (createDate.length() == 5)
                        createDate = com.travelsky.util.QDateTime.dateToString(QDateTime.stringToDate(createDate, "ddmmm"), "yyyy-mm-dd");
                    item.setCreateTime(createDate);
                    item.setOfficeCode(multiItem.getOfficeCode());
                    item.setPassengerName(multiItem.getPassengername());
                    item.setPnrNo(multiItem.getPnrno());
                    item.setArrCode(multiSegment.getDst());
                    item.setDepCode(multiSegment.getOrg());
                    items.add(item);
                } catch (Exception e) {
                    log.error("build multi MLSegmentItem ,error:{}", e.getMessage(), e);
                }
            }
        }
    }

    public List<FDBasePriceRS> fdBasePrice(String airline, String depCode, String arrCode, String flightDate) {
        List<FDBasePriceRS> fdBasePriceRSs = Lists.newArrayList();
        try {

            FDBasePriceRS FDBasePriceRS0 = fdYBasePrice(airline, depCode, arrCode, flightDate);

            if (fdBasePriceRSs == null) return fdBasePriceRSs;

            fdBasePriceRSs.add(FDBasePriceRS0);

            Date endDate = DateUtil.toDateYYYY_MM_DD(FDBasePriceRS0.getFlightDateEnd());
            Calendar c = Calendar.getInstance();
            c.setTime(endDate);
            c.add(Calendar.DATE, 1);

            FDBasePriceRS FDBasePriceRS1 = fdYBasePrice(airline, depCode, arrCode, DateUtil.toStringYYYY_MM_DD(c.getTime()));
            if (FDBasePriceRS1 != null) {
                fdBasePriceRSs.add(FDBasePriceRS1);
            }


        } catch (Exception e) {
            e.getMessage();
        }
        return fdBasePriceRSs;
    }

    public List<FDBasePriceRS> fd(String airline, String depCode, String arrCode, String flightDate) {
        List<FDBasePriceRS> fdBasePriceRSs = Lists.newArrayList();
        try {

            FDBasePriceRS FDBasePriceRS0 = fdYBasePrice(airline, depCode, arrCode, flightDate);

            if (fdBasePriceRSs == null) return fdBasePriceRSs;

            fdBasePriceRSs.add(FDBasePriceRS0);


        } catch (Exception e) {
            e.getMessage();
        }
        return fdBasePriceRSs;
    }

    @Override
    public List<FDBasePriceRS> fdBySeller(String airline, String seller, String depCode, String arrCode, String flightDate) {
        return null;
    }

    @Override
    public BaseResponse reConfirmAirSeg(ReconfirmAirSegReq request) throws IBEException {
        PnrManage pnrManage = IbeCmd.getIbeClient(request.getAirlineCode(), PnrManage.class);
        BaseResponse response = new BaseResponse();


        String result = pnrManage.reconfirmAirSeg(request.getPnrNo(),
                ConvertUtil.map(request.getPnrAirSeg(), PNRAirSeg.class),
                PnrManage.RECONFIRM_CHECK_STRICTLY);
        if ("OK".equalsIgnoreCase(result)) {
            response.setSuccess(true);

        } else {
            response.setErrorInfo(result);
        }


        return response;

    }


    @Override
    public BaseResponse changeAirSeg(String airlineCode, String pnr, BookAirSeg org, BookAirSeg dest) throws IBEException {
        PnrManage pnrManage = IbeCmd.getIbeClient(airlineCode, PnrManage.class);
        BaseResponse response = new BaseResponse();

        String result = pnrManage.changeAirSeg(pnr, org, dest);
        log.info("result :{}", result);
        if ("OK".equalsIgnoreCase(result)) {
            response.setSuccess(true);

        } else {
            response.setErrorInfo(result);
        }
        return response;

    }

    private FDBasePriceRS fdYBasePrice(String airline, String depCode, String arrCode, String flightDate) {
        try {
            long t = System.currentTimeMillis();
            FD fd = IbeCmd.getIbeClient(airline, FD.class);

            FDResult fdResult = null;
            if (StringUtils.isEmpty(flightDate)) {
                fdResult = fd.findPrice(depCode, arrCode, airline);
            } else {
                fdResult = fd.findPrice(depCode, arrCode, DateUtil.stringToDate(flightDate), airline);
            }
            if (fdResult == null) return null;

            Fares fares = fdResult.getFare();
            FDItem fdItem = fares.getFD(airline, "Y");
            FDBasePriceRS fdBasePriceRS = new FDBasePriceRS();
            fdBasePriceRS.setAirline(airline);
            fdBasePriceRS.setDepCode(depCode);
            fdBasePriceRS.setArrCode(arrCode);
            fdBasePriceRS.setBookDate(DateUtil.dateToString(new Date()));
            fdBasePriceRS.setCabin("Y");
            fdBasePriceRS.setPrice(new Double(fdItem.getOnewayPrice()).intValue());
            fdBasePriceRS.setDistance(new Integer(fdResult.getTpm()));


            Calendar c = Calendar.getInstance();
            c.setTime(DateUtil.stringToUsDateDDMMMyy(fdItem.getInvalidDate()));
            int y = c.get(Calendar.YEAR);

            if (y < 2000) {
                String ny = 20 + ((y + "").substring(2));
                c.set(Calendar.YEAR, Integer.parseInt(ny));
            }

            fdBasePriceRS.setFlightDateStart(DateUtil.toStringYYYY_MM_DD(DateUtil.stringToUsDateDDMMMyy(fdItem.getValidDate())));
            fdBasePriceRS.setFlightDateEnd(DateUtil.toStringYYYY_MM_DD(c.getTime()));
            //for(FDBasePriceRS iFdBasePriceRS: fdBasePriceRS){
//            String format = "insert into uni_travelsky_base_price (airline,dep_code,arr_code,book_date,flight_date_start,flight_date_end,price,distance) values ('%s','%s','%s','%s','%s','%s',%s,%s);";
//            String sql = String.format(format,fdBasePriceRS.getAirline(),fdBasePriceRS.getDepCode(),fdBasePriceRS.getArrCode(),fdBasePriceRS.getBookDate(),fdBasePriceRS.getFlightDateStart(),fdBasePriceRS.getFlightDateEnd(),fdBasePriceRS.getPrice(),fdBasePriceRS.getDistance());
//            log.info(sql);
            //log.info("fdYBasePrice airline = {} depCode = {} arrCode = {} flightDate = {} t = {} rs={}",airline, depCode,arrCode,flightDate,(System.currentTimeMillis()-t), JSON.toJSONString(fdBasePriceRS));

            //}
            return fdBasePriceRS;
        } catch (Exception e) {
            //log.error("fdYBasePrice airline = {} depCode = {} arrCode = {} flightDate = {} e={}",airline,depCode,arrCode,flightDate,e.getMessage());
        }
        return null;
    }

    public FDBasePriceRS fdTax(String airline, String depCode, String arrCode, String flightDate, String passType) {
        try {
            long t = System.currentTimeMillis();
            FD fd = IbeCmd.getIbeClient(airline, FD.class);

            FDResult fdResult = fd.findPrice(depCode, arrCode, QDateTime.dateToString(DateUtil.stringToDate(flightDate),"ddmmmyy"), airline,"",passType,false);
            if (fdResult == null) return null;

            Fares fares = fdResult.getFare();
            FDItem fdItem = fares.getFD(airline, "Y");
            FDBasePriceRS fdBasePriceRS = new FDBasePriceRS();
            fdBasePriceRS.setAirline(airline);
            fdBasePriceRS.setDepCode(depCode);
            fdBasePriceRS.setArrCode(arrCode);
//            fdBasePriceRS.setBookDate(DateUtil.dateToString(new Date()));
            fdBasePriceRS.setCabin("Y");
            fdBasePriceRS.setPrice(new Double(fdItem.getOnewayPrice()).intValue());
            fdBasePriceRS.setDistance(new Integer(fdResult.getTpm()));
            fdBasePriceRS.setFuelTax(fdResult.getFuelTax(0));
            fdBasePriceRS.setAirportTax(fdResult.getAirportTax(0));


            Calendar c = Calendar.getInstance();
            c.setTime(DateUtil.stringToUsDateDDMMMyy(fdItem.getInvalidDate()));
            int y = c.get(Calendar.YEAR);

            if (y < 2000) {
                String ny = 20 + ((y + "").substring(2));
                c.set(Calendar.YEAR, Integer.parseInt(ny));
            }

            fdBasePriceRS.setFlightDateStart(DateUtil.toStringYYYY_MM_DD(DateUtil.stringToUsDateDDMMMyy(fdItem.getValidDate())));
            fdBasePriceRS.setFlightDateEnd(DateUtil.toStringYYYY_MM_DD(c.getTime()));
            //for(FDBasePriceRS iFdBasePriceRS: fdBasePriceRS){
//            String format = "insert into uni_travelsky_base_price (airline,dep_code,arr_code,book_date,flight_date_start,flight_date_end,price,distance) values ('%s','%s','%s','%s','%s','%s',%s,%s);";
//            String sql = String.format(format,fdBasePriceRS.getAirline(),fdBasePriceRS.getDepCode(),fdBasePriceRS.getArrCode(),fdBasePriceRS.getBookDate(),fdBasePriceRS.getFlightDateStart(),fdBasePriceRS.getFlightDateEnd(),fdBasePriceRS.getPrice(),fdBasePriceRS.getDistance());
//            log.info(sql);
            //log.info("fdYBasePrice airline = {} depCode = {} arrCode = {} flightDate = {} t = {} rs={}",airline, depCode,arrCode,flightDate,(System.currentTimeMillis()-t), JSON.toJSONString(fdBasePriceRS));

            //}
            return fdBasePriceRS;
        } catch (Exception e) {
            //log.error("fdYBasePrice airline = {} depCode = {} arrCode = {} flightDate = {} e={}",airline,depCode,arrCode,flightDate,e.getMessage());
        }
        return null;
    }

    public FDBasePriceAndCabinPriceRS fdPriceAndTax(String airline, String depCode, String arrCode, String flightDate, String passType) {
        try {
            long t = System.currentTimeMillis();
            FD fd = IbeCmd.getIbeClient(airline, FD.class);

            FDResult fdResult = fd.findPrice(depCode, arrCode, QDateTime.dateToString(DateUtil.stringToDate(flightDate),"ddmmmyy"), airline,"",passType,true);
            if (fdResult == null) return null;

            Fares fares = fdResult.getFare();
            FDItem fdItem = fares.getFD(airline, "Y");
            FDBasePriceAndCabinPriceRS fdPriceRS = new FDBasePriceAndCabinPriceRS();
            fdPriceRS.setYbPrice(fdItem.getOnewayPrice());
            fdPriceRS.setAirline(airline);
            fdPriceRS.setDepCode(depCode);
            fdPriceRS.setArrCode(arrCode);

            fdPriceRS.setDistance(new Integer(fdResult.getTpm()));
            fdPriceRS.setFuelTax(fdResult.getFuelTax(0));
            fdPriceRS.setAirportTax(fdResult.getAirportTax(0));

            Vector<String> cabinDesc = fdResult.getV_CabinDesc();
            Vector<Integer> price = fdResult.getV_SinglePrice();
//            Vector<String> cabinType = fdResult.getV_BasicCabinType();
            List<String> cabinList = new ArrayList<String>();
            cabinList.addAll(cabinDesc);
//            cabinDesc
            fdPriceRS.setCabinList(cabinList);
            List<Integer> priceList = new ArrayList<Integer>();
            priceList.addAll(price);
            fdPriceRS.setPriceList(priceList);

            Calendar c = Calendar.getInstance();
            c.setTime(DateUtil.stringToUsDateDDMMMyy(fdItem.getInvalidDate()));
            int y = c.get(Calendar.YEAR);

            if (y < 2000) {
                String ny = 20 + ((y + "").substring(2));
                c.set(Calendar.YEAR, Integer.parseInt(ny));
            }

            fdPriceRS.setFlightDateStart(DateUtil.toStringYYYY_MM_DD(DateUtil.stringToUsDateDDMMMyy(fdItem.getValidDate())));
            fdPriceRS.setFlightDateEnd(DateUtil.toStringYYYY_MM_DD(c.getTime()));

            return fdPriceRS;
        } catch (Exception e) {
            log.error("fdYBasePrice airline = {} depCode = {} arrCode = {} flightDate = {} e={}",airline,depCode,arrCode,flightDate,e.getMessage());
        }
        return null;
    }

    @Override
    public String removeName(RemoveNameRequest request) throws Exception {
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String removeNameResult = null;
            try {
                removeNameResult = pnrManage.removeName(request.getPnrNo(), request.getPassengers());
            } catch (Exception e) {
                removeNameResult = pnrManage.removeName(request.getPnrNo(), request.getPassengers());
            }
            log.info("removeName, pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            return removeNameResult;
        } catch (Exception e) {
            log.error("removeName error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
        }
        return "";
    }
}