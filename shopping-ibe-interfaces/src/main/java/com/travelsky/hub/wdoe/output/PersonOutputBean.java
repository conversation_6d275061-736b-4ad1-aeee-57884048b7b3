/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PersonOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3363021602079229674L;
/*  20 */   private String bordingTime = "";
/*     */ 
/*  25 */   private String departureTime = "";
/*     */ 
/*  31 */   private String arrivalTime = "";
/*     */ 
/*  37 */   private String boardingGateNumber = "";
/*     */ 
/*  42 */   private List passengers = new ArrayList();
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/*  52 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String i)
/*     */   {
/*  59 */     this.bordingTime = i;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/*  66 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String i)
/*     */   {
/*  73 */     this.departureTime = i;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/*  80 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String i)
/*     */   {
/*  87 */     this.arrivalTime = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/*  94 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String i)
/*     */   {
/* 101 */     this.boardingGateNumber = i;
/*     */   }
/*     */ 
/*     */   public PAAcceptedPsrBean getPassenger(int i)
/*     */   {
/* 109 */     return (PAAcceptedPsrBean)this.passengers.get(i);
/*     */   }
/*     */ 
/*     */   public void addPassenger(PAAcceptedPsrBean i)
/*     */   {
/* 116 */     this.passengers.add(i);
/*     */   }
/*     */ 
/*     */   public int getPassengersSize()
/*     */   {
/* 123 */     return this.passengers.size();
/*     */   }
/*     */ 
/*     */   public void clearPassengers()
/*     */   {
/* 129 */     this.passengers.clear();
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 135 */     clearPassengers();
/* 136 */     this.bordingTime = "";
/* 137 */     this.boardingGateNumber = "";
/* 138 */     this.departureTime = "";
/* 139 */     this.arrivalTime = "";
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PersonOutputBean
 * JD-Core Version:    0.6.0
 */