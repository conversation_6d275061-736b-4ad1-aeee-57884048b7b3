/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class NewSeatMapInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String flightOriCity;
/*    */   private String flightDesCity;
/*    */   private String paxLevel;
/*    */ 
/*    */   public String getFlightOriCity()
/*    */   {
/* 33 */     return this.flightOriCity;
/*    */   }
/*    */ 
/*    */   public void setFlightOriCity(String flightOriCity)
/*    */   {
/* 40 */     this.flightOriCity = flightOriCity;
/*    */   }
/*    */ 
/*    */   public String getFlightDesCity()
/*    */   {
/* 47 */     return this.flightDesCity;
/*    */   }
/*    */ 
/*    */   public void setFlightDesCity(String flightDesCity)
/*    */   {
/* 54 */     this.flightDesCity = flightDesCity;
/*    */   }
/*    */ 
/*    */   public String getPaxLevel()
/*    */   {
/* 62 */     return this.paxLevel;
/*    */   }
/*    */ 
/*    */   public void setPaxLevel(String paxLevel)
/*    */   {
/* 69 */     this.paxLevel = paxLevel;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.NewSeatMapInputBean
 * JD-Core Version:    0.6.0
 */