/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class HbpuBagInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6429690000119273445L;
/*    */   private TxnInfo txnInfo;
/*    */   private AddBaggageInputBean hbpuBagReq;
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 30 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 37 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ 
/*    */   public AddBaggageInputBean getHbpuBagReq()
/*    */   {
/* 45 */     return this.hbpuBagReq;
/*    */   }
/*    */ 
/*    */   public void setHbpuBagReq(AddBaggageInputBean hbpuBagReq)
/*    */   {
/* 52 */     this.hbpuBagReq = hbpuBagReq;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.HbpuBagInputBean
 * JD-Core Version:    0.6.0
 */