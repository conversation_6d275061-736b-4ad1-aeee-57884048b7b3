package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRSSR_TKNE extends PNRSSR {

	/**
	 * 
	 */
	private static final long serialVersionUID = -8196993700523875807L;
	char cabin = ' ';                                                          
	String citypair = null;                                                    
	String couponOtherInfo = "";                                               
	                                                                           
	String date = null;                                                        
	String flightNo = null;                                                    
	boolean infanttkt = false;                                                 
	int personNum = 0;                                                         
	                                                                           
	String tktIndex = null;                                                    
	String tktNo = null;                                                       
	                                                                           
	public String getActionCode()                                              
	{                                                                          
	  return this.actionCode;                                                  
	}                                                                          
	                                                                           
	public String getAirline()                                                 
	{                                                                          
	  return this.airline;                                                     
	}                                                                          
	                                                                           
	public char getCabin()                                                     
	{                                                                          
	  return this.cabin;                                                       
	}                                                                          
	                                                                           
	public String getCitypair()                                                
	{                                                                          
	  return this.citypair;                                                    
	}                                                                          
	                                                                           
	public String getCouponOtherInfo()                                         
	{                                                                          
	  return this.couponOtherInfo;                                             
	}                                                                          
	                                                                           
	public String getDate()                                                    
	{                                                                          
	  return this.date;                                                        
	}                                                                          
	                                                                           
	public String getFlightNo()                                                
	{                                                                          
	  return this.flightNo;                                                    
	}                                                                          
	                                                                           
	public int getPersonNum()                                                  
	{                                                                          
	  return this.personNum;                                                   
	}                                                                          
	                                                                           
	public String getTktIndex()                                                
	{                                                                          
	  return this.tktIndex;                                                    
	}                                                                          
	                                                                           
	public String getTktNo()                                                   
	{                                                                          
	  return this.tktNo;                                                       
	}                                                                          
	                                                                           
	public boolean isInfanttkt()                                               
	{                                                                          
	  return this.infanttkt;                                                   
	}                                                                          
	                                                                           
	public void setActionCode(String newActionCode)                            
	{                                                                          
	  this.actionCode = newActionCode;                                         
	}                                                                          
	                                                                           
	public void setAirline(String newAirline)                                  
	{                                                                          
	  this.airline = newAirline;                                               
	}                                                                          
	                                                                           
	public void setCabin(char newCabin)                                        
	{                                                                          
	  this.cabin = newCabin;                                                   
	}                                                                          
	                                                                           
	public void setCitypair(String newCitypair)                                
	{                                                                          
	  this.citypair = newCitypair;                                             
	}                                                                          
	                                                                           
	public void setCouponOtherInfo(String couponOtherInfo)                     
	{                                                                          
	  this.couponOtherInfo = couponOtherInfo;                                  
	}                                                                          
	                                                                           
	public void setDate(String newDate)                                        
	{                                                                          
	  this.date = newDate;                                                     
	}                                                                          
	                                                                           
	public void setFlightNo(String newFlightNo)                                
	{                                                                          
	  this.flightNo = newFlightNo;                                             
	}                                                                          
	                                                                           
	public void setInfanttkt(boolean newInfanttkt)                             
	{                                                                          
	  this.infanttkt = newInfanttkt;                                           
	}                                                                          
	                                                                           
	public void setPersonNum(int newPersonNum)                                 
	{                                                                          
	  this.personNum = newPersonNum;                                           
	}                                                                          
	                                                                           
	public void setTktIndex(String newTktIndex)                                
	{                                                                          
	  this.tktIndex = newTktIndex;                                             
	}                                                                          
	                                                                           
	public void setTktNo(String newTktNo)                                      
	{                                                                          
	  this.tktNo = newTktNo;                                                   
	}                                                                          
	                                                                           
	public String toString()                                                   
	{                                                                          
	  try                                                                      
	  {                                                                        
	    return this.index + ".SSR  " +                                     
	      getAirline() +                                                       
	      " " +                                                                
	      getSSRType() +                                                       
	      " " +                                                                
	      getActionCode() +                                                    
	      " " +                                                                
	      this.personNum +                                                     
	      " " +                                                                
	      getCitypair() +                                                      
	      " " +                                                                
	      getFlightNo() +                                                      
	      " " +                                                                
	      getCabin() +                                                         
	      " " +                                                                
	      getDate() +                                                          
	      " " +                                                                
	      getTktNo() +                                                         
	      " " +                                                                
	      getTktIndex() +                                                      
	      " " +                                                                
	      getPsgrID() +                                                        
	      "  " +                                                               
	      ((isInfanttkt()) ? "婴儿票" : "普通票"); } catch (Exception e) {     
	  }                                                                        
	  return super.toString();                                                 
	}                                                                          

}
