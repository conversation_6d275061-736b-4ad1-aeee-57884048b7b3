/*    */ package com.travelsky.hub.model.peentity.reserve.create.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PaymentCard
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1604684746459822341L;
/*    */   private String cardNumber;
/*    */   private String cardCode;
/*    */   private String serialNumber;
/*    */ 
/*    */   public void setCardCode(String cardCode)
/*    */   {
/* 44 */     this.cardCode = cardCode;
/*    */   }
/*    */ 
/*    */   public String getCardCode()
/*    */   {
/* 51 */     return this.cardCode;
/*    */   }
/*    */ 
/*    */   public void setCardNumber(String cardNumber)
/*    */   {
/* 58 */     this.cardNumber = cardNumber;
/*    */   }
/*    */ 
/*    */   public String getCardNumber()
/*    */   {
/* 65 */     return this.cardNumber;
/*    */   }
/*    */ 
/*    */   public void setSerialNumber(String serialNumber)
/*    */   {
/* 72 */     this.serialNumber = serialNumber;
/*    */   }
/*    */ 
/*    */   public String getSerialNumber()
/*    */   {
/* 79 */     return this.serialNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.input.PaymentCard
 * JD-Core Version:    0.6.0
 */