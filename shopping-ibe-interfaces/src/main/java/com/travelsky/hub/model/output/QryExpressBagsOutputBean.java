/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class QryExpressBagsOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7319503942028159714L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private List<BagInfo> bagInfos;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  26 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  33 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  49 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  56 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  71 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  78 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  93 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 100 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 112 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 119 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 126 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 133 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public List<BagInfo> getBagInfos()
/*     */   {
/* 140 */     return this.bagInfos;
/*     */   }
/*     */ 
/*     */   public void setBagInfos(List<BagInfo> bagInfos)
/*     */   {
/* 147 */     this.bagInfos = bagInfos;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 155 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 162 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.QryExpressBagsOutputBean
 * JD-Core Version:    0.6.0
 */