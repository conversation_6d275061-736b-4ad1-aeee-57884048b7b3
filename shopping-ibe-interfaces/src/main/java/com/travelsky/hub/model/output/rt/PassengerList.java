/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class PassengerList
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2568813929522159429L;
/*    */   private String paxNumber;
/*    */   private List<Passenger> passengers;
/*    */ 
/*    */   public String getPaxNumber()
/*    */   {
/* 31 */     return this.paxNumber;
/*    */   }
/*    */ 
/*    */   public void setPaxNumber(String paxNumber)
/*    */   {
/* 38 */     this.paxNumber = paxNumber;
/*    */   }
/*    */ 
/*    */   public List<Passenger> getPassengers()
/*    */   {
/* 45 */     return this.passengers;
/*    */   }
/*    */ 
/*    */   public void setPassengers(List<Passenger> passengers)
/*    */   {
/* 52 */     this.passengers = passengers;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.PassengerList
 * JD-Core Version:    0.6.0
 */