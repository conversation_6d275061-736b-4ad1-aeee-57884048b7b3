/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class DifferSeatchartOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String resultCode;
/*     */   private String resultMessage;
/*     */   private String ocAirlineCode;
/*     */   private String ocFlightNumber;
/*     */   private String ocFlightSuffix;
/*     */   private String isIF;
/*     */   private List<SegmentInfo> segmentInfos;
/*     */   private List<LegInfo> legInfos;
/*     */ 
/*     */   public String getResultCode()
/*     */   {
/*  51 */     return this.resultCode;
/*     */   }
/*     */ 
/*     */   public void setResultCode(String resultCode)
/*     */   {
/*  58 */     this.resultCode = resultCode;
/*     */   }
/*     */ 
/*     */   public String getResultMessage()
/*     */   {
/*  65 */     return this.resultMessage;
/*     */   }
/*     */ 
/*     */   public void setResultMessage(String resultMessage)
/*     */   {
/*  72 */     this.resultMessage = resultMessage;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/*  79 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/*  86 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/*  93 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 100 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 107 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 114 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getIsIF()
/*     */   {
/* 121 */     return this.isIF;
/*     */   }
/*     */ 
/*     */   public void setIsIF(String isIF)
/*     */   {
/* 128 */     this.isIF = isIF;
/*     */   }
/*     */ 
/*     */   public List<SegmentInfo> getSegmentInfos()
/*     */   {
/* 135 */     return this.segmentInfos;
/*     */   }
/*     */ 
/*     */   public void setSegmentInfos(List<SegmentInfo> segmentInfos)
/*     */   {
/* 142 */     this.segmentInfos = segmentInfos;
/*     */   }
/*     */ 
/*     */   public List<LegInfo> getLegInfos()
/*     */   {
/* 149 */     return this.legInfos;
/*     */   }
/*     */ 
/*     */   public void setLegInfos(List<LegInfo> legInfos)
/*     */   {
/* 156 */     this.legInfos = legInfos;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DifferSeatchartOutput
 * JD-Core Version:    0.6.0
 */