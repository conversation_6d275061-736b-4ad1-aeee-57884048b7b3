/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class CabinGroup
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String classCode;
/*     */   private String className;
/*     */   private String classSequence;
/*     */   private String columnNumbering;
/*     */   private String lineList;
/*     */   private List<Line> lines;
/*     */   private List<Section> sections;
/*     */ 
/*     */   public String getClassCode()
/*     */   {
/*  45 */     return this.classCode;
/*     */   }
/*     */ 
/*     */   public void setClassCode(String classCode)
/*     */   {
/*  52 */     this.classCode = classCode;
/*     */   }
/*     */ 
/*     */   public String getClassName()
/*     */   {
/*  59 */     return this.className;
/*     */   }
/*     */ 
/*     */   public void setClassName(String className)
/*     */   {
/*  66 */     this.className = className;
/*     */   }
/*     */ 
/*     */   public String getClassSequence()
/*     */   {
/*  73 */     return this.classSequence;
/*     */   }
/*     */ 
/*     */   public void setClassSequence(String classSequence)
/*     */   {
/*  80 */     this.classSequence = classSequence;
/*     */   }
/*     */ 
/*     */   public String getColumnNumbering()
/*     */   {
/*  87 */     return this.columnNumbering;
/*     */   }
/*     */ 
/*     */   public void setColumnNumbering(String columnNumbering)
/*     */   {
/*  94 */     this.columnNumbering = columnNumbering;
/*     */   }
/*     */ 
/*     */   public String getLineList()
/*     */   {
/* 101 */     return this.lineList;
/*     */   }
/*     */ 
/*     */   public void setLineList(String lineList)
/*     */   {
/* 108 */     this.lineList = lineList;
/*     */   }
/*     */ 
/*     */   public List<Line> getLines()
/*     */   {
/* 115 */     return this.lines;
/*     */   }
/*     */ 
/*     */   public void setLines(List<Line> lines)
/*     */   {
/* 122 */     this.lines = lines;
/*     */   }
/*     */ 
/*     */   public List<Section> getSections()
/*     */   {
/* 129 */     return this.sections;
/*     */   }
/*     */ 
/*     */   public void setSections(List<Section> sections)
/*     */   {
/* 136 */     this.sections = sections;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.CabinGroup
 * JD-Core Version:    0.6.0
 */