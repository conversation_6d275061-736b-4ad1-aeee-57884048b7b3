/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class UpgFltOutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2200798551669993317L;
/*    */   private String isSuccess;
/*    */   private List<UpgFlt> upgFlts;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 30 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 37 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public List<UpgFlt> getUpgFlts()
/*    */   {
/* 44 */     return this.upgFlts;
/*    */   }
/*    */ 
/*    */   public void setUpgFlts(List<UpgFlt> upgFlts)
/*    */   {
/* 51 */     this.upgFlts = upgFlts;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 58 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 65 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 72 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 79 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgFltOutBean
 * JD-Core Version:    0.6.0
 */