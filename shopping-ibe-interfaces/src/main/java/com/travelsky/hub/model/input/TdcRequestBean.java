/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class TdcRequestBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5259068840511072415L;
/*     */   private String flightNumber;
/*     */   private String etCode;
/*     */   private String hostNum;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String dptAptCode;
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  25 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  32 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  40 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  47 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getDptAptCode()
/*     */   {
/*  54 */     return this.dptAptCode;
/*     */   }
/*     */ 
/*     */   public void setDptAptCode(String dptAptCode)
/*     */   {
/*  61 */     this.dptAptCode = dptAptCode;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/*  72 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/*  79 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  87 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  94 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 102 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 109 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 117 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 124 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.TdcRequestBean
 * JD-Core Version:    0.6.0
 */