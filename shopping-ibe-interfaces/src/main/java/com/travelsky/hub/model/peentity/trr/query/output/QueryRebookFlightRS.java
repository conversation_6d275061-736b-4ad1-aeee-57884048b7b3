/*     */ package com.travelsky.hub.model.peentity.trr.query.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class QueryRebookFlightRS
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3746465005673556823L;
/*     */   private String resultCode;
/*     */   private String resultMessage;
/*     */   private String mCTTime;
/*     */   private String mCTSource;
/*     */   private String availableDate;
/*     */   private String availableSSR;
/*     */   private AdditionalInfo additionalInfo;
/*     */   private List<SegmentCabin> segmentCabins;
/*     */   private ErrorInfo errorInfo;
/*     */ 
/*     */   public String getResultCode()
/*     */   {
/*  68 */     return this.resultCode;
/*     */   }
/*     */ 
/*     */   public void setResultCode(String resultCode)
/*     */   {
/*  75 */     this.resultCode = resultCode;
/*     */   }
/*     */ 
/*     */   public String getResultMessage()
/*     */   {
/*  82 */     return this.resultMessage;
/*     */   }
/*     */ 
/*     */   public void setResultMessage(String resultMessage)
/*     */   {
/*  89 */     this.resultMessage = resultMessage;
/*     */   }
/*     */ 
/*     */   public String getmCTTime()
/*     */   {
/*  96 */     return this.mCTTime;
/*     */   }
/*     */ 
/*     */   public void setmCTTime(String mCTTime)
/*     */   {
/* 103 */     this.mCTTime = mCTTime;
/*     */   }
/*     */ 
/*     */   public String getmCTSource()
/*     */   {
/* 110 */     return this.mCTSource;
/*     */   }
/*     */ 
/*     */   public void setmCTSource(String mCTSource)
/*     */   {
/* 117 */     this.mCTSource = mCTSource;
/*     */   }
/*     */ 
/*     */   public String getAvailableDate()
/*     */   {
/* 124 */     return this.availableDate;
/*     */   }
/*     */ 
/*     */   public void setAvailableDate(String availableDate)
/*     */   {
/* 131 */     this.availableDate = availableDate;
/*     */   }
/*     */ 
/*     */   public String getAvailableSSR()
/*     */   {
/* 138 */     return this.availableSSR;
/*     */   }
/*     */ 
/*     */   public void setAvailableSSR(String availableSSR)
/*     */   {
/* 145 */     this.availableSSR = availableSSR;
/*     */   }
/*     */ 
/*     */   public AdditionalInfo getAdditionalInfo()
/*     */   {
/* 152 */     return this.additionalInfo;
/*     */   }
/*     */ 
/*     */   public void setAdditionalInfo(AdditionalInfo additionalInfo)
/*     */   {
/* 159 */     this.additionalInfo = additionalInfo;
/*     */   }
/*     */ 
/*     */   public List<SegmentCabin> getSegmentCabins()
/*     */   {
/* 166 */     return this.segmentCabins;
/*     */   }
/*     */ 
/*     */   public void setSegmentCabins(List<SegmentCabin> segmentCabins)
/*     */   {
/* 173 */     this.segmentCabins = segmentCabins;
/*     */   }
/*     */ 
/*     */   public ErrorInfo getErrorInfo()
/*     */   {
/* 180 */     return this.errorInfo;
/*     */   }
/*     */ 
/*     */   public void setErrorInfo(ErrorInfo errorInfo)
/*     */   {
/* 187 */     this.errorInfo = errorInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.QueryRebookFlightRS
 * JD-Core Version:    0.6.0
 */