/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryPsgInfoInput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String flightDate;
/*    */   private String flightNo;
/*    */   private String etCode;
/*    */   private String deptAptCode;
/*    */ 
/*    */   public String getFlightNo()
/*    */   {
/* 22 */     return this.flightNo;
/*    */   }
/*    */ 
/*    */   public void setFlightNo(String flightNo)
/*    */   {
/* 29 */     this.flightNo = flightNo;
/*    */   }
/*    */ 
/*    */   public String getFlightDate()
/*    */   {
/* 39 */     return this.flightDate;
/*    */   }
/*    */ 
/*    */   public void setFlightDate(String flightDate)
/*    */   {
/* 46 */     this.flightDate = flightDate;
/*    */   }
/*    */ 
/*    */   public String getDeptAptCode()
/*    */   {
/* 53 */     return this.deptAptCode;
/*    */   }
/*    */ 
/*    */   public void setDeptAptCode(String deptAptCode)
/*    */   {
/* 60 */     this.deptAptCode = deptAptCode;
/*    */   }
/*    */ 
/*    */   public String getEtCode()
/*    */   {
/* 72 */     return this.etCode;
/*    */   }
/*    */ 
/*    */   public void setEtCode(String etCode)
/*    */   {
/* 79 */     this.etCode = etCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.QueryPsgInfoInput
 * JD-Core Version:    0.6.0
 */