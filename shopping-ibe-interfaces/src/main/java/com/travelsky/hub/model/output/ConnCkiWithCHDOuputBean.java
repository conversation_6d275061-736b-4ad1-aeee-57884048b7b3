/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class ConnCkiWithCHDOuputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<ConnCkiResult> connResult;
/*    */ 
/*    */   public List<ConnCkiResult> getConnResult()
/*    */   {
/* 25 */     return this.connResult;
/*    */   }
/*    */ 
/*    */   public void setConnResult(List<ConnCkiResult> connResult)
/*    */   {
/* 32 */     this.connResult = connResult;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ConnCkiWithCHDOuputBean
 * JD-Core Version:    0.6.0
 */