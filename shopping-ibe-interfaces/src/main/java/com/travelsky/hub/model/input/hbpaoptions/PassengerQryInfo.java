/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerQryInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 549563730608282650L;
/*    */   private String passengerNums;
/*    */   private String passengerName;
/*    */   private String subClass;
/*    */ 
/*    */   public String getPassengerName()
/*    */   {
/* 35 */     return this.passengerName;
/*    */   }
/*    */ 
/*    */   public void setPassengerName(String passengerName)
/*    */   {
/* 42 */     this.passengerName = passengerName;
/*    */   }
/*    */ 
/*    */   public String getSubClass()
/*    */   {
/* 49 */     return this.subClass;
/*    */   }
/*    */ 
/*    */   public void setSubClass(String subClass)
/*    */   {
/* 56 */     this.subClass = subClass;
/*    */   }
/*    */ 
/*    */   public String getPassengerNums()
/*    */   {
/* 63 */     return this.passengerNums;
/*    */   }
/*    */ 
/*    */   public void setPassengerNums(String passengerNums)
/*    */   {
/* 70 */     this.passengerNums = passengerNums;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.PassengerQryInfo
 * JD-Core Version:    0.6.0
 */