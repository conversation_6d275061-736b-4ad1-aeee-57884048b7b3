/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import com.travelsky.hub.model.input.Extraseat;
/*     */ import com.travelsky.hub.model.input.InfInfoBean;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AcceptPsrInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1425208699079103024L;
/*     */   private String passengerName;
/*     */   private String seatNumber;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String tourIndex;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String isGroup;
/*  52 */   private String sNoption = "N";
/*     */   private String xbp;
/*     */   private String psm;
/*     */   private String chd;
/*     */   private String deptTime;
/*     */   private String gender;
/*     */   private String bagWeight;
/*     */   private String bagQuantity;
/*     */   private String bagArrivalAirport;
/*     */   private String hostNo1;
/*     */   private String hostNo2;
/*     */   private String hostNo3;
/*     */   private String tktNoPartner;
/*     */   private String tktNoPartner3;
/*     */   private String tourIndexPartner;
/*     */   private String tourIndexPartner3;
/*     */   private String telnum;
/*     */   private String email;
/*     */   private InfInfoBean infInfo;
/*     */   private String ckinMessage;
/*     */   private Extraseat extraSeat;
/*     */ 
/*     */   public Extraseat getExtraSeat()
/*     */   {
/* 143 */     return this.extraSeat;
/*     */   }
/*     */ 
/*     */   public void setExtraSeat(Extraseat extraSeat)
/*     */   {
/* 151 */     this.extraSeat = extraSeat;
/*     */   }
/*     */ 
/*     */   public String getTelnum()
/*     */   {
/* 160 */     return this.telnum;
/*     */   }
/*     */ 
/*     */   public void setTelnum(String telnum)
/*     */   {
/* 168 */     this.telnum = telnum;
/*     */   }
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 175 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 183 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 191 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 198 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 205 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 212 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 219 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 226 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 233 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 240 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 247 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getPsm()
/*     */   {
/* 254 */     return this.psm;
/*     */   }
/*     */ 
/*     */   public void setPsm(String psm)
/*     */   {
/* 261 */     this.psm = psm;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 268 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getIsGroup()
/*     */   {
/* 275 */     return this.isGroup;
/*     */   }
/*     */ 
/*     */   public String getSNoption()
/*     */   {
/* 282 */     return this.sNoption;
/*     */   }
/*     */ 
/*     */   public void setSNoption(String noption)
/*     */   {
/* 289 */     this.sNoption = noption;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 296 */     this.ffpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 303 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setIsGroup(String isGroup)
/*     */   {
/* 310 */     this.isGroup = isGroup;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 317 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 324 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 331 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 338 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 345 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 352 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 359 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 366 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getBagArrivalAirport()
/*     */   {
/* 373 */     return this.bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getXbp()
/*     */   {
/* 380 */     return this.xbp;
/*     */   }
/*     */ 
/*     */   public void setXbp(String xbp)
/*     */   {
/* 387 */     this.xbp = xbp;
/*     */   }
/*     */ 
/*     */   public void setBagArrivalAirport(String bagArrivalAirport)
/*     */   {
/* 394 */     this.bagArrivalAirport = bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 401 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 408 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getHostNo1()
/*     */   {
/* 415 */     return this.hostNo1;
/*     */   }
/*     */ 
/*     */   public void setHostNo1(String hostNo1)
/*     */   {
/* 422 */     this.hostNo1 = hostNo1;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/* 429 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/* 436 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getHostNo2()
/*     */   {
/* 443 */     return this.hostNo2;
/*     */   }
/*     */ 
/*     */   public void setHostNo2(String hostNo2)
/*     */   {
/* 450 */     this.hostNo2 = hostNo2;
/*     */   }
/*     */ 
/*     */   public String getTktNoPartner()
/*     */   {
/* 457 */     return this.tktNoPartner;
/*     */   }
/*     */ 
/*     */   public void setTktNoPartner(String tktNoPartner)
/*     */   {
/* 464 */     this.tktNoPartner = tktNoPartner;
/*     */   }
/*     */ 
/*     */   public String getTourIndexPartner()
/*     */   {
/* 471 */     return this.tourIndexPartner;
/*     */   }
/*     */ 
/*     */   public void setTourIndexPartner(String tourIndexPartner)
/*     */   {
/* 478 */     this.tourIndexPartner = tourIndexPartner;
/*     */   }
/*     */ 
/*     */   public String getHostNo3()
/*     */   {
/* 485 */     return this.hostNo3;
/*     */   }
/*     */ 
/*     */   public void setHostNo3(String hostNo3)
/*     */   {
/* 492 */     this.hostNo3 = hostNo3;
/*     */   }
/*     */ 
/*     */   public String getTktNoPartner3()
/*     */   {
/* 499 */     return this.tktNoPartner3;
/*     */   }
/*     */ 
/*     */   public void setTktNoPartner3(String tktNoPartner3)
/*     */   {
/* 506 */     this.tktNoPartner3 = tktNoPartner3;
/*     */   }
/*     */ 
/*     */   public String getTourIndexPartner3()
/*     */   {
/* 513 */     return this.tourIndexPartner3;
/*     */   }
/*     */ 
/*     */   public void setTourIndexPartner3(String tourIndexPartner3)
/*     */   {
/* 520 */     this.tourIndexPartner3 = tourIndexPartner3;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 528 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 536 */     this.infInfo = infInfo;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 543 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 550 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.AcceptPsrInputBean
 * JD-Core Version:    0.6.0
 */