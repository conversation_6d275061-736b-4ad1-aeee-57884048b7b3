package com.travelsky.hub.wdoe.util;

public final class PrConstr
{
  public static final String PR_WDOE_ELEMENT = "WDoe";
  public static final String PRINFOREQ_ELEMENT = "PrInfoReq";
  public static final String PR_WDOE_AIRLINECODE = "AirlineCode";
  public static final String PR_WDOE_FLIGHTNUMBER = "FlightNumber";
  public static final String PR_WDOE_DEPARTUREDATE = "DepartureDate";
  public static final String PR_WDOE_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PR_WDOE_TICKETID = "TicketID";
  public static final String PR_WDOE_BOARDINGNUMBER = "BoardingNumber";
  public static final String PR_WDOE_SEATNUMBER = "SeatNumber";
  public static final String PR_WDOE_BAGTAGNUMBER = "BagTagNumber";
  public static final String PR_WDOE_OPERATEFLAG = "OperateFlag";
  public static final String PR_FLIGHTINFO_ELEMENT = "FlightInfo";
  public static final String PR_FLIGHTINFO_AIRLINECODE = "AirlineCode";
  public static final String PR_FLIGHTINFO_FLIGHTNUMBER = "FlightNumber";
  public static final String PR_FLIGHTINFO_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PR_FLIGHTINFO_DEPARTUREDATE = "DepartureDate";
  public static final String PR_FLIGHTINFO_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PR_PASSENGERINFO_ELEMENT = "PassengerInfo";
  public static final String PR_PASSENGERINFO_SURNAME = "Surname";
  public static final String PR_PASSENGERINFO_CHNNAME = "ChnName";
  public static final String PR_PASSENGERINFO_CHECKINCHANNEL = "CheckinChannel";
  public static final String PR_PASSENGERINFO_BAGWEIGHT = "BagWeight";
  public static final String PR_PASSENGERINFO_BAGQUANTITY = "BagQuantity";
  public static final String PR_PASSENGERINFO_BAGARRIVALAIRPORT = "BagArrivalAirport";
  public static final String PR_PASSENGERINFO_DOCTYPE = "DocType";
  public static final String PR_PASSENGERINFO_DOCID = "DocID";
  public static final String PR_PASSENGERINFO_CONTACTINFO = "ContactInfo";
  public static final String PR_PASSENGERINFO_CONTACTTEXT = "ContactText";
  public static final String PR_PASSENGERINFO_BAGTAG = "BagTag";
  public static final String PR_PASSENGERINFO_BAGINDEX = "BagIndex";
  public static final String PR_PASSENGERINFO_BAGTAGNO = "BagTagNo";
  public static final String PR_PASSENGERINFO_BAGSTATUS = "BagStatus";
  public static final String PR_PASSENGERINFO_OPERATIONINFO = "OperationInfo";
  public static final String PR_PASSENGERINFO_OPERATIONTYPE = "OperationType";
  public static final String PR_PASSENGERINFO_OPERATIONPID = "OperationPID";
  public static final String PR_PASSENGERINFO_OPERATIONAGENT = "OperationAgent";
  public static final String PR_PASSENGERINFO_OPERATIONTIME = "OperationTime";
  public static final String PR_PASSENGERINFO_OPERATIONDATE = "OperationDate";
  public static final String PR_PASSENGERINFO_OPERATIONCONTENT = "OperationContent";
  public static final String PR_PASSENGERINFO_ICSPASSENGERNAMERECORD = "ICSPassengerNameRecord";
  public static final String PR_PASSENGERINFO_CRSPASSENGERNAMERECORD = "CRSPassengerNameRecord";
  public static final String PR_PASSENGERINFO_SKYPRIORITYFLAG = "SkyPriorityFlag";
  public static final String PR_PASSENGERINFO_CKINCONTENTS = "CkinContents";
  public static final String PR_PASSENGERINFO_CKINCONTENT = "CkinContent";
  public static final String PR_PASSENGERINFO_ASVCINFOS = "AsvcInfos";
  public static final String PR_PASSENGERINFO_EMDTYPECODE = "EmdTypeCode";
  public static final String PR_PASSENGERINFO_SSRCODE = "SsrCode";
  public static final String PR_PASSENGERINFO_SUBCODE = "SubCode";
  public static final String PR_PASSENGERINFO_DOCUMENTNUM = "DocumentNum";
  public static final String PR_PASSENGERINFO_COUPONNUM = "CouponNum";
  public static final String PR_PASSENGERINFO_EMDSTATUS = "EmdStatus";
  public static final String PR_PASSENGERINFO_COMSERVICENAME = "ComServiceName";
  public static final String PR_PASSENGERINFO_EXPENSE = "Expense";
  public static final String PR_PASSENGERINFO_CONNECTIONINFO = "ConnectionInfo";
  public static final String PR_PASSENGERINFO_AUTOINDICATOR = "AutoIndicator";
  public static final String PR_PASSENGERFLIGHTINFO_ELEMENT = "PassengerFlightInfo";
  public static final String PR_PASSENGERFLIGHTINFO_HOSTNUMBER = "HostNumber";
  public static final String PR_PASSENGERFLIGHTINFO_PARENTCABINTYPE = "ParentCabinType";
  public static final String PR_PASSENGERFLIGHTINFO_CABINTYPE = "CabinType";
  public static final String PR_PASSENGERFLIGHTINFO_SNRFLAG = "SnrFlag";
  public static final String PR_PASSENGERFLIGHTINFO_SEATNUMBER = "SeatNumber";
  public static final String PR_PASSENGERFLIGHTINFO_PROGRAMID = "ProgramID";
  public static final String PR_PASSENGERFLIGHTINFO_MEMBERSHIPID = "MembershipID";
  public static final String PR_PASSENGERFLIGHTINFO_LAYOLLEVEL = "LayolLevel";
  public static final String PR_PASSENGERFLIGHTINFO_CHD = "CHD";
  public static final String PR_PASSENGERFLIGHTINFO_GENDER = "Gender";
  public static final String PR_PASSENGERFLIGHTINFO_GROUPCODE = "GroupCode";
  public static final String PR_PASSENGERFLIGHTINFO_GROUPNUMBER = "GroupNumber";
  public static final String PR_PASSENGERFLIGHTINFO_BOARDINGNUMBER = "BoardingNumber";
  public static final String PR_PASSENGERFLIGHTINFO_STANDBYINDICATOR = "StandByIndicator";
  public static final String PR_PASSENGERFLIGHTINFO_STANDBYNUMBER = "StandByNumber";
  public static final String PR_PASSENGERFLIGHTINFO_MARKETINGAIRLINE = "MarketingAirline";
  public static final String PR_PASSENGERFLIGHTINFO_MARKETINGFLIGHTNUMBER = "MarketingFlightNumber";
  public static final String PR_PASSENGERFLIGHTINFO_FARECLASS = "FareClass";
  public static final String PR_PASSENGERFLIGHTINFO_TICKETID = "TicketID";
  public static final String PR_PASSENGERFLIGHTINFO_SEQUENCENUMBER = "SequenceNumber";
  public static final String PR_PASSENGERFLIGHTINFO_SPECIALSVCINFO = "SpecialSvcInfo";
  public static final String PR_PASSENGERFLIGHTINFO_PASSENGERSTATUS = "PassengerStatus";
  public static final String PR_PASSENGERFLIGHTINFO_AECSEATNUMBER = "AECSeatNumber";
  public static final String PR_PASSENGERFLIGHTINFO_PASSENGERTYPE = "PassengerType";
  public static final String PR_PASSENGERFLIGHTINFO_URESFLAG = "UresFlag";
  public static final String PR_PASSENGERFLIGHTINFO_AQQFLAG = "AqqFlag";
  public static final String PR_PASSENGERFLIGHTINFO_ESTAFLAG = "EstaFlag";
  public static final String PR_PASSENGERFLIGHTINFO_ISASRSEAT = "IsAsrSeat";
  public static final String PR_PASSENGERFLIGHTINFO_VIPFLAG = "VipFlag";
  public static final String PR_PASSENGERFLIGHTINFO_RESERVSEATTYPE = "ReservedSeatType";
  public static final String PR_PASSENGERFLIGHTINFO_FBA = "FBA";
  public static final String PR_PASSENGERFLIGHTINFO_GFBA = "GFBA";
  public static final String PR_PASSENGERFLIGHTINFO_FAMILYCOUNT = "FamilyCount";
  public static final String PR_PASSENGERFLIGHTINFO_ISFAMILY = "IsFamily";
  public static final String PR_PASSENGERFLIGHTINFO_SEATCHANGE = "SeatChange";
  public static final String PR_PASSENGERFLIGHTINFO_EXTRASEAT = "ExtraSeat";
  public static final String PR_PASSENGERFLIGHTINFO_EXTRATYPE = "ExtraType";
  public static final String PR_PASSENGERFLIGHTINFO_EXTRAWEIGHT = "ExtraWeight";
  public static final String PR_PASSENGERFLIGHTINFO_INFANT = "Infant";
  public static final String PR_PASSENGERFLIGHTINFO_INFINFO = "INFInfo";
  public static final String PR_PASSENGERFLIGHTINFO_INFETNO = "INFEtNo";
  public static final String PR_PASSENGERFLIGHTINFO_IFBA = "IFBA";
  public static final String PR_PASSENGERFLIGHTINFO_INFETSEQUENCE = "INFEtSequence";
  public static final String PR_PASSENGERFLIGHTINFO_PASSENGERWEIGHT = "PassengerWeight";
  public static final String PR_PASSENGERFLIGHTINFO_TEXTMSG = "TextMSG";
  public static final String PR_PASSENGERFLIGHTINFO_MSGTYPE = "MSGType";
  public static final String PR_PASSENGERFLIGHTINFO_MSGTEXT = "MSGText";
  public static final String PR_PASSENGERFLIGHTINFO_PSPTINFO = "PsptInfo";
  public static final String PR_PASSENGERFLIGHTINFO_PSPTNUMBER = "PSPTNumber";
  public static final String PR_PASSENGERFLIGHTINFO_MULTIPASSENGER = "Multipassenger";
  public static final String PR_PASSENGERFLIGHTINFO_RECHECKINFO = "RecheckInfo";
  public static final String PR_PASSENGERFLIGHTINFO_BOUNDINFO = "BoundInfo";
  public static final String PR_PASSENGERFLIGHTINFO_AIRLINECODE = "AirlineCode";
  public static final String PR_PASSENGERFLIGHTINFO_FLIGHTNUMBER = "FlightNumber";
  public static final String PR_PASSENGERFLIGHTINFO_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PR_PASSENGERFLIGHTINFO_FLIGHTDATE = "FlightDate";
  public static final String PR_PASSENGERFLIGHTINFO_BOUNDSTATUS = "BoundStatus";
  public static final String PR_PASSENGERFLIGHTINFO_BOUNDTYPE = "BoundType";
  public static final String PR_PASSENGERFLIGHTINFO_BRDINFO = "BrdInfo";
  public static final String PR_PASSENGERFLIGHTINFO_SEATINFO = "SeatInfo";
  public static final String PR_PASSENGERFLIGHTINFO_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PR_PASSENGERFLIGHTINFO_EDI = "EDI";
  public static final String PR_PASSENGERFLIGHTINFO_FLIGHTSUFFIX = "FlightSuffix";
  public static final String PR_PASSENGERFLIGHTINFO_APIINFO = "ApiInfo";
  public static final String PR_PASSENGERFLIGHTINFO_SURNAME = "SurName";
  public static final String PR_PASSENGERFLIGHTINFO_GIVENNAME = "GivenName";
  public static final String PR_PASSENGERFLIGHTINFO_MIDDLENAME = "MiddleName";
  public static final String PR_PASSENGERFLIGHTINFO_DOCHOLDERNATIONALITY = "DocHolderNationality";
  public static final String PR_PASSENGERFLIGHTINFO_BIRTHDATE = "BirthDate";
  public static final String PR_PASSENGERFLIGHTINFO_DOCTYPE = "DocType";
  public static final String PR_PASSENGERFLIGHTINFO_DOCID = "DocID";
  public static final String PR_PASSENGERFLIGHTINFO_DOCISSUECOUNTRY = "DocIssueCountry";
  public static final String PR_PASSENGERFLIGHTINFO_BIRTHLOCATION = "BirthLocation";
  public static final String PR_PASSENGERFLIGHTINFO_BIRTHCOUNTRY = "BirthCountry";
  public static final String PR_PASSENGERFLIGHTINFO_INFANTIND = "InfantInd";
  public static final String PR_PASSENGERFLIGHTINFO_PRIMARYHOLDERIND = "PrimaryHolderInd";
  public static final String PR_PASSENGERFLIGHTINFO_TRANSFERIND = "TransferInd";
  public static final String PR_PASSENGERFLIGHTINFO_DOCISSUESTATEPROV = "DocIssueStateProv";
  public static final String PR_PASSENGERFLIGHTINFO_EFFECTIVEDATE = "EffectiveDate";
  public static final String PR_PASSENGERFLIGHTINFO_EXPIREDATE = "ExpireDate";
  public static final String PR_PASSENGERFLIGHTINFO_RESIDENCECOUNTRY = "ResidenceCountry";
  public static final String PR_PASSENGERFLIGHTINFO_APIINFOSOURCE = "ApiInfoSource";
  public static final String PR_PASSENGERFLIGHTINFO_HOMEADDRESS = "HomeAddress";
  public static final String PR_PASSENGERFLIGHTINFO_DESTADDRESS = "DestAddress";
  public static final String PR_PASSENGERFLIGHTINFO_STREETNMBR = "StreetNmbr";
  public static final String PR_PASSENGERFLIGHTINFO_CITYNAME = "CityName";
  public static final String PR_PASSENGERFLIGHTINFO_STATECODE = "StateCode";
  public static final String PR_PASSENGERFLIGHTINFO_COUNTRYNAME = "CountryName";
  public static final String PR_PASSENGERFLIGHTINFO_POSTALCODE = "PostalCode";
  public static final String PR_PASSENGERFLIGHTINFO_STATEPROV = "StateProv";
  public static final String PR_PASSENGERFLIGHTINFO_TELEPHONER = "Telephoner";
  public static final String PR_PASSENGERFLIGHTINFO_OTHERDOCINFO = "OtherDocInfo";
  public static final String PR_PASSENGERFLIGHTINFO_OTHERDOCISSUEPLACE = "OtherDocIssuePlace";
  public static final String PR_PASSENGERFLIGHTINFO_OTHERDOCTYPE = "OtherDocType";
  public static final String PR_PASSENGERFLIGHTINFO_OTHERDOCID = "OtherDocID";
  public static final String PR_PASSENGERFLIGHTINFO_VISAINFO = "VisaInfo";
  public static final String PR_PASSENGERFLIGHTINFO_VISAISSUEPLACE = "VisaIssuePlace";
  public static final String PR_PASSENGERFLIGHTINFO_VISATYPE = "VisaType";
  public static final String PR_PASSENGERFLIGHTINFO_VISAID = "VisaID";
  public static final String PR_PASSENGERFLIGHTINFO_APPINFO = "AppInfo";
  public static final String PR_PASSENGERFLIGHTINFO_APPSTATUS = "AppStatus";
  public static final String PR_PASSENGERFLIGHTINFO_REMARK = "Remark";
  public static final String PR_PASSENGERFLIGHTINFO_TEXTINFO = "TextInfo";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.PrConstr
 * JD-Core Version:    0.6.0
 */