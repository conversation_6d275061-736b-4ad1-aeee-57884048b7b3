package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.interfaces.dto.ibetools.*;
import com.hna.shopping.ibe.manager.IBEToolsManager;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ibe.client.FD;
import com.travelsky.ibe.client.FDItem;
import com.travelsky.ibe.client.FDResult;
import com.travelsky.ibe.client.pnr.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * IBE 工具
 */
@Component
@Slf4j
public class IBEToolsManagerImpl implements IBEToolsManager {

    /**
     * 航信免流量费--出票
     * @param req
     * @return
     */
    @Override
    public IssueForTravelSkyRes issueForTravelSky(IssueForTravelSkyReq req) {
        log.info("issueForTravelSky, req: {}", req);
        IssueForTravelSkyRes res = new IssueForTravelSkyRes();
        int printerNo = 1;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
            printerNo = 2;
        }
        SellSeat sellSeat = IbeCmd.getIbeClient(configPrefix, SellSeat.class);
        FD fd = IbeCmd.getIbeClient(configPrefix, FD.class);
        ETDZ etdz = IbeCmd.getIbeClient(configPrefix, ETDZ.class);

        try {
            //添加旅客姓名
            String name = req.getPassengerName();
            sellSeat.addAdult(name);

            //添加旅客航段信息
            String airNo = req.getFlightNo();  //航班号
            char fltClass = req.getCabin().charAt(0);     //舱位等级
            String orgCity = req.getDepCode();   //始发城市
            String desCity = req.getArrCode();  //到达城市
            String actionCode = "NN";    //行动代码
            int tktNum = 1;             //订座数
            String departureTime = req.getFlightDate();  //起飞时间

            sellSeat.addAirSeg(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);

            //添加旅客身份证信息
            String airline = "JD";   //航空公司两字代码
            String idtype = "PP";    //身份证件类型 NI身份证，CC信用卡，PP护照
            String id = req.getCertNo();  //对应的身份证件号码

            // sellSeat.addSSR_FOID(airline, idtype, id, name);
            /*添加旅客身份信息--DOCS*/
            /*添加一个 DOCS 信息在 IBE/TEST 名下*/
            String doctype="P";
            String issueCountry = "CN";
            String doc_No = req.getCertNo();
            String nationality = "CN";
            Date birth = DateUtils.addYears(new Date(), -25);
            //Date birth = new SimpleDateFormat("yyyyMMdd").parse("19700304");
            String gender = "F";
            boolean infant = false;
            // Date expireDate = new SimpleDateFormat("yyyyMMdd").parse("20100630");
            Date expireDate = DateUtils.addYears(new Date(), 5);
            String surname = "TESTSUR";
            String firstname = "TESTFIRST";
            String midname = "";
            boolean holder = true;

            sellSeat.addSSR_DOCS(airline,doctype,issueCountry,doc_No,nationality, birth,gender,infant,expireDate,surname,firstname,midname,holder,name);

            String contactinfo = req.getContactNo();
            //添加旅客联系组信息
            sellSeat.addContact(contactinfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
            sellSeat.addOSI("JD", "CTCT" + contactinfo);

            //添加旅客出票时限
            String dateLimit = DateUtil.toStringYYYY_MM_dd_HMS(DateUtils.addHours(new Date(), 1));
            sellSeat.setTimelimit(dateLimit);
            FDItem fdItem = this.fd(fd, req);
            Double baseFare = Double.parseDouble(fdItem.getOnewayPrice());
            Double airportFee = Double.parseDouble(fdItem.getAirportTax());
            Double fuelFee = Double.parseDouble(fdItem.getFuelTax());

            //第七步、添加FC票价计算组
            //字符串参数、格式为FC:出发城市（TAO） 承运航空(SC) 目的城市(PEK) 票面价(600.00) 运价基础（Y）总价（CNY）结束（END）
            //            String fcStr = "FC:TAO SC PEK 600.00Y CNY600.00END";
            //            ss.addFC(fcStr);
            //BookFC对象参数
            BookFC fc= new BookFC();
            fc.addFC(orgCity, desCity, "JD", req.getCabin(), baseFare);//多航段多次添加，价格-1 时表示把该航段价格加到下一航段（最后一个航段），可用于多航段只了解票面总价的情况下使用，在后面的升舱过程由于无数据库保存FC项时可以用到
            sellSeat.addFC(fc);
            //第八步、添加FN票价组
            //字符串参数、格式为FN:票面总价（FCNY）/实收价格(SCNY)/代理费率(C3.00)/机建税(TCNY50.00CN)/燃油税（TCNY70.00YQ）
            //            String fnStr = "FN:FCNY600.00/SCNY600.00/C3.00/TCNY50.00CN/TCNY70.00YQ";
            //            ss.addFN(fnStr);
            //BookFN对象参数
            BookFN fn= new BookFN();
            fn.setAmount(BookFN.F, "CNY", baseFare);//FCNY
            fn.setAmount(BookFN.S, "CNY", baseFare);//SCNY
            fn.setC(0.00);//代理费率
            fn.addTax(BookFN.T, "CNY", airportFee, "CN");//机建
            fn.addTax(BookFN.T, "CNY", fuelFee, "YQ");//燃油
            // FN/M/FCNY320.00/SCNY320.00/C0.00/XCNY70.00/TCNY50.00CN/TCNY20.00YQ/ACNY390.00
//            DecimalFormat format = new DecimalFormat("###############0.00");
//            Double netFare = baseFare;
//            String fnStr = "FN:FCNY" + format.format(netFare)
//                    + "/SCNY" + format.format(netFare)
//                    + "/C0.00"
//                   // + "/XCNY" + format.format(airportFee + fuelFee)
//                    + "/TCNY" + format.format(airportFee) + "CN"
//                    + "/TCNY" + format.format(fuelFee) + "YQ"
//                   // + "/ACNY" + format.format(netFare + airportFee + fuelFee)
//            ;

            sellSeat.addFN(fn);
            sellSeat.addFP("CASH,CNY/P1");

            //完成PNR必须信息输入递交主机，生成PNR
            SSResult ssr = sellSeat.commit1();
            log.info("issueForTravelSky, SellSeat commit1 success: {}", ssr.getPnrno());
            Thread.sleep(3000);
            //PNR
            String pnrNo = ssr.getPnrno();
            res.setPnrNo(pnrNo);
            //出票
            String ok = this.etdz(etdz, pnrNo, printerNo);
            log.info("issueForTravelSky, etdz result: {}", ok);

            RT rt = IbeCmd.getIbeClient(configPrefix, RT.class);
            // 获取票号
            Map<String, String> map = fetchTicketNoWithRetry(rt, pnrNo);
            res.setTicketNo(map.get("ticketNo"));
            res.setTicketStatus("OPEN FOR USE");
            res.setPnrStatus(map.get("pnrStatus"));

        } catch (Exception e) {
            log.error("issueForTravelSky error, req: {}", req);
            e.printStackTrace();
            if (StringUtils.isNotEmpty(res.getPnrNo())) {
                try {
                    RT rt = IbeCmd.getIbeClient(configPrefix, RT.class);
                    PnrManage pnrManage = IbeCmd.getIbeClient(configPrefix, PnrManage.class);
                    String ok = "";
                    RTResult rtResult = rt.retrieve(res.getPnrNo());
                    for (int i = 0; i < rtResult.getAirSegsCount(); i++) {
                        PNRAirSeg airSeg = rtResult.getAirSegAt(i);
                        log.info("issueForTravelSky issue error, cancelAirSeg: {}", res.getPnrNo());
                        ok = pnrManage.cancelAirSeg(res.getPnrNo(), airSeg);
                    }
                } catch (Exception e1) {
                    e1.printStackTrace();
                }
            }
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e.getMessage());
        }

        return res;
    }

    @Override
    public RTResult rt(RtReq req) {
        RTResult rtResult = null;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
        }
        RT rt = IbeCmd.getIbeClient(configPrefix, RT.class);
        try {
            try {
                rtResult = rt.retrieve(req.getPnrNo());
            } catch (Exception e) {
                rtResult = rt.retrieve(req.getPnrNo());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return rtResult;
    }


    @Override
    public RTResult rtHistory(RtReq req) {
        RTResult rtResult = null;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
        }
        RT rt = IbeCmd.getIbeClient(configPrefix, RT.class);
        try {
            try {
                rtResult = rt.retrieveCompletely(req.getPnrNo());
            } catch (Exception e) {
                rtResult = rt.retrieveCompletely(req.getPnrNo());
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return rtResult;
    }

    @Override
    public DETRTKTResult detr(DETRReq req) {
        DETRTKTResult detrtktResult = null;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
        }
        DETR2F detr = IbeCmd.getIbeClient(configPrefix, DETR2F.class);
        try {

            /**
             * 组织双因素。
             * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
             * CN,PNR记录编码，票面任一CPN的任一记录编码均可
             * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
             * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
             * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
             * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
             * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
             */
            detrtktResult = detr.getTicketInfoByTktNo2F(req.getTicketNo(), false, "", "", req.getSecondFactorCode(), req.getSecondFactorValue());

        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return detrtktResult;
    }


    @Override
    public DETRHistoryResult detrHistory(DETRReq req) {
        DETRHistoryResult detrHistoryResult = null;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
        }
        DETR2F detr = IbeCmd.getIbeClient(configPrefix, DETR2F.class);
        try {

            /**
             * 组织双因素。
             * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
             * CN,PNR记录编码，票面任一CPN的任一记录编码均可
             * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
             * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
             * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
             * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
             * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
             */
            detrHistoryResult = detr.getTicketHistoryByTktNo2F(req.getTicketNo(), "", req.getSecondFactorCode(), req.getSecondFactorValue());

        } catch (Exception e) {
            e.printStackTrace();
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return detrHistoryResult;
    }


    /**
     * 航信免流量费--退票、清位
     * @param req
     * @return
     */
    @Override
    public Boolean refundForTravelSky(RefundForTravelSkyReq req) {
        Boolean refundResult = false, cancelResult = false;
        String configPrefix = "JDXIY258";
        if (req.getIbeOfficeNo().equalsIgnoreCase("xiy255")) {
            configPrefix = "JDXIY255";
        } else if (req.getIbeOfficeNo().equalsIgnoreCase("xiy258")) {
            configPrefix = "JDXIY258";
        }
        String errorMsg = "";
        try {
            ETRF etrf = IbeCmd.getIbeClient(configPrefix, ETRF.class);
            String ok = etrf.refundETkt(req.getTicketNo().replaceAll("-", ""), 1, "CNY", 0.0, "1", "");
            if ("OK".equalsIgnoreCase(ok)) {
                refundResult = true;
                log.info("etrf ticketNo success: {} , getTxnTraceKey: {}", req.getTicketNo(), etrf.getTxnTraceKey());
            } else {
                log.info("etrf ticketNo fail: {} , getTxnTraceKey: {}, error: {}", req.getTicketNo(), etrf.getTxnTraceKey(), ok);
            }
        } catch (Exception e) {
            log.error("ticket refund error: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), req.getPnrNo(), req.getTicketNo());
            errorMsg = e.getMessage();
        }

        PnrManage pnrManage = IbeCmd.getIbeClient(configPrefix, PnrManage.class);
        try {
            RT rt = IbeCmd.getIbeClient(configPrefix, RT.class);
            String ok = "";
            RTResult rtResult = rt.retrieve(req.getPnrNo());
            for (int i = 0; i < rtResult.getAirSegsCount(); i++) {
                PNRAirSeg airSeg = rtResult.getAirSegAt(i);
                ok = pnrManage.cancelAirSeg(req.getPnrNo(), airSeg);
            }

            if ("OK".equalsIgnoreCase(ok)) {
                log.info("cancelAirSeg success: {} , getTxnTraceKey: {}", req.getPnrNo(), pnrManage.getTxnTraceKey());
                cancelResult = true;
            } else {
                log.info("cancelAirSeg fail: {} , getTxnTraceKey: {}, error: {}", req.getPnrNo(), pnrManage.getTxnTraceKey(), ok);
            }
        } catch (Exception e) {
            log.error("cancelAirSeg error,  pnrNo: {} , getTxnTraceKey: {} ", req.getPnrNo(), pnrManage.getTxnTraceKey());
            log.error("", e);
            errorMsg = e.getMessage();
        }

        if (refundResult && cancelResult) {
            return true;
        } else {
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, errorMsg);
        }
    }


    /**
     * FD 查价格
     * @param fd
     * @param req
     * @return
     */
    private FDItem fd(FD fd, IssueForTravelSkyReq req) throws Exception {
        FDResult fdResult = fd.findPrice(req.getDepCode(), req.getArrCode(), convertDate(req.getFlightDate()), "JD", "", "AD", true);
        for (Object sortedfare : fdResult.getFare().getSortedfares()) {
            FDItem fdItem = (FDItem) sortedfare;
            if (fdItem.getCabin().equals(req.getCabin())) {
                return fdItem;
            }
        }
        return null;
    }


    public String etdz(ETDZ etdz, String pnrNo, int printerNo) throws Exception {
        return etdz.issueTicket(pnrNo, printerNo);//返回OK
    }


    public String convertDate(String inputDate) {
        // 解析输入的日期
        LocalDate date = LocalDate.parse(inputDate);

        // 创建自定义格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ddMMMyy", Locale.ENGLISH);

        // 转换并返回结果
        return date.format(formatter).toUpperCase();
    }

    /**
     * 获取票号
     * @param rt
     * @param pnrNo
     * @return
     */
    private Map<String, String> fetchTicketNoWithRetry(RT rt, String pnrNo) {
        Map<String, String> result = new HashMap();
        int maxAttempts = 10; // 20秒总共最多尝试10次
        int attempts = 0;
        RTResult rtResult;
        while (attempts < maxAttempts) {
            String ticketNo = "";
            try {
                rtResult = rt.retrieve(pnrNo);
                for (int i = 0; i < rtResult.getTktnosCount(); i++) {
                    PNRTktNo pnrTktNo = rtResult.getTktnoAt(i);
                    ticketNo = pnrTktNo.getTktNo();
                }
                if (!ticketNo.isEmpty()) {
                    result.put("ticketNo", ticketNo); // 获取到票号就立即返回
                    result.put("pnrStatus", rtResult.getAirSegAt(0).getActionCode());
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            try {
                Thread.sleep(2000); // 暂停2秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
            attempts++;
        }
        return result;
    }

    private String fetchTicketNo(RT rt, String pnrNo) {
        String ticketNo = "";
        try {
            RTResult rtResult = rt.retrieve(pnrNo);
            for (int i = 0; i < rtResult.getTktnosCount(); i++) {
                PNRTktNo pnrTktNo = rtResult.getTktnoAt(i);
                ticketNo = pnrTktNo.getTktNo();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ticketNo;
    }
}
