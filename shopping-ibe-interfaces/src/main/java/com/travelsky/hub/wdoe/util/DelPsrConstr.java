package com.travelsky.hub.wdoe.util;

public final class DelPsrConstr
{
  public static final String HBPWOPTIONS_WDOE_ELEMENT = "WDoe";
  public static final String HBPWOPTIONS_REQ_ELEMENT = "HbPwInfo";
  public static final String HBPWOPTIONS_AIRLINECODE = "AirlineCode";
  public static final String HBPWOPTIONS_FLIGHTNUMBER = "FlightNumber";
  public static final String HBPWOPTIONS_FLIGHTDATE = "FlightDate";
  public static final String HBPWOPTIONS_CABIN = "Cabin";
  public static final String HBPWOPTIONS_DEPTCITY = "DeptCity";
  public static final String HBPWOPTIONS_DESTCITY = "DestCity";
  public static final String HBPWOPTIONS_HOSTNUMBER = "HostNumber";
  public static final String HBPWOPTIONS_CKINMESSAGE = "ckinMessage";
  public static final String HBPWOPTIONS_OPTION = "Option";
  public static final String HBPWOPTIONS_TYPE = "type";
  public static final String HBPWOPTIONS_VALUES = "Values";
  public static final String HBPWOPTIONS_FFMESSAGE = "FFPCardNumber";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.DelPsrConstr
 * JD-Core Version:    0.6.0
 */