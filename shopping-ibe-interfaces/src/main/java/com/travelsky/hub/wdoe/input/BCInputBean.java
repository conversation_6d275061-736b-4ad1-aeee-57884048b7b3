/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class BCInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 849132421463972005L;
/*     */   private String boardingNumber;
/*     */   private String groupName;
/*     */   private String reissue;
/*     */   private String passengerName;
/*     */   private String tourIndex;
/*     */   private String local;
/*     */   private String toCity;
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  52 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  59 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/*  67 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/*  74 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/*  84 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/*  91 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getLocal()
/*     */   {
/*  98 */     return this.local;
/*     */   }
/*     */ 
/*     */   public void setLocal(String local)
/*     */   {
/* 105 */     this.local = local;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 112 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 119 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getReissue()
/*     */   {
/* 126 */     return this.reissue;
/*     */   }
/*     */ 
/*     */   public void setReissue(String reissue)
/*     */   {
/* 133 */     this.reissue = reissue;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 155 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 162 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.BCInputBean
 * JD-Core Version:    0.6.0
 */