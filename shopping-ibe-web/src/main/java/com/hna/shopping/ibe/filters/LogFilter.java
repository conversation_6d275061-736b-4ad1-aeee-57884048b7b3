package com.hna.shopping.ibe.filters;


import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2018/10/18
 */

@WebFilter(filterName = "startFilter", urlPatterns = "/*")
@Component
@Slf4j
public class LogFilter implements Filter {


    @Override
    public void init(FilterConfig filterConfig) {

    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String uuid = request.getHeader("UUID");
        if (uuid == null){
            uuid = UUID.randomUUID().toString().replace("-","");
        }
        MDC.put("UUID",uuid);
        filterChain.doFilter(servletRequest, servletResponse);
    }

    @Override
    public void destroy() {

    }
}
