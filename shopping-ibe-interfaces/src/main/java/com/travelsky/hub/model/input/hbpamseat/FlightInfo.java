/*     */ package com.travelsky.hub.model.input.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 875508151440434545L;
/*     */   private String airlineCode;
/*     */   private String arrivalAirport;
/*     */   private String departureAirport;
/*     */   private String departureDate;
/*     */   private String flightNumber;
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  35 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  42 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  57 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  63 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  70 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  77 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  84 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  90 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  97 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 104 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.FlightInfo
 * JD-Core Version:    0.6.0
 */