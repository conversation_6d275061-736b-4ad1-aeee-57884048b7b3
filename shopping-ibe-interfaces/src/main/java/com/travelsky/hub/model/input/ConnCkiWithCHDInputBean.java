/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class ConnCkiWithCHDInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String deptCity;
/*     */   private String airlineCode;
/*     */   private String destCity;
/*     */   private List<FellowPassenger> partner;
/*     */   private String flightNum;
/*     */   private FirstFlightInfo firstFlight;
/*     */   private String flightDate;
/*     */ 
/*     */   public List<FellowPassenger> getPartner()
/*     */   {
/*  35 */     return this.partner;
/*     */   }
/*     */ 
/*     */   public void setPartner(List<FellowPassenger> partner)
/*     */   {
/*  42 */     this.partner = partner;
/*     */   }
/*     */ 
/*     */   public String getFlightNum()
/*     */   {
/*  49 */     return this.flightNum;
/*     */   }
/*     */ 
/*     */   public void setFlightNum(String flightNum)
/*     */   {
/*  56 */     this.flightNum = flightNum;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  63 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  70 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  77 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  84 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  91 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/*  98 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 105 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 112 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public FirstFlightInfo getFirstFlight()
/*     */   {
/* 120 */     return this.firstFlight;
/*     */   }
/*     */ 
/*     */   public void setFirstFlight(FirstFlightInfo firstFlight)
/*     */   {
/* 127 */     this.firstFlight = firstFlight;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ConnCkiWithCHDInputBean
 * JD-Core Version:    0.6.0
 */