/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class HbPachdInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8745144808503265709L;
/*    */   private PersonInputBean psrInput;
/*    */   private List partners;
/*    */ 
/*    */   public PersonInputBean getPsrInput()
/*    */   {
/* 28 */     return this.psrInput;
/*    */   }
/*    */ 
/*    */   public void setPsrInput(PersonInputBean psrInput)
/*    */   {
/* 35 */     this.psrInput = psrInput;
/*    */   }
/*    */ 
/*    */   public List getPartners()
/*    */   {
/* 42 */     return this.partners;
/*    */   }
/*    */ 
/*    */   public void setPartners(List partners)
/*    */   {
/* 50 */     this.partners = partners;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.HbPachdInputBean
 * JD-Core Version:    0.6.0
 */