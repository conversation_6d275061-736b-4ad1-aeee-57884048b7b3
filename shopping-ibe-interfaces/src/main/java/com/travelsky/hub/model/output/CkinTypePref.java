/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CkinTypePref
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String type;
/*    */   private String prate;
/*    */ 
/*    */   public String getType()
/*    */   {
/* 32 */     return this.type;
/*    */   }
/*    */ 
/*    */   public void setType(String type)
/*    */   {
/* 40 */     this.type = type;
/*    */   }
/*    */ 
/*    */   public String getPrate()
/*    */   {
/* 48 */     return this.prate;
/*    */   }
/*    */ 
/*    */   public void setPrate(String prate)
/*    */   {
/* 56 */     this.prate = prate;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.CkinTypePref
 * JD-Core Version:    0.6.0
 */