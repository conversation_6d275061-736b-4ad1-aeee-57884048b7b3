package com.travelsky.ibe.client.pnr;

import java.util.Date;

public class BookSegment extends BookAirSeg {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9116646065452926681L;

	public String getAirNo() {
		return super.getAirNo();
	}

	public BookSegment() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookSegment(char fltClass, String orgCity, String desCity) throws Exception {
		super(fltClass, orgCity, desCity);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			Date departureTime, int type) throws Exception {
		super(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, type);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			Date departureTime) throws Exception {
		super(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			String departureTime, int type) throws Exception {
		super(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, type);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			String departureTime) throws Exception {
		super(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String airNo, char fltClass, String orgCity, String desCity) throws Exception {
		super(airNo, fltClass, orgCity, desCity);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String orgCity, String desCity, Date departureTime) throws Exception {
		super(orgCity, desCity, departureTime);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String orgCity, String desCity, String departureTime) throws Exception {
		super(orgCity, desCity, departureTime);
		// TODO Auto-generated constructor stub
	}

	public BookSegment(String orgCity, String desCity) throws Exception {
		super(orgCity, desCity);
		// TODO Auto-generated constructor stub
	}

	/*     */ public String getActionCode() {
		/* 502 */ return super.getActionCode();
		/*     */ }

	/*     */ public Date getDepartureTime() {
		/* 545 */ return super.getDepartureTime();
		// .departureTime;
		/*     */ }

	/*     */
	/*     */ public String getDepartureTimeString() {
		/* 549 */ return super.getDepartureTimeString();
		// .departureTime.toString();
		/*     */ }

	/*     */ public int getType() {
		/* 571 */ return super.getType();
	}

	// .type; }
	/*     */ public String getDesCity() {
		/* 488 */ return super.getDesCity();
		// .desCity;
		/*     */ }

	/*     */ public char getFltClass() {
		/* 462 */ return super.getFltClass();
		// .fltClass;
		/*     */ }

	/*     */ public int getTktNum() {
		/* 515 */ return super.getTktNum();
		// .tktNum;
		/*     */ }
	
	/*     */  public String getOrgCity() {
		/* 475 */     return super.getOrgCity();//.orgCity;
		/*     */   }
	public void setDepartureTime(String departureTime) throws Exception {
		// 可能是日期类型
		if(null==departureTime)
			return;
		if(departureTime.length()==5||departureTime.length()==7||departureTime.length()==10) {
			super.setDepartureTime(departureTime);
		}else {
			super.setDepartureTime(new Date(Long.parseLong(departureTime)));
		}
			

	}
}
