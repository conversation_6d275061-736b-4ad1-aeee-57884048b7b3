/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CancelOrderOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2212490553104198303L;
/*    */   private String isSuccess;
/*    */   private String orderNum;
/*    */   private String OrderStatus;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 23 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 33 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 43 */     return this.OrderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 50 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 57 */     this.OrderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 66 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 76 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 83 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 90 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 97 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CancelOrderOutPutBean
 * JD-Core Version:    0.6.0
 */