/*     */ package com.travelsky.hub.model.output.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class StopOverInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5302450565246789126L;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String seatNumber;
/*     */   private String cabinType;
/*     */   private String boardingTime;
/*     */   private String boardingGateNumber;
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  50 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  58 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  65 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  72 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  79 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  86 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  93 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 100 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 107 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 114 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 121 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 128 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.StopOverInfo
 * JD-Core Version:    0.6.0
 */