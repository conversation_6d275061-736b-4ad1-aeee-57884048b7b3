/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PaymentDetail
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4957269960730785287L;
/*    */   private String payType;
/*    */   private PaymentCard paymentCard;
/*    */   private LoyaltyRedemption loyaltyRedemption;
/*    */ 
/*    */   public String getPayType()
/*    */   {
/* 43 */     return this.payType;
/*    */   }
/*    */ 
/*    */   public void setPayType(String payType)
/*    */   {
/* 50 */     this.payType = payType;
/*    */   }
/*    */ 
/*    */   public PaymentCard getPaymentCard()
/*    */   {
/* 57 */     return this.paymentCard;
/*    */   }
/*    */ 
/*    */   public void setPaymentCard(PaymentCard paymentCard)
/*    */   {
/* 64 */     this.paymentCard = paymentCard;
/*    */   }
/*    */ 
/*    */   public LoyaltyRedemption getLoyaltyRedemption()
/*    */   {
/* 71 */     return this.loyaltyRedemption;
/*    */   }
/*    */ 
/*    */   public void setLoyaltyRedemption(LoyaltyRedemption loyaltyRedemption)
/*    */   {
/* 78 */     this.loyaltyRedemption = loyaltyRedemption;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.PaymentDetail
 * JD-Core Version:    0.6.0
 */