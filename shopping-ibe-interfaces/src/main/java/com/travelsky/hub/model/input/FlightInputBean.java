/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String fromCity;
/*    */   private String flightNo;
/*    */   private String toCity;
/*    */   private String deptDate;
/*    */ 
/*    */   public String getToCity()
/*    */   {
/* 21 */     return this.toCity;
/*    */   }
/*    */ 
/*    */   public void setToCity(String toCity)
/*    */   {
/* 28 */     this.toCity = toCity;
/*    */   }
/*    */ 
/*    */   public String getDeptDate()
/*    */   {
/* 35 */     return this.deptDate;
/*    */   }
/*    */ 
/*    */   public void setDeptDate(String deptDate)
/*    */   {
/* 42 */     this.deptDate = deptDate;
/*    */   }
/*    */ 
/*    */   public String getFlightNo()
/*    */   {
/* 49 */     return this.flightNo;
/*    */   }
/*    */ 
/*    */   public void setFlightNo(String flightNo)
/*    */   {
/* 56 */     this.flightNo = flightNo;
/*    */   }
/*    */ 
/*    */   public String getFromCity()
/*    */   {
/* 63 */     return this.fromCity;
/*    */   }
/*    */ 
/*    */   public void setFromCity(String fromCity)
/*    */   {
/* 70 */     this.fromCity = fromCity;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FlightInputBean
 * JD-Core Version:    0.6.0
 */