/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class DocCheckDecision
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String decisionCode;
/*    */   private List<CommonMsg> decisionMessages;
/*    */   private List<CommonMsg> conditions;
/*    */   private List<CommonMsg> warnings;
/*    */ 
/*    */   public String getDecisionCode()
/*    */   {
/* 36 */     return this.decisionCode;
/*    */   }
/*    */ 
/*    */   public void setDecisionCode(String decisionCode)
/*    */   {
/* 45 */     this.decisionCode = decisionCode;
/*    */   }
/*    */ 
/*    */   public List<CommonMsg> getDecisionMessages()
/*    */   {
/* 54 */     return this.decisionMessages;
/*    */   }
/*    */ 
/*    */   public void setDecisionMessages(List<CommonMsg> decisionMessages)
/*    */   {
/* 63 */     this.decisionMessages = decisionMessages;
/*    */   }
/*    */ 
/*    */   public List<CommonMsg> getConditions()
/*    */   {
/* 72 */     return this.conditions;
/*    */   }
/*    */ 
/*    */   public void setConditions(List<CommonMsg> conditions)
/*    */   {
/* 81 */     this.conditions = conditions;
/*    */   }
/*    */ 
/*    */   public List<CommonMsg> getWarnings()
/*    */   {
/* 90 */     return this.warnings;
/*    */   }
/*    */ 
/*    */   public void setWarnings(List<CommonMsg> warnings)
/*    */   {
/* 99 */     this.warnings = warnings;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DocCheckDecision
 * JD-Core Version:    0.6.0
 */