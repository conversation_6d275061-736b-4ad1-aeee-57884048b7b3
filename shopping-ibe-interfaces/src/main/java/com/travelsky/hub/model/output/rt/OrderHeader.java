/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class OrderHeader
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 9128725610843278431L;
/*    */   private String bestBuyInd;
/*    */   private String pnrrl;
/*    */   private String crsrl;
/*    */ 
/*    */   public String getBestBuyInd()
/*    */   {
/* 34 */     return this.bestBuyInd;
/*    */   }
/*    */ 
/*    */   public void setBestBuyInd(String bestBuyInd)
/*    */   {
/* 41 */     this.bestBuyInd = bestBuyInd;
/*    */   }
/*    */ 
/*    */   public String getPnrrl()
/*    */   {
/* 48 */     return this.pnrrl;
/*    */   }
/*    */ 
/*    */   public void setPnrrl(String pnrrl)
/*    */   {
/* 55 */     this.pnrrl = pnrrl;
/*    */   }
/*    */ 
/*    */   public String getCrsrl()
/*    */   {
/* 62 */     return this.crsrl;
/*    */   }
/*    */ 
/*    */   public void setCrsrl(String crsrl)
/*    */   {
/* 69 */     this.crsrl = crsrl;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.OrderHeader
 * JD-Core Version:    0.6.0
 */