package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNROSI extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8686049946407494430L;
	 private String osi;                                                                    
	 private String psgrID;                                                                   
	 int index;                                                                         
	                                                                                        
	 public PNROSI()                                                                        
	   throws Exception                                                                     
	 {                                                                                      
	 }                                                                                      
	                                                                                      
	                                                                                        
	 public PNROSI(String osi, String pNum)                                                 
	   throws Exception                                                                     
	 {                                                                                      
	   this.osi = osi;                                                                      
	   this.psgrID = pNum;                                                                    
	 }                                                                                      
	                                                                                             
	                                                                                        
                                                                                  

}
