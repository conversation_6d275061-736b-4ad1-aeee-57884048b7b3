/*    */ package com.travelsky.hub.model.peentity.meal.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BookingMealRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7240887295991782940L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 18 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 27 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 34 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 42 */     return this.resultMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.BookingMealRS
 * JD-Core Version:    0.6.0
 */