package com.hna.shopping.ibe.interfaces.dto.pricing.response;
import java.io.Serializable;
import java.util.List;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
@Data
public class PricedItinerarys implements Serializable {

    private static final long serialVersionUID = 8170505889351242913L;
    private List<Integer> Endorsements_fareindex;
    private int commissionrate;
    private String encryptCode;
    private List<String> encryptRequestParameter;
    private List<String> endorsements;
    private List<Integer> endorsements_fareindex;
    private int fareBreakDownCount;
    private List<FareBreakDowns> fareBreakDowns;
    private List<FareInfos> fareInfos;
    private int fareRuleCount;
    private boolean flightRestriction;
    private List<FlightSegments> flightSegments;
    private int funcCtrlChar;
    private List<String> notes;
    private boolean publicPrivateConstructInd;
    private boolean rBDOverride;
    private boolean rBDRestriction;
    private int segmentCount;
    private int sequenceNumber;
    private boolean ticketTimeConversion;
    private long ticketTimeLimit;
    private String ticketType;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date timestamp;


}