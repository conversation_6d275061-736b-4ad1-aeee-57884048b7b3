
package com.hna.shopping.ibe.interfaces.dto.searchone.response;


import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class SearchOneResponse extends BaseResponse {


    private static final long serialVersionUID = -5872379917792700208L;
    /**
     * flights : [{"id":0,"carrier":"8L ","flightNumber":"802 ","operatingCarrier":"8L ","operatingFlightNumber":"802 ","departureAirport":"BKK","departureTerminal":"  ","arrivalAirport":"KMG","arrivalTerminal":"  ","departureDate":{"year":2020,"day":14,"month":7},"departureTime":{"hour":19,"minutes":25},"arrivalDate":{"year":2020,"day":14,"month":7},"arrivalTime":{"hour":22,"minutes":45},"aircraftChange":false,"intermediateAirports":[],"aircraftTypes":[{"aircraftTypeCode":"33E","aircraftGroupCode":"330","category":"J"}],"serviceType":"J","distance":795,"duration":140,"displayCarrier":"   ","mealService":"","et":true,"ASR":true,"stopQuantity":0,"groundTimes":[]}]
     * fares : [{"id":0,"carrier":"8L ","origin":"BKK","destination":"KMG","fbc":"HLOW9TH ","private":false,"fareTypeCode":"XOX","globalIndicator":"EH"},{"id":1,"carrier":"8L ","origin":"BKK","destination":"KMG","fbc":"CLOW9TH ","private":false,"fareTypeCode":"XOX","globalIndicator":"EH"}]
     * solutions : []
     * multicabinSolutions : [{"sId":0,"requestSegments":[{"origin":"BKK","destination":"KMG","flights":[0]}],"tickets":[{"fares":[{"fareId":0,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"H ","restriction":" "}],"cabin":"Y   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":1,"cabin":"Y   "},{"fareId":1,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"C ","restriction":" "}],"cabin":"C   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":2,"cabin":"C   "}],"platingCarrier":"8L ","pricingIndicator":"PA","passengers":[{"idRequired":false,"fareInfo":[{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"HLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}},{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"CLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}}]}],"multicabinCombos":[{"fares":[1],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":3471,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":3471,"currency":"CNY"},"totalBase":{"amount":3070,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":3070,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":490},{"fares":[2],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":13501,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":13501,"currency":"CNY"},"totalBase":{"amount":13100,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":13100,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":30,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":1908}],"avls":["C:A,D:0,I:0,J:0,Y:A,B:A,H:A,K:0,L:0,M:0,X:0,V:0,N:0,Q:0,P:0,A:0,U:0,T:0,Z:0,R:0,E:0,W:0,S:0,G:0,O:0"]}]}]
     * agencies : [{"id":0,"channel":"1E","pos":"KMG","iataNumber":"********","departmentCode":"C8L    ","travelAgencyCode":"LKE305"}]
     * debugInformation : {"elapseTime":"0.0646s","versions":{"atpcoDbVersion":"20200629_220100-20200630_021405","flightlineDbVersion":"20200630_000000-20200629_181155","currenciesDbVersion":"20200630_023821-20200630_015218","codeVersion":"5.9-3271-gfc1abbf"}}
     * uuid : SWeRJA2w-l4KBTQJCgVZGBOI0SxrCgEAAAAAAAEAAAA=
     */

    private DebugInformationBean debugInformation;
    private String uuid;
    private List<FlightsBean> flights;
    private List<FaresBean> fares;
    private List<MulticabinSolutionsBean> multicabinSolutions;
    private List<AgenciesBean> agencies;

    @NoArgsConstructor
    @Data
    public static class DebugInformationBean implements Serializable {
        private static final long serialVersionUID = -5207803255107741403L;
        /**
         * elapseTime : 0.0646s
         * versions : {"atpcoDbVersion":"20200629_220100-20200630_021405","flightlineDbVersion":"20200630_000000-20200629_181155","currenciesDbVersion":"20200630_023821-20200630_015218","codeVersion":"5.9-3271-gfc1abbf"}
         */

        private String elapseTime;
        private VersionsBean versions;

        @NoArgsConstructor
        @Data
        public static class VersionsBean implements Serializable {
            private static final long serialVersionUID = 2092693927757308099L;
            /**
             * atpcoDbVersion : 20200629_220100-20200630_021405
             * flightlineDbVersion : 20200630_000000-20200629_181155
             * currenciesDbVersion : 20200630_023821-20200630_015218
             * codeVersion : 5.9-3271-gfc1abbf
             */

            private String atpcoDbVersion;
            private String flightlineDbVersion;
            private String currenciesDbVersion;
            private String codeVersion;
        }
    }

    @NoArgsConstructor
    @Data
    public static class FlightsBean implements Serializable {
        private static final long serialVersionUID = 34845850908983614L;
        /**
         * id : 0
         * carrier : 8L
         * flightNumber : 802
         * operatingCarrier : 8L
         * operatingFlightNumber : 802
         * departureAirport : BKK
         * departureTerminal :
         * arrivalAirport : KMG
         * arrivalTerminal :
         * departureDate : {"year":2020,"day":14,"month":7}
         * departureTime : {"hour":19,"minutes":25}
         * arrivalDate : {"year":2020,"day":14,"month":7}
         * arrivalTime : {"hour":22,"minutes":45}
         * aircraftChange : false
         * intermediateAirports : []
         * aircraftTypes : [{"aircraftTypeCode":"33E","aircraftGroupCode":"330","category":"J"}]
         * serviceType : J
         * distance : 795
         * duration : 140
         * displayCarrier :
         * mealService :
         * et : true
         * ASR : true
         * stopQuantity : 0
         * groundTimes : []
         */

        private int id;
        private String carrier;
        private String flightNumber;
        private String operatingCarrier;
        private String operatingFlightNumber;
        private String departureAirport;
        private String departureTerminal;
        private String arrivalAirport;
        private String arrivalTerminal;
        private DepartureDateBean departureDate;
        private DepartureTimeBean departureTime;
        private ArrivalDateBean arrivalDate;
        private ArrivalTimeBean arrivalTime;
        private boolean aircraftChange;
        private String serviceType;
        private int distance;
        private int duration;
        private String displayCarrier;
        private String mealService;
        private boolean et;
        private boolean ASR;
        private int stopQuantity;
        private List<?> intermediateAirports;
        private List<AircraftTypesBean> aircraftTypes;
        private List<?> groundTimes;

        @NoArgsConstructor
        @Data
        public static class DepartureDateBean implements Serializable {
            private static final long serialVersionUID = 8187314358008332319L;
            /**
             * year : 2020
             * day : 14
             * month : 7
             */

            private int year;
            private int day;
            private int month;
        }

        @NoArgsConstructor
        @Data
        public static class DepartureTimeBean implements Serializable {
            private static final long serialVersionUID = -3916041149191711807L;
            /**
             * hour : 19
             * minutes : 25
             */

            private int hour;
            private int minutes;
        }

        @NoArgsConstructor
        @Data
        public static class ArrivalDateBean implements Serializable {
            private static final long serialVersionUID = 2729560464563035077L;
            /**
             * year : 2020
             * day : 14
             * month : 7
             */

            private int year;
            private int day;
            private int month;
        }

        @NoArgsConstructor
        @Data
        public static class ArrivalTimeBean implements Serializable {
            private static final long serialVersionUID = 8141847873554502526L;
            /**
             * hour : 22
             * minutes : 45
             */

            private int hour;
            private int minutes;
        }

        @NoArgsConstructor
        @Data
        public static class AircraftTypesBean implements Serializable {
            private static final long serialVersionUID = -7256567143745207929L;
            /**
             * aircraftTypeCode : 33E
             * aircraftGroupCode : 330
             * category : J
             */

            private String aircraftTypeCode;
            private String aircraftGroupCode;
            private String category;
        }
    }

    @NoArgsConstructor
    @Data
    public static class FaresBean implements Serializable {
        private static final long serialVersionUID = -4278877870962170636L;
        /**
         * id : 0
         * carrier : 8L
         * origin : BKK
         * destination : KMG
         * fbc : HLOW9TH
         * private : false
         * fareTypeCode : XOX
         * globalIndicator : EH
         */

        private int id;
        private String carrier;
        private String origin;
        private String destination;
        private String fbc;
        private boolean privateX;
        private String fareTypeCode;
        private String globalIndicator;
    }

    @NoArgsConstructor
    @Data
    public static class MulticabinSolutionsBean implements Serializable {
        private static final long serialVersionUID = 1131932545205054525L;
        /**
         * sId : 0
         * requestSegments : [{"origin":"BKK","destination":"KMG","flights":[0]}]
         * tickets : [{"fares":[{"fareId":0,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"H ","restriction":" "}],"cabin":"Y   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":1,"cabin":"Y   "},{"fareId":1,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"C ","restriction":" "}],"cabin":"C   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":2,"cabin":"C   "}],"platingCarrier":"8L ","pricingIndicator":"PA","passengers":[{"idRequired":false,"fareInfo":[{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"HLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}},{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"CLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}}]}],"multicabinCombos":[{"fares":[1],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":3471,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":3471,"currency":"CNY"},"totalBase":{"amount":3070,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":3070,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":490},{"fares":[2],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":13501,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":13501,"currency":"CNY"},"totalBase":{"amount":13100,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":13100,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":30,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":1908}],"avls":["C:A,D:0,I:0,J:0,Y:A,B:A,H:A,K:0,L:0,M:0,X:0,V:0,N:0,Q:0,P:0,A:0,U:0,T:0,Z:0,R:0,E:0,W:0,S:0,G:0,O:0"]}]
         */

        private int sId;
        private List<RequestSegmentsBean> requestSegments;
        private List<TicketsBean> tickets;

        @NoArgsConstructor
        @Data
        public static class RequestSegmentsBean implements Serializable {
            private static final long serialVersionUID = 5257433486872453181L;
            /**
             * origin : BKK
             * destination : KMG
             * flights : [0]
             */

            private String origin;
            private String destination;
            private List<Integer> flights;
        }

        @NoArgsConstructor
        @Data
        public static class TicketsBean implements Serializable {
            private static final long serialVersionUID = -1427905244052922128L;
            /**
             * fares : [{"fareId":0,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"H ","restriction":" "}],"cabin":"Y   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":1,"cabin":"Y   "},{"fareId":1,"flights":[{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"C ","restriction":" "}],"cabin":"C   ","seats":10}}],"changeable":false,"refundable":false,"upgradable":false,"io":0,"id":2,"cabin":"C   "}]
             * platingCarrier : 8L
             * pricingIndicator : PA
             * passengers : [{"idRequired":false,"fareInfo":[{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"HLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}},{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"CLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}}]}]
             * multicabinCombos : [{"fares":[1],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":3471,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":3471,"currency":"CNY"},"totalBase":{"amount":3070,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":3070,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":490},{"fares":[2],"private":false,"accountCode":"                    ","tourCode":"               ","ticketPrice":{"amount":13501,"currency":"CNY"},"passengers":[{"ptc":"ADT","price":{"total":{"amount":13501,"currency":"CNY"},"totalBase":{"amount":13100,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":13100,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":30,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}],"sortScore":1908}]
             * avls : ["C:A,D:0,I:0,J:0,Y:A,B:A,H:A,K:0,L:0,M:0,X:0,V:0,N:0,Q:0,P:0,A:0,U:0,T:0,Z:0,R:0,E:0,W:0,S:0,G:0,O:0"]
             */

            private String platingCarrier;
            private String pricingIndicator;
            private List<FaresBeanX> fares;
            private List<PassengersBeanX> passengers;
            private List<MulticabinCombosBean> multicabinCombos;
            private List<String> avls;

            @NoArgsConstructor
            @Data
            public static class FaresBeanX implements Serializable {
                private static final long serialVersionUID = -9190754076542067346L;
                /**
                 * fareId : 0
                 * flights : [{"flightId":0,"passengers":{"rbdInfos":[{"rbd":"H ","restriction":" "}],"cabin":"Y   ","seats":10}}]
                 * changeable : false
                 * refundable : false
                 * upgradable : false
                 * io : 0
                 * id : 1
                 * cabin : Y
                 */

                private int fareId;
                private boolean changeable;
                private boolean refundable;
                private boolean upgradable;
                private int io;
                private int id;
                private String cabin;
                private List<FlightsBeanX> flights;

                @NoArgsConstructor
                @Data
                public static class FlightsBeanX  implements Serializable {
                    private static final long serialVersionUID = 5318342555543608984L;
                    /**
                     * flightId : 0
                     * passengers : {"rbdInfos":[{"rbd":"H ","restriction":" "}],"cabin":"Y   ","seats":10}
                     */

                    private int flightId;
                    private PassengersBean passengers;

                    @NoArgsConstructor
                    @Data
                    public static class PassengersBean implements Serializable {
                        private static final long serialVersionUID = 7838696365495425659L;
                        /**
                         * rbdInfos : [{"rbd":"H ","restriction":" "}]
                         * cabin : Y
                         * seats : 10
                         */

                        private String cabin;
                        private int seats;
                        private List<RbdInfosBean> rbdInfos;

                        @NoArgsConstructor
                        @Data
                        public static class RbdInfosBean implements Serializable {
                            private static final long serialVersionUID = 2602942583753107752L;
                            /**
                             * rbd : H
                             * restriction :
                             */

                            private String rbd;
                            private String restriction;
                        }
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class PassengersBeanX  implements Serializable {
                private static final long serialVersionUID = 7956614274656242100L;
                /**
                 * idRequired : false
                 * fareInfo : [{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"HLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}},{"negotiatedFare":false,"accompaniedTravel":false,"fbcOverride":"CLOW9TH        ","ruleRef1":"key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)","ruleRef2":"008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::","dataSource":"ATPCO","penalty":{"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}}]
                 */

                private boolean idRequired;
                private List<FareInfoBean> fareInfo;

                @NoArgsConstructor
                @Data
                public static class FareInfoBean implements Serializable {
                    private static final long serialVersionUID = 4151486559483934851L;
                    /**
                     * negotiatedFare : false
                     * accompaniedTravel : false
                     * fbcOverride : HLOW9TH
                     * ruleRef1 : key(RuleTariff::Rule::OWRT::Routing::Footnote1::Footnote2::PassengerType::FareType::OriginAddonTariff::OriginAddonFootnote1::OriginAddonFootnote2::DestinationAddonTariff::DestinationAddonFootnote1::DestinationAddonFootnote2::TravelAgencyCode::IataNumber::DepartmentCode::Origin::Destination::FareSource::FbrBaseTariff::FbrBaseRule::AccountCode::FbrBaseFareBasis::OriginAddonRouting::DestinationAddonRouting)
                     * ruleRef2 : 008::KM19::1::0001::11::::ADT::XOX::::::::::::::LKE305::********::C8L::BKK::KMG::ATPCO::::::::::::
                     * dataSource : ATPCO
                     * penalty : {"change":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"refund":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}},"noshow":{"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}}
                     */

                    private boolean negotiatedFare;
                    private boolean accompaniedTravel;
                    private String fbcOverride;
                    private String ruleRef1;
                    private String ruleRef2;
                    private String dataSource;
                    private PenaltyBean penalty;

                    @NoArgsConstructor
                    @Data
                    public static class PenaltyBean implements Serializable {
                        private static final long serialVersionUID = 7442726363923913332L;
                        /**
                         * change : {"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}
                         * refund : {"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowBeforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"noshowAfterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}
                         * noshow : {"beforeDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}},"afterDeparture":{"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}}
                         */

                        private ChangeBean change;
                        private RefundBean refund;
                        private NoshowBean noshow;

                        @NoArgsConstructor
                        @Data
                        public static class ChangeBean implements Serializable {
                            private static final long serialVersionUID = 8407347052543162172L;
                            /**
                             * beforeDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * afterDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * noshowBeforeDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * noshowAfterDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             */

                            private BeforeDepartureBean beforeDeparture;
                            private AfterDepartureBean afterDeparture;
                            private NoshowBeforeDepartureBean noshowBeforeDeparture;
                            private NoshowAfterDepartureBean noshowAfterDeparture;

                            @NoArgsConstructor
                            @Data
                            public static class BeforeDepartureBean implements Serializable {
                                private static final long serialVersionUID = -4031165239092402295L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBean price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBean implements Serializable {
                                    private static final long serialVersionUID = -6668359707365550688L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class AfterDepartureBean implements Serializable {
                                private static final long serialVersionUID = 3873220818700131380L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanX  implements Serializable {
                                    private static final long serialVersionUID = -7836264226298305596L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class NoshowBeforeDepartureBean implements Serializable {
                                private static final long serialVersionUID = 4101150413437985332L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXX  implements Serializable {
                                    private static final long serialVersionUID = -2744595138746299893L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class NoshowAfterDepartureBean implements Serializable {
                                private static final long serialVersionUID = -4759475468537126564L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXX  implements Serializable {
                                    private static final long serialVersionUID = 7580377564830612013L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class RefundBean implements Serializable {
                            private static final long serialVersionUID = -6145692760983901617L;
                            /**
                             * beforeDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * afterDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * noshowBeforeDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * noshowAfterDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             */

                            private BeforeDepartureBeanX beforeDeparture;
                            private AfterDepartureBeanX afterDeparture;
                            private NoshowBeforeDepartureBeanX noshowBeforeDeparture;
                            private NoshowAfterDepartureBeanX noshowAfterDeparture;

                            @NoArgsConstructor
                            @Data
                            public static class BeforeDepartureBeanX  implements Serializable {
                                private static final long serialVersionUID = -7717172374756012679L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXX  implements Serializable {
                                    private static final long serialVersionUID = 9112941970171307379L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class AfterDepartureBeanX  implements Serializable {
                                private static final long serialVersionUID = -8260788003919921654L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXXX  implements Serializable {
                                    private static final long serialVersionUID = -2640193875648803896L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class NoshowBeforeDepartureBeanX  implements Serializable {
                                private static final long serialVersionUID = -1509218019868573156L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXXXX  implements Serializable {
                                    private static final long serialVersionUID = -3682829447954991781L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class NoshowAfterDepartureBeanX  implements Serializable {
                                private static final long serialVersionUID = -1817661519661706738L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXXXXX  implements Serializable {
                                    private static final long serialVersionUID = 7625047099951580915L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class NoshowBean implements Serializable {
                            private static final long serialVersionUID = 5351572956853007053L;
                            /**
                             * beforeDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             * afterDeparture : {"category":16,"allowed":true,"price":{"amount":-1,"currency":"NUC"}}
                             */

                            private BeforeDepartureBeanXX beforeDeparture;
                            private AfterDepartureBeanXX afterDeparture;

                            @NoArgsConstructor
                            @Data
                            public static class BeforeDepartureBeanXX  implements Serializable {
                                private static final long serialVersionUID = -7969911403736518937L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXXXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXXXXXX  implements Serializable  {
                                    private static final long serialVersionUID = -848083721847931687L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }

                            @NoArgsConstructor
                            @Data
                            public static class AfterDepartureBeanXX  implements Serializable {
                                private static final long serialVersionUID = -8546174279743726934L;
                                /**
                                 * category : 16
                                 * allowed : true
                                 * price : {"amount":-1,"currency":"NUC"}
                                 */

                                private int category;
                                private boolean allowed;
                                private PriceBeanXXXXXXXXX price;

                                @NoArgsConstructor
                                @Data
                                public static class PriceBeanXXXXXXXXX  implements Serializable {
                                    private static final long serialVersionUID = 3794360946475038066L;
                                    /**
                                     * amount : -1
                                     * currency : NUC
                                     */

                                    private int amount;
                                    private String currency;
                                }
                            }
                        }
                    }
                }
            }

            @NoArgsConstructor
            @Data
            public static class MulticabinCombosBean implements Serializable {
                private static final long serialVersionUID = -8822961975643036968L;
                /**
                 * fares : [1]
                 * private : false
                 * accountCode :
                 * tourCode :
                 * ticketPrice : {"amount":3471,"currency":"CNY"}
                 * passengers : [{"ptc":"ADT","price":{"total":{"amount":3471,"currency":"CNY"},"totalBase":{"amount":3070,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":3070,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}}]
                 * sortScore : 490
                 */

                private boolean privateX;
                private String accountCode;
                private String tourCode;
                private TicketPriceBean ticketPrice;
                private int sortScore;
                private List<Integer> fares;
                private List<PassengersBeanXX> passengers;

                @NoArgsConstructor
                @Data
                public static class TicketPriceBean implements Serializable {
                    private static final long serialVersionUID = 5270472588773650016L;
                    /**
                     * amount : 3471
                     * currency : CNY
                     */

                    private int amount;
                    private String currency;
                }

                @NoArgsConstructor
                @Data
                public static class PassengersBeanXX  implements Serializable {
                    private static final long serialVersionUID = -4397325664307230811L;
                    /**
                     * ptc : ADT
                     * price : {"total":{"amount":3471,"currency":"CNY"},"totalBase":{"amount":3070,"currency":"CNY"},"totalTaxIata":{"amount":172,"currency":"CNY"},"totalTaxYQYR":{"amount":229,"currency":"CNY"},"base":[{"amount":3070,"currency":"CNY"}],"tax":[{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}],"baggageInfo":[{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}],"freeCarry-onBagInfo":[{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}],"commission":{"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}}
                     */

                    private String ptc;
                    private PriceBeanXXXXXXXXXXX price;

                    @NoArgsConstructor
                    @Data
                    public static class PriceBeanXXXXXXXXXXX  implements Serializable {
                        private static final long serialVersionUID = -5697708050328762936L;
                        /**
                         * total : {"amount":3471,"currency":"CNY"}
                         * totalBase : {"amount":3070,"currency":"CNY"}
                         * totalTaxIata : {"amount":172,"currency":"CNY"}
                         * totalTaxYQYR : {"amount":229,"currency":"CNY"}
                         * base : [{"amount":3070,"currency":"CNY"}]
                         * tax : [{"code":"YQ I","price":{"amount":29,"currency":"CNY"}},{"code":"YR F","price":{"amount":200,"currency":"CNY"}},{"code":"G8  ","price":{"amount":3,"currency":"CNY"}},{"code":"E7  ","price":{"amount":8,"currency":"CNY"}},{"code":"TS  ","price":{"amount":161,"currency":"CNY"}}]
                         * baggageInfo : [{"allowedPieces":0,"allowedWeight":20,"allowedWeightUnit":"K"}]
                         * freeCarry-onBagInfo : [{"allowedPieces":0,"allowedWeight":0,"allowedWeightUnit":""}]
                         * commission : {"type":"NET","percent":0,"amount":{"amount":0,"currency":"   "},"commissionSource":"HOST"}
                         */

                        private TotalBean total;
                        private TotalBaseBean totalBase;
                        private TotalTaxIataBean totalTaxIata;
                        private TotalTaxYQYRBean totalTaxYQYR;
                        private CommissionBean commission;
                        private List<BaseBean> base;
                        private List<TaxBean> tax;
                        private List<BaggageInfoBean> baggageInfo;
                        private List<FreeCarryonBagInfoBean> freeCarryonBagInfo;

                        @NoArgsConstructor
                        @Data
                        public static class TotalBean implements Serializable {
                            private static final long serialVersionUID = -7382690622146410915L;
                            /**
                             * amount : 3471
                             * currency : CNY
                             */

                            private int amount;
                            private String currency;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TotalBaseBean implements Serializable {
                            private static final long serialVersionUID = -854402009334029312L;
                            /**
                             * amount : 3070
                             * currency : CNY
                             */

                            private int amount;
                            private String currency;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TotalTaxIataBean implements Serializable {
                            private static final long serialVersionUID = -5866235702836157315L;
                            /**
                             * amount : 172
                             * currency : CNY
                             */

                            private int amount;
                            private String currency;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TotalTaxYQYRBean implements Serializable {
                            private static final long serialVersionUID = 455876053335198505L;
                            /**
                             * amount : 229
                             * currency : CNY
                             */

                            private int amount;
                            private String currency;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class CommissionBean implements Serializable {
                            private static final long serialVersionUID = -3456215285050942830L;
                            /**
                             * type : NET
                             * percent : 0
                             * amount : {"amount":0,"currency":"   "}
                             * commissionSource : HOST
                             */

                            private String type;
                            private int percent;
                            private AmountBean amount;
                            private String commissionSource;

                            @NoArgsConstructor
                            @Data
                            public static class AmountBean implements Serializable {
                                private static final long serialVersionUID = -8596673470502074795L;
                                /**
                                 * amount : 0
                                 * currency :
                                 */

                                private int amount;
                                private String currency;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class BaseBean implements Serializable {
                            private static final long serialVersionUID = 2270093628381529967L;
                            /**
                             * amount : 3070
                             * currency : CNY
                             */

                            private int amount;
                            private String currency;
                            private int originAddonAmount;
                            private int destinationAddonAmount;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class TaxBean implements Serializable {
                            private static final long serialVersionUID = 3761084514423753370L;
                            /**
                             * code : YQ I
                             * price : {"amount":29,"currency":"CNY"}
                             */

                            private String code;
                            private PriceBeanXXXXXXXXXX price;

                            @NoArgsConstructor
                            @Data
                            public static class PriceBeanXXXXXXXXXX  implements Serializable {
                                private static final long serialVersionUID = 5884256489177605573L;
                                /**
                                 * amount : 29
                                 * currency : CNY
                                 */

                                private int amount;
                                private String currency;
                            }
                        }

                        @NoArgsConstructor
                        @Data
                        public static class BaggageInfoBean implements Serializable {
                            private static final long serialVersionUID = 7945428465722040101L;
                            /**
                             * allowedPieces : 0
                             * allowedWeight : 20
                             * allowedWeightUnit : K
                             */

                            private int allowedPieces;
                            private int allowedWeight;
                            private String allowedWeightUnit;
                        }

                        @NoArgsConstructor
                        @Data
                        public static class FreeCarryonBagInfoBean implements Serializable {
                            private static final long serialVersionUID = -6585368227149975352L;
                            /**
                             * allowedPieces : 0
                             * allowedWeight : 0
                             * allowedWeightUnit :
                             */

                            private int allowedPieces;
                            private int allowedWeight;
                            private String allowedWeightUnit;
                        }
                    }
                }
            }
        }
    }

    @NoArgsConstructor
    @Data
    public static class AgenciesBean implements Serializable {
        private static final long serialVersionUID = 3971545463875010494L;
        /**
         * id : 0
         * channel : 1E
         * pos : KMG
         * iataNumber : ********
         * departmentCode : C8L
         * travelAgencyCode : LKE305
         */

        private int id;
        private String channel;
        private String pos;
        private String iataNumber;
        private String departmentCode;
        private String travelAgencyCode;
    }
}