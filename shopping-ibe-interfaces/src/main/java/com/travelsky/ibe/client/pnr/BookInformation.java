package com.travelsky.ibe.client.pnr;

import com.google.common.collect.Lists;
import com.hna.shopping.ibe.interfaces.dto.FareBox;
import com.travelsky.ibe.exceptions.IBELocalException;
import com.travelsky.util.CommandParser;
import com.travelsky.util.QDateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

public class BookInformation extends BookInfomation{
	private static final long serialVersionUID = -8126335750410623124L;
//	 private List<BookSegment> airsegs;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
//	 private List<BookBA> bas;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
//	 private List<BookContactEx> contacts;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  
//	 private List<BookEI> eis;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
//	 private String envelopType;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
	private List<BookFCEx> pnrfcs = Lists.newArrayList();
	private List<BookFNEx> bookFNExs = Lists.newArrayList();
//	 private List<PNRFN> pnrfns;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
//	 private List<BookFP> fps;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
//	 private String groupName;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
//	 private boolean groupticket;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
//	 private List<BookInfantEx> infants;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
//	 private String officeCode = "";                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
//	 private List<BookOI> ois;
//	 private List<BookOSIEx> osis;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
//	 private int passengernumber;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
//	 private List<BookPassengerEx> passengers;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
//	 private String pnrno;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
//	 private List<BookRMKEx> rmks;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
//	 private List<BookSSREx> ssrs;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
//	 private List<BookTC> tcs;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
//	 private List<BookTktStatus> tktStatus;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
//	 private String tkttimelimit;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
	 private List<AuxiliaryService> auxiliaryServices;
//	 public List<BookOP> ops = new ArrayList();                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

	public void addOI(BookOIEx oi) throws Exception{
		super.addOI(oi);
	}

	public BookInformation() {
	  	super();
	}

	public void addAdult(String name) throws Exception {
		BookPassengerEx passenger = new BookPassengerEx(name);
	    super.addPassenger(passenger);
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addAirSeg(BookSegment airseg) throws Exception {
	    super.addAirSeg(airseg);
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addAirSeg(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum, Date departureTime) throws Exception {
	    BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
	    super.addAirSeg(airseg);
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addAirSeg(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum, String departureTime) throws Exception {
	   if (departureTime != null)
	   	if (departureTime.length() == 7)
	       	departureTime = QDateTime.dateToString(QDateTime.stringToDate(departureTime, "ddmmmyy"), "yyyy-mm-dd");
		else if (departureTime.length() == 5)
	       departureTime = QDateTime.dateToString(QDateTime.stringToDate(departureTime, "ddmmm"), "yyyy-mm-dd");
	   BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
	   super.addAirSeg(airseg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
	 }                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

	public void addChild(String name) throws Exception {
	   BookPassengerEx passenger = new BookPassengerEx(name, 1, 0);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
	   super.addPassenger(passenger);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addContact(BookContactEx contact) throws Exception {
	   super.addContact(contact);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addContact(String contactinfo) throws Exception {
	   BookContactEx contact = new BookContactEx(contactinfo);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
	   super.addContact(contact);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        
	}
	         
	public void addEI(String ei) {
		if (ei != null) {
			BookEIEx eiEx = new BookEIEx(ei);
			super.addEI(eiEx);
		}
	}

	public void addEI(BookEIEx ei) {
	   if (ei != null)
	     super.addEI(ei);
	}
	
	public void addEI(BookEIEx ei, String[] name) {
	   if (ei != null)
	     super.addEI(ei, name);
	}

	public void addFC(BookFCEx fc)
	 {                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
		 this.pnrfcs.add(fc);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
	 }                     

	public void addFC(String fcStr) {
		BookFCEx pnrfc = new BookFCEx(fcStr);
        this.pnrfcs.add(pnrfc);
	}

	public void addFN(BookFNEx fn) {
		this.bookFNExs.add(fn);
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addFN(String fnStr) {
		this.bookFNExs.add(new BookFNEx(fnStr));
	}

	public void addInfant(BookInfantEx infant) throws Exception {
		super.addInfant(infant);
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addInfant(Date birth, String carrierName, String name) throws Exception {
	   BookInfantEx infant = new BookInfantEx(birth, carrierName, name);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	   super.addInfant(infant);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	public void addInfant(String birth, String carrierName, String name) throws Exception {
	   if (birth.length() == 7)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
	     birth = QDateTime.dateToString(QDateTime.stringToDate(birth, "ddmmmyy"), "yyyy-mm-dd");
	   BookInfantEx infant = new BookInfantEx(birth, carrierName, name);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
	   super.addInfant(infant);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
	}
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addInfoAirSeg(String orgCity, String desCity) throws Exception {
	   BookSegment airseg = new BookSegment(orgCity, desCity);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
	   super.addAirSeg(airseg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addInfoAirSeg(String orgCity, String desCity, Date departureTime) throws Exception {
	   BookSegment airseg = new BookSegment(orgCity, desCity, departureTime);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
	   super.addAirSeg(airseg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addInfoAirSeg(String orgCity, String desCity, String departureTime) throws Exception {
	   if ((departureTime != null) && (departureTime.length() > 0))                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
	     if (departureTime.length() == 7)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 
	       departureTime = QDateTime.dateToString(QDateTime.stringToDate(departureTime, "ddmmmyy"), "yyyy-mm-dd");
	     else if (departureTime.length() == 5)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
	       departureTime = QDateTime.dateToString(QDateTime.stringToDate(departureTime, "ddmmm"), "yyyy-mm-dd");
	   BookSegment airseg = new BookSegment(orgCity, desCity, departureTime);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               
	   super.addAirSeg(airseg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    }

    public void addOpenAirSeg(String airNo, char fltClass, String orgCity, String desCity) throws Exception {
	   BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             
	   super.addAirSeg(airseg);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addOSI(BookOSIEx osi) throws Exception {
	   super.addOSI(osi);
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addOSI(String airCode, String osiinfo) throws Exception {
	   BookOSIEx osi = new BookOSIEx(airCode, osiinfo);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
	   super.addOSI(osi);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addPassenger(BookPassengerEx passenger) throws Exception {
	    super.addPassenger(passenger);
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addRMK(BookRMKEx rmk) throws Exception {
	   super.addRMK(rmk);//.add(rmk);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
    }
	                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
    public void addRMK(String rmktype, String rmkinfo) throws Exception {
	   BookRMKEx rmk = new BookRMKEx(rmktype, rmkinfo);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
	   super.addRMK(rmk);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                
    }

    public void addUnCompanionChild(String name, int age) throws Exception {
	   BookPassengerEx passenger = new BookPassengerEx(name, 2, age);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         
	   super.addPassenger(passenger);                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    
    }

    public void setAirsegs(List<BookSegment> airsegs) {
		if (null == airsegs)
			return;
         for (BookSegment bookSegment : airsegs) {
			try {
				this.addAirSeg(bookSegment);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	
	public void setPassengers(List<BookPassengerEx> passengerExs) {
		if(null == passengerExs)
			return;
		for(BookPassengerEx bookPassengerEx:passengerExs) {
			try {
				this.addPassenger(bookPassengerEx);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
	}
	
	public void setOsis(List<BookOSIEx> bookOSIExs) {
		if(null == bookOSIExs)
			return;
		for(BookOSIEx bookPassengerEx:bookOSIExs) {
			try {
				this.addOSI(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setContacts(List<BookContactEx> bookOSIExs) {
		if(null == bookOSIExs)
			return;
		for(BookContactEx bookPassengerEx:bookOSIExs) {
			try {
				this.addContact(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	public void setEi(List<BookEIEx> bookOSIExs) {
		if(null == bookOSIExs)
			return;
		for(BookEIEx bookPassengerEx:bookOSIExs) {
			try {
				this.addEI(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setRmks(List<BookRMKEx> bookOSIExs) {
		if(null == bookOSIExs)
			return;
		for(BookRMKEx bookPassengerEx:bookOSIExs) {
			try {
				this.addRMK(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}
	public void setSsrs(List<BookSSREx> bookOSIExs) {
		if(null == bookOSIExs)
			return;		
		for(BookSSREx bookPassengerEx:bookOSIExs) {
			try {
				this.addSSR(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setTc(List<BookTC> bookOSIExs) {
		if(null == bookOSIExs)
			return;		
		for(BookTC bookPassengerEx:bookOSIExs) {
			try {
				this.addTC(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setTktStatus(List<BookTktStatus> bookOSIExs) {
		if(null == bookOSIExs)
			return;		
		for(BookTktStatus bookPassengerEx:bookOSIExs) {
			try {
				this.addTktstatus(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public void setAuxiliary(List<AuxiliaryService> bookOSIExs) {
		if(null == bookOSIExs)
			return;		
		for(AuxiliaryService bookPassengerEx:bookOSIExs) {
			try {
				if(bookPassengerEx instanceof BookAuxiliaryService)
					this.addAuxiliaryService((BookAuxiliaryService) bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

//	public void setFp(List<BookFP> bookOSIExs) {
//		if(null == bookOSIExs)
//			return;
//		for(BookFP bookPassengerEx:bookOSIExs) {
//			try {
//				this.addFP(bookPassengerEx);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//	}

//	public void setFc(List<BookFCEx> bookOSIExs) {
//		if(null == bookOSIExs)
//			return;
//		for(BookFCEx bookPassengerEx:bookOSIExs) {
//			try {
//				this.addFC(bookPassengerEx);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//	}
//
//	public void setFn(List<BookFNEx> bookOSIExs) {
//		if(null == bookOSIExs)
//			return;
//		for(BookFNEx bookPassengerEx:bookOSIExs) {
//			try {
//				this.addFN(bookPassengerEx);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//		}
//	}

	public void setInfants(List<BookInfantEx> bookOSIExs) {
		if(null == bookOSIExs)
			return;		
		for(BookInfantEx bookPassengerEx:bookOSIExs) {
			try {
				this.addInfant(bookPassengerEx);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	public List<BookFCEx> getPnrfcs() {
		return pnrfcs;
	}

	public void setPnrfcs(List<BookFCEx> pnrfcs) {
		this.pnrfcs = pnrfcs;
		if(null != pnrfcs)
		  for(BookFCEx pnrfc:pnrfcs) {
			 BookFC fcEx = new BookFC();
			 BeanUtils.copyProperties(pnrfc, fcEx,"rtax","tax","otax","segments","surcharges","charges","fc","psgrname");
			 for(com.hna.shopping.ibe.interfaces.dto.FareCalculation.fcitem ito : pnrfc.getSegments()) {
				 fcEx.addFC(ito.getOrg(), ito.getDst(), ito.getAircorp(), ito.getFltclass(), ito.getAmount(), ito.getFreePackageCount(), ito.getFreePackageWeight(), ito.getFreePackageWeightUnit(), ito.getInvalidBefore(), ito.getInvalidAfter(), ito.isE(), ito.isStopover(), ito.getTraveldirection(), ito.getMilesurcharge(), ito.getMsseg(), ito.getQ(), ito.getQseg(), ito.getMileIntermediatePoint(), ito.getFareseg(), ito.isTktFromFlag());
			 }
			 for(com.hna.shopping.ibe.interfaces.dto.FareCalculation.fctax ito : pnrfc.getTax()) {
				 fcEx.insertTax(ito.getCurrency(), ito.getValue(), ito.getCode());
			 }
			 for(com.hna.shopping.ibe.interfaces.dto.FareCalculation.fctax ito : pnrfc.getRtax()) {
				 fcEx.insertRTax(ito.getCurrency(), ito.getValue(), ito.getCode());
			 }
			 for(com.hna.shopping.ibe.interfaces.dto.FareCalculation.fctax ito : pnrfc.getOtax()) {
				 fcEx.insertOTax(ito.getCurrency(), ito.getValue(), ito.getCode());
			 }
			 if(!StringUtils.isEmpty(pnrfc.getFc()))
				fcEx.setFc(pnrfc.getFc());
			    for(String pName : pnrfc.getPsgrname()) {
				    fcEx.addPsgrname(pName);
			    }
			 this.addFC(fcEx);
		  }
	}

	public List<BookFNEx> getBookFNExs() {
		return bookFNExs;
	}

	public void setBookFNExs(List<BookFNEx> bookFNExs) {
		this.bookFNExs = bookFNExs;
		if(null != bookFNExs) {
			int[] types = new int[] {FareBox.F,FareBox.S,FareBox.A,FareBox.E,FareBox.R,FareBox.X};
			 for(BookFNEx fn:bookFNExs) {
				 BookFN fcEx = new BookFN();
				 fcEx.setC(fn.getC());
				 if(!StringUtils.isEmpty(fn.getFn()))
					 fcEx.setFn(fn.getFn());
				 fcEx.setInfant(fn.isInfant());
				 fcEx.setInputCode(fn.getInputCode());
				 fcEx.setType(fn.getType());
				 int para = FareBox.T;
					for (int j = 0; j < fn.getTaxCnt(); j++) {
						fcEx.addTax(para, fn.getTaxCurrency(para, j), fn.getTaxAmount(para, j), fn.getTaxCode(para, j));
					}
					para = FareBox.O;
					for (int j = 0; j < fn.getOTaxCnt(); j++) {
						fcEx.addTax(para, fn.getTaxCurrency(para, j), fn.getTaxAmount(para, j), fn.getTaxCode(para, j));
					}
					for(int type:types) {
						fcEx.setAmount(type, fn.getCurrency(type), fn.getAmount(type));
					}
					for(String pName : fn.getPsgrname()) {
						fcEx.addPsgrname(pName);
					}
					super.addFN(fcEx);
			 }
		}
	}
	
	public void setTimelimit(String dateLimit) throws Exception{
		if(null == dateLimit || dateLimit.trim().equals(""))
			return;
		if(dateLimit.length() == 5||dateLimit.length() == 7||dateLimit.length() == 10 || dateLimit.indexOf("-") > 0) {
			super.setTimelimit(dateLimit);//(departureTime);
		}else {
			super.setTimelimit(new Date(Long.parseLong(dateLimit)));
		}
	}

    public static String[] encodeBookinfo(BookInformation bookinfo, String pnrno, int milliseconds) throws Exception {
        try {
            String[] args = new String[]{null, "Office:" + bookinfo.getOfficeCode(), "AirSeg:", "Contact:", "Infant:", "OSI:", "RMK:", "SSR:", "Tkt:", "GN:", "NM:", pnrno, bookinfo.getEnvelopType(), "FC:", "FP:", "FN:", "BA:", "EI:", "TC:", "TK:", "OI:", "BL:", "AUX:", "WAIT:" + String.valueOf(milliseconds), "OP:"};
            String[] osiStrings;
            if(bookinfo.getPassengers().size() != 0) {
                String[] psgrStrings = new String[bookinfo.getPassengers().size()];
                for(int i = 0; i < bookinfo.getPassengers().size(); ++i) {
                    int j = 0;
                    BookPassenger p1 = (BookPassenger)bookinfo.getPassengers().elementAt(i);
                    osiStrings = new String[4];
                    osiStrings[j] = p1.getName();
                    i = j + 1;
                    osiStrings[i] = String.valueOf(p1.getType());
                    ++i;
                    osiStrings[i] = String.valueOf(p1.getAge());
                    ++i;
                    osiStrings[i++] = String.valueOf(p1.getPassengerType());
                    psgrStrings[i] = CommandParser.encode(osiStrings);
                }
                args[10] = args[10] + CommandParser.encode(psgrStrings);
            }

            if(bookinfo.getAirsegs().size() != 0) {
                String[] airsegStrings = new String[bookinfo.getAirsegs().size()];
                for(int i = 0; i < bookinfo.getAirsegs().size(); ++i) {
                    BookAirSeg airseg = (BookAirSeg)bookinfo.getAirsegs().elementAt(i);
                    osiStrings = new String[12];
                    byte j = 0;
                    osiStrings[j] = airseg.getAirNo();
                    i = j + 1;
                    osiStrings[i] = String.valueOf(airseg.getFltClass());
                    ++i;
                    osiStrings[i] = airseg.getOrgCity();
                    ++i;
                    osiStrings[i] = airseg.getDesCity();
                    ++i;
                    osiStrings[i] = airseg.getActionCode();
                    if(osiStrings[i] == null || osiStrings[i].equals("")) {
                        osiStrings[i] = "nn";
                    }

                    ++i;
                    osiStrings[i] = String.valueOf(airseg.getTktNum());
                    ++i;
                    osiStrings[i] = QDateTime.dateToString(airseg.getDepartureTime(), "yyyy-mm-dd hh:mi:ss");
                    ++i;
                    osiStrings[i] = String.valueOf(airseg.getType());
                    ++i;
                    osiStrings[i] = String.valueOf(airseg.isManualInputTime());
                    ++i;
                    osiStrings[i] = airseg.getManualDepTimeStr();
                    ++i;
                    osiStrings[i] = airseg.getManualArrTimeStr();
                    ++i;
                    osiStrings[i] = String.valueOf(airseg.getPriority());
                    airsegStrings[i] = CommandParser.encode(osiStrings);
                }
                args[2] = args[2] + CommandParser.encode(airsegStrings);
            }

            if(bookinfo.getContacts().size() != 0) {
                String[] contactStrings = new String[bookinfo.getContacts().size()];
                for(int i = 0; i < bookinfo.getContacts().size(); ++i) {
                    osiStrings = new String[3];
                    byte j = 0;
                    BookContact contact = (BookContact)bookinfo.getContacts().elementAt(i);
                    osiStrings[j] = contact.getCity();
                    i = j + 1;
                    osiStrings[i] = contact.getContact();
                    ++i;
                    osiStrings[i] = contact.getpsgrName();
                    ++i;
                    contactStrings[i] = CommandParser.encode(osiStrings);
                }
                args[3] = args[3] + CommandParser.encode(contactStrings);
            }

            String[] rmkStrings;
            if(bookinfo.getInfants().size() != 0) {
                String[] infantStrings = new String[bookinfo.getInfants().size()];
                for(int i = 0; i < bookinfo.getInfants().size(); ++i) {
                    rmkStrings = new String[3];
                    int j = 0;
                    BookInfant infant = (BookInfant)bookinfo.getInfants().elementAt(i);
                    rmkStrings[j] = infant.getName();
                    i = j + 1;
                    rmkStrings[i] = QDateTime.dateToString(infant.getBirth(), "yyyy-mm-dd hh:mi:ss");
                    ++i;
                    rmkStrings[i] = infant.getCarrierName();
                    ++i;
                    infantStrings[i] = CommandParser.encode(rmkStrings);
                }
                args[4] = args[4] + CommandParser.encode(infantStrings);
            }

            String[] ssrStrings;
            if(bookinfo.getOsis().size() != 0) {
                osiStrings = new String[bookinfo.getOsis().size()];
                for(int i = 0; i < bookinfo.getOsis().size(); ++i) {
                    ssrStrings = new String[3];
                    BookOSI osi = (BookOSI)bookinfo.getOsis().elementAt(i);
                    int j = 0;
                    ssrStrings[j] = osi.getAirCode();
                    i = j + 1;
                    ssrStrings[i] = osi.getOsi();
                    ++i;
                    ssrStrings[i] = osi.getpsgrName();
                    ++i;
                    osiStrings[i] = CommandParser.encode(ssrStrings);
                }
                args[5] = args[5] + CommandParser.encode(osiStrings);
            }

            String[] fcs;
            if(bookinfo.getRmks().size() != 0) {
                rmkStrings = new String[bookinfo.getRmks().size()];
                for(int i = 0; i < bookinfo.getRmks().size(); ++i) {
                    fcs = new String[4];
                    BookRMK rmk = (BookRMK)bookinfo.getRmks().elementAt(i);
                    byte j = 0;
                    fcs[j] = rmk.getRmktype();
                    i = j + 1;
                    fcs[i] = rmk.getRmkinfo();
                    ++i;
                    fcs[i] = rmk.getpsgrName();
                    ++i;
                    fcs[i] = rmk.getInputCode();
                    ++i;
                    rmkStrings[i] = CommandParser.encode(fcs);
                }
                args[6] = args[6] + CommandParser.encode(rmkStrings);
            }

            String[] fps;
            if(bookinfo.getSsrs().size() != 0) {
                ssrStrings = new String[bookinfo.getSsrs().size()];
                for(int i = 0; i < bookinfo.getSsrs().size(); ++i) {
                    fps = new String[11];
                    byte j = 0;
                    BookSSR ssr = (BookSSR)bookinfo.getSsrs().elementAt(i);
                    fps[j] = ssr.getServeCode();
                    i = j + 1;
                    fps[i] = ssr.getAirCode();
                    ++i;
                    fps[i] = ssr.getActionCode();
                    ++i;
                    fps[i] = String.valueOf(ssr.getPerson());
                    ++i;
                    fps[i] = ssr.getCityPair();
                    ++i;
                    fps[i] = ssr.getAirNo();
                    ++i;
                    fps[i] = String.valueOf(ssr.getFltClass());
                    ++i;
                    fps[i] = QDateTime.dateToString(ssr.getDepartureTime(), "yyyy-mm-dd hh:mi:ss");
                    ++i;
                    fps[i] = ssr.getServeInfo();
                    ++i;
                    fps[i] = ssr.getpsgrName();
                    ++i;
                    fps[i] = ssr.getSegidx();
                    ++i;
                    ssrStrings[i] = CommandParser.encode(fps);
                }
                args[7] = args[7] + CommandParser.encode(ssrStrings);
            }

            if(bookinfo.getTkttimelimit() != null && !bookinfo.getTkttimelimit().equals("")) {
                fcs = new String[]{bookinfo.getTkttimelimit()};
                args[8] = "Tkt:" + CommandParser.encode(fcs);
            }

            String[] fnStrings;
            String[] fns;
            if(bookinfo.getFc().size() > 0) {
                fcs = new String[bookinfo.getFc().size()];
                for(int i = 0; i < bookinfo.getFc().size(); ++i) {
                    fns = new String[5];
                    BookFC bookfc = (BookFC)bookinfo.getFc().elementAt(i);
                    fns[0] = bookfc.getFc();
                    fns[1] = String.valueOf(bookfc.isInfant());
                    fnStrings = new String[bookfc.getPsgrname().size()];
                    for(i = 0; i < fnStrings.length; ++i) {
                        fnStrings[i] = (String)bookfc.getPsgrname().elementAt(i);
                    }

                    fns[2] = CommandParser.encode(fnStrings);
                    if(bookfc.getFc() == null || bookfc.getFc().length() == 0) {
                        fns[3] = BookFC.encodeFC(bookfc);
                    }
                    fns[4] = bookfc.getInputCode();
                    fcs[i] = CommandParser.encode(fns);
                }
                args[13] = args[13] + CommandParser.encode(fcs);
            }

            String[] bas;
            String[] eis;
            if(bookinfo.getFp().size() > 0) {
                fps = new String[bookinfo.getFp().size()];
                for(int i = 0; i < bookinfo.getFp().size(); ++i) {
                    bas = new String[6];
                    BookFP bookfp = (BookFP)bookinfo.getFp().elementAt(i);
                    bas[0] = bookfp.getFp();
                    bas[1] = String.valueOf(bookfp.isInfant());
                    eis = new String[bookfp.getPsgrname().size()];
                    for(i = 0; i < eis.length; ++i) {
                        eis[i] = (String)bookfp.getPsgrname().elementAt(i);
                    }
                    bas[2] = CommandParser.encode(eis);
                    bas[3] = bookfp.getMoneytype();
                    bas[4] = bookfp.getPaytype();
                    bas[5] = bookfp.getOtherinfo();
                    fps[i] = CommandParser.encode(bas);
                }
                args[14] = args[14] + CommandParser.encode(fps);
            }

            String[] oi;
            if(bookinfo.getFn().size() > 0) {
                fns = new String[bookinfo.getFn().size()];
                for(int i = 0; i < bookinfo.getFn().size(); ++i) {
                    fnStrings = new String[5];
                    com.travelsky.ibe.client.pnr.FareBox bookfn = (com.travelsky.ibe.client.pnr.FareBox)bookinfo.getFn().elementAt(i);
                    fnStrings[0] = bookfn.getFn();
                    fnStrings[1] = String.valueOf(bookfn.isInfant());
                    if(bookfn instanceof BookFN) {
                        BookFN bookfn1 = (BookFN)bookfn;
                        oi = new String[bookfn1.getPsgrname().size()];
                        for(i = 0; i < oi.length; ++i) {
                            oi[i] = (String)bookfn1.getPsgrname().elementAt(i);
                        }
                        fnStrings[2] = CommandParser.encode(oi);
                    }

                    if(fnStrings[0] == null || fnStrings[0].length() == 0) {
                        fnStrings[3] = com.travelsky.ibe.client.pnr.FareBox.encodeFN(bookfn);
                    }

                    if(bookfn instanceof BookFN) {
                        fnStrings[4] = ((BookFN)bookfn).getInputCode();
                    }
                    fns[i] = CommandParser.encode(fnStrings);
                }
                args[15] = args[15] + CommandParser.encode(fns);
            }

            if(bookinfo.getBa().size() > 0) {
                bas = new String[bookinfo.getBa().size()];
                for(int i = 0; i < bookinfo.getBa().size(); ++i) {
                    eis = new String[2];
                    BookBA ba = (BookBA)bookinfo.getBa().elementAt(i);
                    eis[0] = ba.getBAInfo();
                    eis[1] = ba.getPsgrname();
                    bas[i] = CommandParser.encode(eis);
                }
                args[16] = args[16] + CommandParser.encode(bas);
            }

            String[] tk;
            if(bookinfo.getTc().size() > 0) {
                fnStrings = new String[bookinfo.getTc().size()];
                for(int i = 0; i < bookinfo.getTc().size(); ++i) {
                    tk = new String[5];
                    BookTC tc = (BookTC)bookinfo.getTc().elementAt(i);
                    tk[0] = tc.getFreecode();
                    tk[1] = tc.getTc();
                    tk[2] = tc.getPsgrName();
                    tk[3] = String.valueOf(tc.infant);
                    tk[4] = tc.getInputCode();
                    fnStrings[i] = CommandParser.encode(tk);
                }
                args[18] = args[18] + CommandParser.encode(fnStrings);
            }

            if(bookinfo.getEi().size() > 0) {
                eis = new String[bookinfo.getEi().size()];
                for(int i = 0; i < bookinfo.getEi().size(); ++i) {
                    oi = new String[4];
                    BookEI ei = (BookEI)bookinfo.getEi().elementAt(i);
                    oi[0] = ei.getEndorse();
                    oi[1] = ei.getPsgrName();
                    oi[2] = String.valueOf(ei.isInfant());
                    oi[3] = ei.getInputCode();
                    eis[i] = CommandParser.encode(oi);
                }
                args[17] = args[17] + CommandParser.encode(eis);
            }

            String[] opstrs;
            if(bookinfo.getTktStatus().size() > 0) {
                tk = new String[bookinfo.getTktStatus().size()];
                for(int i = 0; i < bookinfo.getTktStatus().size(); ++i) {
                    opstrs = new String[5];
                    BookTktStatus bt = (BookTktStatus)bookinfo.getTktStatus().elementAt(i);
                    opstrs[0] = String.valueOf(bt.type);
                    if(bt.getType() == 32) {
                        opstrs[1] = bt.getFreetext();
                    } else if(bt.getType() == 16) {
                        opstrs[1] = bt.getTlOffice();
                    }
                    opstrs[2] = bt.timelimit == null ? "01Jan19700000":QDateTime.dateToString(bt.timelimit, "ddmmmyyyyhhmi");
                    opstrs[3] = bt.psgrName;
                    opstrs[4] = bt.freetext;
                    tk[i] = CommandParser.encode(opstrs);
                }
                args[19] = args[19] + CommandParser.encode(tk);
            }

            if(bookinfo.getOis().size() > 0) {
                oi = new String[bookinfo.getOis().size()];
                for(int i = 0; i < bookinfo.getOis().size(); ++i) {
                    String[] oistr = new String[10];
                    BookOI boi = (BookOI)bookinfo.getOis().elementAt(i);
                    oistr[0] = boi.getTktno();
                    oistr[1] = boi.getCoupon();
                    oistr[2] = boi.getIssueCity();
                    oistr[3] = boi.getIssueOffice();
                    if(boi.getIssueDate() != null) {
                        oistr[4] = QDateTime.dateToString(boi.getIssueDate(), "DDMMMYYYY");
                    } else {
                        oistr[4] = "01JAN1970";
                    }
                    oistr[5] = boi.getPsgrid();
                    oistr[6] = boi.getSecondtktno();
                    oistr[7] = boi.getSecondcoupon();
                    oistr[8] = String.valueOf(boi.getOitype());
                    oistr[9] = String.valueOf(boi.isInfant());
                    oi[i] = CommandParser.encode(oistr);
                }
                args[20] = args[20] + CommandParser.encode(oi);
            }

            if(bookinfo.isGroupticket()) {
                opstrs = new String[]{String.valueOf(bookinfo.getPassengernumber()), bookinfo.getGroupName().toUpperCase()};
                args[9] = args[9] + CommandParser.encode(opstrs);
            }

            if(bookinfo.getAuxiliary().size() > 0) {
                opstrs = new String[bookinfo.getAuxiliary().size()];

                for(int i = 0; i < bookinfo.auxiliaryServices.size(); ++i) {
                    String[] auxstr = new String[13];
                    BookAuxiliaryService ba = (BookAuxiliaryService)bookinfo.auxiliaryServices.get(i);
                    auxstr[0] = ba.getServiceName();
                    auxstr[1] = ba.getAct_id();
                    auxstr[2] = ba.getActionCode();
                    auxstr[3] = String.valueOf(ba.getNbr());
                    auxstr[4] = ba.getCity();
                    auxstr[5] = QDateTime.calendarToString(ba.getInDate(), "ddmmmyy");
                    auxstr[6] = ba.getOutDate() == null? null : QDateTime.calendarToString(ba.getOutDate(), "ddmmmyy");
                    auxstr[7] = ba.getBed();
                    auxstr[8] = ba.getRoom();
                    auxstr[9] = ba.getHotel();
                    auxstr[10] = ba.getText();
                    auxstr[11] = ba.getAddress();
                    auxstr[12] = ba.getPsgrName();
                    opstrs[i] = CommandParser.encode(auxstr);
                }
                args[22] = args[22] + CommandParser.encode(opstrs);
            }

            if(bookinfo.getOps().size() > 0) {
                opstrs = new String[bookinfo.getOps().size()];
                for(int i = 0; i < bookinfo.getOps().size(); ++i) {
                    BookOP bop = bookinfo.getOps().get(i);
                    opstrs[i] = CommandParser.encode(new String[]{bop.date, bop.office, bop.psgrName, bop.text});
                }
                args[24] = args[24] + CommandParser.encode(opstrs);
            }
            return args;
        } catch (Exception e) {
//            var23.printStackTrace();
            throw new IBELocalException(e.getMessage());
        }
    }
}