/*    */ package com.travelsky.hub.model.peentity.seatchart.input;
/*    */ 
///*    */ import com.alibaba.fastjson.annotation.JSONField;
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class RequestCriteria
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -477944728614469330L;
/*    */   private Passenger passenger;
/*    */   private FlightSegment flightSegment;
/*    */   private List<FrequentFlyerProgram> frequentFlyerPrograms;
/*    */ 
/*    */   public Passenger getPassenger()
/*    */   {
/* 40 */     return this.passenger;
/*    */   }
/*    */ 
/*    */   public void setPassenger(Passenger passenger)
/*    */   {
/* 47 */     this.passenger = passenger;
/*    */   }
/*    */ 
/*    */   public FlightSegment getFlightSegment()
/*    */   {
/* 54 */     return this.flightSegment;
/*    */   }
/*    */ 
/*    */   public void setFlightSegment(FlightSegment flightSegment)
/*    */   {
/* 61 */     this.flightSegment = flightSegment;
/*    */   }
/*    */ 
///*    */   @JSONField(name="frequentFlyerInfos")
/*    */   public List<FrequentFlyerProgram> getFrequentFlyerPrograms() {
/* 67 */     return this.frequentFlyerPrograms;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerPrograms(List<FrequentFlyerProgram> frequentFlyerPrograms) {
/* 71 */     this.frequentFlyerPrograms = frequentFlyerPrograms;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.RequestCriteria
 * JD-Core Version:    0.6.0
 */