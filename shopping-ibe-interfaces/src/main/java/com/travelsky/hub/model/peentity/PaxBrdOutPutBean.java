/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PaxBrdOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2720331513758642549L;
/*    */   private String isSuccess;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/* 17 */   private String brdStatus = "";
/*    */ 
/* 19 */   private String brdTime = "";
/*    */ 
/* 27 */   private String hostNbr = "";
/*    */ 
/* 38 */   private String brdGate = "";
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 21 */     return this.errorMsg;
/*    */   }
/*    */   public void setErrorMsg(String errorMsg) {
/* 24 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getBrdStatus()
/*    */   {
/* 29 */     return this.brdStatus;
/*    */   }
/*    */   public void setBrdStatus(String brdStatus) {
/* 32 */     this.brdStatus = brdStatus;
/*    */   }
/*    */   public String getBrdTime() {
/* 35 */     return this.brdTime;
/*    */   }
/*    */ 
/*    */   public String getHostNbr()
/*    */   {
/* 40 */     return this.hostNbr;
/*    */   }
/*    */   public void setHostNbr(String hostNbr) {
/* 43 */     this.hostNbr = hostNbr;
/*    */   }
/*    */ 
/*    */   public String getIsSuccess() {
/* 47 */     return this.isSuccess;
/*    */   }
/*    */   public void setIsSuccess(String isSuccess) {
/* 50 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public String getErrorCode() {
/* 54 */     return this.errorCode;
/*    */   }
/*    */   public void setErrorCode(String errorCode) {
/* 57 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public void setBrdTime(String brdTime) {
/* 61 */     this.brdTime = brdTime;
/*    */   }
/*    */ 
/*    */   public String getBrdGate() {
/* 65 */     return this.brdGate;
/*    */   }
/*    */   public void setBrdGate(String brdGate) {
/* 68 */     this.brdGate = brdGate;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.PaxBrdOutPutBean
 * JD-Core Version:    0.6.0
 */