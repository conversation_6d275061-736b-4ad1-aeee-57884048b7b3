/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Seatinfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String seatNumber;
/*    */   private String seatAttribute;
/*    */   private String seatValue;
/*    */   private String priceValue;
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 28 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 35 */     this.seatNumber = seatNumber;
/*    */   }
/*    */ 
/*    */   public String getSeatAttribute()
/*    */   {
/* 42 */     return this.seatAttribute;
/*    */   }
/*    */ 
/*    */   public void setSeatAttribute(String seatAttribute)
/*    */   {
/* 49 */     this.seatAttribute = seatAttribute;
/*    */   }
/*    */ 
/*    */   public String getSeatValue()
/*    */   {
/* 56 */     return this.seatValue;
/*    */   }
/*    */ 
/*    */   public void setSeatValue(String seatValue)
/*    */   {
/* 63 */     this.seatValue = seatValue;
/*    */   }
/*    */ 
/*    */   public String getPriceValue()
/*    */   {
/* 70 */     return this.priceValue;
/*    */   }
/*    */ 
/*    */   public void setPriceValue(String priceValue)
/*    */   {
/* 77 */     this.priceValue = priceValue;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Seatinfo
 * JD-Core Version:    0.6.0
 */