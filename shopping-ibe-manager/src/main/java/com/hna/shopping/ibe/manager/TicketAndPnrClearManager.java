package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.*;

public interface TicketAndPnrClearManager {


	/**
	 * 票务处理。退票+清位
	 * @param request
	 * @return
	 */
	Boolean ticketAndPnrClear(TicketAndPnrClearRequest request);

	/**
	 * 票务处理。退票+清位(中文国际票)
	 * @param request
	 * @return
	 */
	Boolean ticketAndPnrClearInter(TicketAndPnrClearRequest request);

	/**
	 * 票务处理。退票+清位(海外站)
	 * @param request
	 * @return
	 */
	Boolean ticketAndPnrClearHWZ(TicketAndPnrClearRequest request);

}
