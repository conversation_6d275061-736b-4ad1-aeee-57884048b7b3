/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class Infant
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 958270916458230973L;
/*    */   private Individual infIndividual;
/*    */   private List<IdentityDoc> identityDocs;
/*    */   private Foid foid;
/*    */   private Fare fare;
/*    */ 
/*    */   public Individual getInfIndividual()
/*    */   {
/* 41 */     return this.infIndividual;
/*    */   }
/*    */ 
/*    */   public void setInfIndividual(Individual infIndividual)
/*    */   {
/* 48 */     this.infIndividual = infIndividual;
/*    */   }
/*    */ 
/*    */   public List<IdentityDoc> getIdentityDocs()
/*    */   {
/* 55 */     return this.identityDocs;
/*    */   }
/*    */ 
/*    */   public void setIdentityDocs(List<IdentityDoc> identityDocs)
/*    */   {
/* 62 */     this.identityDocs = identityDocs;
/*    */   }
/*    */ 
/*    */   public Foid getFoid()
/*    */   {
/* 69 */     return this.foid;
/*    */   }
/*    */ 
/*    */   public void setFoid(Foid foid)
/*    */   {
/* 76 */     this.foid = foid;
/*    */   }
/*    */ 
/*    */   public Fare getFare()
/*    */   {
/* 83 */     return this.fare;
/*    */   }
/*    */ 
/*    */   public void setFare(Fare fare)
/*    */   {
/* 90 */     this.fare = fare;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Infant
 * JD-Core Version:    0.6.0
 */