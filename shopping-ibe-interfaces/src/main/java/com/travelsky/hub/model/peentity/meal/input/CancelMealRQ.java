/*    */ package com.travelsky.hub.model.peentity.meal.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CancelMealRQ
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2655494801387505419L;
/*    */   private OfficeInfo officeInfo;
/*    */   private String pnrCode;
/*    */   private String orderItemID;
/*    */ 
/*    */   public OfficeInfo getOfficeInfo()
/*    */   {
/* 27 */     return this.officeInfo;
/*    */   }
/*    */ 
/*    */   public void setOfficeInfo(OfficeInfo officeInfo) {
/* 31 */     this.officeInfo = officeInfo;
/*    */   }
/*    */ 
/*    */   public String getPnrCode() {
/* 35 */     return this.pnrCode;
/*    */   }
/*    */ 
/*    */   public void setPnrCode(String pnrCode) {
/* 39 */     this.pnrCode = pnrCode;
/*    */   }
/*    */ 
/*    */   public String getOrderItemID() {
/* 43 */     return this.orderItemID;
/*    */   }
/*    */ 
/*    */   public void setOrderItemID(String orderItemID) {
/* 47 */     this.orderItemID = orderItemID;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.CancelMealRQ
 * JD-Core Version:    0.6.0
 */