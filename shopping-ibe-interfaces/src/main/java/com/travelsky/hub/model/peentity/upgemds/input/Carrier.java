/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Carrier
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7047209835647860888L;
/*    */   private String flightNumber;
/*    */   private String airlineCode;
/*    */   private String flightSuffix;
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 44 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 51 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setAirlineCode(String airlineCode)
/*    */   {
/* 58 */     this.airlineCode = airlineCode;
/*    */   }
/*    */ 
/*    */   public String getAirlineCode()
/*    */   {
/* 65 */     return this.airlineCode;
/*    */   }
/*    */ 
/*    */   public void setFlightSuffix(String flightSuffix)
/*    */   {
/* 72 */     this.flightSuffix = flightSuffix;
/*    */   }
/*    */ 
/*    */   public String getFlightSuffix()
/*    */   {
/* 79 */     return this.flightSuffix;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Carrier
 * JD-Core Version:    0.6.0
 */