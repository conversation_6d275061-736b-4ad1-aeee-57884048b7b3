package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.hna.shopping.ibe.manager.TicketInfoManager;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

@Component
@Slf4j
public class AsyncGetTicketInfo {

    @Autowired
    private TicketInfoManager ticketInfoManager;

    /**
     * 使用该项目默认的线程池，如果项目中没有配置，就是springboot提供的默认线程池
     */
    @Async
    public Future<List<AllChanTicketInfo>> mulThreadGetTicketInfo(String airline , CountDownLatch countDownLatch, DETR2F detr2f, String ticketNo, String needPnrBookTime, String needTicketChangeHis, String secondFactorCode, String secondFactorValue) throws Exception {
        try {
//            log.info("concurrent thread start");
            long currentTime = System.currentTimeMillis();
            DETRTKTResult detrResult = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
            List<AllChanTicketInfo> ticketInfoList = ticketInfoManager.mapTicketInfoResult(airline,true, detrResult, detr2f, 0, null, needPnrBookTime, needTicketChangeHis, 0, secondFactorCode, secondFactorValue);
            log.info(TicketInfoSearchCallable.class.getSimpleName() + "|search ticket take time:" + (System.currentTimeMillis() - currentTime) + "ms");
            countDownLatch.countDown();
            return new AsyncResult<List<AllChanTicketInfo>>(ticketInfoList);
        } catch (Exception e) {
            log.error("证件号异步获取客票信息失败", e);
        }
        return new AsyncResult<List<AllChanTicketInfo>>(null);
    }
}

