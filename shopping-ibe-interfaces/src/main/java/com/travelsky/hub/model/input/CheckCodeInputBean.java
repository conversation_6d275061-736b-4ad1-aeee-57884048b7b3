/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CheckCodeInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String etNumber;
/*    */   private String tourIndex;
/*    */ 
/*    */   public String getEtNumber()
/*    */   {
/* 24 */     return this.etNumber;
/*    */   }
/*    */ 
/*    */   public void setEtNumber(String etNumber)
/*    */   {
/* 31 */     this.etNumber = etNumber;
/*    */   }
/*    */ 
/*    */   public String getTourIndex()
/*    */   {
/* 38 */     return this.tourIndex;
/*    */   }
/*    */ 
/*    */   public void setTourIndex(String tourIndex)
/*    */   {
/* 45 */     this.tourIndex = tourIndex;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.CheckCodeInputBean
 * JD-Core Version:    0.6.0
 */