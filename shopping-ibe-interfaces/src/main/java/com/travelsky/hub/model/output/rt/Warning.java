/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Warning
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1479833555465413269L;
/*    */   private String warningCode;
/*    */   private String description;
/*    */ 
/*    */   public String getWarningCode()
/*    */   {
/* 30 */     return this.warningCode;
/*    */   }
/*    */ 
/*    */   public void setWarningCode(String warningCode)
/*    */   {
/* 37 */     this.warningCode = warningCode;
/*    */   }
/*    */ 
/*    */   public String getDescription()
/*    */   {
/* 44 */     return this.description;
/*    */   }
/*    */ 
/*    */   public void setDescription(String description)
/*    */   {
/* 51 */     this.description = description;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Warning
 * JD-Core Version:    0.6.0
 */