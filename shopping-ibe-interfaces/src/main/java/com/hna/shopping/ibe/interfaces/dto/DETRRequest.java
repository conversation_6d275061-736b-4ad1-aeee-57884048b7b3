package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class DETRRequest extends BaseRequest {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7403633170823348955L;
	@ApiModelProperty(value = "根据票号提客票记录")
	private String ticketNo;

	@ApiModelProperty(value = "根据pnr提客票记录")
	private String pnrNo;

	@ApiModelProperty(value = "根据证件号/证件类型提客票记录")
	private String passengerType;

	@ApiModelProperty(value = "根据证件号/证件类型提客票记录")
	private String passengerNo;

	@ApiModelProperty(value = "航司")
	private String airline;

	/**
	 * 旅客姓名
	 */
	private String passengerName;

	@ApiModelProperty(value = "根据证件号/证件类型提取客票记录，是否只提取openforuse")
	private Boolean onlyOpen;//不传false,则默认为true
}
