/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String deptCity;
/*     */   private String flightNum;
/*     */   private String arvCity;
/*     */   private String flightDate;
/*     */   private FlightInfo connFlightInfo;
/*     */   private String airlineCode;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  46 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  53 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  61 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  68 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setConnFlightInfo(FlightInfo connFlightInfo)
/*     */   {
/*  75 */     this.connFlightInfo = connFlightInfo;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  82 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public String getFlightNum()
/*     */   {
/*  89 */     return this.flightNum;
/*     */   }
/*     */ 
/*     */   public String getArvCity()
/*     */   {
/*  96 */     return this.arvCity;
/*     */   }
/*     */ 
/*     */   public void setFlightNum(String flightNum)
/*     */   {
/* 103 */     this.flightNum = flightNum;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 110 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public void setArvCity(String arvCity)
/*     */   {
/* 117 */     this.arvCity = arvCity;
/*     */   }
/*     */ 
/*     */   public FlightInfo getConnFlightInfo()
/*     */   {
/* 124 */     return this.connFlightInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FlightInfo
 * JD-Core Version:    0.6.0
 */