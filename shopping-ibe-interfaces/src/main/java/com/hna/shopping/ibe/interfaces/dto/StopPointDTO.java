package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 经停信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel
public class StopPointDTO implements Serializable {
    private static final long serialVersionUID = 468636555456491225L;

    @ApiModelProperty(value = "经停城市三字码")
    private String stopCityCode;

    @ApiModelProperty(value = "经停城市航站楼")
    private String stopCityTerm;

    @ApiModelProperty(value = "到达经停点时间")
    private Date arrivalTime;

    @ApiModelProperty(value = "离开经停点时间")
    private Date departureTime;
}
