/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class StreamMessage
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7344883733554249377L;
/*    */   private List<String> boardingPassStream;
/*    */ 
/*    */   public List<String> getBoardingPassStream()
/*    */   {
/* 26 */     return this.boardingPassStream;
/*    */   }
/*    */ 
/*    */   public void setBoardingPassStream(List<String> boardingPassStream)
/*    */   {
/* 33 */     this.boardingPassStream = boardingPassStream;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.StreamMessage
 * JD-Core Version:    0.6.0
 */