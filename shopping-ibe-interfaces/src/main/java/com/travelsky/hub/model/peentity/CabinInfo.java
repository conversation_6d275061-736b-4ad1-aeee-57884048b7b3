/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class CabinInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String toCabin;
/*     */   private String upPrice;
/*     */   private String upLevel;
/*     */   private String reseatNum;
/*     */   private String currency;
/*     */   private String mileage;
/*     */ 
/*     */   public String getToCabin()
/*     */   {
/*  33 */     return this.toCabin;
/*     */   }
/*     */ 
/*     */   public void setToCabin(String toCabin)
/*     */   {
/*  40 */     this.toCabin = toCabin;
/*     */   }
/*     */ 
/*     */   public String getUpPrice()
/*     */   {
/*  47 */     return this.upPrice;
/*     */   }
/*     */ 
/*     */   public void setUpPrice(String upPrice)
/*     */   {
/*  54 */     this.upPrice = upPrice;
/*     */   }
/*     */ 
/*     */   public String getUpLevel()
/*     */   {
/*  61 */     return this.upLevel;
/*     */   }
/*     */ 
/*     */   public void setUpLevel(String upLevel)
/*     */   {
/*  68 */     this.upLevel = upLevel;
/*     */   }
/*     */   public String getReseatNum() {
/*  71 */     return this.reseatNum;
/*     */   }
/*     */   public void setReseatNum(String reseatNum) {
/*  74 */     this.reseatNum = reseatNum;
/*     */   }
/*     */ 
/*     */   public String getCurrency()
/*     */   {
/*  82 */     return this.currency;
/*     */   }
/*     */ 
/*     */   public void setCurrency(String currency)
/*     */   {
/*  90 */     this.currency = currency;
/*     */   }
/*     */ 
/*     */   public String getMileage()
/*     */   {
/*  98 */     return this.mileage;
/*     */   }
/*     */ 
/*     */   public void setMileage(String mileage)
/*     */   {
/* 106 */     this.mileage = mileage;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CabinInfo
 * JD-Core Version:    0.6.0
 */