/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class CheckInStatusQueryInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4359340729665859642L;
/*     */   private String ticketNumber;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String flightNumber;
/*     */   private String depatureFrom;
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/*  52 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/*  59 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  66 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  73 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  88 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  95 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 102 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 109 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepatureFrom()
/*     */   {
/* 116 */     return this.depatureFrom;
/*     */   }
/*     */ 
/*     */   public void setDepatureFrom(String depatureFrom)
/*     */   {
/* 123 */     this.depatureFrom = depatureFrom;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.CheckInStatusQueryInputBean
 * JD-Core Version:    0.6.0
 */