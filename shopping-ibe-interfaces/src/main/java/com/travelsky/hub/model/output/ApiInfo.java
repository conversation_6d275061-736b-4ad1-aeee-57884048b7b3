/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ApiInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 9107862108938828021L;
/*     */   private String surName;
/*     */   private String givenName;
/*     */   private String birthDate;
/*     */   private String gender;
/*     */   private String transferInd;
/*     */   private String primaryHolderInd;
/*     */   private String docID;
/*     */   private String docType;
/*     */   private String docHolderNationality;
/*     */   private String expireDate;
/*     */   private String docIssueCountry;
/*     */   private String middleName;
/*     */   private String birthLocation;
/*     */   private String effectiveDate;
/*     */   private String residenceCountry;
/*     */   private VisaInfo visaInfo;
/*     */   private OtherDocInfo otherDocInfo;
/*     */   private HomeAddress homeAddress;
/*     */   private DestAddress destAddress;
/*     */   private String redressNumber;
/*     */   private String knownTravelerNumber;
/*     */ 
/*     */   public String getBirthDate()
/*     */   {
/* 102 */     return this.birthDate;
/*     */   }
/*     */ 
/*     */   public void setBirthDate(String birthDate)
/*     */   {
/* 109 */     this.birthDate = birthDate;
/*     */   }
/*     */ 
/*     */   public String getBirthLocation()
/*     */   {
/* 116 */     return this.birthLocation;
/*     */   }
/*     */ 
/*     */   public void setBirthLocation(String birthLocation)
/*     */   {
/* 123 */     this.birthLocation = birthLocation;
/*     */   }
/*     */ 
/*     */   public DestAddress getDestAddress()
/*     */   {
/* 130 */     return this.destAddress;
/*     */   }
/*     */ 
/*     */   public void setDestAddress(DestAddress destAddress)
/*     */   {
/* 137 */     this.destAddress = destAddress;
/*     */   }
/*     */ 
/*     */   public String getDocHolderNationality()
/*     */   {
/* 144 */     return this.docHolderNationality;
/*     */   }
/*     */ 
/*     */   public void setDocHolderNationality(String docHolderNationality)
/*     */   {
/* 151 */     this.docHolderNationality = docHolderNationality;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 158 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 165 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getDocIssueCountry()
/*     */   {
/* 172 */     return this.docIssueCountry;
/*     */   }
/*     */ 
/*     */   public void setDocIssueCountry(String docIssueCountry)
/*     */   {
/* 179 */     this.docIssueCountry = docIssueCountry;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 186 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 193 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getEffectiveDate()
/*     */   {
/* 200 */     return this.effectiveDate;
/*     */   }
/*     */ 
/*     */   public void setEffectiveDate(String effectiveDate)
/*     */   {
/* 207 */     this.effectiveDate = effectiveDate;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/* 214 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/* 221 */     this.expireDate = expireDate;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 228 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 235 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getGivenName()
/*     */   {
/* 242 */     return this.givenName;
/*     */   }
/*     */ 
/*     */   public void setGivenName(String givenName)
/*     */   {
/* 249 */     this.givenName = givenName;
/*     */   }
/*     */ 
/*     */   public HomeAddress getHomeAddress()
/*     */   {
/* 256 */     return this.homeAddress;
/*     */   }
/*     */ 
/*     */   public void setHomeAddress(HomeAddress homeAddress)
/*     */   {
/* 263 */     this.homeAddress = homeAddress;
/*     */   }
/*     */ 
/*     */   public String getMiddleName()
/*     */   {
/* 270 */     return this.middleName;
/*     */   }
/*     */ 
/*     */   public void setMiddleName(String middleName)
/*     */   {
/* 277 */     this.middleName = middleName;
/*     */   }
/*     */ 
/*     */   public OtherDocInfo getOtherDocInfo()
/*     */   {
/* 284 */     return this.otherDocInfo;
/*     */   }
/*     */ 
/*     */   public void setOtherDocInfo(OtherDocInfo otherDocInfo)
/*     */   {
/* 291 */     this.otherDocInfo = otherDocInfo;
/*     */   }
/*     */ 
/*     */   public String getPrimaryHolderInd()
/*     */   {
/* 298 */     return this.primaryHolderInd;
/*     */   }
/*     */ 
/*     */   public void setPrimaryHolderInd(String primaryHolderInd)
/*     */   {
/* 305 */     this.primaryHolderInd = primaryHolderInd;
/*     */   }
/*     */ 
/*     */   public String getResidenceCountry()
/*     */   {
/* 312 */     return this.residenceCountry;
/*     */   }
/*     */ 
/*     */   public void setResidenceCountry(String residenceCountry)
/*     */   {
/* 319 */     this.residenceCountry = residenceCountry;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 326 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 333 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getTransferInd()
/*     */   {
/* 340 */     return this.transferInd;
/*     */   }
/*     */ 
/*     */   public void setTransferInd(String transferInd)
/*     */   {
/* 347 */     this.transferInd = transferInd;
/*     */   }
/*     */ 
/*     */   public VisaInfo getVisaInfo()
/*     */   {
/* 354 */     return this.visaInfo;
/*     */   }
/*     */ 
/*     */   public void setVisaInfo(VisaInfo visInfo)
/*     */   {
/* 361 */     this.visaInfo = visInfo;
/*     */   }
/*     */ 
/*     */   public String getRedressNumber()
/*     */   {
/* 368 */     return this.redressNumber;
/*     */   }
/*     */ 
/*     */   public void setRedressNumber(String redressNumber)
/*     */   {
/* 375 */     this.redressNumber = redressNumber;
/*     */   }
/*     */ 
/*     */   public String getKnownTravelerNumber()
/*     */   {
/* 382 */     return this.knownTravelerNumber;
/*     */   }
/*     */ 
/*     */   public void setKnownTravelerNumber(String knownTravelerNumber)
/*     */   {
/* 389 */     this.knownTravelerNumber = knownTravelerNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ApiInfo
 * JD-Core Version:    0.6.0
 */