/*     */ package com.travelsky.hub.model.peentity.upgemds.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class EMDCoupon
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5897105064703144971L;
/*     */   private String couponStatus;
/*     */   private String couponNumber;
/*     */   private String sCurrencyCode;
/*     */   private String sBasePriceAmout;
/*     */   private String sCurrencyCodeNumber;
/*     */   private String sPriceTotal;
/*     */   private String trxRefKey;
/*     */ 
/*     */   public String getCouponStatus()
/*     */   {
/*  57 */     return this.couponStatus;
/*     */   }
/*     */ 
/*     */   public void setCouponStatus(String couponStatus)
/*     */   {
/*  64 */     this.couponStatus = couponStatus;
/*     */   }
/*     */ 
/*     */   public String getCouponNumber()
/*     */   {
/*  71 */     return this.couponNumber;
/*     */   }
/*     */ 
/*     */   public void setCouponNumber(String couponNumber)
/*     */   {
/*  78 */     this.couponNumber = couponNumber;
/*     */   }
/*     */ 
/*     */   public String getsCurrencyCode()
/*     */   {
/*  85 */     return this.sCurrencyCode;
/*     */   }
/*     */ 
/*     */   public void setsCurrencyCode(String sCurrencyCode)
/*     */   {
/*  92 */     this.sCurrencyCode = sCurrencyCode;
/*     */   }
/*     */ 
/*     */   public String getsBasePriceAmout()
/*     */   {
/*  99 */     return this.sBasePriceAmout;
/*     */   }
/*     */ 
/*     */   public void setsBasePriceAmout(String sBasePriceAmout)
/*     */   {
/* 106 */     this.sBasePriceAmout = sBasePriceAmout;
/*     */   }
/*     */ 
/*     */   public String getsCurrencyCodeNumber()
/*     */   {
/* 113 */     return this.sCurrencyCodeNumber;
/*     */   }
/*     */ 
/*     */   public void setsCurrencyCodeNumber(String sCurrencyCodeNumber)
/*     */   {
/* 120 */     this.sCurrencyCodeNumber = sCurrencyCodeNumber;
/*     */   }
/*     */ 
/*     */   public String getsPriceTotal()
/*     */   {
/* 127 */     return this.sPriceTotal;
/*     */   }
/*     */ 
/*     */   public void setsPriceTotal(String sPriceTotal)
/*     */   {
/* 134 */     this.sPriceTotal = sPriceTotal;
/*     */   }
/*     */ 
/*     */   public String getTrxRefKey()
/*     */   {
/* 141 */     return this.trxRefKey;
/*     */   }
/*     */ 
/*     */   public void setTrxRefKey(String trxRefKey)
/*     */   {
/* 148 */     this.trxRefKey = trxRefKey;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.output.EMDCoupon
 * JD-Core Version:    0.6.0
 */