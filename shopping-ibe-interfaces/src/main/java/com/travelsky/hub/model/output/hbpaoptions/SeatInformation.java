/*     */ package com.travelsky.hub.model.output.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -82590396370423988L;
/*     */   private String seatNumber;
/*     */   private String arrivalAirport;
/*     */   private String extraSeatNumber;
/*     */   private String extraSeatType;
/*     */   private String extraSeatWeight;
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  39 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  46 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  53 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  60 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatNumber()
/*     */   {
/*  67 */     return this.extraSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatNumber(String extraSeatNumber)
/*     */   {
/*  74 */     this.extraSeatNumber = extraSeatNumber;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatType()
/*     */   {
/*  81 */     return this.extraSeatType;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatType(String extraSeatType)
/*     */   {
/*  88 */     this.extraSeatType = extraSeatType;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatWeight()
/*     */   {
/*  95 */     return this.extraSeatWeight;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatWeight(String extraSeatWeight)
/*     */   {
/* 102 */     this.extraSeatWeight = extraSeatWeight;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.SeatInformation
 * JD-Core Version:    0.6.0
 */