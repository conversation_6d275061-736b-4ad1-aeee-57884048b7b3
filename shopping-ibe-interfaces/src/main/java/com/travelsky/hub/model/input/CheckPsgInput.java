/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class CheckPsgInput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String pnr;
/*    */   private List<Document> document;
/*    */   private List<Segment> segment;
/*    */ 
/*    */   public String getPnr()
/*    */   {
/* 33 */     return this.pnr;
/*    */   }
/*    */ 
/*    */   public void setPnr(String pnr)
/*    */   {
/* 40 */     this.pnr = pnr;
/*    */   }
/*    */ 
/*    */   public List<Document> getDocument()
/*    */   {
/* 47 */     return this.document;
/*    */   }
/*    */ 
/*    */   public void setDocument(List<Document> document)
/*    */   {
/* 54 */     this.document = document;
/*    */   }
/*    */ 
/*    */   public List<Segment> getSegment()
/*    */   {
/* 61 */     return this.segment;
/*    */   }
/*    */ 
/*    */   public void setSegment(List<Segment> segment)
/*    */   {
/* 68 */     this.segment = segment;
/*    */   }
/*    */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.input.CheckPsgInput
 * JD-Core Version:    0.6.0
 */