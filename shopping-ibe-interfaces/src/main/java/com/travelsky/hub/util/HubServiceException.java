/*     */ package com.travelsky.hub.util;
/*     */ 
/*     */ public class HubServiceException extends RuntimeException
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  17 */   private String message = "";
/*     */   private long errorCode;
/*  25 */   private String extraInfo = "";
/*     */   private Throwable rootCause;
/*     */ 
/*     */   public HubServiceException(Throwable rootCause)
/*     */   {
/*  37 */     super(rootCause);
/*  38 */     this.rootCause = rootCause;
/*     */   }
/*     */ 
/*     */   public HubServiceException(String message, long errorCode, Throwable rootCause, String extraInfo)
/*     */   {
/*  50 */     super(message, rootCause);
/*  51 */     this.errorCode = errorCode;
/*  52 */     this.message = message;
/*  53 */     this.rootCause = rootCause;
/*  54 */     this.extraInfo = extraInfo;
/*     */   }
/*     */ 
/*     */   public String getMessage()
/*     */   {
/*  63 */     return this.message;
/*     */   }
/*     */ 
/*     */   public void setMessage(String message)
/*     */   {
/*  71 */     this.message = message;
/*     */   }
/*     */ 
/*     */   public long getErrorCode()
/*     */   {
/*  80 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(long errorCode)
/*     */   {
/*  88 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public Throwable getRootCause()
/*     */   {
/*  98 */     return this.rootCause;
/*     */   }
/*     */ 
/*     */   public void setRootCause(Throwable rootCause)
/*     */   {
/* 107 */     this.rootCause = rootCause;
/*     */   }
/*     */ 
/*     */   public String getExtraInfo()
/*     */   {
/* 115 */     return this.extraInfo;
/*     */   }
/*     */ 
/*     */   public void setExtraInfo(String extraInfo)
/*     */   {
/* 123 */     this.extraInfo = extraInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.util.HubServiceException
 * JD-Core Version:    0.6.0
 */