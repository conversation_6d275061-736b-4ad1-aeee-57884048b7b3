/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatChange
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 806779095202254271L;
/*    */   private String cabinType;
/*    */   private String seatNumber;
/*    */   private String arrivalAirport;
/*    */ 
/*    */   public String getCabinType()
/*    */   {
/* 40 */     return this.cabinType;
/*    */   }
/*    */ 
/*    */   public void setCabinType(String cabinType)
/*    */   {
/* 47 */     this.cabinType = cabinType;
/*    */   }
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 54 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 61 */     this.seatNumber = seatNumber;
/*    */   }
/*    */ 
/*    */   public String getArrivalAirport()
/*    */   {
/* 68 */     return this.arrivalAirport;
/*    */   }
/*    */ 
/*    */   public void setArrivalAirport(String arrivalAirport)
/*    */   {
/* 75 */     this.arrivalAirport = arrivalAirport;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatChange
 * JD-Core Version:    0.6.0
 */