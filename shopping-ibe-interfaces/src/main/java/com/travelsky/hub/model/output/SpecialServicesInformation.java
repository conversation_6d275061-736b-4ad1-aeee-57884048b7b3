/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SpecialServicesInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -93263020035079592L;
/*    */   private String specialServicesType;
/*    */   private String specialServicesRemark;
/*    */ 
/*    */   public String getSpecialServicesRemark()
/*    */   {
/* 33 */     return this.specialServicesRemark;
/*    */   }
/*    */ 
/*    */   public void setSpecialServicesRemark(String specialServicesRemark)
/*    */   {
/* 40 */     this.specialServicesRemark = specialServicesRemark;
/*    */   }
/*    */ 
/*    */   public String getSpecialServicesType()
/*    */   {
/* 48 */     return this.specialServicesType;
/*    */   }
/*    */ 
/*    */   public void setSpecialServicesType(String specialServicesType)
/*    */   {
/* 56 */     this.specialServicesType = specialServicesType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SpecialServicesInformation
 * JD-Core Version:    0.6.0
 */