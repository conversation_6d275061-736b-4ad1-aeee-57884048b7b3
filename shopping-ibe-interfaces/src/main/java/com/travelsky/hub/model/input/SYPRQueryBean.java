/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SYPRQueryBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightDate;
/*     */   private String flightNo;
/*     */   private String deptAptCode;
/*     */   private String arvAptCode;
/*     */   private String etCode;
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  46 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  55 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  62 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  71 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getDeptAptCode()
/*     */   {
/*  78 */     return this.deptAptCode;
/*     */   }
/*     */ 
/*     */   public void setDeptAptCode(String deptAptCode)
/*     */   {
/*  87 */     this.deptAptCode = deptAptCode;
/*     */   }
/*     */ 
/*     */   public String getArvAptCode()
/*     */   {
/*  94 */     return this.arvAptCode;
/*     */   }
/*     */ 
/*     */   public void setArvAptCode(String arvAptCode)
/*     */   {
/* 103 */     this.arvAptCode = arvAptCode;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/* 110 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/* 118 */     this.etCode = etCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.SYPRQueryBean
 * JD-Core Version:    0.6.0
 */