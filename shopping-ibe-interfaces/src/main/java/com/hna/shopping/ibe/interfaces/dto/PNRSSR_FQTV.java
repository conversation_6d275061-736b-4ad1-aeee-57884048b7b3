package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRSSR_FQTV extends PNRSSR {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6871969983899823516L;
	char cabin = ' ';                                                             
	String cardLvl = "";                                                          
	String cardNo = "";                                                           
	                                                                              
	String citypair = "";                                                         
	String date = "";                                                             
	String flight = "";                                                           
	int personNum = 0;                                                            
	                                                                              
	public char getCabin()                                                        
	{                                                                             
	  return this.cabin;                                                          
	}                                                                             
	                                                                              
	public String getCardLvl()                                                    
	{                                                                             
	  return this.cardLvl;                                                        
	}                                                                             
	                                                                              
	public String getCardNo()                                                     
	{                                                                             
	  return this.cardNo;                                                         
	}                                                                             
	                                                                              
	public String getCitypair()                                                   
	{                                                                             
	  return this.citypair;                                                       
	}                                                                             
	                                                                              
	public String getDate()                                                       
	{                                                                             
	  return this.date;                                                           
	}                                                                             
	                                                                              
	public String getFlight()                                                     
	{                                                                             
	  return this.flight;                                                         
	}                                                                             
	                                                                              
	public int getPersonNum()                                                     
	{                                                                             
	  return this.personNum;                                                      
	}                                                                             
	                                                                              
	public void setCabin(char c)                                                  
	{                                                                             
	  this.cabin = c;                                                             
	}                                                                             
	                                                                              
	public void setCardLvl(String string)                                         
	{                                                                             
	  this.cardLvl = string;                                                      
	}                                                                             
	                                                                              
	public void setCardNo(String string)                                          
	{                                                                             
	  this.cardNo = string;                                                       
	}                                                                             
	                                                                              
	public void setCitypair(String string)                                        
	{                                                                             
	  this.citypair = string;                                                     
	}                                                                             
	                                                                              
	public void setDate(String string)                                            
	{                                                                             
	  this.date = string;                                                         
	}                                                                             
	                                                                              
	public void setFlight(String string)                                          
	{                                                                             
	  this.flight = string;                                                       
	}                                                                             
	                                                                              
	public void setPersonNum(int i)                                               
	{                                                                             
	  this.personNum = i; }                                                       
	                                                                              
	public String toString() {                                                    
	  try {                                                                       
	    StringBuffer sb = new StringBuffer();                                     
	    sb.append(this.index);                                                
	    sb.append(".SSR FQTV ");                                                  
	    sb.append(this.airline);                                                  
	    sb.append(" 常旅客  " + this.actionCode);                                 
	    sb.append((this.personNum == 0) ? "/" : String.valueOf(this.personNum));  
	    sb.append("  " + this.cardNo);                                            
	    sb.append("  " + this.cardLvl);                                           
	    if (this.personNum > 0) {                                                 
	      sb.append("  " + this.citypair);                                        
	      sb.append("  " + this.flight);                                          
	      sb.append("  " + this.cabin);                                           
	      sb.append("  " + this.date);                                            
	    }                                                                         
	                                                                              
	    sb.append("  " + this.psgrID);                                            
	    return sb.toString(); } catch (Exception e) {                             
	  }                                                                           
	  return super.toString();                                                    
	}                                                                             

}
