/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long PNR的签注信息列表 = -3838146556127414972L;
/*     */   private String paxSequence;
/*     */   private Individual individual;
/*     */   private PassengerType passengerType;
/*     */   private List<IdentityDoc> identityDocs;
/*     */   private List<Foid> foids;
/*     */   private List<ContactInfo> contactInfos;
/*     */ 
/*     */   public String getPaxSequence()
/*     */   {
/*  48 */     return this.paxSequence;
/*     */   }
/*     */ 
/*     */   public void setPaxSequence(String paxSequence)
/*     */   {
/*  55 */     this.paxSequence = paxSequence;
/*     */   }
/*     */ 
/*     */   public Individual getIndividual()
/*     */   {
/*  62 */     return this.individual;
/*     */   }
/*     */ 
/*     */   public void setIndividual(Individual individual)
/*     */   {
/*  69 */     this.individual = individual;
/*     */   }
/*     */ 
/*     */   public PassengerType getPassengerType()
/*     */   {
/*  76 */     return this.passengerType;
/*     */   }
/*     */ 
/*     */   public void setPassengerType(PassengerType passengerType)
/*     */   {
/*  83 */     this.passengerType = passengerType;
/*     */   }
/*     */ 
/*     */   public List<IdentityDoc> getIdentityDocs()
/*     */   {
/*  90 */     return this.identityDocs;
/*     */   }
/*     */ 
/*     */   public void setIdentityDocs(List<IdentityDoc> identityDocs)
/*     */   {
/*  97 */     this.identityDocs = identityDocs;
/*     */   }
/*     */ 
/*     */   public List<Foid> getFoids()
/*     */   {
/* 104 */     return this.foids;
/*     */   }
/*     */ 
/*     */   public void setFoids(List<Foid> foids)
/*     */   {
/* 111 */     this.foids = foids;
/*     */   }
/*     */ 
/*     */   public List<ContactInfo> getContactInfos()
/*     */   {
/* 118 */     return this.contactInfos;
/*     */   }
/*     */ 
/*     */   public void setContactInfos(List<ContactInfo> contactInfos)
/*     */   {
/* 125 */     this.contactInfos = contactInfos;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.PassengerInfo
 * JD-Core Version:    0.6.0
 */