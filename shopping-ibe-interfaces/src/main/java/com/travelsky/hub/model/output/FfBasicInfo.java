/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FfBasicInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String ffAirlineCode;
/*    */   private String ffCardNo;
/*    */   private String ffLevel;
/*    */ 
/*    */   public String getFfAirlineCode()
/*    */   {
/* 33 */     return this.ffAirlineCode;
/*    */   }
/*    */ 
/*    */   public void setFfAirlineCode(String ffAirlineCode)
/*    */   {
/* 41 */     this.ffAirlineCode = ffAirlineCode;
/*    */   }
/*    */ 
/*    */   public String getFfCardNo()
/*    */   {
/* 49 */     return this.ffCardNo;
/*    */   }
/*    */ 
/*    */   public void setFfCardNo(String ffCardNo)
/*    */   {
/* 57 */     this.ffCardNo = ffCardNo;
/*    */   }
/*    */ 
/*    */   public String getFfLevel()
/*    */   {
/* 65 */     return this.ffLevel;
/*    */   }
/*    */ 
/*    */   public void setFfLevel(String ffLevel)
/*    */   {
/* 73 */     this.ffLevel = ffLevel;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.FfBasicInfo
 * JD-Core Version:    0.6.0
 */