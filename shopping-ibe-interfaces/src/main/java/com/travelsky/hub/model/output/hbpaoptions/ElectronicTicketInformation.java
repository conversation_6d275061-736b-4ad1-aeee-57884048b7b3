/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ElectronicTicketInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7174663872048762775L;
/*    */   private String electronicTicketCoupon;
/*    */   private String electronicTicketNumber;
/*    */   private String electronicTicketType;
/*    */ 
/*    */   public String getElectronicTicketType()
/*    */   {
/* 35 */     return this.electronicTicketType;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketType(String electronicTicketType)
/*    */   {
/* 42 */     this.electronicTicketType = electronicTicketType;
/*    */   }
/*    */ 
/*    */   public String getElectronicTicketCoupon()
/*    */   {
/* 49 */     return this.electronicTicketCoupon;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketCoupon(String electronicTicketCoupon)
/*    */   {
/* 56 */     this.electronicTicketCoupon = electronicTicketCoupon;
/*    */   }
/*    */ 
/*    */   public String getElectronicTicketNumber()
/*    */   {
/* 63 */     return this.electronicTicketNumber;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketNumber(String electronicTicketNumber)
/*    */   {
/* 70 */     this.electronicTicketNumber = electronicTicketNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.ElectronicTicketInformation
 * JD-Core Version:    0.6.0
 */