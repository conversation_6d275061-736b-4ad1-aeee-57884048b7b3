/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BagTagBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7295634590547657248L;
/*    */   private String index;
/*    */   private String bagTagNo;
/*    */   private String bagArrivalAirport;
/*    */   private String bagStatus;
/*    */ 
/*    */   public String getIndex()
/*    */   {
/* 38 */     return this.index;
/*    */   }
/*    */ 
/*    */   public void setIndex(String index)
/*    */   {
/* 45 */     this.index = index;
/*    */   }
/*    */ 
/*    */   public String getBagTagNo()
/*    */   {
/* 52 */     return this.bagTagNo;
/*    */   }
/*    */ 
/*    */   public void setBagTagNo(String bagTagNo)
/*    */   {
/* 59 */     this.bagTagNo = bagTagNo;
/*    */   }
/*    */ 
/*    */   public String getBagArrivalAirport()
/*    */   {
/* 66 */     return this.bagArrivalAirport;
/*    */   }
/*    */ 
/*    */   public void setBagArrivalAirport(String bagArrivalAirport)
/*    */   {
/* 73 */     this.bagArrivalAirport = bagArrivalAirport;
/*    */   }
/*    */ 
/*    */   public String getBagStatus()
/*    */   {
/* 80 */     return this.bagStatus;
/*    */   }
/*    */ 
/*    */   public void setBagStatus(String bagStatus)
/*    */   {
/* 87 */     this.bagStatus = bagStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BagTagBean
 * JD-Core Version:    0.6.0
 */