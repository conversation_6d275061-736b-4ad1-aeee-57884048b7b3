/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class UpgFltInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String isSuccess;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String errorCode;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String arrivalAirport;
/*     */   private String errorMsg;
/*     */   private String flightSuffix;
/*     */   private String upgStartTime;
/*     */   private String upgStopTime;
/*     */   private String ruleStartDate;
/*     */   private String ruleEndDate;
/*     */   private String pnr;
/*     */   private String ticketNumber;
/*     */   private List<CabinInfo> availableCabins;
/*     */   private String psrName;
/*     */   private String tourIndex;
/*     */   private String certType;
/*     */   private String psrLevel;
/*     */   private String cabin;
/*     */   private String certNo;
/*     */   private String contactInfo;
/*     */   private String seat;
/*     */ 
/*     */   public String getIsSuccess()
/*     */   {
/*  79 */     return this.isSuccess;
/*     */   }
/*     */ 
/*     */   public void setIsSuccess(String isSuccess)
/*     */   {
/*  86 */     this.isSuccess = isSuccess;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  93 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 100 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 107 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 114 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getErrorMsg()
/*     */   {
/* 121 */     return this.errorMsg;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 128 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 135 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setErrorMsg(String errorMsg)
/*     */   {
/* 142 */     this.errorMsg = errorMsg;
/*     */   }
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/* 149 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/* 156 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public List<CabinInfo> getAvailableCabins()
/*     */   {
/* 163 */     return this.availableCabins;
/*     */   }
/*     */ 
/*     */   public void setAvailableCabins(List<CabinInfo> availableCabins)
/*     */   {
/* 170 */     this.availableCabins = availableCabins;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 177 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 184 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 191 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 198 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 206 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 213 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 221 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 228 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 235 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 242 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/* 249 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/* 256 */     if ((null == contactInfo) || ("".endsWith(contactInfo.trim())) || ("null".equals(contactInfo.trim())))
/* 257 */       this.contactInfo = null;
/*     */     else
/* 259 */       this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/* 267 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/* 274 */     if ((null == psrLevel) || ("".endsWith(psrLevel.trim())) || ("null".equals(psrLevel.trim()))) {
/* 275 */       this.psrLevel = null;
/* 276 */       return;
/*     */     }
/* 278 */     if (psrLevel.trim().endsWith("/")) {
/* 279 */       this.psrLevel = psrLevel.trim().substring(0, psrLevel.length() - 1);
/* 280 */       return;
/*     */     }
/* 282 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 291 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 298 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 305 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 312 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getSeat()
/*     */   {
/* 319 */     return this.seat;
/*     */   }
/*     */ 
/*     */   public void setSeat(String seat)
/*     */   {
/* 326 */     if ((null == seat) || ("".endsWith(seat.trim())) || ("null".equals(seat.trim())))
/* 327 */       this.seat = null;
/*     */     else
/* 329 */       this.seat = seat;
/*     */   }
/*     */ 
/*     */   public String getUpgStartTime()
/*     */   {
/* 338 */     return this.upgStartTime;
/*     */   }
/*     */ 
/*     */   public void setUpgStartTime(String upgStartTime)
/*     */   {
/* 346 */     this.upgStartTime = upgStartTime;
/*     */   }
/*     */ 
/*     */   public String getUpgStopTime()
/*     */   {
/* 354 */     return this.upgStopTime;
/*     */   }
/*     */ 
/*     */   public void setUpgStopTime(String upgStopTime)
/*     */   {
/* 362 */     this.upgStopTime = upgStopTime;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/* 369 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/* 376 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getRuleStartDate()
/*     */   {
/* 384 */     return this.ruleStartDate;
/*     */   }
/*     */ 
/*     */   public void setRuleStartDate(String ruleStartDate)
/*     */   {
/* 392 */     this.ruleStartDate = ruleStartDate;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 399 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 406 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getRuleEndDate()
/*     */   {
/* 414 */     return this.ruleEndDate;
/*     */   }
/*     */ 
/*     */   public void setRuleEndDate(String ruleEndDate)
/*     */   {
/* 422 */     this.ruleEndDate = ruleEndDate;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 429 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 436 */     this.certNo = certNo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgFltInfo
 * JD-Core Version:    0.6.0
 */