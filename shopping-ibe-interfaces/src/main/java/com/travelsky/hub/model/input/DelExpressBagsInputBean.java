/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class DelExpressBagsInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 58162772090877531L;
/*     */   private String flightNumber;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String flightSuffix;
/*     */   private String departureAirport;
/*     */   private String flightClass;
/*     */   private String bagCount;
/*     */   private String arrivalAirport;
/*     */   private String isCrewBag;
/*     */   private String bagRemark;
/*     */   private String bagWeight;
/*     */   private String bagDestination;
/*     */   private String bagAirlineCode;
/*     */   private String isManualBag;
/*     */   private List<String> bagTagNumbers;
/*     */ 
/*     */   public String getBagAirlineCode()
/*     */   {
/*  79 */     return this.bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setBagAirlineCode(String bagAirlineCode)
/*     */   {
/*  92 */     this.bagAirlineCode = bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 102 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 110 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 117 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 125 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 132 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 140 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 150 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 158 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 165 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 173 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 180 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 188 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagRemark()
/*     */   {
/* 195 */     return this.bagRemark;
/*     */   }
/*     */ 
/*     */   public void setBagRemark(String bagRemark)
/*     */   {
/* 203 */     this.bagRemark = bagRemark;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 210 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 218 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTagNumbers()
/*     */   {
/* 225 */     return this.bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumbers(List<String> bagTagNumbers)
/*     */   {
/* 233 */     this.bagTagNumbers = bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public String getBagDestination()
/*     */   {
/* 241 */     return this.bagDestination;
/*     */   }
/*     */ 
/*     */   public void setBagDestination(String bagDestination)
/*     */   {
/* 249 */     this.bagDestination = bagDestination;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 257 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 265 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 272 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 280 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getIsManualBag()
/*     */   {
/* 287 */     return this.isManualBag;
/*     */   }
/*     */ 
/*     */   public void setIsManualBag(String isManualBag)
/*     */   {
/* 295 */     this.isManualBag = isManualBag;
/*     */   }
/*     */ 
/*     */   public String getIsCrewBag()
/*     */   {
/* 303 */     return this.isCrewBag;
/*     */   }
/*     */ 
/*     */   public void setIsCrewBag(String isCrewBag)
/*     */   {
/* 311 */     this.isCrewBag = isCrewBag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.DelExpressBagsInputBean
 * JD-Core Version:    0.6.0
 */