package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.interfaces.dto.DtoTicketInfoResponse;
import com.hna.shopping.ibe.interfaces.dto.TicketInfo;
import com.hna.shopping.ibe.manager.TicketManager;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Created by la<PERSON><PERSON> on 18/11/21.
 * 线程并发处理客票信息
 */
@Slf4j
public class TicketInfoSearchCallable extends IBETicketManager implements Callable {


    /**
     * 并发控制器
     */
    private CountDownLatch countDownLatch;

    private DETR2F detr2f;

    private String ticketNo;

    private String needPnrBookTime;

    private String needTicketChangeHis;

    private DtoTicketInfoResponse response;

    private String secondFactorCode;
    private String secondFactorValue;

    public TicketInfoSearchCallable(CountDownLatch countDownLatch, DETR2F detr2f, String ticketNo, String needPnrBookTime, String needTicketChangeHis, DtoTicketInfoResponse response, String secondFactorCode, String secondFactorValue) {
        this.countDownLatch = countDownLatch;
        this.detr2f = detr2f;
        this.ticketNo = ticketNo;
        this.needPnrBookTime = needPnrBookTime;
        this.needTicketChangeHis = needTicketChangeHis;
        this.response = response;
        this.secondFactorCode = secondFactorCode;
        this.secondFactorValue = secondFactorValue;
    }

    @Override
    public List<TicketInfo> call() throws Exception {
        long currentTime = System.currentTimeMillis();
        DETRTKTResult detrResult = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
        List<TicketInfo> ticketInfoList =  this.mapTicketInfoResult(detrResult, detr2f, 0, null,needPnrBookTime,  needTicketChangeHis, 0, response, secondFactorCode, secondFactorValue);
        log.info(TicketInfoSearchCallable.class.getSimpleName() + "|search ticket take time:" + (System.currentTimeMillis() - currentTime) + "ms");
        countDownLatch.countDown();
        return ticketInfoList;
    }
}
