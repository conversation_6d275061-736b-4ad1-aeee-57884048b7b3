/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AdvancePassengerInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6974682092727413814L;
/*     */   private String gender;
/*     */   private String passportNumber;
/*     */   private String birthDate;
/*     */   private String nationality;
/*     */   private String lastName;
/*     */   private String firstName;
/*     */   private String secondName;
/*     */   private String birthPlace;
/*     */   private String isInfantPassport;
/*     */   private String isPassportHolder;
/*     */   private String isInTransitPassenger;
/*     */   private String passportType;
/*     */   private String passportIssueCountry;
/*     */   private String passportIssueDate;
/*     */   private String passportExpiryDate;
/*     */   private String residenceCountry;
/*     */   private String homeAddress;
/*     */   private String homeCity;
/*     */   private String homeState;
/*     */   private String homeRegion;
/*     */   private String homePostalcode;
/*     */   private String homeContactPhone;
/*     */   private String destinationAddress;
/*     */   private String destinationCity;
/*     */   private String destinationState;
/*     */   private String destinationCountry;
/*     */   private String destinationPostalcode;
/*     */   private String destinationContactPhone;
/*     */   private String visaNumber;
/*     */   private String visaType;
/*     */   private String visaIssueDate;
/*     */   private String visaExpiryDate;
/*     */   private String visaIssuePlace;
/*     */   private String othersNumber;
/*     */   private String othersType;
/*     */   private String othersIssueDate;
/*     */   private String othersExpiryDate;
/*     */   private String othersIssuePlace;
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 177 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 184 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getPassportNumber()
/*     */   {
/* 191 */     return this.passportNumber;
/*     */   }
/*     */ 
/*     */   public void setPassportNumber(String passportNumber)
/*     */   {
/* 198 */     this.passportNumber = passportNumber;
/*     */   }
/*     */ 
/*     */   public String getBirthDate()
/*     */   {
/* 205 */     return this.birthDate;
/*     */   }
/*     */ 
/*     */   public void setBirthDate(String birthDate)
/*     */   {
/* 212 */     this.birthDate = birthDate;
/*     */   }
/*     */ 
/*     */   public String getNationality()
/*     */   {
/* 219 */     return this.nationality;
/*     */   }
/*     */ 
/*     */   public void setNationality(String nationality)
/*     */   {
/* 226 */     this.nationality = nationality;
/*     */   }
/*     */ 
/*     */   public String getLastName()
/*     */   {
/* 233 */     return this.lastName;
/*     */   }
/*     */ 
/*     */   public void setLastName(String lastName)
/*     */   {
/* 240 */     this.lastName = lastName;
/*     */   }
/*     */ 
/*     */   public String getFirstName()
/*     */   {
/* 247 */     return this.firstName;
/*     */   }
/*     */ 
/*     */   public void setFirstName(String firstName)
/*     */   {
/* 254 */     this.firstName = firstName;
/*     */   }
/*     */ 
/*     */   public String getSecondName()
/*     */   {
/* 261 */     return this.secondName;
/*     */   }
/*     */ 
/*     */   public void setSecondName(String secondName)
/*     */   {
/* 268 */     this.secondName = secondName;
/*     */   }
/*     */ 
/*     */   public String getBirthPlace()
/*     */   {
/* 275 */     return this.birthPlace;
/*     */   }
/*     */ 
/*     */   public void setBirthPlace(String birthPlace)
/*     */   {
/* 282 */     this.birthPlace = birthPlace;
/*     */   }
/*     */ 
/*     */   public String getIsInfantPassport()
/*     */   {
/* 289 */     return this.isInfantPassport;
/*     */   }
/*     */ 
/*     */   public void setIsInfantPassport(String isInfantPassport)
/*     */   {
/* 296 */     this.isInfantPassport = isInfantPassport;
/*     */   }
/*     */ 
/*     */   public String getIsPassportHolder()
/*     */   {
/* 303 */     return this.isPassportHolder;
/*     */   }
/*     */ 
/*     */   public void setIsPassportHolder(String isPassportHolder)
/*     */   {
/* 310 */     this.isPassportHolder = isPassportHolder;
/*     */   }
/*     */ 
/*     */   public String getIsInTransitPassenger()
/*     */   {
/* 317 */     return this.isInTransitPassenger;
/*     */   }
/*     */ 
/*     */   public void setIsInTransitPassenger(String isInTransitPassenger)
/*     */   {
/* 324 */     this.isInTransitPassenger = isInTransitPassenger;
/*     */   }
/*     */ 
/*     */   public String getPassportType()
/*     */   {
/* 331 */     return this.passportType;
/*     */   }
/*     */ 
/*     */   public void setPassportType(String passportType)
/*     */   {
/* 338 */     this.passportType = passportType;
/*     */   }
/*     */ 
/*     */   public String getPassportIssueCountry()
/*     */   {
/* 345 */     return this.passportIssueCountry;
/*     */   }
/*     */ 
/*     */   public void setPassportIssueCountry(String passportIssueCountry)
/*     */   {
/* 352 */     this.passportIssueCountry = passportIssueCountry;
/*     */   }
/*     */ 
/*     */   public String getPassportIssueDate()
/*     */   {
/* 359 */     return this.passportIssueDate;
/*     */   }
/*     */ 
/*     */   public void setPassportIssueDate(String passportIssueDate)
/*     */   {
/* 366 */     this.passportIssueDate = passportIssueDate;
/*     */   }
/*     */ 
/*     */   public String getPassportExpiryDate()
/*     */   {
/* 373 */     return this.passportExpiryDate;
/*     */   }
/*     */ 
/*     */   public void setPassportExpiryDate(String passportExpiryDate)
/*     */   {
/* 380 */     this.passportExpiryDate = passportExpiryDate;
/*     */   }
/*     */ 
/*     */   public String getResidenceCountry()
/*     */   {
/* 387 */     return this.residenceCountry;
/*     */   }
/*     */ 
/*     */   public void setResidenceCountry(String residenceCountry)
/*     */   {
/* 394 */     this.residenceCountry = residenceCountry;
/*     */   }
/*     */ 
/*     */   public String getHomeAddress()
/*     */   {
/* 401 */     return this.homeAddress;
/*     */   }
/*     */ 
/*     */   public void setHomeAddress(String homeAddress)
/*     */   {
/* 408 */     this.homeAddress = homeAddress;
/*     */   }
/*     */ 
/*     */   public String getHomeCity()
/*     */   {
/* 415 */     return this.homeCity;
/*     */   }
/*     */ 
/*     */   public void setHomeCity(String homeCity)
/*     */   {
/* 422 */     this.homeCity = homeCity;
/*     */   }
/*     */ 
/*     */   public String getHomeState()
/*     */   {
/* 429 */     return this.homeState;
/*     */   }
/*     */ 
/*     */   public void setHomeState(String homeState)
/*     */   {
/* 436 */     this.homeState = homeState;
/*     */   }
/*     */ 
/*     */   public String getHomeRegion()
/*     */   {
/* 443 */     return this.homeRegion;
/*     */   }
/*     */ 
/*     */   public void setHomeRegion(String homeRegion)
/*     */   {
/* 450 */     this.homeRegion = homeRegion;
/*     */   }
/*     */ 
/*     */   public String getHomePostalcode()
/*     */   {
/* 457 */     return this.homePostalcode;
/*     */   }
/*     */ 
/*     */   public void setHomePostalcode(String homePostalcode)
/*     */   {
/* 464 */     this.homePostalcode = homePostalcode;
/*     */   }
/*     */ 
/*     */   public String getHomeContactPhone()
/*     */   {
/* 471 */     return this.homeContactPhone;
/*     */   }
/*     */ 
/*     */   public void setHomeContactPhone(String homeContactPhone)
/*     */   {
/* 478 */     this.homeContactPhone = homeContactPhone;
/*     */   }
/*     */ 
/*     */   public String getDestinationAddress()
/*     */   {
/* 485 */     return this.destinationAddress;
/*     */   }
/*     */ 
/*     */   public void setDestinationAddress(String destinationAddress)
/*     */   {
/* 492 */     this.destinationAddress = destinationAddress;
/*     */   }
/*     */ 
/*     */   public String getDestinationCity()
/*     */   {
/* 499 */     return this.destinationCity;
/*     */   }
/*     */ 
/*     */   public void setDestinationCity(String destinationCity)
/*     */   {
/* 506 */     this.destinationCity = destinationCity;
/*     */   }
/*     */ 
/*     */   public String getDestinationState()
/*     */   {
/* 513 */     return this.destinationState;
/*     */   }
/*     */ 
/*     */   public void setDestinationState(String destinationState)
/*     */   {
/* 520 */     this.destinationState = destinationState;
/*     */   }
/*     */ 
/*     */   public String getDestinationCountry()
/*     */   {
/* 527 */     return this.destinationCountry;
/*     */   }
/*     */ 
/*     */   public void setDestinationCountry(String destinationCountry)
/*     */   {
/* 534 */     this.destinationCountry = destinationCountry;
/*     */   }
/*     */ 
/*     */   public String getDestinationPostalcode()
/*     */   {
/* 541 */     return this.destinationPostalcode;
/*     */   }
/*     */ 
/*     */   public void setDestinationPostalcode(String destinationPostalcode)
/*     */   {
/* 548 */     this.destinationPostalcode = destinationPostalcode;
/*     */   }
/*     */ 
/*     */   public String getDestinationContactPhone()
/*     */   {
/* 555 */     return this.destinationContactPhone;
/*     */   }
/*     */ 
/*     */   public void setDestinationContactPhone(String destinationContactPhone)
/*     */   {
/* 562 */     this.destinationContactPhone = destinationContactPhone;
/*     */   }
/*     */ 
/*     */   public String getVisaNumber()
/*     */   {
/* 569 */     return this.visaNumber;
/*     */   }
/*     */ 
/*     */   public void setVisaNumber(String visaNumber)
/*     */   {
/* 576 */     this.visaNumber = visaNumber;
/*     */   }
/*     */ 
/*     */   public String getVisaType()
/*     */   {
/* 583 */     return this.visaType;
/*     */   }
/*     */ 
/*     */   public void setVisaType(String visaType)
/*     */   {
/* 590 */     this.visaType = visaType;
/*     */   }
/*     */ 
/*     */   public String getVisaIssueDate()
/*     */   {
/* 597 */     return this.visaIssueDate;
/*     */   }
/*     */ 
/*     */   public void setVisaIssueDate(String visaIssueDate)
/*     */   {
/* 604 */     this.visaIssueDate = visaIssueDate;
/*     */   }
/*     */ 
/*     */   public String getVisaExpiryDate()
/*     */   {
/* 611 */     return this.visaExpiryDate;
/*     */   }
/*     */ 
/*     */   public void setVisaExpiryDate(String visaExpiryDate)
/*     */   {
/* 618 */     this.visaExpiryDate = visaExpiryDate;
/*     */   }
/*     */ 
/*     */   public String getVisaIssuePlace()
/*     */   {
/* 625 */     return this.visaIssuePlace;
/*     */   }
/*     */ 
/*     */   public void setVisaIssuePlace(String visaIssuePlace)
/*     */   {
/* 632 */     this.visaIssuePlace = visaIssuePlace;
/*     */   }
/*     */ 
/*     */   public String getOthersNumber()
/*     */   {
/* 639 */     return this.othersNumber;
/*     */   }
/*     */ 
/*     */   public void setOthersNumber(String othersNumber)
/*     */   {
/* 646 */     this.othersNumber = othersNumber;
/*     */   }
/*     */ 
/*     */   public String getOthersType()
/*     */   {
/* 653 */     return this.othersType;
/*     */   }
/*     */ 
/*     */   public void setOthersType(String othersType)
/*     */   {
/* 660 */     this.othersType = othersType;
/*     */   }
/*     */ 
/*     */   public String getOthersIssueDate()
/*     */   {
/* 667 */     return this.othersIssueDate;
/*     */   }
/*     */ 
/*     */   public void setOthersIssueDate(String othersIssueDate)
/*     */   {
/* 674 */     this.othersIssueDate = othersIssueDate;
/*     */   }
/*     */ 
/*     */   public String getOthersExpiryDate()
/*     */   {
/* 681 */     return this.othersExpiryDate;
/*     */   }
/*     */ 
/*     */   public void setOthersExpiryDate(String othersExpiryDate)
/*     */   {
/* 688 */     this.othersExpiryDate = othersExpiryDate;
/*     */   }
/*     */ 
/*     */   public String getOthersIssuePlace()
/*     */   {
/* 695 */     return this.othersIssuePlace;
/*     */   }
/*     */ 
/*     */   public void setOthersIssuePlace(String othersIssuePlace)
/*     */   {
/* 702 */     this.othersIssuePlace = othersIssuePlace;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AdvancePassengerInformation
 * JD-Core Version:    0.6.0
 */