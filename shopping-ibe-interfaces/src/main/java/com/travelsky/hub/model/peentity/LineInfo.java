/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class LineInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6311873886504097323L;
/*     */   private String compartmentId;
/*     */   private String emptyLine;
/*     */   private String legId;
/*     */   private String lineAttr;
/*     */   private String lineId;
/*     */   private String lineNo;
/*     */   private String lineSeq;
/*     */   private List<SeatInfo> seatInfos;
/*     */ 
/*     */   public String getCompartmentId()
/*     */   {
/*  45 */     return this.compartmentId;
/*     */   }
/*     */ 
/*     */   public void setCompartmentId(String compartmentId)
/*     */   {
/*  52 */     this.compartmentId = compartmentId;
/*     */   }
/*     */ 
/*     */   public String getEmptyLine()
/*     */   {
/*  59 */     return this.emptyLine;
/*     */   }
/*     */ 
/*     */   public void setEmptyLine(String emptyLine)
/*     */   {
/*  66 */     this.emptyLine = emptyLine;
/*     */   }
/*     */ 
/*     */   public String getLegId()
/*     */   {
/*  73 */     return this.legId;
/*     */   }
/*     */ 
/*     */   public void setLegId(String legId)
/*     */   {
/*  80 */     this.legId = legId;
/*     */   }
/*     */ 
/*     */   public String getLineAttr()
/*     */   {
/*  87 */     return this.lineAttr;
/*     */   }
/*     */ 
/*     */   public void setLineAttr(String lineAttr)
/*     */   {
/*  94 */     this.lineAttr = lineAttr;
/*     */   }
/*     */ 
/*     */   public String getLineId()
/*     */   {
/* 101 */     return this.lineId;
/*     */   }
/*     */ 
/*     */   public void setLineId(String lineId)
/*     */   {
/* 108 */     this.lineId = lineId;
/*     */   }
/*     */ 
/*     */   public String getLineNo()
/*     */   {
/* 115 */     return this.lineNo;
/*     */   }
/*     */ 
/*     */   public void setLineNo(String lineNo)
/*     */   {
/* 122 */     this.lineNo = lineNo;
/*     */   }
/*     */ 
/*     */   public String getLineSeq()
/*     */   {
/* 129 */     return this.lineSeq;
/*     */   }
/*     */ 
/*     */   public void setLineSeq(String lineSeq)
/*     */   {
/* 136 */     this.lineSeq = lineSeq;
/*     */   }
/*     */ 
/*     */   public List<SeatInfo> getSeatInfos()
/*     */   {
/* 143 */     return this.seatInfos;
/*     */   }
/*     */ 
/*     */   public void setSeatInfos(List<SeatInfo> seatInfos)
/*     */   {
/* 150 */     this.seatInfos = seatInfos;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.LineInfo
 * JD-Core Version:    0.6.0
 */