/*    */ package com.travelsky.hub.model.input.flightBannedControl;
/*    */ 
/*    */ import com.travelsky.hub.model.input.TxnInfo;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightControlRequestBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3363274609681595731L;
/*    */   private FlightControlBean flightControlBean;
/*    */   private TxnInfo txnInfo;
/*    */ 
/*    */   public FlightControlBean getFlightControlBean()
/*    */   {
/* 38 */     return this.flightControlBean;
/*    */   }
/*    */ 
/*    */   public void setFlightControlBean(FlightControlBean flightControlBean)
/*    */   {
/* 45 */     this.flightControlBean = flightControlBean;
/*    */   }
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 52 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 59 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.flightBannedControl.FlightControlRequestBean
 * JD-Core Version:    0.6.0
 */