package com.travelsky.ibe.client.pnr;

public class BookRMKEx extends BookRMK {

	public BookRMKEx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookRMKEx(String rmktype, String rmkinfo, String psgrName) throws Exception {
		super(rmktype, rmkinfo, psgrName);
		// TODO Auto-generated constructor stub
	}

	public BookRMKEx(String rmktype, String rmkinfo) throws Exception {
		super(rmktype, rmkinfo);
		// TODO Auto-generated constructor stub
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = -7161188318154459665L;
	/*     */ public  String getRmkinfo() {
		/* 104 */     return super.getRmkinfo();//.rmkinfo;
		/*     */   }
	/*     */ public  String getPsgrName() {
		/* 120 */     return super.getpsgrName();
		/*     */   }
	/*     */public   String getRmktype() {
		/*  88 */     return super.getRmktype();//.rmktype;
		/*     */   }
}
