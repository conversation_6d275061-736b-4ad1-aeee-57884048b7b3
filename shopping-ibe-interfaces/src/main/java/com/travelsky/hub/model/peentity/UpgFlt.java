/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class UpgFlt
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7458144366749326192L;
/*     */   private String airlineCode;
/*     */   private String flightSuffix;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String estTime;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String beginTime;
/*     */   private String endTime;
/*     */   private List<CabinSeatInfo> availableCabins;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  42 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  49 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  56 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  63 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getEstTime()
/*     */   {
/*  70 */     return this.estTime;
/*     */   }
/*     */ 
/*     */   public void setEstTime(String estTime)
/*     */   {
/*  77 */     this.estTime = estTime;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  84 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  91 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  98 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 105 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBeginTime()
/*     */   {
/* 112 */     return this.beginTime;
/*     */   }
/*     */ 
/*     */   public void setBeginTime(String beginTime)
/*     */   {
/* 119 */     this.beginTime = beginTime;
/*     */   }
/*     */ 
/*     */   public String getEndTime()
/*     */   {
/* 126 */     return this.endTime;
/*     */   }
/*     */ 
/*     */   public void setEndTime(String endTime)
/*     */   {
/* 133 */     this.endTime = endTime;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 140 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 147 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 154 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 161 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public List<CabinSeatInfo> getAvailableCabins() {
/* 165 */     return this.availableCabins;
/*     */   }
/*     */   public void setAvailableCabins(List<CabinSeatInfo> availableCabins) {
/* 168 */     this.availableCabins = availableCabins;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgFlt
 * JD-Core Version:    0.6.0
 */