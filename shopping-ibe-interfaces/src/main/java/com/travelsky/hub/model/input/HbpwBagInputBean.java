/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class HbpwBagInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8229612937286830223L;
/*    */   private DelBagInputBean hbpwBagReq;
/*    */   private TxnInfo txnInfo;
/*    */ 
/*    */   public DelBagInputBean getHbpwBagReq()
/*    */   {
/* 36 */     return this.hbpwBagReq;
/*    */   }
/*    */ 
/*    */   public void setHbpwBagReq(DelBagInputBean hbpwBagReq)
/*    */   {
/* 43 */     this.hbpwBagReq = hbpwBagReq;
/*    */   }
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 50 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 57 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.HbpwBagInputBean
 * JD-Core Version:    0.6.0
 */