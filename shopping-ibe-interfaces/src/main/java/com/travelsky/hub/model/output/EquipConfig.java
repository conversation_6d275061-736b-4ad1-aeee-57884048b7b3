/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EquipConfig
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String tcardType;
/*    */   private String saleableConfig;
/*    */   private String departureVersion;
/*    */   private String physicalConfig;
/*    */   private String iataType;
/*    */ 
/*    */   public String getTcardType()
/*    */   {
/* 36 */     return this.tcardType;
/*    */   }
/*    */ 
/*    */   public void setTcardType(String tcardType)
/*    */   {
/* 43 */     this.tcardType = tcardType;
/*    */   }
/*    */ 
/*    */   public String getSaleableConfig()
/*    */   {
/* 50 */     return this.saleableConfig;
/*    */   }
/*    */ 
/*    */   public void setSaleableConfig(String saleableConfig)
/*    */   {
/* 57 */     this.saleableConfig = saleableConfig;
/*    */   }
/*    */ 
/*    */   public String getDepartureVersion()
/*    */   {
/* 64 */     return this.departureVersion;
/*    */   }
/*    */ 
/*    */   public void setDepartureVersion(String departureVersion)
/*    */   {
/* 71 */     this.departureVersion = departureVersion;
/*    */   }
/*    */ 
/*    */   public String getPhysicalConfig()
/*    */   {
/* 78 */     return this.physicalConfig;
/*    */   }
/*    */ 
/*    */   public void setPhysicalConfig(String physicalConfig)
/*    */   {
/* 85 */     this.physicalConfig = physicalConfig;
/*    */   }
/*    */ 
/*    */   public String getIataType()
/*    */   {
/* 92 */     return this.iataType;
/*    */   }
/*    */ 
/*    */   public void setIataType(String iataType)
/*    */   {
/* 99 */     this.iataType = iataType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EquipConfig
 * JD-Core Version:    0.6.0
 */