/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightControlOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightStatus;
/*     */   private String beginDay;
/*     */   private String beginTime;
/*     */   private String endTime;
/*     */   private String timeZone;
/*     */   private String timeType;
/*     */   private String beforeHours;
/*     */ 
/*     */   public String getFlightStatus()
/*     */   {
/*  35 */     return this.flightStatus;
/*     */   }
/*     */ 
/*     */   public void setFlightStatus(String flightStatus)
/*     */   {
/*  43 */     this.flightStatus = flightStatus;
/*     */   }
/*     */ 
/*     */   public String getBeginDay()
/*     */   {
/*  52 */     return this.beginDay;
/*     */   }
/*     */ 
/*     */   public void setBeginDay(String beginDay)
/*     */   {
/*  60 */     this.beginDay = beginDay;
/*     */   }
/*     */ 
/*     */   public String getBeginTime()
/*     */   {
/*  69 */     return this.beginTime;
/*     */   }
/*     */ 
/*     */   public void setBeginTime(String beginTime)
/*     */   {
/*  77 */     this.beginTime = beginTime;
/*     */   }
/*     */ 
/*     */   public String getEndTime()
/*     */   {
/*  86 */     return this.endTime;
/*     */   }
/*     */ 
/*     */   public void setEndTime(String endTime)
/*     */   {
/*  94 */     this.endTime = endTime;
/*     */   }
/*     */ 
/*     */   public String getTimeZone()
/*     */   {
/* 103 */     return this.timeZone;
/*     */   }
/*     */ 
/*     */   public void setTimeZone(String timeZone)
/*     */   {
/* 111 */     this.timeZone = timeZone;
/*     */   }
/*     */ 
/*     */   public String getTimeType()
/*     */   {
/* 118 */     return this.timeType;
/*     */   }
/*     */ 
/*     */   public void setTimeType(String timeType)
/*     */   {
/* 125 */     this.timeType = timeType;
/*     */   }
/*     */ 
/*     */   public String getBeforeHours()
/*     */   {
/* 132 */     return this.beforeHours;
/*     */   }
/*     */ 
/*     */   public void setBeforeHours(String beforeHours)
/*     */   {
/* 139 */     this.beforeHours = beforeHours;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.FlightControlOutputBean
 * JD-Core Version:    0.6.0
 */