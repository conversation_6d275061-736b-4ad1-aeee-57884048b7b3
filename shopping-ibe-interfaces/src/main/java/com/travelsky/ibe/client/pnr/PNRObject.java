package com.travelsky.ibe.client.pnr;

import com.travelsky.ibe.client.IBEResult;

public abstract class PNRObject extends IBEResult                                   
implements PNRObjectInterface                                                     
{                                                                                   
private String textInPnr = "N/A";                                                 
private static final long serialVersionUID = -1322793078627795577L;               
                                                                                  
public abstract int getIndex();                                                   
                                                                                  
public String getTextInPNR()                                                      
{                                                                                 
  return this.textInPnr; }                                                        
                                                                                  
public void setTextInPnr(String textInPnr) {                                      
  this.textInPnr = textInPnr;                                                     
}                                                                                 
                                                                                  
public String getPsgrid()                                                         
  throws Exception, UnsupportedOperationException                                 
{   
	return null;
  //throw new UnsupportedOperationException("这个类型的条目不支持指定适用旅客序号");
}                                                                                 
}                                                                                   
