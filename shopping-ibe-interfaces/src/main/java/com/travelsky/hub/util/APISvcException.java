/*     */ package com.travelsky.hub.util;
/*     */ 
/*     */ public class APISvcException extends RuntimeException
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  16 */   private String message = "";
/*     */   private String errorCode;
/*  40 */   private String extraInfo = "";
/*     */   private Throwable rootCause;
/*     */ 
/*     */   public String getMessage()
/*     */   {
/*  23 */     return this.message;
/*     */   }
/*     */ 
/*     */   public void setMessage(String message)
/*     */   {
/*  31 */     this.message = message;
/*     */   }
/*     */ 
/*     */   public APISvcException()
/*     */   {
/*     */   }
/*     */ 
/*     */   public APISvcException(Throwable rootCause)
/*     */   {
/*  59 */     super(rootCause);
/*  60 */     this.rootCause = rootCause;
/*     */   }
/*     */ 
/*     */   public APISvcException(String message, String errorCode, Throwable rootCause, String extraInfo)
/*     */   {
/*  72 */     super(message, rootCause);
/*  73 */     this.errorCode = errorCode;
/*  74 */     this.message = message;
/*  75 */     this.rootCause = rootCause;
/*  76 */     this.extraInfo = extraInfo;
/*     */   }
/*     */ 
/*     */   public APISvcException(String message, String errorCode)
/*     */   {
/*  86 */     super(message, null);
/*  87 */     this.errorCode = errorCode;
/*  88 */     this.message = message;
/*     */   }
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/*  97 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/* 105 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public String getExtraInfo()
/*     */   {
/* 113 */     return this.extraInfo;
/*     */   }
/*     */ 
/*     */   public void setExtraInfo(String extraInfo)
/*     */   {
/* 121 */     this.extraInfo = extraInfo;
/*     */   }
/*     */ 
/*     */   public Throwable getRootCause()
/*     */   {
/* 130 */     return this.rootCause;
/*     */   }
/*     */ 
/*     */   public void setRootCause(Throwable rootCause)
/*     */   {
/* 139 */     this.rootCause = rootCause;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.util.APISvcException
 * JD-Core Version:    0.6.0
 */