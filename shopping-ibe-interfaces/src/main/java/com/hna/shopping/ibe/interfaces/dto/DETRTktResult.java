package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
//@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DETRTktResult implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2567237855488486485L;
	public static final int PASSENGER_ADULT = 0;
	 public static final int PASSENGER_CHILD = 1;
	 public static final int PASSENGER_CHILD_UNACCOMPANIED = 2;
	 public static final int PASSENGER_INFANT = 3;
	 public static final int AIRLINE_DOMESTIC_ETICKET = 10;
	 public static final int AIRLINE_INTERNATIONAL_ETICKET = 11;
	 public static final int BSP_DOMESTIC_ETICKET = 12;
	 public static final int BSP_INTERNATIONAL_ETICKET = 13;
	 public static final int OTHER_TYPE_ETICKET = 14;
	 public static final int UNDEFINED_ETICKET_TYPE = 15;
	 private String issueAirline;
	 private String orgCity;
	 private String dstCity;
	 private String ISI;
	 private String tourCode;
	 private String signingInfo;
	 private String passengerName;
	 private int passengerType;
	 private int unaccompaniedChildAge;
	 private Date infantBirthday;
	 private String exchangeInfo;
	 private String followTicketNo;
	
	 private String fareCompute;
	 private String currencyType;
	 private double fare;
	 private double tax;
	 private int taxLength;
	 private double totalAmount;
	 private String payMethod;
	 private String originalIssue;
	 private String ticketNo;
	 private boolean receiptPrinted;
	 private String currencyType_total;
	 private int eTicketType;
	 private String remark;
	 private double eqviuFare;
	 private String eqviuCurrencyType = null;
	 private String iataNo = null;
	 private Date issueDate = null;
	 private String issuecity = null;
	 private String passengerID;
	 private boolean hasMoreTax;
	 private boolean isIT = false;
	 private boolean isBT = false;
	 private List<DETRSeg> segs = new ArrayList<DETRSeg>();
	 private List<DETRTax> taxs = new ArrayList<DETRTax>();
	
	 public String toString()
	 {
	   StringBuffer output = new StringBuffer();
	   try
	   {
	     String etkttype;
	     String type;
	     output.append("ISSUED BY: " + this.issueAirline);
	     output.append(
	       "\t\t\t\tORG/DST: " + this.orgCity + "/" + this.dstCity);
	     output.append("\tISI: " + this.ISI);
	
	     if (this.eTicketType == 10)
	       etkttype = "AIRLINE DOMESTIC";
	     else if (this.eTicketType == 11)
	       etkttype = "AIRLINE INTERNATIONAL";
	     else if (this.eTicketType == 12)
	       etkttype = "BSP DOMESTIC";
	     else if (this.eTicketType == 13)
	       etkttype = "BSP INTERNATIONAL";
	     else if (this.eTicketType == 14)
	       etkttype = "OTHER TYPE";
	     else if (this.eTicketType == 15)
	       etkttype = "UNDEFINED";
	     else
	       etkttype = "UNKNOWN";
	     output.append("\tETKT TYPE: " + etkttype + "\r\n");
	
	     if (this.signingInfo != null) {
	       output.append("E/R: " + this.signingInfo + "\r\n");
	     }
	     output.append("TOUR CODE: " + this.tourCode);
	
	     if (this.receiptPrinted)
	       output.append("\t\t\t\t\t\t\t\t\t\t\t\t\tRECEIPT PRINTED\r\n");
	     else {
	       output.append("\r\n");
	     }
	     output.append("PASSENGER: ");
	     output.append(getPassengerName() + "\t");
	
	     if (getPassengerType() == 1) {
	       type = "CHILD";
	     }
	     else if (getPassengerType() ==
	       2)
	       type = "UNCOMPANY CHILD";
	     else if (getPassengerType() == 3)
	       type = "INFANT";
	     else
	       type = "ADULT";
	     output.append("\t" + type + "\t");
	     if (getPassengerType() ==
	       2)
	       output.append("AGE: " + getUnaccompaniedChildAge() + "\t");
	     if (getPassengerType() == 3) {
	       String birthday;
	       try {
	         birthday =
	        		 new SimpleDateFormat("MMMyy").format(
	           getInfantBirthday()
	          );
	       } catch (Exception e) {
	         birthday = null;
	       }
	       output.append("BIRTHDAY: " + birthday + "\t");
	     }
	     output.append("\r\n");
	
	     output.append("EXCH: " + this.exchangeInfo);
	     output.append(
	       "\t\t\t\t\t\t\tCONJ TKT: " + this.followTicketNo + "\r\n");
	
	     for (int i = 0; i < this.segs.size(); ++i) {
	       output.append(this.segs.get(i).toString());
	     }
	     if ((this.fareCompute != null) && (this.fareCompute.length() > 0)) {
	       output.append("FC: " + this.fareCompute + "\r\n");
	     }
	     output.append("FARE: \t\t\t" + this.currencyType + "\t");
	     output.append(this.fare);
	     output.append(" | FOP:" + this.payMethod + "\r\n");
	
	     if (this.eqviuCurrencyType != null) {
	       output.append("EQUIV.FARE PD:\t\t" + this.eqviuCurrencyType + "\t");
	       output.append(this.eqviuFare);
	       output.append(" |\r\n");
	     }
	
	     int taxCount = 0;
	     if (this.getTaxs().size() >= 1) {
	       output.append(
	         "TAX: \t\t\t" + this.getTaxs().get(taxCount).getTaxCurrency() + "\t");
	       output.append(this.getTaxs().get(taxCount).getTaxAmount());
	       output.append(this.getTaxs().get(taxCount).getTaxCode());
	     } else {
	       output.append("TAX: \t\t\t" + this.currencyType + "\t");
	       output.append(this.tax);
	     }
	     output.append("\t | OI:" + this.originalIssue + "\r\n");
	     for (++taxCount; taxCount < this.getTaxs().size(); ++taxCount) {
	       output.append(
	         "TAX: \t\t\t" + this.getTaxs().get(taxCount).getTaxCurrency() + "\t");
	       output.append(this.getTaxs().get(taxCount).getTaxAmount());
	       output.append(this.getTaxs().get(taxCount).getTaxCode() + "\t |\r\n");
	     }
	
	     output.append("TOTAL: \t\t\t" + this.currencyType_total + "\t");
	     output.append(this.totalAmount);
	     output.append(" | TKTN:" + this.ticketNo + "\r\n");
	
	     if ((getIataNo() != null) || (getIssueDate() != null) || (getPassengerID() != null))
	       output.append("IATA OFFC:" + getIataNo() + "\t\tISSUED:" +  new SimpleDateFormat("ddMMMyy").format(getIssueDate()) + "\t\tPASS ID:" + getPassengerID() + "\r\n");
	     output.append("IT:" + this.isIT);
	     output.append("\r\nBT:" + this.isBT);
	     return output.toString();
	   } catch (Exception e) {
		   e.printStackTrace();
	   }
	   return null;
	 }
}
