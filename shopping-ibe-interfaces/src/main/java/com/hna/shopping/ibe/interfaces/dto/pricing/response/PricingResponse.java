package com.hna.shopping.ibe.interfaces.dto.pricing.response;
import java.util.List;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import lombok.Data;
@Data
public class PricingResponse extends BaseResponse {

    private static final long serialVersionUID = 5538976088730494418L;
    private Pos pos;
    private List<PricedItinerarys> pricedItinerarys;
    private int pricedItinerarysCount;

}