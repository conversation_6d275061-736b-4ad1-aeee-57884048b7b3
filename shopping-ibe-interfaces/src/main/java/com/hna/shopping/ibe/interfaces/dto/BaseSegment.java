package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

import org.springframework.format.annotation.DateTimeFormat;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class BaseSegment implements Serializable {
	/**
	* 
	*/
	private static final long serialVersionUID = -965100946266861906L;
	public static final int AIRSEG_NORMAL = 0;
	public static final int AIRSEG_ARNK = 1;
	public static final int AIRSEG_OPEN = 2;
	private String airNo;
	private char fltClass;
	private String orgCity;
	private String desCity;
	private String actionCode;
	private int tktNum;
	@DateTimeFormat( pattern = "yyyy-MM-dd" )
	private Date departureTime;
	private boolean skChanged;
	private int type;

	public BaseSegment() {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

	}

//	public BaseSegment(String orgCity, String desCity, String departureTime) throws Exception {
//		this.airNo = "";
//		this.fltClass = '$';
//		this.orgCity = "";
//		this.desCity = "";
//		this.actionCode = "";
//		this.tktNum = 0;
//		this.departureTime = new Date(1L);
//		this.skChanged = false;
//		this.type = 0;
//
//		try {
//			this.orgCity = orgCity;
//			this.desCity = desCity;
//			setDepartureTime(departureTime);
//			this.type = 1;
//		} catch (Exception e) {
//			throw e;
//		}
//	}

	public BaseSegment(String orgCity, String desCity) throws Exception {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

		try {
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.type = 1;
		} catch (Exception e) {
			throw e;
		}
	}

	public BaseSegment(String orgCity, String desCity, Date departureTime) throws Exception {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

		try {
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.departureTime = departureTime;
			this.type = 1;
		} catch (Exception e) {
			throw e;
		}
	}

//	public BaseSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
//			String departureTime, int type) throws Exception {
//		this.airNo = "";
//		this.fltClass = '$';
//		this.orgCity = "";
//		this.desCity = "";
//		this.actionCode = "";
//		this.tktNum = 0;
//		this.departureTime = new Date(1L);
//		this.skChanged = false;
//		this.type = 0;
//
//		try {
//			this.airNo = airNo;
//			this.fltClass = fltClass;
//			this.orgCity = orgCity;
//			this.desCity = desCity;
//			this.actionCode = actionCode;
//			this.tktNum = tktNum;
//			setDepartureTime(departureTime);
//			this.type = (((type != 1) && (type != 2)) ? 0 : type);
//		} catch (Exception e) {
//			throw e;
//		}
//	}

	public BaseSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			Date departureTime, int type) throws Exception {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

		try {
			this.airNo = airNo;
			this.fltClass = fltClass;
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.actionCode = actionCode;
			this.tktNum = tktNum;
			this.departureTime = departureTime;
			this.type = (((type != 1) && (type != 2)) ? 0 : type);
		} catch (Exception e) {
			throw e;
		}
	}

//	public BaseSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
//			String departureTime) throws Exception {
//		this(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
//	}

	public BaseSegment(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum,
			Date departureTime) throws Exception {
		this(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
	}

	public BaseSegment(String airNo, char fltClass, String orgCity, String desCity) throws Exception {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

		try {
			this.airNo = airNo;
			this.fltClass = fltClass;
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.type = 2;
		} catch (Exception e) {
			throw e;
		}
	}

	public BaseSegment(char fltClass, String orgCity, String desCity) throws Exception {
		this.airNo = "";
		this.fltClass = '$';
		this.orgCity = "";
		this.desCity = "";
		this.actionCode = "";
		this.tktNum = 0;
		this.departureTime = new Date(1L);
		this.skChanged = false;
		this.type = 0;

		try {
			this.fltClass = fltClass;
			this.orgCity = orgCity;
			this.desCity = desCity;
			this.type = 2;
		} catch (Exception e) {
			throw e;
		}
	}

//	public void setDepartureTime(String departureTime) throws Exception {
//		try {
//			if (!(departureTime.equals(""))) {
//				if(departureTime.length()==10)
//				this.departureTime = QDateTime.stringToDate(departureTime, "yyyy-MM-dd");//new SimpleDateFormat("yyyy-MM-dd").parse(departureTime);
//				else
//					this.departureTime = new Date(Long.parseLong(departureTime));
//				return;
//			}
//			throw new Exception("Error BookAirSeg departureTime");
//		} catch (Exception e) {
//			throw e;
//		}
//	}

}
