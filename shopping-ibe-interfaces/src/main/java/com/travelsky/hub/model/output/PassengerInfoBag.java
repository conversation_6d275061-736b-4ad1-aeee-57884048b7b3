/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerInfoBag
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5582869402037443812L;
/*     */   private String surName;
/*     */   private String chnName;
/*     */   private String bagWeight;
/*     */   private String bagQuantity;
/*     */   private String bagArrivalAirport;
/*     */   private List<BagTagBean> bagTagList;
/*     */ 
/*     */   public String getSurName()
/*     */   {
/*  47 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/*  54 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getChnName()
/*     */   {
/*  61 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/*  68 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/*  75 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/*  82 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/*  89 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/*  96 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getBagArrivalAirport()
/*     */   {
/* 103 */     return this.bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagArrivalAirport(String bagArrivalAirport)
/*     */   {
/* 110 */     this.bagArrivalAirport = bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public List<BagTagBean> getBagTagList()
/*     */   {
/* 119 */     return this.bagTagList;
/*     */   }
/*     */ 
/*     */   public void setBagTagList(List<BagTagBean> bagTagList)
/*     */   {
/* 126 */     this.bagTagList = bagTagList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerInfoBag
 * JD-Core Version:    0.6.0
 */