/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SegHoldType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8739639664732134451L;
/*    */   private String hoType;
/*    */   private String arrivalStation;
/*    */   private String depatureStation;
/*    */   private String operationType;
/*    */ 
/*    */   public String getHoType()
/*    */   {
/* 33 */     return this.hoType;
/*    */   }
/*    */ 
/*    */   public void setHoType(String hoType)
/*    */   {
/* 40 */     this.hoType = hoType;
/*    */   }
/*    */ 
/*    */   public String getArrivalStation()
/*    */   {
/* 47 */     return this.arrivalStation;
/*    */   }
/*    */ 
/*    */   public void setArrivalStation(String arrivalStation)
/*    */   {
/* 54 */     this.arrivalStation = arrivalStation;
/*    */   }
/*    */ 
/*    */   public String getDepatureStation()
/*    */   {
/* 61 */     return this.depatureStation;
/*    */   }
/*    */ 
/*    */   public void setDepatureStation(String depatureStation)
/*    */   {
/* 68 */     this.depatureStation = depatureStation;
/*    */   }
/*    */ 
/*    */   public String getOperationType()
/*    */   {
/* 75 */     return this.operationType;
/*    */   }
/*    */ 
/*    */   public void setOperationType(String operationType)
/*    */   {
/* 82 */     this.operationType = operationType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SegHoldType
 * JD-Core Version:    0.6.0
 */