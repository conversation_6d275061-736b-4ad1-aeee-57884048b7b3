/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class MarketingInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8502122437294015054L;
/*    */   private String airline;
/*    */   private String flightNumber;
/*    */   private String flightSuffix;
/*    */   private String subClass;
/*    */ 
/*    */   public String getAirline()
/*    */   {
/* 35 */     return this.airline;
/*    */   }
/*    */ 
/*    */   public void setAirline(String airline)
/*    */   {
/* 42 */     this.airline = airline;
/*    */   }
/*    */ 
/*    */   public String getFlightSuffix()
/*    */   {
/* 50 */     return this.flightSuffix;
/*    */   }
/*    */ 
/*    */   public void setFlightSuffix(String flightSuffix)
/*    */   {
/* 57 */     this.flightSuffix = flightSuffix;
/*    */   }
/*    */ 
/*    */   public String getSubClass()
/*    */   {
/* 64 */     return this.subClass;
/*    */   }
/*    */ 
/*    */   public void setSubClass(String subClass)
/*    */   {
/* 71 */     this.subClass = subClass;
/*    */   }
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 78 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 85 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.MarketingInformation
 * JD-Core Version:    0.6.0
 */