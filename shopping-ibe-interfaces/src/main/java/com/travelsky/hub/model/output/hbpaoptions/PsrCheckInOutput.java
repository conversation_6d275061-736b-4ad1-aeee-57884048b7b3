/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PsrCheckInOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1619136514182607902L;
/*    */   private FlightInformation flightInformation;
/*    */   private StreamMessage streamMessage;
/*    */ 
/*    */   public FlightInformation getFlightInformation()
/*    */   {
/* 31 */     return this.flightInformation;
/*    */   }
/*    */ 
/*    */   public void setFlightInformation(FlightInformation flightInformation)
/*    */   {
/* 38 */     this.flightInformation = flightInformation;
/*    */   }
/*    */ 
/*    */   public StreamMessage getStreamMessage()
/*    */   {
/* 45 */     return this.streamMessage;
/*    */   }
/*    */ 
/*    */   public void setStreamMessage(StreamMessage streamMessage)
/*    */   {
/* 52 */     this.streamMessage = streamMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.PsrCheckInOutput
 * JD-Core Version:    0.6.0
 */