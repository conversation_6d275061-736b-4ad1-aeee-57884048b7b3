/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PmcSeOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightLayout;
/*     */   private String planeType;
/*     */   private String planeClass;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String seatMap;
/*     */   private List<SeatAttr> seatAttr;
/*     */ 
/*     */   public String getFlightLayout()
/*     */   {
/*  60 */     return this.flightLayout;
/*     */   }
/*     */ 
/*     */   public void setFlightLayout(String flightLayout)
/*     */   {
/*  71 */     this.flightLayout = flightLayout;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/*  80 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/*  91 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getPlaneClass()
/*     */   {
/* 100 */     return this.planeClass;
/*     */   }
/*     */ 
/*     */   public void setPlaneClass(String planeClass)
/*     */   {
/* 111 */     this.planeClass = planeClass;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 120 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 131 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 140 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 151 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getSeatMap()
/*     */   {
/* 160 */     return this.seatMap;
/*     */   }
/*     */ 
/*     */   public void setSeatMap(String seatMap)
/*     */   {
/* 171 */     this.seatMap = seatMap;
/*     */   }
/*     */ 
/*     */   public List<SeatAttr> getSeatAttr()
/*     */   {
/* 180 */     return this.seatAttr;
/*     */   }
/*     */ 
/*     */   public void setSeatAttr(List<SeatAttr> seatAttr)
/*     */   {
/* 191 */     this.seatAttr = seatAttr;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PmcSeOutput
 * JD-Core Version:    0.6.0
 */