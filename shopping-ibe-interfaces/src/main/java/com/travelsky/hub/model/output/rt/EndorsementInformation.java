/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EndorsementInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7270070249810176732L;
/*    */   private String overflowInd;
/*    */   private String text;
/*    */   private String isManInput;
/*    */ 
/*    */   public String getOverflowInd()
/*    */   {
/* 34 */     return this.overflowInd;
/*    */   }
/*    */ 
/*    */   public void setOverflowInd(String overflowInd)
/*    */   {
/* 41 */     this.overflowInd = overflowInd;
/*    */   }
/*    */ 
/*    */   public String getText()
/*    */   {
/* 48 */     return this.text;
/*    */   }
/*    */ 
/*    */   public void setText(String text)
/*    */   {
/* 55 */     this.text = text;
/*    */   }
/*    */ 
/*    */   public String getIsManInput()
/*    */   {
/* 62 */     return this.isManInput;
/*    */   }
/*    */ 
/*    */   public void setIsManInput(String isManInput)
/*    */   {
/* 69 */     this.isManInput = isManInput;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.EndorsementInformation
 * JD-Core Version:    0.6.0
 */