package com.hna.shopping.ibe.interfaces.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel

public class RTResponse extends BaseResponse {

	/**
	 * 
	 */
	private static final long serialVersionUID = -956668106866953400L;
	 private String blockSeatType = "";
	 private boolean group;
	 private String groupname;
	 private int passengerNumber;
	 private int groupNumber;
	 private String pnrcode;
	 private String oringinalRT;

	private List<PNRPassenger> passengers=new ArrayList<PNRPassenger>();
	private List<PNRSegment> segemnts=new ArrayList<PNRSegment>();
	
}
