/*     */ package com.travelsky.hub.model.peentity.upgemds.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Document
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1533834541518408162L;
/*     */   private String passengerTypeCode;
/*     */   private String fciCode;
/*     */   private String monetaryType;
/*     */   private String travellerSurname;
/*     */   private String documentNumber;
/*     */   private EMDTotal emdTotal;
/*     */   private EMDIdentification emdIdentification;
/*     */   private EMDCoupon emdCoupon;
/*     */   private String docType;
/*     */   private String status;
/*     */ 
/*     */   public String getPassengerTypeCode()
/*     */   {
/*  73 */     return this.passengerTypeCode;
/*     */   }
/*     */ 
/*     */   public void setPassengerTypeCode(String passengerTypeCode)
/*     */   {
/*  80 */     this.passengerTypeCode = passengerTypeCode;
/*     */   }
/*     */ 
/*     */   public String getFciCode()
/*     */   {
/*  87 */     return this.fciCode;
/*     */   }
/*     */ 
/*     */   public void setFciCode(String fciCode)
/*     */   {
/*  94 */     this.fciCode = fciCode;
/*     */   }
/*     */ 
/*     */   public String getMonetaryType()
/*     */   {
/* 101 */     return this.monetaryType;
/*     */   }
/*     */ 
/*     */   public void setMonetaryType(String monetaryType)
/*     */   {
/* 108 */     this.monetaryType = monetaryType;
/*     */   }
/*     */ 
/*     */   public String getTravellerSurname()
/*     */   {
/* 115 */     return this.travellerSurname;
/*     */   }
/*     */ 
/*     */   public void setTravellerSurname(String travellerSurname)
/*     */   {
/* 122 */     this.travellerSurname = travellerSurname;
/*     */   }
/*     */ 
/*     */   public String getDocumentNumber()
/*     */   {
/* 129 */     return this.documentNumber;
/*     */   }
/*     */ 
/*     */   public void setDocumentNumber(String documentNumber)
/*     */   {
/* 136 */     this.documentNumber = documentNumber;
/*     */   }
/*     */ 
/*     */   public EMDTotal getEmdTotal()
/*     */   {
/* 143 */     return this.emdTotal;
/*     */   }
/*     */ 
/*     */   public void setEmdTotal(EMDTotal emdTotal)
/*     */   {
/* 150 */     this.emdTotal = emdTotal;
/*     */   }
/*     */ 
/*     */   public EMDIdentification getEmdIdentification()
/*     */   {
/* 157 */     return this.emdIdentification;
/*     */   }
/*     */ 
/*     */   public void setEmdIdentification(EMDIdentification emdIdentification)
/*     */   {
/* 164 */     this.emdIdentification = emdIdentification;
/*     */   }
/*     */ 
/*     */   public EMDCoupon getEmdCoupon()
/*     */   {
/* 171 */     return this.emdCoupon;
/*     */   }
/*     */ 
/*     */   public void setEmdCoupon(EMDCoupon emdCoupon)
/*     */   {
/* 178 */     this.emdCoupon = emdCoupon;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 185 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 192 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getStatus()
/*     */   {
/* 199 */     return this.status;
/*     */   }
/*     */ 
/*     */   public void setStatus(String status)
/*     */   {
/* 206 */     this.status = status;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.output.Document
 * JD-Core Version:    0.6.0
 */