package com.hna.shopping.ibe.manager.esb;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hnair.opcnet.api.cddata.IETSQueryApi;
import com.hnair.opcnet.api.ods.foc.FocFltInfoApi;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import com.hnair.opcnet.rpc.annotation.OdsReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


/**
 * ESB接口方法封装类
 */
@Service
@Slf4j
public class ESBService {


    /**
     * 高频数据---统一客票查询(HSD-iETS)(http://hna-wiki.hnair.com/pages/viewpage.action?pageId=6522200)
     */
    //@Reference(version = "1.0.0", retries=2,timeout=300000 , parameters = { "protocol", "dubbo" })
    @OdsReference
    private IETSQueryApi ietsQueryApi;

    /**
     * 航班动态接口
     */
    @Reference(version = "1.0.0", retries=2,timeout=300000, parameters = { "protocol", "dubbo" })
    private FocFltInfoApi focFltInfoApi;


    /**
     * 场景一。 根据航班日期+PNR查询。 输入条件：fltDate+icsNo+其他（可选）
     * @param icsNo ICS系统PNR号。必填
     * @param fltDate 航班日期或航班日期范围。 选填，不输入默认为当前日期前3个月内，格式：yyyyMMdd或yyyyMMdd-yyyyMMdd
     */
    public ApiResponse queryIETSByIcsnoAndFltDate(String icsNo, String fltDate) {
        ApiRequest request = new ApiRequest();
        request.setOption("icdNo", icsNo);
        request.setOption("fltDate", fltDate);
        return ietsQueryApi.queryIETSByIcsnoAndFltDate(request);
    }

    /**
     * 场景二。 根据票号查询。输入条件：tkne+其他（可选）
     * @param tkne ET票号。必填。 880-123456789
     * @return
     */
    public ApiResponse queryIETSByTkne(String tkne) {
        ApiRequest request = new ApiRequest();
        request.setOption("tkne", tkne);
        return ietsQueryApi.queryIETSByTkne(request);
    }

    /**
     * 场景三。 根据航班日期+旅客证件号查询。输入条件：fltDate+rteeIdNo+其他（可选）
     * @param rteeIdNo RTEE清洗后的旅客证件号。 必输。该字段兼容 「完整证件号」及 「去除字母前缀的证件号」 2种类型的查询 如：51010119900101021X  G1234567或1234567
     * @param fltDate 航班日期或航班日期范围。 选填，不输入默认为当前日期前3个月内，格式：yyyyMMdd或yyyyMMdd-yyyyMMdd。不输入默认为当前查询日期前后1年（2年）内的行程。
     * @return
     */
    public ApiResponse queryIETSByIdinfoAndFltDate(String rteeIdNo, String fltDate) {
        ApiRequest request = new ApiRequest();
        request.setOption("rteeIdNo", rteeIdNo);
        request.setOption("fltDate", fltDate);
        return ietsQueryApi.queryIETSByIdinfoAndFltDate(request);
    }

    /**
     * 场景四。 根据航班日期+航班号查询。输入条件：fltDate+rteeFlightNo+其他（可选）
     * @param fltDate 航班日期。必输，只能为指定日期，不能为日期范围 yyyyMMdd
     * @param rteeFlightNo RTEE清洗后的承运航班号。必输。HU0496 （航司代码+4位航班号，如航班号为3位需补0为4位。如HU469需输入HU0469）
     * @param orig 出发地三字码。选填。输入出发、到达地三字码可区分经停航班的不同航段
     * @param dest 到达地三字码。选填。输入出发、到达地三字码可区分经停航班的不同航段
     * @param getPsgOtherSegs 是否获取同票号下的其他航段信息。 默认Y 表示包含输入，N 仅输出符合以上条件的航段
     * @return
     */
    public ApiResponse queryIETSByFlightNo(String fltDate, String rteeFlightNo, String orig, String dest, String getPsgOtherSegs) {
        ApiRequest request = new ApiRequest();
        request.setOption("fltDate", fltDate);
        request.setOption("rteeFlightNo", rteeFlightNo);
        if (StringUtils.hasText(orig)) {
            request.setOption("orig", orig);
        }
        if (StringUtils.hasText(dest)) {
            request.setOption("dest", dest);
        }
        if (StringUtils.hasText(getPsgOtherSegs)) {
            request.setOption("getPsgOtherSegs", getPsgOtherSegs);
        }
        return ietsQueryApi.queryIETSByFlightNo(request);
    }


    /**
     * 场景五。 根据航班（计划）到港日期、航站、中转衔接时间范围，查询中转旅客信息。输入条件：arrivalDate+dest+rteeTransferDay+rteeTransferTime+其他（可选）
     * @param arrivalDate 航班计划到港日期。必输，只能为指定日期，不能为日期范围 yyyyMMdd
     * @param dest 到达地（中转航站）。必输。PEK
     * @param rteeTransferDay 本航段与下航段的中转衔接最大天数。必输，整型，小于等于该值。与rteeTransferTime为「并且」关系
     * @param rteeTransferTime 本航段与下航段的中转衔接最小分钟数。必输，整型，大于等于输入值。与rteeTransferDay为「并且」关系
     * @param rteeConjunctiveTkts 是否仅针对连续票号的识别处理。 若该参数输入为Y，将对票号为连续的2张客票关联，只筛选符合以上条件的结果。默认N, 注意，当该参数为Y时，仅会查询连续票号符合中转查询条件的信息，不包含单票号中符合中转条件的数据
     * @return
     */
    public ApiResponse queryIETSTransferPsgs(String arrivalDate, String dest, String rteeTransferDay, String rteeTransferTime, String rteeConjunctiveTkts) {
        ApiRequest request = new ApiRequest();
        request.setOption("arrivalDate", arrivalDate);
        request.setOption("rteeTransferDay", rteeTransferDay);
        request.setOption("rteeTransferTime", rteeTransferTime);
        request.setOption("rteeConjunctiveTkts", rteeConjunctiveTkts);
        return ietsQueryApi.queryIETSTransferPsgs(request);
    }


    /**
     * 查询航班动态接口方法。 该方法是 V2版接口，封装输出对象中的 data 为具体业务类
     * @param apiRequest
     * @return
     */
    public EsbApiResponse<StandardFocFlightInfo> getStandardFocFlightInfo(ApiRequest apiRequest) {
        log.info("ESB航班动态查询接口V2，请求：{}", JSONArray.toJSONString(apiRequest));
        long start = System.currentTimeMillis();
        ApiResponse apiResponse = focFltInfoApi.getStandardFocFlightInfo(apiRequest);
        long end = System.currentTimeMillis();
        log.info("ESB航班动态查询接口，调用结果：{}，分页信息：{}，耗时：{}ms", JSONObject.toJSONString(apiResponse.getResult()), JSONObject.toJSONString(apiResponse.getPageResult()), end - start);
        return new EsbApiResponse<StandardFocFlightInfo>().wrapper(apiResponse, StandardFocFlightInfo.class);
    }

}
