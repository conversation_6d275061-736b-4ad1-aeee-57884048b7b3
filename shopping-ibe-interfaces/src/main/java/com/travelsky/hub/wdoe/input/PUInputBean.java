/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PUInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 823410560345379988L;
/*  20 */   private String passengerName = "";
/*     */ 
/*  34 */   private String mseatNumber = "";
/*     */ 
/*  38 */   private String tourIndex = "";
/*     */ 
/*  42 */   private String ffpAirlineCode = "";
/*     */ 
/*  46 */   private String ffpCardNumber = "";
/*     */ 
/*  50 */   private String ffpCardPrior = "";
/*     */ 
/*  54 */   private String mFFPAirlineCode = "";
/*     */ 
/*  58 */   private String mFFPCardNumber = "";
/*     */ 
/*  62 */   private String mFFPCardPrior = "";
/*     */ 
/*  66 */   private String certificateType = "";
/*     */ 
/*  70 */   private String certificateNumber = "";
/*     */ 
/*  74 */   private String telephoneNumber = "";
/*     */ 
/*  78 */   private String email = "";
/*     */ 
/*  82 */   private String isBppPrinted = "";
/*     */   private List<TxtMsg> msgList;
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 167 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 175 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 183 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 191 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 199 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 207 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 215 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 223 */     this.ffpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 231 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 239 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getFFPCardPrior()
/*     */   {
/* 247 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFFPCardPrior(String cardPrior)
/*     */   {
/* 255 */     this.ffpCardPrior = cardPrior;
/*     */   }
/*     */ 
/*     */   public String getIsBppPrinted()
/*     */   {
/* 263 */     return this.isBppPrinted;
/*     */   }
/*     */ 
/*     */   public void setIsBppPrinted(String isBppPrinted)
/*     */   {
/* 271 */     this.isBppPrinted = isBppPrinted;
/*     */   }
/*     */ 
/*     */   public String getMFFPAirlineCode()
/*     */   {
/* 279 */     return this.mFFPAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMFFPAirlineCode(String airlineCode)
/*     */   {
/* 287 */     this.mFFPAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getMFFPCardNumber()
/*     */   {
/* 295 */     return this.mFFPCardNumber;
/*     */   }
/*     */ 
/*     */   public void setMFFPCardNumber(String cardNumber)
/*     */   {
/* 303 */     this.mFFPCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getMFFPCardPrior()
/*     */   {
/* 311 */     return this.mFFPCardPrior;
/*     */   }
/*     */ 
/*     */   public void setMFFPCardPrior(String cardPrior)
/*     */   {
/* 319 */     this.mFFPCardPrior = cardPrior;
/*     */   }
/*     */ 
/*     */   public String getMseatNumber()
/*     */   {
/* 327 */     return this.mseatNumber;
/*     */   }
/*     */ 
/*     */   public void setMseatNumber(String mseatNumber)
/*     */   {
/* 335 */     this.mseatNumber = mseatNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 343 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 351 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getTelephoneNumber()
/*     */   {
/* 368 */     return this.telephoneNumber;
/*     */   }
/*     */ 
/*     */   public void setTelephoneNumber(String telephoneNumber)
/*     */   {
/* 376 */     this.telephoneNumber = telephoneNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 384 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 392 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public List<TxtMsg> getMsgList()
/*     */   {
/* 400 */     return this.msgList;
/*     */   }
/*     */ 
/*     */   public void setMsgList(List<TxtMsg> msgList)
/*     */   {
/* 408 */     this.msgList = msgList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PUInputBean
 * JD-Core Version:    0.6.0
 */