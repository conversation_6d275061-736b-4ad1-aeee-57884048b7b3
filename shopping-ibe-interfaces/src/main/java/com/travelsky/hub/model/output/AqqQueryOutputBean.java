/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AqqQueryOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String result;
/*    */   private String msgType;
/*    */ 
/*    */   public void setResult(String result)
/*    */   {
/* 23 */     this.result = result;
/*    */   }
/*    */ 
/*    */   public String getResult()
/*    */   {
/* 34 */     return this.result;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 41 */     this.msgType = msgType;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 48 */     return this.msgType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AqqQueryOutputBean
 * JD-Core Version:    0.6.0
 */