/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ public class BaseHaqqOutputBean
/*    */ {
/*    */   private String result;
/*    */   private String msgType;
/*    */ 
/*    */   public String getResult()
/*    */   {
/* 15 */     return this.result;
/*    */   }
/*    */ 
/*    */   public void setResult(String result)
/*    */   {
/* 22 */     this.result = result;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 29 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 36 */     this.msgType = msgType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.BaseHaqqOutputBean
 * JD-Core Version:    0.6.0
 */