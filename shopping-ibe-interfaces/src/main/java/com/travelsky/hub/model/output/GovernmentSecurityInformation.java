/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class GovernmentSecurityInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7375479554381963284L;
/*    */   private String governmentSecurityCheckType;
/*    */   private String governmentSecurityCheckStatus;
/*    */ 
/*    */   public String getGovernmentSecurityCheckType()
/*    */   {
/* 33 */     return this.governmentSecurityCheckType;
/*    */   }
/*    */ 
/*    */   public void setGovernmentSecurityCheckType(String governmentSecurityCheckType)
/*    */   {
/* 40 */     this.governmentSecurityCheckType = governmentSecurityCheckType;
/*    */   }
/*    */ 
/*    */   public String getGovernmentSecurityCheckStatus()
/*    */   {
/* 47 */     return this.governmentSecurityCheckStatus;
/*    */   }
/*    */ 
/*    */   public void setGovernmentSecurityCheckStatus(String governmentSecurityCheckStatus)
/*    */   {
/* 54 */     this.governmentSecurityCheckStatus = governmentSecurityCheckStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.GovernmentSecurityInformation
 * JD-Core Version:    0.6.0
 */