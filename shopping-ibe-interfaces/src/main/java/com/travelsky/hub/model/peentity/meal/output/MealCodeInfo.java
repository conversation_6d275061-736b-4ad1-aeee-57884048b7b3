/*     */ package com.travelsky.hub.model.peentity.meal.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class MealCodeInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3422789212015589328L;
/*     */   private String mealType;
/*     */   private String mealTypeText;
/*     */   private String mealSubType;
/*     */   private String mealControlLevel;
/*     */   private List<MealStatusInfo> mealStatusInfos;
/*     */   private String mealGroup;
/*     */ 
/*     */   public String getMealType()
/*     */   {
/*  48 */     return this.mealType;
/*     */   }
/*     */ 
/*     */   public void setMealType(String mealType)
/*     */   {
/*  57 */     this.mealType = mealType;
/*     */   }
/*     */ 
/*     */   public String getMealTypeText()
/*     */   {
/*  66 */     return this.mealTypeText;
/*     */   }
/*     */ 
/*     */   public void setMealTypeText(String mealTypeText)
/*     */   {
/*  75 */     this.mealTypeText = mealTypeText;
/*     */   }
/*     */ 
/*     */   public String getMealSubType()
/*     */   {
/*  84 */     return this.mealSubType;
/*     */   }
/*     */ 
/*     */   public void setMealSubType(String mealSubType)
/*     */   {
/*  93 */     this.mealSubType = mealSubType;
/*     */   }
/*     */ 
/*     */   public String getMealControlLevel()
/*     */   {
/* 100 */     return this.mealControlLevel;
/*     */   }
/*     */ 
/*     */   public void setMealControlLevel(String mealControlLevel)
/*     */   {
/* 107 */     this.mealControlLevel = mealControlLevel;
/*     */   }
/*     */ 
/*     */   public List<MealStatusInfo> getMealStatusInfos()
/*     */   {
/* 114 */     return this.mealStatusInfos;
/*     */   }
/*     */ 
/*     */   public void setMealStatusInfos(List<MealStatusInfo> mealStatusInfos)
/*     */   {
/* 121 */     this.mealStatusInfos = mealStatusInfos;
/*     */   }
/*     */ 
/*     */   public String getMealGroup()
/*     */   {
/* 128 */     return this.mealGroup;
/*     */   }
/*     */ 
/*     */   public void setMealGroup(String mealGroup)
/*     */   {
/* 135 */     this.mealGroup = mealGroup;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.MealCodeInfo
 * JD-Core Version:    0.6.0
 */