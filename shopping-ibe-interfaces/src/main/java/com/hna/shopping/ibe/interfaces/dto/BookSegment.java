package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.BookAirSeg;
import lombok.Data;

import java.util.Date;

@Data
public class BookSegment extends BookAirSeg {
	private static final long serialVersionUID = 775691259208364169L;
	public static final int AIRSEG_NORMAL = 0;
	public static final int AIRSEG_ARNK = 1;
	public static final int AIRSEG_OPEN = 2;
	private String airNo;
	private char fltClass;
	private String orgCity;
	private String desCity;
	private String actionCode;
	private int tktNum;
	private Date departureTime;
	private boolean skChanged;
	private int type;
	private int priority;
	private String manualDepTimeStr;
	private String manualArrTimeStr;
	boolean manualInputTime;
}