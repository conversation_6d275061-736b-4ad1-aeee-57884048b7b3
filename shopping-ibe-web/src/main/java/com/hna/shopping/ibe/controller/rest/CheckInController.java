package com.hna.shopping.ibe.controller.rest;

import com.hna.shopping.ibe.common.responsecode.CodePE;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.checkin.CheckInService;
import com.hna.shopping.ibe.interfaces.dto.checkin.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * @Author: deyi
 * @Date: 2019/11/19 10:54
 * @Version 1.0
 */

@Api("值机接口")
@RestController
@RequestMapping("/api/checkin")
@Slf4j
public class CheckInController {

    @Resource(name = "IBECheckInService")
    private CheckInService IBECheckInService;

    @ApiOperation(value = "航班是否初始化")
    @PostMapping("hasInitialize")
    public RestResponse hasInitialized(@RequestBody FlightInitRQ req) {
        boolean result = IBECheckInService.hasInitialized(req);
        if (!result) {
            return RestResponse.exception(CodePE.INVOKE_PE_ERROR);
        }
        return RestResponse.ok("ok");
    }

    @ApiOperation(value = "航班值机信息")
    @PostMapping("info")
    public RestResponse<FlightCheckInInfoRS> flightCheckInInfo(@RequestBody FlightInitRQ req) {
        FlightCheckInInfoRS data = IBECheckInService.queryCheckInFlightInfo(req);
        return RestResponse.ok(data);
    }

    @ApiOperation(value = "单人值机")
    @PostMapping("singleCheckIn")
    public RestResponse<SingleCheckInRS> singleCheckIn(@RequestBody SingleCheckInRQ req) {
        SingleCheckInRS data = IBECheckInService.singleCheckIn(req);
        return RestResponse.ok(data);
    }


    @ApiOperation(value = "取消值机")
    @PostMapping("cancelCheckIn")
    public RestResponse<CancelCheckInRS> cancelCheckIn(@RequestBody CancelCheckInRQ req) {
        CancelCheckInRS data = IBECheckInService.cancelCheckIn(req);
        return RestResponse.ok(data);
    }

    @ApiOperation(value = "预选座位")
    @PostMapping("connectingFlightCheckIn")
    public RestResponse<PUOutputRS> connectingFlightCheckIn(@RequestBody HbpuoInputRQ req) {
        PUOutputRS data = IBECheckInService.connectingFlightCheckIn(req);
        return RestResponse.ok(data);
    }

    @ApiOperation(value = "重打登机牌")
    @PostMapping("printBoarding")
    public RestResponse<String> printBoarding(@RequestBody PrintBoardingRQ req) {
        String data = IBECheckInService.printBoarding(req);
        return RestResponse.ok(data);
    }

    @ApiOperation(value = "天气")
    @PostMapping("weather")
    public RestResponse<WeatherRS> weather(@RequestBody WeatherRQ req) {
        WeatherRS result = IBECheckInService.weather(req);
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "QR码 二维码")
    @PostMapping("qrEbp")
    public RestResponse<EbpRS> qrEbp(@RequestBody EbpRQ req) {
        EbpRS result = IBECheckInService.qrEbp(req);
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "PDF417 二维码")
    @PostMapping("pdfEbp")
    public RestResponse<EbpRS> pdfEbp(@RequestBody EbpRQ req) {
        EbpRS result = IBECheckInService.pdfEbp(req);
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "一维条形码")
    @PostMapping("barEbp")
    public RestResponse<EbpRS> barEbp(@RequestBody EbpRQ req) {
        EbpRS result = IBECheckInService.barEbp(req);
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "提取所有行程")
    @PostMapping("retrieve")
    public RestResponse retrieve(@Valid @RequestBody RetrieveRQ rq ){
        return RestResponse.ok(IBECheckInService.retrieve(rq));
    }

    @ApiOperation(value = "座位图 离港座位查询")
    @PostMapping("seatMap")
    public RestResponse seatMap(@Valid @RequestBody SeatMapRQ rq ){
        return RestResponse.ok(IBECheckInService.seatMap(rq));
    }

    @ApiOperation(value = "获取旅客信息 国内")
    @PostMapping("retrievePassenger")
    public RestResponse retrievePassenger(@Valid @RequestBody RetrievePassengerRQ rq ){
        return RestResponse.ok(IBECheckInService.retrievePassenger(rq));
    }

    @ApiOperation(value = "获取旅客信息 国外")
    @PostMapping("retrieveForeignerPassenger")
    public RestResponse retrieveForeignerPassenger(@Valid @RequestBody RetrieveForeignerPassengerRQ rq ){
        return RestResponse.ok(IBECheckInService.retrieveForeignerPassenger(rq));
    }

    @ApiOperation(value = "查询座位图 预约值机")
    @PostMapping("getSeatChart")
    public RestResponse getSeatChart(@Valid @RequestBody SeatChartRQ rq ){
        return RestResponse.ok(IBECheckInService.getSeatChart(rq));
    }

    @ApiOperation(value = "预定座位")
    @PostMapping("bookingSeat")
    public RestResponse bookingSeat(@RequestBody BookingSeatRQ rq){
        return RestResponse.ok(IBECheckInService.bookingSeat(rq));
    }

    @ApiOperation(value = "旅客座位释放")
    @PostMapping("seatRelease")
    public RestResponse seatRelease(@RequestBody SeatReleaseRQ rq){
        return RestResponse.ok(IBECheckInService.seatRelease(rq));
    }
}
