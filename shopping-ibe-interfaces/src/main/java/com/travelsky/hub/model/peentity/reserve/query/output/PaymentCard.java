/*    */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PaymentCard
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7086872082817039050L;
/*    */   private String cardCode;
/*    */   private String cardNumber;
/*    */   private String serialNumber;
/*    */ 
/*    */   public String getCardCode()
/*    */   {
/* 40 */     return this.cardCode;
/*    */   }
/*    */ 
/*    */   public void setCardCode(String cardCode)
/*    */   {
/* 47 */     this.cardCode = cardCode;
/*    */   }
/*    */ 
/*    */   public String getCardNumber()
/*    */   {
/* 54 */     return this.cardNumber;
/*    */   }
/*    */ 
/*    */   public void setCardNumber(String cardNumber)
/*    */   {
/* 61 */     this.cardNumber = cardNumber;
/*    */   }
/*    */ 
/*    */   public String getSerialNumber()
/*    */   {
/* 68 */     return this.serialNumber;
/*    */   }
/*    */ 
/*    */   public void setSerialNumber(String serialNumber)
/*    */   {
/* 75 */     this.serialNumber = serialNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.PaymentCard
 * JD-Core Version:    0.6.0
 */