/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class RecheckInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3822378237807531643L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String cabinType;
/*     */   private String flightDate;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  48 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  55 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  62 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  69 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  76 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  83 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  90 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  97 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 104 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 111 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.RecheckInfo
 * JD-Core Version:    0.6.0
 */