/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PaxSeutOutPutBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4975167460254702617L;
/*     */   private String isSuccess;
/*     */   private String errorMsg;
/*     */   private String errorCode;
/*     */   private String seatNbr;
/*     */   private String passFlag;
/*     */   private String seutTime;
/*     */   private String seutStatus;
/*     */   private String seutChannel;
/*     */   private String seutTxt;
/*     */   private String seutIoc;
/*     */   private String handFlag;
/*     */   private String psgScid;
/*     */   private String brdpCode;
/*     */   private String brdpType;
/*     */   private String seutSign;
/*     */   private String brdpPsgCode;
/*     */   private String ebpFlag;
/*     */   private String seutDataFlag;
/*     */   private String rdId;
/*     */   private String comn;
/*     */   private String psgDocValidityDate;
/*     */   private String psgDocIssueDpt;
/*     */   private String psgAddr;
/*     */   private String psgBirthday;
/*     */   private String psgNat;
/*     */   private String psgNameZh;
/*     */   private String psgNameEn;
/*     */   private String psgGender;
/*     */   private String psgPhoto;
/*     */   private String psgDocNbr;
/*     */   private String psgDocType;
/*     */ 
/*     */   public String getIsSuccess()
/*     */   {
/*  74 */     return this.isSuccess;
/*     */   }
/*     */   public void setErrorCode(String errorCode) {
/*  77 */     this.errorCode = errorCode;
/*     */   }
/*     */   public void setSeatNbr(String seatNbr) {
/*  80 */     this.seatNbr = seatNbr;
/*     */   }
/*     */   public String getPassFlag() {
/*  83 */     return this.passFlag;
/*     */   }
/*     */   public void setPassFlag(String passFlag) {
/*  86 */     this.passFlag = passFlag;
/*     */   }
/*     */   public void setIsSuccess(String isSuccess) {
/*  89 */     this.isSuccess = isSuccess;
/*     */   }
/*     */   public String getErrorMsg() {
/*  92 */     return this.errorMsg;
/*     */   }
/*     */   public void setErrorMsg(String errorMsg) {
/*  95 */     this.errorMsg = errorMsg;
/*     */   }
/*     */   public String getErrorCode() {
/*  98 */     return this.errorCode;
/*     */   }
/*     */   public String getSeutTime() {
/* 101 */     return this.seutTime;
/*     */   }
/*     */   public String getSeatNbr() {
/* 104 */     return this.seatNbr;
/*     */   }
/*     */   public void setSeutTime(String seutTime) {
/* 107 */     this.seutTime = seutTime;
/*     */   }
/*     */   public String getSeutStatus() {
/* 110 */     return this.seutStatus;
/*     */   }
/*     */   public void setSeutStatus(String seutStatus) {
/* 113 */     this.seutStatus = seutStatus;
/*     */   }
/*     */   public String getSeutChannel() {
/* 116 */     return this.seutChannel;
/*     */   }
/*     */   public void setSeutChannel(String seutChannel) {
/* 119 */     this.seutChannel = seutChannel;
/*     */   }
/*     */   public String getSeutTxt() {
/* 122 */     return this.seutTxt;
/*     */   }
/*     */   public void setSeutTxt(String seutTxt) {
/* 125 */     this.seutTxt = seutTxt;
/*     */   }
/*     */   public String getSeutIoc() {
/* 128 */     return this.seutIoc;
/*     */   }
/*     */   public void setSeutIoc(String seutIoc) {
/* 131 */     this.seutIoc = seutIoc;
/*     */   }
/*     */   public String getHandFlag() {
/* 134 */     return this.handFlag;
/*     */   }
/*     */   public void setHandFlag(String handFlag) {
/* 137 */     this.handFlag = handFlag;
/*     */   }
/*     */   public String getPsgScid() {
/* 140 */     return this.psgScid;
/*     */   }
/*     */   public void setPsgScid(String psgScid) {
/* 143 */     this.psgScid = psgScid;
/*     */   }
/*     */   public String getBrdpCode() {
/* 146 */     return this.brdpCode;
/*     */   }
/*     */   public void setBrdpCode(String brdpCode) {
/* 149 */     this.brdpCode = brdpCode;
/*     */   }
/*     */   public String getBrdpType() {
/* 152 */     return this.brdpType;
/*     */   }
/*     */   public void setBrdpType(String brdpType) {
/* 155 */     this.brdpType = brdpType;
/*     */   }
/*     */   public String getSeutSign() {
/* 158 */     return this.seutSign;
/*     */   }
/*     */   public void setSeutSign(String seutSign) {
/* 161 */     this.seutSign = seutSign;
/*     */   }
/*     */   public String getBrdpPsgCode() {
/* 164 */     return this.brdpPsgCode;
/*     */   }
/*     */   public void setBrdpPsgCode(String brdpPsgCode) {
/* 167 */     this.brdpPsgCode = brdpPsgCode;
/*     */   }
/*     */   public String getEbpFlag() {
/* 170 */     return this.ebpFlag;
/*     */   }
/*     */   public void setEbpFlag(String ebpFlag) {
/* 173 */     this.ebpFlag = ebpFlag;
/*     */   }
/*     */   public String getSeutDataFlag() {
/* 176 */     return this.seutDataFlag;
/*     */   }
/*     */   public void setSeutDataFlag(String seutDataFlag) {
/* 179 */     this.seutDataFlag = seutDataFlag;
/*     */   }
/*     */   public String getRdId() {
/* 182 */     return this.rdId;
/*     */   }
/*     */   public void setRdId(String rdId) {
/* 185 */     this.rdId = rdId;
/*     */   }
/*     */   public String getComn() {
/* 188 */     return this.comn;
/*     */   }
/*     */   public void setComn(String comn) {
/* 191 */     this.comn = comn;
/*     */   }
/*     */   public String getPsgDocValidityDate() {
/* 194 */     return this.psgDocValidityDate;
/*     */   }
/*     */   public void setPsgDocValidityDate(String psgDocValidityDate) {
/* 197 */     this.psgDocValidityDate = psgDocValidityDate;
/*     */   }
/*     */   public String getPsgDocIssueDpt() {
/* 200 */     return this.psgDocIssueDpt;
/*     */   }
/*     */   public void setPsgDocIssueDpt(String psgDocIssueDpt) {
/* 203 */     this.psgDocIssueDpt = psgDocIssueDpt;
/*     */   }
/*     */   public String getPsgAddr() {
/* 206 */     return this.psgAddr;
/*     */   }
/*     */   public void setPsgAddr(String psgAddr) {
/* 209 */     this.psgAddr = psgAddr;
/*     */   }
/*     */   public String getPsgBirthday() {
/* 212 */     return this.psgBirthday;
/*     */   }
/*     */   public void setPsgBirthday(String psgBirthday) {
/* 215 */     this.psgBirthday = psgBirthday;
/*     */   }
/*     */   public String getPsgNat() {
/* 218 */     return this.psgNat;
/*     */   }
/*     */   public void setPsgNat(String psgNat) {
/* 221 */     this.psgNat = psgNat;
/*     */   }
/*     */   public String getPsgNameZh() {
/* 224 */     return this.psgNameZh;
/*     */   }
/*     */   public void setPsgNameZh(String psgNameZh) {
/* 227 */     this.psgNameZh = psgNameZh;
/*     */   }
/*     */   public String getPsgNameEn() {
/* 230 */     return this.psgNameEn;
/*     */   }
/*     */   public void setPsgNameEn(String psgNameEn) {
/* 233 */     this.psgNameEn = psgNameEn;
/*     */   }
/*     */   public String getPsgGender() {
/* 236 */     return this.psgGender;
/*     */   }
/*     */   public void setPsgGender(String psgGender) {
/* 239 */     this.psgGender = psgGender;
/*     */   }
/*     */   public String getPsgPhoto() {
/* 242 */     return this.psgPhoto;
/*     */   }
/*     */   public void setPsgPhoto(String psgPhoto) {
/* 245 */     this.psgPhoto = psgPhoto;
/*     */   }
/*     */   public String getPsgDocNbr() {
/* 248 */     return this.psgDocNbr;
/*     */   }
/*     */   public void setPsgDocNbr(String psgDocNbr) {
/* 251 */     this.psgDocNbr = psgDocNbr;
/*     */   }
/*     */   public String getPsgDocType() {
/* 254 */     return this.psgDocType;
/*     */   }
/*     */   public void setPsgDocType(String psgDocType) {
/* 257 */     this.psgDocType = psgDocType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.PaxSeutOutPutBean
 * JD-Core Version:    0.6.0
 */