/*    */ package com.travelsky.hub.model.peentity.seatchart.input;
/*    */ 
///*    */ import com.alibaba.fastjson.annotation.JSONField;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FrequentFlyerProgram
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2112268888594185338L;
/*    */   private String ffpAirline;
/*    */   private String ffpLevel;
/*    */   private Integer mileage;
/*    */ 
///*    */   @JSONField(name="ffpAirlineID")
/*    */   public String getFfpAirline()
/*    */   {
/* 40 */     return this.ffpAirline;
/*    */   }
/*    */ 
/*    */   public void setFfpAirline(String ffpAirline)
/*    */   {
/* 47 */     this.ffpAirline = ffpAirline;
/*    */   }
/*    */ 
/*    */   public String getFfpLevel()
/*    */   {
/* 54 */     return this.ffpLevel;
/*    */   }
/*    */ 
/*    */   public void setFfpLevel(String ffpLevel)
/*    */   {
/* 61 */     this.ffpLevel = ffpLevel;
/*    */   }
/*    */ 
/*    */   public Integer getMileage()
/*    */   {
/* 68 */     return this.mileage;
/*    */   }
/*    */ 
/*    */   public void setMileage(Integer mileage)
/*    */   {
/* 75 */     this.mileage = mileage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.FrequentFlyerProgram
 * JD-Core Version:    0.6.0
 */