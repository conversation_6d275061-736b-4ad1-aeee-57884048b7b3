/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SyprAsvcBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String emdNumber;
/*     */   private String couponNum;
/*     */   private String ssrCode;
/*     */   private String asvcStatus;
/*     */   private String bagWeight;
/*     */   private String seatNo;
/*     */   private String expense;
/*     */   private String text;
/*     */ 
/*     */   public SyprAsvcBean()
/*     */   {
/*  50 */     this.asvcStatus = "";
/*  51 */     this.bagWeight = "";
/*  52 */     this.couponNum = "";
/*  53 */     this.emdNumber = "";
/*  54 */     this.expense = "";
/*  55 */     this.seatNo = "";
/*  56 */     this.ssrCode = "";
/*  57 */     this.text = "";
/*     */   }
/*     */ 
/*     */   public void setCouponNum(String couponNum)
/*     */   {
/*  64 */     this.couponNum = couponNum;
/*     */   }
/*     */ 
/*     */   public String getSsrCode()
/*     */   {
/*  71 */     return this.ssrCode;
/*     */   }
/*     */ 
/*     */   public String getCouponNum()
/*     */   {
/*  78 */     return this.couponNum;
/*     */   }
/*     */ 
/*     */   public String getEmdNumber()
/*     */   {
/*  85 */     return this.emdNumber;
/*     */   }
/*     */ 
/*     */   public void setEmdNumber(String emdNumber)
/*     */   {
/*  92 */     this.emdNumber = emdNumber;
/*     */   }
/*     */ 
/*     */   public void setAsvcStatus(String asvcStatus)
/*     */   {
/* 100 */     this.asvcStatus = asvcStatus;
/*     */   }
/*     */ 
/*     */   public void setSsrCode(String ssrCode)
/*     */   {
/* 107 */     this.ssrCode = ssrCode;
/*     */   }
/*     */ 
/*     */   public String getAsvcStatus()
/*     */   {
/* 114 */     return this.asvcStatus;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 121 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 128 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 135 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 142 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getExpense()
/*     */   {
/* 149 */     return this.expense;
/*     */   }
/*     */ 
/*     */   public void setExpense(String expense)
/*     */   {
/* 156 */     this.expense = expense;
/*     */   }
/*     */ 
/*     */   public String getText()
/*     */   {
/* 163 */     return this.text;
/*     */   }
/*     */ 
/*     */   public void setText(String text)
/*     */   {
/* 170 */     this.text = text;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SyprAsvcBean
 * JD-Core Version:    0.6.0
 */