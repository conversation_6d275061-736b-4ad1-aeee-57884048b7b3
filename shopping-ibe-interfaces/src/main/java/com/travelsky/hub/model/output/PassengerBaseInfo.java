/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerBaseInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1867494932229279572L;
/*    */   private String passengerResult;
/*    */   private String pnr;
/*    */   private String airlinePAXReference;
/*    */   private String countryOfResidence;
/*    */   private String passengerGUID;
/*    */ 
/*    */   public String getPassengerResult()
/*    */   {
/* 39 */     return this.passengerResult;
/*    */   }
/*    */ 
/*    */   public String getPnr()
/*    */   {
/* 45 */     return this.pnr;
/*    */   }
/*    */ 
/*    */   public String getAirlinePAXReference()
/*    */   {
/* 51 */     return this.airlinePAXReference;
/*    */   }
/*    */ 
/*    */   public String getCountryOfResidence()
/*    */   {
/* 57 */     return this.countryOfResidence;
/*    */   }
/*    */ 
/*    */   public String getPassengerGUID()
/*    */   {
/* 63 */     return this.passengerGUID;
/*    */   }
/*    */ 
/*    */   public void setPassengerResult(String passengerResult)
/*    */   {
/* 69 */     this.passengerResult = passengerResult;
/*    */   }
/*    */ 
/*    */   public void setPnr(String pnr)
/*    */   {
/* 75 */     this.pnr = pnr;
/*    */   }
/*    */ 
/*    */   public void setAirlinePAXReference(String airlinePAXReference)
/*    */   {
/* 81 */     this.airlinePAXReference = airlinePAXReference;
/*    */   }
/*    */ 
/*    */   public void setCountryOfResidence(String countryOfResidence)
/*    */   {
/* 87 */     this.countryOfResidence = countryOfResidence;
/*    */   }
/*    */ 
/*    */   public void setPassengerGUID(String passengerGUID)
/*    */   {
/* 93 */     this.passengerGUID = passengerGUID;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerBaseInfo
 * JD-Core Version:    0.6.0
 */