/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class DelBagInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1595828525982515397L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String flightDate;
/*     */   private String cabinType;
/*     */   private String ticketNumber;
/*     */   private BagInfo bagInfo;
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  56 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  63 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  70 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  77 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  83 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  90 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  97 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 104 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 111 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 118 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 124 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 131 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public BagInfo getBagInfo()
/*     */   {
/* 138 */     return this.bagInfo;
/*     */   }
/*     */ 
/*     */   public void setBagInfo(BagInfo bagInfo)
/*     */   {
/* 145 */     this.bagInfo = bagInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.DelBagInputBean
 * JD-Core Version:    0.6.0
 */