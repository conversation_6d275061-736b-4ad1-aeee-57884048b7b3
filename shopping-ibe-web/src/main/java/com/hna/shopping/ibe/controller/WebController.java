/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Api(description = "MVC 网页跳转示例")
@Controller
@RequestMapping("/web")
@Slf4j
public class WebController {

    @RequestMapping("welcome")
    String welcome(@RequestParam(value = "name") String name, Model model) {
        model.addAttribute("name", name);
        return "welcome";
    }
}
