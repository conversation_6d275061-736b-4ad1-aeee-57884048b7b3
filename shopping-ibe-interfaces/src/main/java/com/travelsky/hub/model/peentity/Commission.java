/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Commission
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3298246810911150038L;
/*    */   private String Amount;
/*    */   private String Currency;
/*    */   private String Percent;
/*    */ 
/*    */   public String getAmount()
/*    */   {
/* 35 */     return this.Amount;
/*    */   }
/*    */ 
/*    */   public void setAmount(String amount)
/*    */   {
/* 42 */     this.Amount = amount;
/*    */   }
/*    */ 
/*    */   public String getCurrency()
/*    */   {
/* 49 */     return this.Currency;
/*    */   }
/*    */ 
/*    */   public void setCurrency(String currency)
/*    */   {
/* 56 */     this.Currency = currency;
/*    */   }
/*    */ 
/*    */   public String getPercent()
/*    */   {
/* 63 */     return this.Percent;
/*    */   }
/*    */ 
/*    */   public void setPercent(String percent)
/*    */   {
/* 70 */     this.Percent = percent;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.Commission
 * JD-Core Version:    0.6.0
 */