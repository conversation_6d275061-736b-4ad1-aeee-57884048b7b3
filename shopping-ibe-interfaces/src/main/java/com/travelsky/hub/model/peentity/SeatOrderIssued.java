/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatOrderIssued
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7916828414694964101L;
/*     */   private String isSuccess;
/*     */   private String orderNum;
/*     */   private String errorMsg;
/*     */   private String errorCode;
/*     */   private String orderStatus;
/*     */   private String emdNo;
/*     */   private String emdCoupon;
/*     */ 
/*     */   public String getEmdNo()
/*     */   {
/*  34 */     return this.emdNo;
/*     */   }
/*     */ 
/*     */   public void setEmdNo(String emdNo)
/*     */   {
/*  41 */     this.emdNo = emdNo;
/*     */   }
/*     */ 
/*     */   public String getIsSuccess()
/*     */   {
/*  48 */     return this.isSuccess;
/*     */   }
/*     */ 
/*     */   public void setIsSuccess(String isSuccess)
/*     */   {
/*  55 */     this.isSuccess = isSuccess;
/*     */   }
/*     */ 
/*     */   public String getEmdCoupon()
/*     */   {
/*  62 */     return this.emdCoupon;
/*     */   }
/*     */ 
/*     */   public void setEmdCoupon(String emdCoupon)
/*     */   {
/*  69 */     this.emdCoupon = emdCoupon;
/*     */   }
/*     */ 
/*     */   public String getErrorMsg()
/*     */   {
/*  76 */     return this.errorMsg;
/*     */   }
/*     */ 
/*     */   public void setErrorMsg(String errorMsg)
/*     */   {
/*  83 */     this.errorMsg = errorMsg;
/*     */   }
/*     */ 
/*     */   public String getOrderStatus()
/*     */   {
/*  90 */     return this.orderStatus;
/*     */   }
/*     */ 
/*     */   public void setOrderStatus(String orderStatus)
/*     */   {
/*  97 */     this.orderStatus = orderStatus;
/*     */   }
/*     */ 
/*     */   public String getErrorCode()
/*     */   {
/* 104 */     return this.errorCode;
/*     */   }
/*     */ 
/*     */   public void setErrorCode(String errorCode)
/*     */   {
/* 111 */     this.errorCode = errorCode;
/*     */   }
/*     */ 
/*     */   public String getOrderNum()
/*     */   {
/* 119 */     return this.orderNum;
/*     */   }
/*     */ 
/*     */   public void setOrderNum(String orderNum)
/*     */   {
/* 126 */     this.orderNum = orderNum;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatOrderIssued
 * JD-Core Version:    0.6.0
 */