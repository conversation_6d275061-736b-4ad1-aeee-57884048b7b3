/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import com.travelsky.hub.model.output.ApiInfo;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ApiShowOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6980377643456331805L;
/*     */   private String flightNumber;
/*     */   private String airlineCode;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String hostNumber;
/*     */   private String arrivalAirport;
/*     */   private ApiInfo apiInfo;
/*     */   private String deniedBoardingVolunteerInd;
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  53 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  60 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  67 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  74 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  82 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  89 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  96 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 103 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public ApiInfo getApiInfo()
/*     */   {
/* 111 */     return this.apiInfo;
/*     */   }
/*     */ 
/*     */   public void setApiInfo(ApiInfo apiInfo)
/*     */   {
/* 118 */     this.apiInfo = apiInfo;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 125 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 132 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDeniedBoardingVolunteerInd()
/*     */   {
/* 139 */     return this.deniedBoardingVolunteerInd;
/*     */   }
/*     */ 
/*     */   public void setDeniedBoardingVolunteerInd(String deniedBoardingVolunteerInd)
/*     */   {
/* 146 */     this.deniedBoardingVolunteerInd = deniedBoardingVolunteerInd;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 153 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 160 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.ApiShowOutputBean
 * JD-Core Version:    0.6.0
 */