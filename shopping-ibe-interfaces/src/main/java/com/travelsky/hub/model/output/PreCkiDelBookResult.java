/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PreCkiDelBookResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String resultCode;
/*    */   private String resultMsg;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 19 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 26 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMsg()
/*    */   {
/* 35 */     return this.resultMsg;
/*    */   }
/*    */ 
/*    */   public void setResultMsg(String resultMsg)
/*    */   {
/* 42 */     this.resultMsg = resultMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PreCkiDelBookResult
 * JD-Core Version:    0.6.0
 */