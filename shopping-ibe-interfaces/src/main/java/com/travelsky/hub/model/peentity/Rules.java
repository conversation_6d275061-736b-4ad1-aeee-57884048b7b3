/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Rules
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6126810502946847394L;
/*     */   private String priceLevel;
/*     */   private String psrLevel;
/*     */   private String price;
/*     */   private String seats;
/*     */   private String currency;
/*     */   private String seatLevel;
/*     */   private String pricenine;
/*     */ 
/*     */   public String getPriceLevel()
/*     */   {
/*  34 */     return this.priceLevel;
/*     */   }
/*     */ 
/*     */   public void setPriceLevel(String priceLevel)
/*     */   {
/*  41 */     this.priceLevel = priceLevel;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/*  49 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/*  56 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ 
/*     */   public String getPrice()
/*     */   {
/*  63 */     return this.price;
/*     */   }
/*     */ 
/*     */   public void setPrice(String price)
/*     */   {
/*  70 */     this.price = price;
/*     */   }
/*     */ 
/*     */   public String getSeats()
/*     */   {
/*  77 */     return this.seats;
/*     */   }
/*     */ 
/*     */   public void setSeats(String seats)
/*     */   {
/*  84 */     this.seats = seats;
/*     */   }
/*     */ 
/*     */   public String getCurrency()
/*     */   {
/*  91 */     return this.currency;
/*     */   }
/*     */ 
/*     */   public void setCurrency(String currency)
/*     */   {
/*  98 */     this.currency = currency;
/*     */   }
/*     */ 
/*     */   public String getSeatLevel()
/*     */   {
/* 105 */     return this.seatLevel;
/*     */   }
/*     */ 
/*     */   public void setSeatLevel(String seatLevel)
/*     */   {
/* 112 */     this.seatLevel = seatLevel;
/*     */   }
/*     */ 
/*     */   public String getPricenine()
/*     */   {
/* 119 */     return this.pricenine;
/*     */   }
/*     */ 
/*     */   public void setPricenine(String pricenine)
/*     */   {
/* 126 */     this.pricenine = pricenine;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.Rules
 * JD-Core Version:    0.6.0
 */