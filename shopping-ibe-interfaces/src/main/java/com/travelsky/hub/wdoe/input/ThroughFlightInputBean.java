/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ThroughFlightInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8160807897614594642L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String deptCity;
/*     */   private String destCity;
/*     */   private ThroughFlightInputBean throughFlight;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  44 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  52 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  60 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  68 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  76 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/*  84 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/*  92 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 100 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public ThroughFlightInputBean getThroughFlight()
/*     */   {
/* 108 */     return this.throughFlight;
/*     */   }
/*     */ 
/*     */   public void setThroughFlight(ThroughFlightInputBean throughFlight)
/*     */   {
/* 116 */     this.throughFlight = throughFlight;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.wdoe.input.ThroughFlightInputBean
 * JD-Core Version:    0.6.0
 */