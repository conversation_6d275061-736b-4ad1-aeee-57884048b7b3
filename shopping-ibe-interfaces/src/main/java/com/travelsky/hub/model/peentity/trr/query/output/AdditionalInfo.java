/*    */ package com.travelsky.hub.model.peentity.trr.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AdditionalInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6363148651498473914L;
/*    */   private String seat;
/*    */   private String meal;
/*    */ 
/*    */   public String getSeat()
/*    */   {
/* 36 */     return this.seat;
/*    */   }
/*    */ 
/*    */   public void setSeat(String seat)
/*    */   {
/* 43 */     this.seat = seat;
/*    */   }
/*    */ 
/*    */   public String getMeal()
/*    */   {
/* 50 */     return this.meal;
/*    */   }
/*    */ 
/*    */   public void setMeal(String meal)
/*    */   {
/* 57 */     this.meal = meal;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.AdditionalInfo
 * JD-Core Version:    0.6.0
 */