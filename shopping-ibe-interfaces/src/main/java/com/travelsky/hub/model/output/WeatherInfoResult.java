/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class WeatherInfoResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String city;
/*    */   private String chnCity;
/* 30 */   private List<DayWhetherBean> whether = new ArrayList();
/*    */ 
/*    */   public List<DayWhetherBean> getWhether()
/*    */   {
/* 37 */     return this.whether;
/*    */   }
/*    */ 
/*    */   public void setWhether(List<DayWhetherBean> whether)
/*    */   {
/* 44 */     this.whether = whether;
/*    */   }
/*    */ 
/*    */   public String getChnCity()
/*    */   {
/* 51 */     return this.chnCity;
/*    */   }
/*    */ 
/*    */   public void setChnCity(String chnCity)
/*    */   {
/* 58 */     this.chnCity = chnCity;
/*    */   }
/*    */ 
/*    */   public String getCity()
/*    */   {
/* 65 */     return this.city;
/*    */   }
/*    */ 
/*    */   public void setCity(String city)
/*    */   {
/* 72 */     this.city = city;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.WeatherInfoResult
 * JD-Core Version:    0.6.0
 */