package com.hna.shopping.ibe.manager.impl;

import com.google.common.collect.Lists;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.dao.entity.PnrAndTicketRecord;
import com.hna.shopping.ibe.dao.entity.PnrClearRecord;
import com.hna.shopping.ibe.dao.mapper.PnrAndTicketRecordMapper;
import com.hna.shopping.ibe.dao.mapper.PnrClearRecordMapper;
import com.hna.shopping.ibe.interfaces.dto.CabinDTO;
import com.hna.shopping.ibe.interfaces.dto.IssueTicketPassenger;
import com.hna.shopping.ibe.interfaces.dto.IssueTicketSegment;
import com.hna.shopping.ibe.interfaces.dto.SegmentDTO;
import com.hna.shopping.ibe.manager.IbeManager;
import com.hna.shopping.ibe.manager.ibe.IbeUtil;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ibe.client.IBEClient;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.exceptions.IBEException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.text.DecimalFormat;
import java.util.*;

/**
 * DOC
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class IbeManagerImpl implements IbeManager {
    // 仓位状态
    private static final String ICS_OPEN = "OP";
    private static final String ICS_CLOSE = "CD";
    private static final String ICS_APPLY = "AP";

    @Autowired
    private PnrAndTicketRecordMapper pnrAndTicketRecordMapper;

    @Autowired
    private PnrClearRecordMapper pnrClearRecordMapper;


    @Async
    public void avAsync(String airline, String orgCity, String dstCity, Date flightDate, boolean isDirect, String sellerChannels, String allAirline) {
        log.info("avAsync airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(flightDate) + " sellerChannels=" + sellerChannels);
        av(airline, orgCity, dstCity, flightDate, isDirect, sellerChannels, allAirline);
    }

    @Override
    public List<SegmentDTO> av(String supplier, String airline, String orgCity, String dstCity, Date flightDate, boolean isDirect, String sellerChannels, String allAirline) {
        if ("8L".equals(airline)) isDirect = true;
        String cacheKey = this.getCacheKey(airline, orgCity, dstCity, flightDate);
        // 走一次 AV
        try {
            List<SegmentDTO> avResult = IbeUtil.av(supplier,airline, orgCity, dstCity, flightDate, isDirect, sellerChannels, allAirline);

            List<SegmentDTO> result = Lists.newArrayList();
            // 按 ET 习惯转换 Cabin
            for (SegmentDTO segmentDTO : avResult) {

                for (CabinDTO cabinDTO : segmentDTO.getCabins()) {
                    this.convertCabin(cabinDTO);
                }

                result.add(segmentDTO);
            }

            // 存缓存
            //CacheManagerUtil.put(CacheName.AV, cacheKey, result);
//            try {
//                asyncTask.put2Cache(cacheKey, result);
//            } catch (Exception ec) {
//                log.error("put cacheKey = {}, e={}", cacheKey, ec.getMessage());
//            }
            return result;
        }catch(Exception e){
            log.error("==============message:{}",e.getMessage());
            log.debug("av error",e);
//            if (e.getMessage() != null && e.getMessage().contains("20000002")) {
//                asyncTask.clear2Cache(cacheKey);
//            }

        }
        return null;
    }

    public List<SegmentDTO> av(String airline, String orgCity, String dstCity, Date flightDate, boolean isDirect, String sellerChannels, String allAirline) {
        return av(null, airline, orgCity, dstCity, flightDate, isDirect, sellerChannels, allAirline);
    }


    /**
     * 获取缓存 key
     */
    public String getCacheKey(String airline, String orgCity, String dstCity, Date flightDate) {
        return String.format("%s%s%s%s", airline, orgCity, dstCity, DateUtil.toStringYMD(flightDate));
    }


    /**
     * 按 ET 的习惯转换 cabin
     */
    private void convertCabin(CabinDTO cabinDTO) {
        if (StringUtils.isNumeric(cabinDTO.getQuantity())) {
            cabinDTO.setInventory(Integer.parseInt(cabinDTO.getQuantity()));
            cabinDTO.setStatus(ICS_OPEN);
        } else {
            if ("A".equalsIgnoreCase(cabinDTO.getQuantity())) {
                cabinDTO.setInventory(10);
                cabinDTO.setStatus(ICS_OPEN);
            } else if ("Q".equalsIgnoreCase(cabinDTO.getQuantity()) || "S".equalsIgnoreCase(cabinDTO.getQuantity())) {
                cabinDTO.setInventory(0);
                cabinDTO.setStatus(ICS_APPLY);
            } else {
                cabinDTO.setInventory(0);
                cabinDTO.setStatus(ICS_CLOSE);
            }
        }
    }

    @Override
    public List<IssueTicketPassenger> sellSeatAndIssueTicket(String airline, List<IssueTicketSegment> issueTicketSegmentList,
                                                             List<IssueTicketPassenger> passengers, String contactInfo, int timeLimitMinutes)
    throws  IBEException{





        List<IssueTicketPassenger> issueTicketResultList = new ArrayList<>();

//        if (1 == 1) {
//            throw  new IBEException("");
//            return issueTicketResultList;
//        }

        if (passengers != null && passengers.size() == 0) {
            return issueTicketResultList;
        }

        List<IssueTicketPassenger> adultList = new ArrayList<>();
        List<IssueTicketPassenger> childList = new ArrayList<>();
        List<IssueTicketPassenger> infantList = new ArrayList<>();

        for (IssueTicketPassenger passengerIssueTicket : passengers) {
            String passengerType = passengerIssueTicket.getPassengerType();
            if ("AD".equals(passengerType)) {
                adultList.add(passengerIssueTicket);
            }else
            if ("CH".equals(passengerType)) {
                childList.add(passengerIssueTicket);
            }else
            if ("IN".equals(passengerType)) {
                infantList.add(passengerIssueTicket);
            }
        }

        //1.1 生成成人编码
        SellSeat sellSeat = null;
        SSResult ssr = null;
        try {
            sellSeat = IbeCmd.getIbeClient(airline+"G", SellSeat.class);

             /*
            sellSeat.addAirSeg(flightNo, fltClass, orgCity, desCity, "NN", adultList.size(), flightDate);
            sellSeat.addContact(contactInfo);
            sellSeat.addOSI("JD", "CTCT"+ contactInfo);
            //添加旅客出票时限
            Calendar now = Calendar.getInstance();
            now.add(Calendar.MINUTE,timeLimitMinutes);
            sellSeat.setTimelimit(now.getTime());
            sellSeat.addFC(toFcString(orgCity,desCity,adultList.get(0).getNetFare().doubleValue(),airline,fltClass+"",productCode,true,false));
            sellSeat.addFN(toFnString(adultList.get(0).getNetFare().doubleValue(),adultList.get(0).getAirportTax().doubleValue(),adultList.get(0).getFuelTax().doubleValue()));
            sellSeat.addEI(new BookEI(eiInfo));
            */
            addPnrMsg(airline, sellSeat, issueTicketSegmentList, adultList, contactInfo, timeLimitMinutes, "");
            //完成PNR必须信息输入递交主机，生成PNR
            ssr = sellSeat.commit1();
        } catch (Exception e) {
            log.error("SellSeat error:" + e.getMessage());
            if (sellSeat != null) {
                getTxnid(sellSeat);
            }
            throw new IBEException(e);
        }
        //PNR结果
        String adultPnrNo = ssr.getPnrno();
        log.info("sellSeatAndIssueTicket adultPnrNo:"+adultPnrNo);

        //2.1 成人编码入库
        if (adultPnrNo != null) {
            for (IssueTicketPassenger passengerIssueTicket : adultList) {
                passengerIssueTicket.setPnrNo(adultPnrNo);
                passengerIssueTicket.setPnrCreateTime(new Date());
                savePnrRecord(adultPnrNo, passengerIssueTicket, issueTicketSegmentList.get(0));
            }
        }

        //预定儿童，儿童编码里要备注成人 //先把编码都出了，再处理异常
        //1.2 生成儿童编码
        SellSeat sellSeatForChild = null;
        SSResult ssrChild = null;
        if ( adultPnrNo != null && childList.size() > 0) {
            try {
                sellSeatForChild = IbeCmd.getIbeClient(airline + "G", SellSeat.class);
                addPnrMsg(airline, sellSeatForChild, issueTicketSegmentList, childList, contactInfo, timeLimitMinutes, adultPnrNo);
                ssrChild = sellSeatForChild.commit1();
            } catch (Exception e) {
                log.error("SellSeat error:" + e.getMessage());
                if (sellSeat != null) {
                    getTxnid(sellSeatForChild);
                }
                IssueTicketSegment cacelSegment = issueTicketSegmentList.get(0);
                //儿童占座失败，把成人也清了
                cancelSegment(airline, adultPnrNo, cacelSegment.getDepDate(), cacelSegment.getFlightNo(),
                        cacelSegment.getDepCode(), cacelSegment.getArrCode(), adultList.get(0).getPassengerName());
                throw new IBEException(e);
            }
        }
        String childPnrNo = null;
        log.info("sellSeatAndIssueTicket childPnrNo:" + childPnrNo);

        if ( ssrChild != null && childList.size() > 0) {
            childPnrNo = ssrChild.getPnrno();
        }

        //2.2 儿童编码入库
        if (childPnrNo != null) {
            for (IssueTicketPassenger passengerIssueTicket : childList) {
                passengerIssueTicket.setPnrNo(childPnrNo);
                passengerIssueTicket.setPnrCreateTime(new Date());
                savePnrRecord(childPnrNo, passengerIssueTicket, issueTicketSegmentList.get(0));
            }
        }


        //3 成人客票处理
        if (adultPnrNo != null) {
            //3.1 成人出票
            String result = null;
            ETDZ etdz = null;
            try {
                etdz = IbeCmd.getIbeClient(airline+"G", ETDZ.class);
                result = etdz.issueTicket(adultPnrNo, 1);
                for (int i = 0; i < 3; i++) {
                    if ("OK".equals(result)) {
                        break;
                    } else {
                        result = etdz.issueTicket(adultPnrNo, 1);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                if (etdz != null) {
                    getTxnid(etdz);
                }
                //*成人出票失败，全部清位*
                IssueTicketSegment cacelSegment = issueTicketSegmentList.get(0);
                cancelSegment(airline,adultPnrNo, cacelSegment.getDepDate(), cacelSegment.getFlightNo(),
                        cacelSegment.getDepCode(), cacelSegment.getArrCode(), adultList.get(0).getPassengerName());
                cancelSegment(airline,childPnrNo, cacelSegment.getDepDate(), cacelSegment.getFlightNo(),
                        cacelSegment.getDepCode(), cacelSegment.getArrCode(), childList.get(0).getPassengerName());
                throw new IBEException(e);
            }
            log.info(adultPnrNo + " issue ticket result " + result);

            //3.2 提取客票
            RT rt = null;
            if ("OK".equals(result)) {
                rt = IbeCmd.getIbeClient(airline+"G", RT.class);
                RTResult rtResult = null;
                boolean flag = false;
                int i = 3;
                while (!flag && i > 0) {
                    try {
                        rtResult = rt.retrieve(adultPnrNo);
                        log.info("ReservePNRCommand.checkInftStatus rt pnr|adultPnrNo:" + adultPnrNo + "|" + getTxnid(rt));
                        if (rtResult != null) {
                            flag = true;
                        }
                    } catch (Exception e) {
                        log.error("ReservePNRCommand.checkInftStatus rt pnr|adultPnrNo:" + adultPnrNo + "|" + getTxnid(rt) + "|times:" + i + "|error:" + e.getMessage(), e);
                    }
                    i = i - 1;
                }

                if (rtResult != null) {
                    Vector<PNRTktNo> ticketList = rtResult.getTktnos();
                    //pnr中旅客和位置关系
                    Map<String,String> adultPnrPosAndPaxName = new HashMap<>();
                    for (PNRPassenger passenger : (Vector<PNRPassenger>) rtResult.getPassengers()) {
                        adultPnrPosAndPaxName.put("P"+passenger.getIndex(),passenger.getName());
                    }
                    //3.3 客票入库
                    for (PNRTktNo tktNo : ticketList) {
                        String position = tktNo.getPsgrID();
                        String ticketNo = tktNo.getTktNo();
                        for (IssueTicketPassenger issueTicketPassenger : adultList) {
                            if (issueTicketPassenger.getPassengerName().equals(
                                    adultPnrPosAndPaxName.get(position))) {
                                issueTicketPassenger.setTicketNo(ticketNo);
                                issueTicketPassenger.setIssueTicketTime(new Date());
                                IssueTicketSegment segment = issueTicketSegmentList.get(0);
                                findThenAddTicketNo(adultPnrNo,segment.getDepCode(),segment.getFlightNo(),
                                        segment.getDepDate(),issueTicketPassenger.getPassengerName(),issueTicketPassenger.getPassengerID(),ticketNo);
                                break;
                            }
                        }
                    }

                    // 3.4 pnr确认座位 HK变RR
                    PnrManage pnrMgr = IbeCmd.getIbeClient(airline+"G", PnrManage.class);
                    String reconfirmResult = pnrMgr.reconfirmAirSeg(adultPnrNo, null, pnrMgr.RECONFIRM_ALL_POSSIBLE_ACTION);
                    log.info("reconfirmAirSeg pnrno:" + adultPnrNo + reconfirmResult);
                }
            } else {
                //3.5 出票失败
                if (rt != null) {
                    log.error("getTxnid:" + adultPnrNo);
                    getTxnid(rt);
                }
                return issueTicketResultList;
            }

            //4 儿童出票
            if ( adultPnrNo != null && childList.size() > 0) {

                if (childPnrNo != null) {
                    //4.1 儿童出票
                    ETDZ etdzChild = null;
                    String resultChild = null;
                    try {
                        etdzChild = IbeCmd.getIbeClient(airline + "G", ETDZ.class);
                        resultChild = etdzChild.issueTicket(childPnrNo, 1);
                        for (int i = 0; i < 3; i++) {
                            if ("OK".equals(result)) {
                                break;
                            } else {
                                resultChild = etdz.issueTicket(adultPnrNo, 1);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        if (etdz != null) {
                            log.error("getTxnid:" + adultPnrNo);
                            getTxnid(etdzChild);
                        }
                        //儿童出票失败，线下保障
//                        cancelSegment(childPnrNo, airline);
//                        throw new IBEException(e);
                    }

                    log.info(childPnrNo + " issue ticket result " + result);

                    RT rtChild = null;
                    if ("OK".equals(resultChild)) {
                        //4.2 儿童提取客票信息
                        rtChild = IbeCmd.getIbeClient(airline + "G", RT.class);
                        RTResult rtResult = null;
                        boolean flag = false;
                        int i = 3;
                        while (!flag && i > 0) {
                            try {
                                rtResult = rtChild.retrieve(childPnrNo);
                                log.info("ReservePNRCommand.checkInftStatus rt pnr|childPnrNo:" + childPnrNo + "|" + getTxnid(rt));
                                if (rtResult != null) {
                                    flag = true;
                                }
                            } catch (Exception e) {
                                log.error("ReservePNRCommand.checkInftStatus rt pnr|childPnrNo:" + childPnrNo + "|" + getTxnid(rt) + "|times:" + i + "|error:" + e.getMessage(), e);
                            }
                            i = i - 1;
                        }

                        if (rtResult != null) {
                            Vector<PNRTktNo> ticketList = rtResult.getTktnos();

                            Map<String, String> childPnrPosAndPaxName = new HashMap<>();
                            for (PNRPassenger passenger : (Vector<PNRPassenger>) rtResult.getPassengers()) {
                                childPnrPosAndPaxName.put("P" + passenger.getIndex(), passenger.getName());
                            }

                            //4.3 儿童票号入库
                            for (PNRTktNo tktNo : ticketList) {
                                String position = tktNo.getPsgrID();
                                String ticketNo = tktNo.getTktNo();
                                for (IssueTicketPassenger issueTicketPassenger : childList) {
                                    if (issueTicketPassenger.getPassengerName().equals(
                                            childPnrPosAndPaxName.get(position))) {
                                        issueTicketPassenger.setTicketNo(ticketNo);
                                        issueTicketPassenger.setIssueTicketTime(new Date());
                                        IssueTicketSegment segment = issueTicketSegmentList.get(0);
                                        findThenAddTicketNo(childPnrNo, segment.getDepCode(), segment.getFlightNo(),
                                                segment.getDepDate(), issueTicketPassenger.getPassengerName(), issueTicketPassenger.getPassengerID(), ticketNo);
                                        break;
                                    }
                                }
                            }

                            //4.4 儿童pnr确认座位 HK变RR
                            PnrManage pnrMgr = IbeCmd.getIbeClient(airline + "G", PnrManage.class);
                            String reconfirmResult = pnrMgr.reconfirmAirSeg(childPnrNo, null, pnrMgr.RECONFIRM_ALL_POSSIBLE_ACTION);
                            log.info(reconfirmResult);
                        } else {
                            log.error("getTxnid:" + childPnrNo);
                            getTxnid(rt);
//                            throw new IBEException("");
                        }
                    }
                } else {
                    // 儿童出票失败，线下处理，不清位，不抛异常
                }
            }
        }
        issueTicketResultList.addAll(adultList);
        issueTicketResultList.addAll(childList);

        return issueTicketResultList;
    }

    @Override
    public String getTicketNo(String airline ,String pnrNo, String depCode, String flightNo,
                              String flightDate, String passengerName, String idNo) {


        String result = "";

        Map<String,Object> map = new HashMap<>();
        map.put("pnr_no",pnrNo);
        map.put("dep_code",depCode);
        map.put("flight_no",flightNo);
        map.put("flight_date",flightDate);
        map.put("passenger_name",passengerName);
        map.put("id_card",idNo);

        List<PnrAndTicketRecord> pnrTicketList = pnrAndTicketRecordMapper.selectByMap(map);

        if (pnrTicketList != null && pnrTicketList.size() > 0) {
            for (PnrAndTicketRecord record : pnrTicketList) {
                if (record != null && StringUtils.isNotEmpty(record.getTicketNo())) {
                    return record.getTicketNo();
                }
            }
        }

        //库里没有，IBE查一下
        RT rt = null;

        rt = IbeCmd.getIbeClient(airline+"G", RT.class);
        RTResult rtResult = null;
        boolean flag = false;
        int i = 3;
        while (!flag && i > 0) {
            try {
                rtResult = rt.retrieve(pnrNo);
                log.info("ReservePNRCommand.checkInftStatus rt pnr|adultPnrNo:" + pnrNo + "|" + getTxnid(rt));
                if (rtResult != null) {
                    flag = true;
                }
            } catch (Exception e) {
                log.error("ReservePNRCommand.checkInftStatus rt pnr|adultPnrNo:" + pnrNo + "|" + getTxnid(rt) + "|times:" + i + "|error:" + e.getMessage(), e);
            }
            i = i - 1;
        }

        if (rtResult != null) {
            Vector<PNRTktNo> ticketList = rtResult.getTktnos();
            //pnr中旅客和位置关系
            Map<String,String> adultPnrPosAndPaxName = new HashMap<>();
            for (PNRPassenger passenger : (Vector<PNRPassenger>) rtResult.getPassengers()) {
                adultPnrPosAndPaxName.put("P"+passenger.getIndex(),passenger.getName());
            }
            //3.3 客票入库
            for (PNRTktNo tktNo : ticketList) {
                String position = tktNo.getPsgrID();
                String ticketNo = tktNo.getTktNo();
                if (passengerName.equals(
                        adultPnrPosAndPaxName.get(position))) {
                    result = ticketNo;
                    findThenAddTicketNo(pnrNo,depCode,flightNo, flightDate,passengerName,idNo,ticketNo);
                    break;
                }

            }

            // 3.4 pnr确认座位 HK变RR
            PnrManage pnrMgr = IbeCmd.getIbeClient(airline+"G", PnrManage.class);
            String reconfirmResult = null;
            try {
                reconfirmResult = pnrMgr.reconfirmAirSeg(pnrNo, null, pnrMgr.RECONFIRM_ALL_POSSIBLE_ACTION);
            } catch (IBEException e) {
                log.error("IbeManagerImpl getTicketNo error:" + e.getMessage());
            }
            log.info("reconfirmAirSeg pnrno:" + pnrNo + reconfirmResult);
        }


        return result;
    }

    private String toFcString(String depCode, String arrCode, double netFare
            , String airlineCode,String cabin,String productCode, boolean isChild, boolean isY) {
        DecimalFormat format = new DecimalFormat("###############0.00");
        StringBuffer s=new StringBuffer();
        s.append("FC/");
        if("infant".equals("")){
            s.append("IN/");
        }
        int i=0;
        double totalPrice=0;

        double printedPrice=netFare;

        if (i == 0) {
            s.append(depCode).append(" ");
        }
        s.append(airlineCode).append(" ")
                .append(arrCode).append(" ")
                .append(format.format(printedPrice));

        //设置票价级别栏
        s.append(cabin+(isChild&isY?"CH50":"")+"/"+productCode+" ");

        totalPrice += printedPrice;

        s.append("CNY").append(format.format(totalPrice)).append("END");
        return s.toString();
    }


    private String toFnString(double totalPrice,double airportTax,double fuelSurcharge) {
        DecimalFormat format = new DecimalFormat("###############0.00");

        StringBuffer sBuffer=new StringBuffer();
        sBuffer.append("FN");
        if("infant".equals("")){
            sBuffer.append("/IN");
        }
        sBuffer.append("/FCNY").append(format.format(totalPrice));

        sBuffer.append("/SCNY").append(format.format(totalPrice)).append("/C").append(format.format(0));

        if(airportTax>0){
            sBuffer.append("/TCNY").append(format.format(airportTax)).append("CN");
        }else{
            sBuffer.append("/TEXEMPTCN");
        }
        if(fuelSurcharge>0){
            sBuffer.append("/TCNY").append(format.format(fuelSurcharge)).append("YQ");
        }else{
            sBuffer.append("/TEXEMPTYQ");
        }
        return sBuffer.toString();
    }

    private void addPnrMsg(String airline, SellSeat sellSeat,  List<IssueTicketSegment> issueTicketSegmentList,
                           List<IssueTicketPassenger> adultList, String contactInfo, int timeLimitMinutes,
                           String adultPnrNo) throws Exception{

        int index = 0;
        for(IssueTicketPassenger passengerIssueTicket : adultList) {
            sellSeat.addAdult(passengerIssueTicket.getPassengerName()+("CH".equals(passengerIssueTicket.getPassengerType())?"CHD":""));
            sellSeat.addSSR_FOID(airline,passengerIssueTicket.getIdType(), passengerIssueTicket.getPassengerID(), passengerIssueTicket.getPassengerName()+("CH".equals(passengerIssueTicket.getPassengerType())?"CHD":""));
            sellSeat.addOSI(airline, "OSI " + airline+ " CTCM"+passengerIssueTicket.getMobileNo()+"/P" + ++index);
//            sellExample.addSSR_FQTV(airline,idtype,  name, id);//常客信息
        }
        for (IssueTicketSegment segment : issueTicketSegmentList) {
            sellSeat.addAirSeg(segment.getFlightNo(), segment.getFltClass(), segment.getDepCode(), segment.getArrCode(), "NN", adultList.size(), segment.getDepDate());
        }
        //添加旅客联系组信息
        sellSeat.addContact(contactInfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
        sellSeat.addOSI(airline, "CTCT"+ contactInfo);

        //添加旅客出票时限
        Calendar now = Calendar.getInstance();
        now.add(Calendar.MINUTE,timeLimitMinutes);
        sellSeat.setTimelimit(now.getTime());
        int segmentIndex = 0;
        for (IssueTicketSegment segment : issueTicketSegmentList) {
            if (org.springframework.util.StringUtils.hasText(adultPnrNo)) {
                //儿童
                sellSeat.addFC(toFcString(segment.getDepCode(),segment.getArrCode(),segment.getChildNetFare().doubleValue(),airline,segment.getFltClass()+"",segment.getProductCode(),true,false));
                sellSeat.addFN(toFnString(segment.getChildNetFare().doubleValue(),segment.getChildAirportTax().doubleValue(),segment.getChildFuelTax().doubleValue()));
                if (segmentIndex == 0) {
                    for (IssueTicketPassenger issueTicketPassenger : adultList) {
                        issueTicketPassenger.setNetFare(segment.getChildNetFare());
                        issueTicketPassenger.setMarketFare(segment.getChildMarketFare());
                        issueTicketPassenger.setAirportTax(segment.getChildAirportTax());
                        issueTicketPassenger.setFuelTax(segment.getChildFuelTax());
                    }
                } else if (segmentIndex == 1) {
                    for (IssueTicketPassenger issueTicketPassenger : adultList) {
                        issueTicketPassenger.setNetFareBack(segment.getChildNetFare());
                        issueTicketPassenger.setMarketFareBack(segment.getChildMarketFare());
                        issueTicketPassenger.setAirportTaxBack(segment.getChildAirportTax());
                        issueTicketPassenger.setFuelTaxBack(segment.getChildFuelTax());
                    }
                }
            } else {
                //成人
                sellSeat.addFC(toFcString(segment.getDepCode(),segment.getArrCode(),segment.getAdultNetFare().doubleValue(),airline,segment.getFltClass()+"",segment.getProductCode(),false,false));
                sellSeat.addFN(toFnString(segment.getAdultNetFare().doubleValue(),segment.getAdultAirportTax().doubleValue(),segment.getAdultFuelTax().doubleValue()));
                if (segmentIndex == 0) {
                    for (IssueTicketPassenger issueTicketPassenger : adultList) {
                        issueTicketPassenger.setNetFare(segment.getAdultNetFare());
                        issueTicketPassenger.setMarketFare(segment.getAdultMarketFare());
                        issueTicketPassenger.setAirportTax(segment.getAdultAirportTax());
                        issueTicketPassenger.setFuelTax(segment.getAdultFuelTax());
                    }
                } else if (segmentIndex == 1) {
                    for (IssueTicketPassenger issueTicketPassenger : adultList) {
                        issueTicketPassenger.setNetFareBack(segment.getAdultNetFare());
                        issueTicketPassenger.setMarketFareBack(segment.getAdultMarketFare());
                        issueTicketPassenger.setAirportTaxBack(segment.getAdultAirportTax());
                        issueTicketPassenger.setFuelTaxBack(segment.getAdultFuelTax());
                    }
                }
            }
            segmentIndex++;
            sellSeat.addEI(new BookEI(segment.getEiInfo()+segment.getProductCode()));
        }
        if (org.springframework.util.StringUtils.hasText(adultPnrNo)) {
            // RMK JD OTHS JD ADULT PNR IS MHJQTL
            sellSeat.addRMK(airline, "ADULT PNR IS " + adultPnrNo);
        }
    }

    private void savePnrRecord(String pnrNo, IssueTicketPassenger passengerIssueTicket,IssueTicketSegment segment) {
        PnrAndTicketRecord pnrRecode = new PnrAndTicketRecord();

        pnrRecode.setPassengerName(passengerIssueTicket.getPassengerName());
        pnrRecode.setIdCard(passengerIssueTicket.getPassengerID());

        pnrRecode.setDepCode(segment.getDepCode());
        pnrRecode.setArrCode(segment.getArrCode());
        pnrRecode.setFlightDate(segment.getDepDate());
        pnrRecode.setCabin(segment.getCabin());
        pnrRecode.setFlightNo(segment.getFlightNo());
        pnrRecode.setPnrNo(pnrNo);
        pnrRecode.setPnrCreateTime(new Date());

        int id = pnrAndTicketRecordMapper.insert(pnrRecode);
    }

    //找票号空的记录，补充票号
    private void findThenAddTicketNo(String pnrNo, String depCode,String flightNo, String flightDate, String passengerName, String idNo,
                                String ticketNo) {
        Map<String,Object> map = new HashMap<>();
        map.put("pnr_no",pnrNo);
        map.put("dep_code",depCode);
        map.put("flight_no",flightNo);
        map.put("flight_date",flightDate);
        map.put("passenger_name",passengerName);
        map.put("id_card",idNo);
//        map.put("ticket_no","");
        List<PnrAndTicketRecord> pnrTicketList = pnrAndTicketRecordMapper.selectByMap(map);
        if (pnrTicketList != null && pnrTicketList.size() == 1) {
            PnrAndTicketRecord record = pnrTicketList.get(0);
            record.setTicketNo(ticketNo);
            record.setIssueTicketTime(new Date());
            pnrAndTicketRecordMapper.updateById(pnrTicketList.get(0));
        }
    }


    private void cancelSegment(String airline, String pnrNo, String fltDate, String fltNo, String depCode,
                               String arrCode, String passengerName) {
        if (StringUtils.isEmpty(pnrNo)) {
            return;
        }
        log.info("cancelSegment pnrNo:"+pnrNo);
        PnrManage pnrMgr = IbeCmd.getIbeClient(airline+"G", PnrManage.class);
        try {
            String result = pnrMgr.cancelAirSeg(pnrNo, null);
            if (!"OK".equals(result)) {
                result = pnrMgr.cancelAirSeg(pnrNo, null);
                if (!"OK".equals(result)) {
                    //存库
                    savePnrClearRecord(pnrNo,"UNHANDLE", fltDate,fltNo,depCode,arrCode,passengerName);
                }
            }
            if ("OK".equals(result)) {
                savePnrClearRecord(pnrNo,"DONE", fltDate,fltNo,depCode,arrCode,passengerName);
                log.info("cancelSegment success:"+pnrNo);
            }
        } catch (IBEException e) {
            log.error("cancelSegment error:" + pnrNo + e.getMessage());
            savePnrClearRecord(pnrNo, "UNHANDLE", fltDate,fltNo,depCode,arrCode,passengerName);
            getTxnid(pnrMgr);
        }
    }

    private void savePnrClearRecord(String pnrNo,String status,String fltDate, String fltNo, String depCode,
                                    String arrCode, String passengerName) {
        try {
            PnrClearRecord pnrClearRecord = new PnrClearRecord();
            pnrClearRecord.setPnrNo(pnrNo);
            if ("DONE".equals(status)) {
                pnrClearRecord.setClearPnrTime(new Date());
            }
            pnrClearRecord.setStatus("DONE".equals(status)?1:0);
            pnrClearRecord.setFlightDate(fltDate);
            pnrClearRecord.setFlightNo(fltNo);
            pnrClearRecord.setDepCode(depCode);
            pnrClearRecord.setArrCode(arrCode);
            pnrClearRecord.setPassengerName(passengerName);
            pnrClearRecord.setCreateTime(new Date());
            pnrClearRecordMapper.insert(pnrClearRecord);
        } catch (Exception e) {
            //防止pnr丢失
            log.error("savePnrClearRecord error:"+ e.getMessage()+ "/" + pnrNo + fltDate + fltNo
                            + depCode + arrCode + passengerName);
        }
    }


    public static String getTxnid(IBEClient client){
        log.error("txnid:" + client.getTxnTraceKey());
        return "txnid:" + client.getTxnTraceKey();
    }
}
