/*     */ package com.travelsky.hub.model.peentity.reserve.create.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ReserveUpgRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 435010756857437809L;
/*     */   private String ocFlightNumber;
/*     */   private String ocAirlineCode;
/*     */   private String mcAirlineCode;
/*     */   private String ocFlightSuffix;
/*     */   private String mcFlightSuffix;
/*     */   private String mcFlightNumber;
/*     */   private String departureAirport;
/*     */   private String flightDate;
/*     */   private String psrName;
/*     */   private String arrivalAirport;
/*     */   private String ticketNumber;
/*     */   private String pnrNumber;
/*     */   private String certType;
/*     */   private String tourIndex;
/*     */   private String contactInfo;
/*     */   private String certNo;
/*     */   private String subcode;
/*     */   private UpgInfo upgInfo;
/*     */   private String priority;
/*     */   private PaymentDetail paymentDetail;
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/* 128 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/* 135 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 142 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 149 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 156 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 163 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineCode(String mcAirlineCode)
/*     */   {
/* 170 */     this.mcAirlineCode = mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getMcAirlineCode()
/*     */   {
/* 177 */     return this.mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 184 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 191 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 198 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightSuffix(String mcFlightSuffix)
/*     */   {
/* 205 */     this.mcFlightSuffix = mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getMcFlightSuffix()
/*     */   {
/* 212 */     return this.mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 219 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 227 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 235 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 242 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 249 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 256 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setPnrNumber(String pnrNumber)
/*     */   {
/* 263 */     this.pnrNumber = pnrNumber;
/*     */   }
/*     */ 
/*     */   public String getPnrNumber()
/*     */   {
/* 270 */     return this.pnrNumber;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 277 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 284 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 291 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 298 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 305 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 312 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 319 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 326 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 333 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/* 340 */     this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/* 347 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setUpgInfo(UpgInfo upgInfo)
/*     */   {
/* 354 */     this.upgInfo = upgInfo;
/*     */   }
/*     */ 
/*     */   public UpgInfo getUpgInfo()
/*     */   {
/* 361 */     return this.upgInfo;
/*     */   }
/*     */ 
/*     */   public void setSubcode(String subcode)
/*     */   {
/* 368 */     this.subcode = subcode;
/*     */   }
/*     */ 
/*     */   public String getSubcode()
/*     */   {
/* 375 */     return this.subcode;
/*     */   }
/*     */ 
/*     */   public void setPaymentDetail(PaymentDetail paymentDetail)
/*     */   {
/* 382 */     this.paymentDetail = paymentDetail;
/*     */   }
/*     */ 
/*     */   public PaymentDetail getPaymentDetail()
/*     */   {
/* 389 */     return this.paymentDetail;
/*     */   }
/*     */ 
/*     */   public void setPriority(String priority)
/*     */   {
/* 396 */     this.priority = priority;
/*     */   }
/*     */ 
/*     */   public String getPriority()
/*     */   {
/* 403 */     return this.priority;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.input.ReserveUpgRQ
 * JD-Core Version:    0.6.0
 */