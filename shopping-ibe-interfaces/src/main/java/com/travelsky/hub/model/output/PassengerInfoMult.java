/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerInfoMult
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String ticketID;
/*     */   private String sequenceNumber;
/*     */   private String surName;
/*     */   private String chnName;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String seatNumber;
/*     */   private String boardingNumber;
/*     */   private String bordingTime;
/*     */   private String boardingGateNumber;
/*     */   private String cabin;
/*     */   private String marketingAirlineCode;
/*     */   private String psrFqt;
/*     */   private String psrFqtLevel;
/*     */   private String psrPnr;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String checkCode;
/*     */   private String standByNumber;
/*     */   private String pstCkiStatus;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private List<StopOverInfo> stopOverInfos;
/*     */   private List<String> boardStreams;
/*     */ 
/*     */   public PassengerInfoMult()
/*     */   {
/*  72 */     this.departureAirport = "";
/*  73 */     this.arrivalAirport = "";
/*  74 */     this.ticketID = "";
/*  75 */     this.sequenceNumber = "";
/*  76 */     this.surName = "";
/*  77 */     this.chnName = "";
/*  78 */     this.airlineCode = "";
/*  79 */     this.flightNumber = "";
/*  80 */     this.flightDate = "";
/*  81 */     this.seatNumber = "";
/*  82 */     this.boardingNumber = "";
/*  83 */     this.bordingTime = "";
/*  84 */     this.boardingGateNumber = "";
/*  85 */     this.cabin = "";
/*  86 */     this.marketingAirlineCode = "";
/*  87 */     this.checkCode = "";
/*  88 */     this.ffpAirlineCode = "";
/*  89 */     this.ffpCardNumber = "";
/*  90 */     this.ffpCardPrior = "";
/*     */   }
/*     */ 
/*     */   public String getCheckCode()
/*     */   {
/*  99 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */   public void setCheckCode(String checkCode)
/*     */   {
/* 107 */     this.checkCode = checkCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 115 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 123 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 131 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 139 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 147 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 155 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 163 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 171 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 179 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 187 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getChnName()
/*     */   {
/* 195 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/* 203 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 211 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 219 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 227 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 235 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getMarketingAirlineCode()
/*     */   {
/* 243 */     return this.marketingAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMarketingAirlineCode(String marketingAirlineCode)
/*     */   {
/* 251 */     this.marketingAirlineCode = marketingAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 259 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 267 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 275 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 283 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 291 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 299 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 307 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String bordingTime)
/*     */   {
/* 315 */     this.bordingTime = bordingTime;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 323 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 331 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 339 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 347 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 355 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 363 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 371 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 379 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrFqt()
/*     */   {
/* 387 */     return this.psrFqt;
/*     */   }
/*     */ 
/*     */   public void setPsrFqt(String psrFqt)
/*     */   {
/* 395 */     this.psrFqt = psrFqt;
/*     */   }
/*     */ 
/*     */   public String getPsrFqtLevel()
/*     */   {
/* 403 */     return this.psrFqtLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrFqtLevel(String psrFqtLevel)
/*     */   {
/* 411 */     this.psrFqtLevel = psrFqtLevel;
/*     */   }
/*     */ 
/*     */   public String getPsrPnr()
/*     */   {
/* 419 */     return this.psrPnr;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 426 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 434 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setPsrPnr(String psrPnr)
/*     */   {
/* 442 */     this.psrPnr = psrPnr;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardStreams()
/*     */   {
/* 450 */     return this.boardStreams;
/*     */   }
/*     */ 
/*     */   public void setBoardStreams(List<String> boardStreams)
/*     */   {
/* 458 */     this.boardStreams = boardStreams;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 465 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 473 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOvers()
/*     */   {
/* 480 */     return this.stopOverInfos;
/*     */   }
/*     */ 
/*     */   public void setStopOvers(List<StopOverInfo> stopOverInfos)
/*     */   {
/* 488 */     this.stopOverInfos = stopOverInfos;
/*     */   }
/*     */ 
/*     */   public String getStandByNumber()
/*     */   {
/* 496 */     return this.standByNumber;
/*     */   }
/*     */ 
/*     */   public void setStandByNumber(String standByNumber)
/*     */   {
/* 504 */     this.standByNumber = standByNumber;
/*     */   }
/*     */ 
/*     */   public String getPstCkiStatus()
/*     */   {
/* 512 */     return this.pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPstCkiStatus(String pstCkiStatus)
/*     */   {
/* 520 */     this.pstCkiStatus = pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 528 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 536 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PassengerInfoMult
 * JD-Core Version:    0.6.0
 */