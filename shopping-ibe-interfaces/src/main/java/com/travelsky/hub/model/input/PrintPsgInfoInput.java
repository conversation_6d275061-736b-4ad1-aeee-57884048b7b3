/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PrintPsgInfoInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5400934824790188267L;
/*     */   private String flightNo;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String cabin;
/*     */   private String depCity;
/*     */   private String arrCity;
/*     */   private String hostNumber;
/*     */   private String etNo;
/*     */   private String psrName;
/*     */   private String bagTag;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  71 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  79 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  87 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  95 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 103 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 111 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 119 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 127 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getDepCity()
/*     */   {
/* 135 */     return this.depCity;
/*     */   }
/*     */ 
/*     */   public void setDepCity(String depCity)
/*     */   {
/* 143 */     this.depCity = depCity;
/*     */   }
/*     */ 
/*     */   public String getArrCity()
/*     */   {
/* 151 */     return this.arrCity;
/*     */   }
/*     */ 
/*     */   public void setArrCity(String arrCity)
/*     */   {
/* 159 */     this.arrCity = arrCity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 167 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 175 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getEtNo()
/*     */   {
/* 183 */     return this.etNo;
/*     */   }
/*     */ 
/*     */   public void setEtNo(String etNo)
/*     */   {
/* 191 */     this.etNo = etNo;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 199 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 207 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getBagTag()
/*     */   {
/* 215 */     return this.bagTag;
/*     */   }
/*     */ 
/*     */   public void setBagTag(String bagTag)
/*     */   {
/* 223 */     this.bagTag = bagTag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PrintPsgInfoInput
 * JD-Core Version:    0.6.0
 */