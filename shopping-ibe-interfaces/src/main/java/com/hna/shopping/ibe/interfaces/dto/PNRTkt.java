package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRTkt extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -869495134747374429L;
	private String type;                                                               
	private Date dateLimit;                                                            
	private String office;                                                             
	private String psgrID;                                                             
	private int index;                                                                     
	private String rmk;                                                                
	private boolean tkted = false;                                                     
	                                                                                   
	public PNRTkt()                                                                    
	  throws Exception                                                                 
	{                                                                                  
	}                                                                                  
	                                                                                   
	public PNRTkt(String type, Date dateLimit, String office, String psgrID)           
	  throws Exception                                                                 
	{                                                                                  
	  this.type = type;                                                                
	  this.dateLimit = dateLimit;                                                      
	  this.office = office;                                                            
	  this.psgrID = psgrID;                                                            
	}                                                                                  
	                                                                                   
	public void setType(String type)                                                          
	{                                                                                  
	  this.type = type;                                                                
	}                                                                                  
	                                                                                   
	public String getType()                                                            
	{                                                                                  
	  return this.type;                                                                
	}                                                                                  
	                                                                                   
	public	void setDateLimit(Date dateLimit) {                                                
	  this.dateLimit = dateLimit;                                                      
	}                                                                                  
	                                                                                   
	public void setDateLimit(String dateLimit) throws Exception                               
	{                                                                                  
	  try {
//	    this.dateLimit = QDateTime.stringToDate(dateLimit, "YYYYMMDD");
	  } catch (Exception e) {

	    throw e;                                                                       
	  }                                                                                
	}                                                                                  
	                                                                                   
	public Date getDateLimit()                                                         
	{                                                                                  
	  return this.dateLimit;                                                           
	}                                                                                  
	                                                                                   
	public String getDateLimitString()                                                 
	{                                                                                  
	  if (this.dateLimit == null) {                                                    
	    return null;                                                                   
	  }                                                                                
	  return this.dateLimit.toString();                                                
	}                                                                                  
	                                                                                   
	public void setOffice(String office) {                                                    
	  this.office = office;                                                            
	}                                                                                  
	                                                                                   
	public String getOffice()                                                          
	{                                                                                  
	  return this.office;                                                              
	}                                                                                  
	                                                                                   
	public	void setPsgrID(String psgrID) {                                                    
	  this.psgrID = psgrID;                                                            
	}                                                                                  
	                                                                                   
	public String getPsgrID()                                                          
	{                                                                                  
	  return this.psgrID;                                                              
	}                                                                                  
	                                                                                   
	public PNRTkt(String type, Date dateLimit, String office, String psgrID, String rmk)
	  throws Exception                                                                 
	{                                                                                  
	  this.type = type;                                                                
	  this.dateLimit = dateLimit;                                                      
	  this.office = office;                                                            
	  this.psgrID = psgrID;                                                            
	  this.rmk = rmk;                                                                  
	}                                                                                  
                                                                         
	                                                                                   
	public String getPsgrid() throws Exception, UnsupportedOperationException          
	{                                                                                  
	  return getPsgrID();                                                              
	}                                                                                  
	                                                                                   
	public String getRmk()                                                             
	{                                                                                  
	  return this.rmk;                                                                 
	}                                                                                  
	                                                                                   
	public void setRmk(String newRmk)                                                         
	{                                                                                  
	  this.rmk = newRmk;                                                               
	}                                                                                  
	                                                                                   
	public boolean isTkted()                                                           
	{                                                                                  
	  return this.tkted;                                                               
	}                                                                                  
	                                                                                   
	public void setTkted(boolean newTkted)                                                    
	{                                                                                  
	  this.tkted = newTkted;                                                           
	}                                                                                  
	                                                                                   
	public String toString()                                                           
	{                                                                                  
	  try                                                                              
	  {                                                                                
	    return getIndex() + "." + getType() + " " +                                    
	      getDateLimitString() + " " + getOffice() +                                   
	      getPsgrID() + ((this.tkted) ? " 已出票 " : "  ") + "  备注:" +               
	      getRmk();                                                                    
	  } catch (Exception e) {                                                          
	  }                                                                                
	  return super.toString();                                                         
	}
}
