package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@ApiModel
public class BaseResponse implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3636613546467434304L;
	@ApiModelProperty(value = "是否成功")
	private boolean success;
	@ApiModelProperty(value = "错误代码")
	private String errorCode;
	@ApiModelProperty(value = "错误信息")
	private String errorInfo;
	
}
