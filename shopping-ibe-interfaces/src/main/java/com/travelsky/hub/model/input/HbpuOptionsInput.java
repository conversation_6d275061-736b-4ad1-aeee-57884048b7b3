/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class HbpuOptionsInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 861156884611748134L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String cabin;
/*     */   private String deptCity;
/*     */   private String destCity;
/*     */   private String hostNumber;
/*     */   private String ckinMessage;
/*     */   private String ctcMessage;
/*     */   private String msgMessage;
/*     */   private String psmMessage;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  27 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  34 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  56 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  63 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  92 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  99 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 108 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 115 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 121 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 128 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getMsgMessage()
/*     */   {
/* 134 */     return this.msgMessage;
/*     */   }
/*     */ 
/*     */   public void setMsgMessage(String msgMessage)
/*     */   {
/* 141 */     this.msgMessage = msgMessage;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 148 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 155 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ 
/*     */   public String getCtcMessage()
/*     */   {
/* 162 */     return this.ctcMessage;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/* 168 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 175 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public void setCtcMessage(String ctcMessage)
/*     */   {
/* 181 */     this.ctcMessage = ctcMessage;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 188 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 195 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getPsmMessage()
/*     */   {
/* 202 */     return this.psmMessage;
/*     */   }
/*     */ 
/*     */   public void setPsmMessage(String psmMessage)
/*     */   {
/* 209 */     this.psmMessage = psmMessage;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.HbpuOptionsInput
 * JD-Core Version:    0.6.0
 */