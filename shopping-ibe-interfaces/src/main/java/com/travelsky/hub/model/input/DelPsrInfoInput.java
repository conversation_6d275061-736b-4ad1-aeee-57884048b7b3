/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class DelPsrInfoInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7361454789988320238L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String deptCity;
/*     */   private String flightDate;
/*     */   private String cabin;
/*     */   private String destCity;
/*     */   private String hostNumber;
/*     */   private String ckinMessage;
/*     */   private String ffpCardNumber;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  65 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  72 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  79 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  86 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  93 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 100 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 107 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 114 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 121 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 128 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 135 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 142 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 149 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 156 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 163 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 170 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 177 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 184 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.DelPsrInfoInput
 * JD-Core Version:    0.6.0
 */