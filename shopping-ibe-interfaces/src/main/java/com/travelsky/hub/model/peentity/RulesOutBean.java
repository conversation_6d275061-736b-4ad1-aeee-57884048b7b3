/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class RulesOutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8274072785500519744L;
/*    */   private List<Rules> rules;
/*    */ 
/*    */   public List<Rules> getRules()
/*    */   {
/* 23 */     return this.rules;
/*    */   }
/*    */ 
/*    */   public void setRules(List<Rules> rules)
/*    */   {
/* 30 */     this.rules = rules;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.RulesOutBean
 * JD-Core Version:    0.6.0
 */