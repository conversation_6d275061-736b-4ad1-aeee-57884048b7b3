/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class PsrOrdersOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5986614795750542916L;
/*    */   private String isSuccess;
/*    */   private List<Order> orders;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 31 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 38 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 46 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public List<Order> getOrders()
/*    */   {
/* 54 */     return this.orders;
/*    */   }
/*    */ 
/*    */   public void setOrders(List<Order> orders)
/*    */   {
/* 61 */     this.orders = orders;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 68 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 75 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 82 */     return this.errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.PsrOrdersOutPutBean
 * JD-Core Version:    0.6.0
 */