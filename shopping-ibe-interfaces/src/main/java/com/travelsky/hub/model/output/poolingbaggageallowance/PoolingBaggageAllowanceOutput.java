/*     */ package com.travelsky.hub.model.output.poolingbaggageallowance;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PoolingBaggageAllowanceOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3620868416547877298L;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String opType;
/*     */   private List<PoolingTotal> poolingTotal;
/*     */   private List<PassengerList> passengerList;
/*     */ 
/*     */   public String getOpType()
/*     */   {
/*  55 */     return this.opType;
/*     */   }
/*     */ 
/*     */   public void setOpType(String opType)
/*     */   {
/*  62 */     this.opType = opType;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  69 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  76 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  83 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  90 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  97 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 104 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 111 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 118 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public List<PoolingTotal> getPoolingTotal()
/*     */   {
/* 125 */     return this.poolingTotal;
/*     */   }
/*     */ 
/*     */   public void setPoolingTotal(List<PoolingTotal> poolingTotal)
/*     */   {
/* 132 */     this.poolingTotal = poolingTotal;
/*     */   }
/*     */ 
/*     */   public List<PassengerList> getPassengerList()
/*     */   {
/* 139 */     return this.passengerList;
/*     */   }
/*     */ 
/*     */   public void setPassengerList(List<PassengerList> passengerList)
/*     */   {
/* 146 */     this.passengerList = passengerList;
/*     */   }
/*     */ 
/*     */   public String getAirline()
/*     */   {
/* 153 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/* 160 */     this.airline = airline;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.poolingbaggageallowance.PoolingBaggageAllowanceOutput
 * JD-Core Version:    0.6.0
 */