/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class RemarksInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1826149904976397381L;
/*    */   private String remarksType;
/*    */   private String remarksText;
/*    */ 
/*    */   public void setRemarksType(String remarksType)
/*    */   {
/* 31 */     this.remarksType = remarksType;
/*    */   }
/*    */ 
/*    */   public String getRemarksType()
/*    */   {
/* 38 */     return this.remarksType;
/*    */   }
/*    */ 
/*    */   public void setRemarksText(String remarksText)
/*    */   {
/* 47 */     this.remarksText = remarksText;
/*    */   }
/*    */ 
/*    */   public String getRemarksText()
/*    */   {
/* 53 */     return this.remarksText;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.RemarksInformation
 * JD-Core Version:    0.6.0
 */