/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ContactInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3793054259701497584L;
/*    */   private String contactType;
/*    */   private String contactContent;
/*    */ 
/*    */   public String getContactType()
/*    */   {
/* 31 */     return this.contactType;
/*    */   }
/*    */ 
/*    */   public void setContactType(String contactType)
/*    */   {
/* 38 */     this.contactType = contactType;
/*    */   }
/*    */ 
/*    */   public String getContactContent()
/*    */   {
/* 45 */     return this.contactContent;
/*    */   }
/*    */ 
/*    */   public void setContactContent(String contactContent)
/*    */   {
/* 52 */     this.contactContent = contactContent;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.ContactInformation
 * JD-Core Version:    0.6.0
 */