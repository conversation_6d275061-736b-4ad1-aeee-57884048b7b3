package com.travelsky.hub.wdoe.util;

public final class DifferSeatchartConstr
{
  public static final String DIFFERSEATCHART_WDOE_ELEMENT = "WDoe";
  public static final String DIFFERSEATCHART_WDOE_AIRLINECODE = "AirlineCode";
  public static final String DIFFERSEATCHART_WDOE_FLIGHTNUM = "FlightNumber";
  public static final String DIFFERSEATCHART_REQUEST = "DisplaySeatMapRequest";
  public static final String DIFFERSEATCHART_WDOE_FLIGHTSUFFIX = "FlightSuffix";
  public static final String DIFFERSEATCHART_WDOE_FLIGHTDATE = "DepartureDate";
  public static final String DIFFERSEATCHART_WDOE_FROMCITY = "DepartureAirport";
  public static final String DIFFERSEATCHART_WDOE_TOCITY = "ArrivalAirport";
  public static final String DIFFERSEATCHART_WDOE_CABIN = "SubClass";
  public static final String DIFFERSEATCHART_WDOE_TICKETNO = "EtNumber";
  public static final String DIFFERSEATCHART_WDOE_HOSTNUM = "HostNumber";
  public static final String DIFFERSEATCHART_WDOE_PRIORITY = "Priority";
  public static final String DIFFERSEATCHART_WDOE_OPTIONTYPE = "OptionType";
  public static final String DSMRES_ELEMENT = "DisplaySeatMapResponse";
  public static final String DSMRES_SEGMENTINFOS = "SegmentInfos";
  public static final String DSMRES_LEGINFOS = "LegInfos";
  public static final String DSMRES_CABINGROUPS = "CabinGroups";
  public static final String DSMRES_LINES = "Lines";
  public static final String DSMRES_SEATINFOS = "Seatinfos";
  public static final String DSMRES_SECTIONS = "Sections";
  public static final String DSMRES_SEGMENTINFO = "SegmentInfo";
  public static final String DSMRES_LEGINFO = "LegInfo";
  public static final String DSMRES_EQUIPCONFIG = "EquipConfig";
  public static final String DSMRES_TERMINALINFOMATION = "TerminalInformation";
  public static final String DSMRES_ISNOSELECTION = "IsNoselection";
  public static final String DSMRES_CABINGROUP = "CabinGroup";
  public static final String DSMRES_LINE = "Line";
  public static final String DSMRES_SEATINFO = "SeatInfo";
  public static final String DSMRES_SECTION = "Section";
  public static final String DSM_RESULTCODE = "ResultCode";
  public static final String DSM_RESULTMESSAGE = "ResultMessage";
  public static final String DSM_AIRLINECODE = "OcAirlineCode";
  public static final String DSM_FLIGHTNUMBER = "OcFlightNumber";
  public static final String DSM_FLIGHTSUFFIX = "OcFlightSuffix";
  public static final String DSM_ISIF = "IsIF";
  public static final String DSM_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String DSM_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String DSM_ASM = "ASM";
  public static final String DSM_CONFIGURATION = "Configuration";
  public static final String DSM_AIRCRAFTVERSION = "AircraftVersion";
  public static final String DSM_EQUIPMENTCODE = "EquipmentCode";
  public static final String DSM_SEGMENTNUMBER = "SegmentNumber";
  public static final String DSM_TCARDTYPE = "TcardType";
  public static final String DSM_SALEABLECONFIG = "SaleableConfig";
  public static final String DSM_DEPARTUREVERSION = "DepartureVersion";
  public static final String DSM_PHYSICALCONFIG = "PhysicalConfig";
  public static final String DSM_IATATYPE = "IataType";
  public static final String DSM_DEPARTUREAIRPORTCODE = "DepartureAirportCode";
  public static final String DSM_ARRIVALAIRPORTCODE = "ArrivalAirportCode";
  public static final String DSM_DEPARTUREAIRPORTSEGMENT = "DepartureAirportSegment";
  public static final String DSM_ARRIVALAIRPORTSEGMENT = "ArrivalAirportSegment";
  public static final String DSM_DEPARTURETIME = "DepartureTime";
  public static final String DSM_ARRIVALTIME = "ArrivalTime";
  public static final String DSM_CLASSCODE = "ClassCode";
  public static final String DSM_CLASSNAME = "ClassName";
  public static final String DSM_CLASSSEQUENCE = "ClassSequence";
  public static final String DSM_COLUMNNUMBERING = "ColumnNumbering";
  public static final String DSM_LINELIST = "LineList";
  public static final String DSM_LINENUMBER = "LineNumber";
  public static final String DSM_LINEATTRIBUTES = "LineAttributes";
  public static final String DSM_FRONTEMPTYLINES = "FrontEmptyLines";
  public static final String DSM_REAREMPTYLINES = "RearEmptyLines";
  public static final String DSM_SEATNUMBER = "SeatNumber";
  public static final String DSM_SEATATTRIBUTE = "SeatAttribute";
  public static final String DSM_SEATVALUE = "SeatValue";
  public static final String DSM_PRICEVALUE = "PriceValue";
  public static final String DSM_ROWNUMBER = "RowNumber";
  public static final String DSM_ROWSEQUENCE = "RowSequence";
  public static final String DSM_ROWATTRIBUTES = "RowAttributes";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.DifferSeatchartConstr
 * JD-Core Version:    0.6.0
 */