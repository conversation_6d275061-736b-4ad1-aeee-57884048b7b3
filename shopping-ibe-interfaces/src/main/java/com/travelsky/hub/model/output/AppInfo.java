/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AppInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6307669574080960862L;
/*     */   private String surName;
/*     */   private String givenName;
/*     */   private String middleName;
/*     */   private String gender;
/*     */   private String docHolderNationality;
/*     */   private String birthDate;
/*     */   private String docType;
/*     */   private String docID;
/*     */   private String appStatus;
/*     */ 
/*     */   public String getSurName()
/*     */   {
/*  66 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/*  73 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getGivenName()
/*     */   {
/*  80 */     return this.givenName;
/*     */   }
/*     */ 
/*     */   public void setGivenName(String givenName)
/*     */   {
/*  87 */     this.givenName = givenName;
/*     */   }
/*     */ 
/*     */   public String getMiddleName()
/*     */   {
/*  94 */     return this.middleName;
/*     */   }
/*     */ 
/*     */   public void setMiddleName(String middleName)
/*     */   {
/* 101 */     this.middleName = middleName;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 108 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 115 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getDocHolderNationality()
/*     */   {
/* 122 */     return this.docHolderNationality;
/*     */   }
/*     */ 
/*     */   public void setDocHolderNationality(String docHolderNationality)
/*     */   {
/* 129 */     this.docHolderNationality = docHolderNationality;
/*     */   }
/*     */ 
/*     */   public String getBirthDate()
/*     */   {
/* 136 */     return this.birthDate;
/*     */   }
/*     */ 
/*     */   public void setBirthDate(String birthDate)
/*     */   {
/* 143 */     this.birthDate = birthDate;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 150 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 157 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 164 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 171 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getAppStatus()
/*     */   {
/* 178 */     return this.appStatus;
/*     */   }
/*     */ 
/*     */   public void setAppStatus(String appStatus)
/*     */   {
/* 185 */     this.appStatus = appStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AppInfo
 * JD-Core Version:    0.6.0
 */