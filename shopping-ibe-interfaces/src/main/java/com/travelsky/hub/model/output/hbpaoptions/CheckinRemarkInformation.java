/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CheckinRemarkInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6215375099725613719L;
/*    */   private String checkinRemarkType;
/*    */   private String checkinRemarksText;
/*    */ 
/*    */   public String getCheckinRemarkType()
/*    */   {
/* 31 */     return this.checkinRemarkType;
/*    */   }
/*    */ 
/*    */   public void setCheckinRemarkType(String checkinRemarkType)
/*    */   {
/* 38 */     this.checkinRemarkType = checkinRemarkType;
/*    */   }
/*    */ 
/*    */   public String getCheckinRemarksText()
/*    */   {
/* 45 */     return this.checkinRemarksText;
/*    */   }
/*    */ 
/*    */   public void setCheckinRemarksText(String checkinRemarksText)
/*    */   {
/* 52 */     this.checkinRemarksText = checkinRemarksText;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.CheckinRemarkInformation
 * JD-Core Version:    0.6.0
 */