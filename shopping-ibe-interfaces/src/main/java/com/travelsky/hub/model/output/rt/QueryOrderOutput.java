/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class QueryOrderOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4029863522602010680L;
/*    */   private PassengerList passengerList;
/*    */   private OrderHeader orderHeader;
/*    */   private List<Warning> warnings;
/*    */   private Group group;
/*    */ 
/*    */   public PassengerList getPassengerList()
/*    */   {
/* 40 */     return this.passengerList;
/*    */   }
/*    */ 
/*    */   public void setPassengerList(PassengerList passengerList)
/*    */   {
/* 47 */     this.passengerList = passengerList;
/*    */   }
/*    */ 
/*    */   public OrderHeader getOrderHeader()
/*    */   {
/* 54 */     return this.orderHeader;
/*    */   }
/*    */ 
/*    */   public void setOrderHeader(OrderHeader orderHeader)
/*    */   {
/* 61 */     this.orderHeader = orderHeader;
/*    */   }
/*    */ 
/*    */   public List<Warning> getWarnings()
/*    */   {
/* 68 */     return this.warnings;
/*    */   }
/*    */ 
/*    */   public void setWarnings(List<Warning> warnings)
/*    */   {
/* 75 */     this.warnings = warnings;
/*    */   }
/*    */ 
/*    */   public Group getGroup()
/*    */   {
/* 82 */     return this.group;
/*    */   }
/*    */ 
/*    */   public void setGroup(Group group)
/*    */   {
/* 89 */     this.group = group;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.QueryOrderOutput
 * JD-Core Version:    0.6.0
 */