/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class IcsSeatMapInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -68974368966277010L;
/*     */   private String flightNumber;
/*     */   private String pnr;
/*     */   private String airlineCode;
/*     */   private String departureAirport;
/*     */   private String flightDate;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/*  34 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  41 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/*  49 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  56 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  63 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  70 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  77 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  84 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  91 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  98 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 105 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 112 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.IcsSeatMapInput
 * JD-Core Version:    0.6.0
 */