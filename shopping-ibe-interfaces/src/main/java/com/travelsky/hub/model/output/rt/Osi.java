/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Osi
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -23450811702530498L;
/*    */   private String actIden;
/*    */   private String text;
/*    */ 
/*    */   public String getActIden()
/*    */   {
/* 31 */     return this.actIden;
/*    */   }
/*    */ 
/*    */   public void setActIden(String actIden)
/*    */   {
/* 38 */     this.actIden = actIden;
/*    */   }
/*    */ 
/*    */   public String getText()
/*    */   {
/* 45 */     return this.text;
/*    */   }
/*    */ 
/*    */   public void setText(String text)
/*    */   {
/* 52 */     this.text = text;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Osi
 * JD-Core Version:    0.6.0
 */