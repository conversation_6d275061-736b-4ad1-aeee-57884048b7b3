/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class FareCalculation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2131529429534445160L;
/*     */   private List<FcTaxSummary> fcTaxSummaryList;
/*     */   private String btInd;
/*     */   private String tourCodeInd;
/*     */   private String creationDate;
/*     */   private String itInd;
/*     */   private String overFlow;
/*     */   private String govermentFareInd;
/*     */   private String fareCalculationAirline;
/*     */   private String text;
/*     */   private String automaticInd;
/*     */ 
/*     */   public List<FcTaxSummary> getFcTaxSummaryList()
/*     */   {
/*  64 */     return this.fcTaxSummaryList;
/*     */   }
/*     */ 
/*     */   public void setFcTaxSummaryList(List<FcTaxSummary> fcTaxSummaryList)
/*     */   {
/*  71 */     this.fcTaxSummaryList = fcTaxSummaryList;
/*     */   }
/*     */ 
/*     */   public String getBtInd()
/*     */   {
/*  78 */     return this.btInd;
/*     */   }
/*     */ 
/*     */   public void setBtInd(String btInd)
/*     */   {
/*  85 */     this.btInd = btInd;
/*     */   }
/*     */ 
/*     */   public String getTourCodeInd()
/*     */   {
/*  92 */     return this.tourCodeInd;
/*     */   }
/*     */ 
/*     */   public void setTourCodeInd(String tourCodeInd)
/*     */   {
/*  99 */     this.tourCodeInd = tourCodeInd;
/*     */   }
/*     */ 
/*     */   public String getCreationDate()
/*     */   {
/* 106 */     return this.creationDate;
/*     */   }
/*     */ 
/*     */   public void setCreationDate(String creationDate)
/*     */   {
/* 113 */     this.creationDate = creationDate;
/*     */   }
/*     */ 
/*     */   public String getItInd()
/*     */   {
/* 120 */     return this.itInd;
/*     */   }
/*     */ 
/*     */   public void setItInd(String itInd)
/*     */   {
/* 127 */     this.itInd = itInd;
/*     */   }
/*     */ 
/*     */   public String getOverFlow()
/*     */   {
/* 134 */     return this.overFlow;
/*     */   }
/*     */ 
/*     */   public void setOverFlow(String overFlow)
/*     */   {
/* 141 */     this.overFlow = overFlow;
/*     */   }
/*     */ 
/*     */   public String getGovermentFareInd()
/*     */   {
/* 148 */     return this.govermentFareInd;
/*     */   }
/*     */ 
/*     */   public void setGovermentFareInd(String govermentFareInd)
/*     */   {
/* 155 */     this.govermentFareInd = govermentFareInd;
/*     */   }
/*     */ 
/*     */   public String getFareCalculationAirline()
/*     */   {
/* 162 */     return this.fareCalculationAirline;
/*     */   }
/*     */ 
/*     */   public void setFareCalculationAirline(String fareCalculationAirline)
/*     */   {
/* 169 */     this.fareCalculationAirline = fareCalculationAirline;
/*     */   }
/*     */ 
/*     */   public String getText()
/*     */   {
/* 176 */     return this.text;
/*     */   }
/*     */ 
/*     */   public void setText(String text)
/*     */   {
/* 183 */     this.text = text;
/*     */   }
/*     */ 
/*     */   public String getAutomaticInd()
/*     */   {
/* 190 */     return this.automaticInd;
/*     */   }
/*     */ 
/*     */   public void setAutomaticInd(String automaticInd)
/*     */   {
/* 197 */     this.automaticInd = automaticInd;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FareCalculation
 * JD-Core Version:    0.6.0
 */