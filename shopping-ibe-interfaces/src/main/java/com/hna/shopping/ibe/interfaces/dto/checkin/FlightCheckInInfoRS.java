package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import com.hna.shopping.ibe.interfaces.dto.DETRCreResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:21
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class FlightCheckInInfoRS extends BaseResponse {
    private String boardingGateNumber;
    private String boardingTime;
    private String deptTime;
}