package com.hna.shopping.ibe.interfaces.dto;

import java.text.SimpleDateFormat;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;

//import com.hnas.inter.service.base.BaseRequest;
@Data
@ApiModel
public class DETRSeg  {
	private int segmentIndex = 0;
	private String stopType;
	private String depAirportCode;
	private String arrAirportCode;
	private Date depTime;
	private Date arrTime;
	private String flightNo;
	private char cabin = ' ';
	private String rate;
	private boolean isFPC = false;
	private int baggageWeight = 0;
	private String baggageWeightUnit = "KG";
	private int baggagePiece = 0;
	private String pnrNo;
	private String crsPnrNo;
	private String crsType;
	private String ticketStatus;
	private Date startValidityDate = null;
	private Date endValidityDate = null;
	private String depAirportTerminal = "";
	private String arrAirportTerminal = "";
	private String mcoNumber;
	private String emdFlag;
	 private String changeReason = "";
	private String airline;
	private String operationAirline;
	 public static final int AIRSEG_ARNK = 1;
	 public static final int AIRSEG_NORMAL = 2;
	 public static final int AIRSEG_OPEN = 3;
	private String BoardingNo;
	private String segmentStatus;
	private int type;
	private Boolean addOn;
	private String segGroup;

	 public String toString()
	 {
	   StringBuffer output = new StringBuffer();
	   try {
	     output.append(getStopType() + " " + String.valueOf(getSegmentIndex()) + " FM:" + this.depAirportCode + " TO " + this.arrAirportCode);
	     SimpleDateFormat sdf = new SimpleDateFormat("ddMMMyy");
	     SimpleDateFormat sdf2 = new SimpleDateFormat("ddMMM HHmm");
	     if (this.type == 1) {
	       output.append("\tVOID");
	     } else if (this.type == 2) {
	       output.append("\t" + this.airline + " " + ((this.operationAirline != null) ? this.operationAirline : "  ") + "\t\t" + ((this.flightNo.length() > 2) ? this.flightNo.substring(2) : "    ") + "\t");
	       output.append(this.cabin);
	       output.append("\t" + ((this.depTime != null) ? sdf2.format(this.depTime) : "          ") + ((this.arrTime != null) ? "\t" + sdf2.format(this.arrTime) : "") + "\t" + ((this.segmentStatus != null) ? this.segmentStatus : "  ") + "\t" + ((this.rate != null) ? this.rate : "     ") + "\t");
	       output.append(((this.startValidityDate == null) ? "       " :sdf.format(this.startValidityDate)) + "/" + ((this.endValidityDate == null) ? "       " : sdf.format(this.endValidityDate)) + "\t");
	       output.append(((isFPC()) ? getBaggagePiece() + "PC" : (this.baggagePiece == -3) ? "未知" : new StringBuilder(String.valueOf(this.baggageWeight)).append(this.baggageWeightUnit).toString()) + "\t");
	       output.append(this.ticketStatus);
	     } else {
	       output.append("\t" + this.airline + " " + ((this.operationAirline != null) ? this.operationAirline : "  ") + "\t\t" + "OPEN" + "\t");
	       output.append(this.cabin);
	       output.append("\tOPEN      \t" + ((this.segmentStatus != null) ? this.segmentStatus : "  ") + "\t" + ((this.rate != null) ? this.rate : "     ") + "\t");
	       output.append(((this.startValidityDate == null) ? "       " : sdf.format(this.startValidityDate)) + "/" + ((this.endValidityDate == null) ? "       " : sdf.format(this.endValidityDate)) + "\t");
	       output.append(((isFPC()) ? getBaggagePiece() + "PC" : (this.baggagePiece == -3) ? "未知" : new StringBuilder(String.valueOf(this.baggageWeight)).append(this.baggageWeightUnit).toString()) + "\t");
	       output.append(this.ticketStatus);
	     }
	
	     StringBuffer terminal = new StringBuffer("----");
	     terminal.replace(0, this.depAirportTerminal.length(), this.depAirportTerminal);
	     terminal.replace(2, 2 + this.arrAirportTerminal.length(), this.arrAirportTerminal);
	
	     output.append("\r\n\t\t" + terminal.toString() + "\tRL:" + ((this.pnrNo != null) ? this.pnrNo : "     ") + "\t/" + ((this.crsPnrNo != null) ? this.crsPnrNo : "     ") + "\t" + ((this.crsType != null) ? this.crsType : "  ") + "\tMCO: " + ((this.mcoNumber != null) ? this.mcoNumber : "") + "\tBN:" + ((this.BoardingNo != null) ? this.BoardingNo : "") + (((this.changeReason != null) && (this.changeReason.length() > 0)) ? "CHANGE:" + this.changeReason : "") + ((this.emdFlag != null) ? "EMD:" + this.emdFlag : "") + "\r\n");
	     return output.toString(); } catch (Exception e) { 
	    	 e.printStackTrace();
	   }
	   return null;
	 }

}
