/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class ChdPsrInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private PsrInputBean psrInput;
/*    */   private String hostNumber;
/*    */   private List<ChdPartner> partners;
/*    */ 
/*    */   public PsrInputBean getPsrInput()
/*    */   {
/* 30 */     return this.psrInput;
/*    */   }
/*    */ 
/*    */   public void setPsrInput(PsrInputBean psrInput)
/*    */   {
/* 37 */     this.psrInput = psrInput;
/*    */   }
/*    */ 
/*    */   public String getHostNumber()
/*    */   {
/* 44 */     return this.hostNumber;
/*    */   }
/*    */ 
/*    */   public void setHostNumber(String hostNumber)
/*    */   {
/* 51 */     this.hostNumber = hostNumber;
/*    */   }
/*    */ 
/*    */   public List<ChdPartner> getPartners()
/*    */   {
/* 58 */     return this.partners;
/*    */   }
/*    */ 
/*    */   public void setPartners(List<ChdPartner> partners)
/*    */   {
/* 65 */     this.partners = partners;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ChdPsrInputBean
 * JD-Core Version:    0.6.0
 */