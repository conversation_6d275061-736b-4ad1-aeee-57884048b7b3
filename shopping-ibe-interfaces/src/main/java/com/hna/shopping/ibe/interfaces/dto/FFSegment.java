package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
@ApiModel
public class FFSegment implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 7367860196065651133L;
	private String orgCity;
	private String dstCity;
	private Date departrueTime;
	private Date arrivalTime;
	private String planeModel;
	private String controlOffice;
	private String cabinMap;
	private List<FFCabinSeat> cabinSeats = new ArrayList<FFCabinSeat>();

	public String toString() {
		StringBuffer strtmp = new StringBuffer();
		try {
			strtmp.append("始发城市：" + this.orgCity + "   ");
			strtmp.append("到达城市：" + this.dstCity + "   ");
			strtmp.append("起飞时间：" + this.departrueTime + "  ");
			strtmp.append("到达时间：" + this.arrivalTime + "  ");
			strtmp.append("机型: "
					+ ((this.planeModel == null) ? "   " : this.planeModel)
					+ "   ");
			if (this.controlOffice != null) {
				strtmp.append("Control Office: " + this.controlOffice + "   ");
				strtmp.append("销售舱位: " + this.cabinMap + "   ");
//				if ((this.cabins != null) && (this.cabins.size() > 0)
//						&& (this.cabinSeats != null)
//						&& (this.cabinSeats.size() > 0)
//						&& (this.cabins.size() == this.cabinSeats.size())) {
//					for (int i = 0; i < this.cabins.size(); ++i) {
//						strtmp.append(this.cabins.get(i) + ":"
//								+ this.cabinSeats.get(i) + "   ");
//					}
//					strtmp.append("   \n");
//				}
			} else {
				strtmp.append("\n");
			}
			return strtmp.toString();
		} catch (Exception e) {
		}
		return null;
	}
//

}