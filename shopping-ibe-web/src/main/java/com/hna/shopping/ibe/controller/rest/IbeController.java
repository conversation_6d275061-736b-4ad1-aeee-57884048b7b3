/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.responsecode.CodeIbe;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.FlightManager;
import com.hna.shopping.ibe.manager.IbeManager;
import com.hna.shopping.ibe.manager.PNRManager;
import com.hna.shopping.ibe.manager.util.RedisUtil;
import com.travelsky.ibe.exceptions.IBEException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

import java.util.*;

@Api(description = "IBE 接口封装")
@RestController
@RequestMapping("/api/ibe")
@Slf4j
public class IbeController {

    @Autowired
    private IbeManager ibeManager;

    @Autowired
    private PNRManager pnrManager;

    @Autowired
    private FlightManager flightManager;

    @ApiOperation(value = "AV 查询")
    @RequestMapping(value = "av", method = RequestMethod.GET)
    RestResponse<List<SegmentDTO>> av(@ApiParam(value = "航司代码")
                                      @RequestParam(value = "airline") String airline,

                                      @ApiParam(value = "出发城市三字码")
                                      @RequestParam(value = "orgCity") String orgCity,

                                      @ApiParam(value = "抵达城市三字码")
                                      @RequestParam(value = "dstCity") String dstCity,

                                      @ApiParam(value = "出发日期 格式 yyyyMMdd")
                                      @RequestParam(value = "depDate")
                                      @DateTimeFormat(pattern = "yyyyMMdd") Date depDate,
                                      @ApiParam(value = "是否直达")
                                      @RequestParam(value = "isDirect") boolean isDirect,
                                      @ApiParam(value = "销售商渠道")
                                      @RequestParam(value = "sellerChannels",required = false) String sellerChannels,
                                      @ApiParam(value = "所有航司")
                                      @RequestParam(value = "allAirline",required = false) String allAirline


    ) {
        airline = StringUtils.upperCase(airline);
        log.info("av airline="+airline+" orgCity="+orgCity+ " dstCity=" +dstCity+" flightDate="+DateUtil.dateToString(depDate) +" sellerChannels="+sellerChannels);
        List<SegmentDTO> result = ibeManager.av(airline, orgCity, dstCity, depDate, isDirect,"",allAirline);
        //log.info("av airline="+airline+" orgCity="+orgCity+ " dstCity=" +dstCity+" flightDate="+DateUtil.dateToString(depDate) +" sellerChannels="+sellerChannels+" result = {}",result);
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "AV 查询")
    @RequestMapping(value = "avV2", method = RequestMethod.POST)
    RestResponse<List<SegmentDTO>> avV2(@RequestBody AvRQ avRQ) {
        String airline = StringUtils.upperCase(avRQ.getAirline());
        log.info("avV2 airline="+airline+" orgCity="+avRQ.getOrgCity()+ " dstCity=" +avRQ.getDstCity()+" flightDate="+DateUtil.dateToString(avRQ.getDepDate()) +" sellerChannels="+avRQ.getSellerChannels());
        List<SegmentDTO> result = ibeManager.av(airline, avRQ.getOrgCity(), avRQ.getDstCity(), avRQ.getDepDate(), avRQ.isDirect(),"",avRQ.getAllAirline());
        //log.info("avV2 airline="+airline+" orgCity="+orgCity+ " dstCity=" +dstCity+" flightDate="+DateUtil.dateToString(depDate) +" sellerChannels="+sellerChannels+" result = {}",result);
        return RestResponse.ok(result);
    }



    @ApiOperation(value = "AV CACHE 查询")
    @RequestMapping(value = "avCache", method = RequestMethod.GET)
    RestResponse<List<SegmentDTO>> avCache(@ApiParam(value = "航司代码")
                                           @RequestParam(value = "airline") String airline,

                                           @ApiParam(value = "出发城市三字码")
                                           @RequestParam(value = "orgCity") String orgCity,

                                           @ApiParam(value = "抵达城市三字码")
                                           @RequestParam(value = "dstCity") String dstCity,

                                           @ApiParam(value = "出发日期 格式 yyyyMMdd")
                                           @RequestParam(value = "depDate")
                                           @DateTimeFormat(pattern = "yyyyMMdd") Date depDate,

                                           @ApiParam(value = "是否直达")
                                           @RequestParam(value = "isDirect") boolean isDirect,
                                           @ApiParam(value = "销售商渠道")
                                           @RequestParam(value = "sellerChannels",required = false) String sellerChannels,
                                           @ApiParam(value = "所有航司")
                                           @RequestParam(value = "allAirline",required = false) String allAirline


    ) {
        airline = StringUtils.upperCase(airline);
        if("8L".equals(airline)) {
            String cacheKey = ibeManager.getCacheKey(airline, orgCity, dstCity, depDate);
            List<SegmentDTO> result = getResultFromCache(cacheKey,airline, orgCity, dstCity, depDate,isDirect,sellerChannels,allAirline);
            if(result != null) {
                return RestResponse.ok(result);
            }
            log.info("avCache airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(depDate) + " sellerChannels=" + sellerChannels);
            result = ibeManager.av(airline, orgCity, dstCity, depDate, isDirect,"",allAirline);
            return RestResponse.ok(result);
        }else{
            log.info("av airline=" + airline + " orgCity=" + orgCity + " dstCity=" + dstCity + " flightDate=" + DateUtil.dateToString(depDate) + " sellerChannels=" + sellerChannels);
            List<SegmentDTO> result = ibeManager.av(airline, orgCity, dstCity, depDate, isDirect,"", allAirline);
            return RestResponse.ok(result);
        }
    }

    private List<SegmentDTO> getResultFromCache(String cacheKey,String airline,String orgCity,String dstCity,Date flightDate,boolean isDirect,String sellerChannels,String allAirline){
//        for(String avSpace : CacheName.AV_SPACES){
//            //List<SegmentDTO> result = (List<SegmentDTO>) CacheManagerUtil.get(avSpace, cacheKey);
//            AvCache avCache = (AvCache) CacheManagerUtil.get(avSpace, cacheKey);
//
//            if(avCache == null){
//                List<SegmentDTO> result = ibeManager.av(airline, orgCity, dstCity, flightDate, isDirect,allAirline);
//                return result;
//            }
//
//            Long cacheTime = avCache.getCacheTime();
//            List<SegmentDTO> result = avCache.getSegments();
//            if (CollectionUtils.isNotEmpty(result)) {
//                if(result.get(0).getCabins().size() > 0){
//                    //缓存超过10分钟时需要发起一次真实av start
//
//                    long cacheTimeOffset = Calendar.getInstance().getTimeInMillis() - cacheTime.longValue();
//
//                    if(cacheTimeOffset >= (5 * 60 * 1000)){//如果缓存超过10分钟需要实时查询一次
//                        //异步发起一次实时av
//                        ibeManager.avAsync(airline, orgCity, dstCity, flightDate, isDirect,sellerChannels,allAirline);
//                    }
//
//                    //缓存超过10分钟时需要发起一次真实av end
//
//
//                    log.info("get getResultFromCache cacheKey = {} avSpace = {} time = {} datetime = {}",cacheKey,avSpace,avCache.getCacheTime(),DateUtil.toStringYMDHMS(new Date(avCache.getCacheTime())));
//                    return result;
//                }else{
//                    result = ibeManager.av(airline, orgCity, dstCity, flightDate, isDirect,allAirline);
//                    return result;
//                }
//            }
//        }
        return null;
    }

    @ApiOperation(value = "fdBasePrice")
    @RequestMapping(value = "fdBasePrice", method = RequestMethod.POST)
    RestResponse<BaseResponse> fdBasePrice(@ApiParam(value = "fd参数", required = true)
                                           @RequestBody FdRQ fdRQ){
        String uuid = UUID.randomUUID().toString();
        log.info("fdBasePrice.Start uuid: {} , rq: {}", uuid, JSON.toJSONString(fdRQ));
//        long t1 = System.currentTimeMillis();
//        String airline = "8L";
//        String[] lines = getLines();
//        for(String line : lines){
//            String[] orgDst = line.split("-");
//            String org = orgDst[0];
//            String dst = orgDst[1];
//            List<FDBasePriceRS> res =  pnrManager.fdBasePrice(airline,org,dst,null);
//        }

        List<FDBasePriceRS> res =  pnrManager.fdBasePrice(fdRQ.getAirline(),fdRQ.getDepCode(),fdRQ.getArrCode(),fdRQ.getFlightDate());
        if(res == null || res.size() == 0) return RestResponse.ok(null);

        log.info("fdBasePrice uuid: {} rs = {}",uuid,JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

    @ApiOperation(value = "fd")
    @RequestMapping(value = "fd", method = RequestMethod.POST)
    RestResponse<BaseResponse> fd(@ApiParam(value = "fd参数", required = true)
                                           @RequestBody FdRQ fdRQ){
        String uuid = UUID.randomUUID().toString();
        log.info("fdBasePrice.Start uuid: {} , rq: {}", uuid, JSON.toJSONString(fdRQ));


        List<FDBasePriceRS> res =  pnrManager.fd(fdRQ.getAirline(),fdRQ.getDepCode(),fdRQ.getArrCode(),fdRQ.getFlightDate());
        if(res == null || res.size() == 0) return RestResponse.ok(null);

        log.info("fdBasePrice uuid: {} rs = {}",uuid,JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

    @ApiOperation(value = "fdTax")
    @RequestMapping(value = "fdTax", method = RequestMethod.POST)
    RestResponse<BaseResponse> fdTax(@ApiParam(value = "fd参数", required = true)
                                  @RequestBody FdRQ fdRQ){
        String uuid = UUID.randomUUID().toString();
        log.info("fdBasePrice.Start uuid: {} , rq: {}", uuid, JSON.toJSONString(fdRQ));
        FDBasePriceRS res =  pnrManager.fdTax(fdRQ.getAirline(),fdRQ.getDepCode(),fdRQ.getArrCode(),fdRQ.getFlightDate(),fdRQ.getPassType());
        log.info("fdBasePrice uuid: {} rs = {}",uuid,JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

    @ApiOperation(value = "价格区间和税费")
    @RequestMapping(value = "fdPriceRange", method = RequestMethod.POST)
    RestResponse<BaseResponse> fdPriceRange(@ApiParam(value = "fd参数", required = true)
                                            @RequestBody AVRequest request){
        String uuid = UUID.randomUUID().toString();
        log.info("fdPriceRange.Start uuid: {} , rq: {}", uuid, JSON.toJSONString(request));

        String avRedisKey = "AV-" + request.getCarrier()+request.getOrigin()+ request.getDestination() + DateUtils.formatDate(request.getDepart(),"yyyy-MM-dd");


        List<AVAndFDResponse> dataList = new ArrayList<AVAndFDResponse>();

        Date depTime = request.getDepart();
        Calendar now = Calendar.getInstance();
        now.setTime(depTime);
        now.set(Calendar.HOUR_OF_DAY,0);
        now.set(Calendar.MINUTE,0);
        request.setDepart(now.getTime());
        Object avResponseObj = RedisUtil.get(avRedisKey);
        AVResponse avResponse;
        if (avResponseObj == null) {
            avResponse = flightManager.av(request);
            RedisUtil.set(avRedisKey,avResponse,10*60);
            log.info("av real:"+avRedisKey);
        } else {
            avResponse = (AVResponse)avResponseObj;
            log.info("av hit:"+avRedisKey);
        }

        if (avResponse == null || !avResponse.isSuccess()) {
            log.error("AV error:" + request.getCarrier()+request.getOrigin()+ request.getDestination() + DateUtils.formatDate(request.getDepart(),"yyyy-MM-dd"));
            return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
        } else { // 过滤掉 av 返回的航班日期与查询航班日期不匹配的航段
            List<AVSegment> segList = avResponse.getSegments();
            Iterator<AVSegment> segIterator = segList.iterator();
            while (segIterator.hasNext()) {
                AVSegment segment = segIterator.next();
                List<AVFlightInfo> flightInfoList = segment.getFlights();
                Iterator<AVFlightInfo> flightIterator = flightInfoList.iterator();
                while (flightIterator.hasNext()) {
                    AVFlightInfo flightInfo = flightIterator.next();
                    if (!DateUtil.dateToString(request.getDepart(), "yyyy-MM-dd").equals(DateUtil.dateToString(flightInfo.getDepartDate(),"yyyy-MM-dd"))) {
                        flightIterator.remove();
                    } else {
                        //AV结果的舱位用于找最低开放的公布运价的舱位，如果所有舱位都没库存，移除掉当前航班
                        boolean noCabinInventory = true;
                        List<AVCabinInfo> cabinInfoList = flightInfo.getCabins();
                        for (AVCabinInfo cabinInfo : cabinInfoList) {
                            if (cabinInfo.getInventory() != null) {
                                noCabinInventory = false;
                            }
                        }
                        if (noCabinInventory) {
                            flightIterator.remove();
                        }
                    }
                }
                if (flightInfoList.isEmpty()) {
                    segIterator.remove();
                }
            }
        }

        List<AVSegment> segmentList = avResponse.getSegments();
        if (segmentList.size() > 0) {

            //FD 有航段信息，查公布运价和成人税费
            FDBasePriceAndCabinPriceRS adultTax = getBasePriceAndTax(request,"AD");

            if (adultTax == null) {
                return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
            }

            //FD 处理价格和舱位
            List<String> cabinList = adultTax.getCabinList();
            List<Integer> priceList = adultTax.getPriceList();

            Map<String,Integer> cabinPriceMap = new HashMap<String,Integer>();
            for (int i = 0; i < cabinList.size(); i++) {
                cabinPriceMap.put(cabinList.get(i),priceList.get(i));
            }
            //FD 儿童税费
            FDBasePriceAndCabinPriceRS childTax  = getBasePriceAndTax(request,"CH");;

            if (childTax == null) {
                return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
            }

            for (AVSegment segment : segmentList) {

                AVAndFDResponse res = new AVAndFDResponse();
                dataList.add(res);

                //补充FD内容
                res.setCabin("Y");
                res.setYbPrice(new BigDecimal(adultTax.getYbPrice()).intValue());
                res.setAdultAirportTax(adultTax.getAirportTax());
                res.setAdultFuelTax(adultTax.getFuelTax());
                res.setChildAirportTax(childTax.getAirportTax());
                res.setChildFuelTax(childTax.getFuelTax());
                //补充FD内容结束

                List<AVFlightInfo> flightInfoList = segment.getFlights();
                for (AVFlightInfo flightInfo : flightInfoList) {
                    res.setAirline("JD");
                    res.setDepCode(flightInfo.getOrigin());
                    res.setArrCode(flightInfo.getDestination());
                    res.setFlightDate(DateUtil.dateToString(flightInfo.getDepartDate(),"yyyy-MM-dd"));
                    res.setFlightNo(flightInfo.getFlightNo());
                    res.setDepTime(DateUtil.dateToString(flightInfo.getDepartDate(),"HH:mm"));
                    res.setArrTime(DateUtil.dateToString(flightInfo.getArriveDate(),"HH:mm"));
                    res.setDepTerminal(flightInfo.getTerminal1());
                    res.setArrTerminal(flightInfo.getTerminal2());
                    res.setStop(flightInfo.getStop());
                    res.setArriTimeModify(flightInfo.getArriTimeModify());
                    res.setPlaneStyle(flightInfo.getPlaneStyle());

                    //AV结果的舱位用于找最低开放的公布运价的舱位
                    List<AVCabinInfo> cabinInfoList = flightInfo.getCabins();
                    List<String> openCabinList = new ArrayList<String>();
                    for (AVCabinInfo cabinInfo : cabinInfoList) {
                        if (cabinInfo.getInventory() != null) {
                            openCabinList.add(cabinInfo.getCabin());
                        }
                    }
                    Collections.reverse(openCabinList);

                    String lowestCabin = null;
                    Integer lowestPrice = null;
                    for (String cabin : openCabinList) {
                        if (cabinPriceMap.get(cabin) != null) {
                            lowestCabin = cabin;
                            lowestPrice = new BigDecimal(cabinPriceMap.get(cabin)+"").intValue();
                            break;
                        }
                    }

                    res.setLowestCabin(lowestCabin);
                    res.setLowestPrice(lowestPrice);

                }

            }
        } else {
            return RestResponse.ok("");
        }

        log.info("fdPriceRange uuid: {} rs = {}",uuid,JSON.toJSONString(dataList));

        return RestResponse.ok(dataList);
    }

    @ApiOperation(value = "订座并出票")
    @RequestMapping(value = "sellSeatAndIssueTickets", method = RequestMethod.POST)
    RestResponse<BaseResponse> sellSeatAndIssueTickets(@ApiParam(value = "出票信息", required = true)
                                            @RequestBody SellSeatRequest request){

        if (request.getIssueTicketSegmentList() == null || request.getPassengerIssueTicketList() == null ||
                request.getIssueTicketSegmentList().isEmpty() || request.getPassengerIssueTicketList().isEmpty() ||
                request.getAdultTripPrice() == null) {
            return RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
        }

        if (request.getTimeLimitMinutes() == 0) {
            request.setTimeLimitMinutes(30);
        }
        if (StringUtils.isEmpty(request.getContactInfo())) {
            //没传取第一个旅客的手机号
            request.setContactInfo(request.getPassengerIssueTicketList().get(0).getMobileNo());
        }

        if (StringUtils.isEmpty(request.getAirlineCode())) {
           request.setAirlineCode("JD");
        }

        int leftSeats = 0;
        String leftSeatskey = "leftSeats";
        for (IssueTicketSegment segment : request.getIssueTicketSegmentList()) {
            if (segment.getFltClass() == '\u0000' || "".equals(segment.getFltClass())) segment.setFltClass('F');
            if (StringUtils.isEmpty(segment.getEiInfo())) segment.setEiInfo("不得自愿签转");
            if (StringUtils.isEmpty(segment.getProductCode()))segment.setProductCode("PXY3");

            //判断座位剩余数
            AVRequest avRequest = new AVRequest();

            Calendar now = Calendar.getInstance();
            now.setTime(DateUtils.parseDate(segment.getDepDate(),new String[]{"yyyy-MM-dd"}));
            now.set(Calendar.HOUR_OF_DAY,0);
            now.set(Calendar.MINUTE,0);
            avRequest.setDepart(now.getTime());

//            avRequest.setDepart(DateUtils.parseDate(segment.getDepDate() +" 00:00:00",new String[]{"yyyy-MM-dd HH:mm:ss"}));
            avRequest.setOrigin(segment.getDepCode());
            avRequest.setDestination(segment.getArrCode());
            avRequest.setCarrier(request.getAirlineCode());
            AVResponse avResponse = flightManager.av(avRequest);

            if (avResponse == null || !avResponse.isSuccess()) {
                log.error("AV error:" + segment.getDepDate() + segment.getDepCode() + segment.getArrCode());
                return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
            }

            List<AVSegment> segmentList = avResponse.getSegments();
            boolean matched = false;
            if (segmentList.size() > 0) {
                for (AVSegment avSegment : segmentList) {
                    List<AVFlightInfo> flightInfoList = avSegment.getFlights();
                    if (flightInfoList.size() > 0) {
                        log.info("av response:" + segment.getFlightNo() + segment.getDepCode() + flightInfoList.size());
                        for (AVFlightInfo flightInfo : flightInfoList) {
                            if (segment.getFlightNo().equals(flightInfo.getFlightNo())) {
                                //AV结果的舱位用于找最低开放的公布运价的舱位
                                List<AVCabinInfo> cabinInfoList = flightInfo.getCabins();
                                boolean cabinMatched = false;
                                for (AVCabinInfo cabinInfo : cabinInfoList) {
                                    if ((segment.getFltClass()+"").equals(cabinInfo.getCabin())) {
                                        cabinMatched = true;
                                        if (cabinInfo.getInventory() == null) {
                                            log.error("cabin is not open " + cabinInfo.getCabin() + flightInfo.getFlightNo() + segment.getDepDate());
                                            JSONObject json = new JSONObject();
                                            json.put(leftSeatskey,0);
                                            return RestResponse.tip(CodeIbe.SEATS_NOT_ENOUGH_EXCEPTION,json);
                                        }
                                        int cabinOpenNum = cabinInfo.getInventory();
                                        if (request.getPassengerIssueTicketList().size() > cabinOpenNum) {
                                            log.error("cabin is not enough " + cabinInfo.getCabin() + flightInfo.getFlightNo() + segment.getDepDate());
                                            JSONObject json = new JSONObject();
                                            json.put(leftSeatskey,cabinOpenNum);
                                            return RestResponse.tip(CodeIbe.SEATS_NOT_ENOUGH_EXCEPTION,json);
                                        } else {
                                            log.info("cabin leftSeats:" + cabinOpenNum + cabinInfo.getCabin());
                                            leftSeats = cabinOpenNum;
                                            matched = true;
                                            break;
                                        }

                                    }
                                }

                                if (!cabinMatched) {
                                    log.error("sellSeatAndIssueTickets cabin is not matched" + segment.getFltClass()+"/" + request.getIssueTicketSegmentList().get(0).getFlightNo());
                                }

                            } else {
                                log.error("sellSeatAndIssueTickets flightNo is  not matched:"+segment.getFlightNo()+ ":" + flightInfo.getFlightNo());
                            }
                        }
                    } else {
                        log.error("sellSeatAndIssueTickets flightInfoList is empty:"+segment.getFlightNo());
                    }
                }
            } else {
                log.error("sellSeatAndIssueTickets segmentList is empty:"+segment.getFlightNo());
            }

            if (matched){
                //成人税费重新计算
                FDBasePriceAndCabinPriceRS adultTax = getBasePriceAndTax(avRequest,"AD");
                if (adultTax == null) {
                    return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
                }
                segment.setAdultFuelTax(new BigDecimal(adultTax.getFuelTax()));
                segment.setAdultAirportTax(new BigDecimal(adultTax.getAirportTax()));
                //票价等于预约价格减去机建和燃油
                segment.setAdultNetFare(request.getAdultTripPrice().subtract(segment.getAdultAirportTax()).subtract(segment.getAdultFuelTax()));
                segment.setAdultMarketFare(segment.getAdultNetFare());
                boolean hasChild = false;
                for (IssueTicketPassenger passenger : request.getPassengerIssueTicketList()) {
                    if ("CH".equals(passenger.getPassengerType())) {
                        hasChild = true;
                        break;
                    }
                }
                if (hasChild) {

                    if (request.getChildTripPrice() == null) {
                        return RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
                    }
                    //儿童税费重新计算
                    FDBasePriceAndCabinPriceRS childTax = getBasePriceAndTax(avRequest,"CH");
                    if (childTax == null) {
                        return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
                    }
                    segment.setChildFuelTax(new BigDecimal(childTax.getFuelTax()));
                    segment.setChildAirportTax(new BigDecimal(childTax.getAirportTax()));
                    //票价等于预约价格减去机建和燃油
                    segment.setChildNetFare(request.getChildTripPrice().subtract(segment.getChildAirportTax()).subtract(segment.getChildFuelTax()));
                    segment.setChildMarketFare(segment.getChildNetFare());
                }
            } else {
                //没有符合的航班
                JSONObject json = new JSONObject();
                json.put(leftSeatskey,0);
                return RestResponse.tip(CodeIbe.SEATS_NOT_ENOUGH_EXCEPTION, json);
            }

        }

        List<IssueTicketPassenger>  list = new ArrayList<>();
        try {
            list = ibeManager.sellSeatAndIssueTicket(request.getAirlineCode(),request.getIssueTicketSegmentList()  ,
                    request.getPassengerIssueTicketList(), request.getContactInfo(),
                    request.getTimeLimitMinutes()  );
        } catch (IBEException e) {
            e.printStackTrace();
            return RestResponse.tip(CodeIbe.IBE_EXCEPTION, "");
        }
        JSONObject json = new JSONObject();
        json.put("ticketInfo",list);
        json.put(leftSeatskey,leftSeats-request.getPassengerIssueTicketList().size());

        return RestResponse.ok(json);
    }


    private FDBasePriceAndCabinPriceRS getBasePriceAndTax(AVRequest request, String passengerType) {
        String fdRedisKey = "FD-" + request.getCarrier()+request.getOrigin()+ request.getDestination() + DateUtils.formatDate(request.getDepart(),"yyyy-MM-dd");
        String redisKeyAdultFD = fdRedisKey+"-"+passengerType;
        Object adultFdResponseObj = RedisUtil.get(redisKeyAdultFD);
        FDBasePriceAndCabinPriceRS adultTax = null;
        if (adultFdResponseObj == null) {
            adultTax = pnrManager.fdPriceAndTax(request.getCarrier(),request.getOrigin(),request.getDestination(), DateUtils.formatDate(request.getDepart(),"yyyy-MM-dd"),passengerType);
            RedisUtil.set(redisKeyAdultFD, adultTax ,10*60);
            log.info("fd real "+ passengerType +":"+ redisKeyAdultFD);
        } else {
            adultTax = (FDBasePriceAndCabinPriceRS)adultFdResponseObj;
            log.info("fd hit"+ passengerType +":"+ redisKeyAdultFD);
        }
        return adultTax;
    }


    @ApiOperation(value = "通过pnr和航班旅客信息获取票号，未测试")
    @RequestMapping(value = "getTicketNo", method = RequestMethod.POST)
    RestResponse<BaseResponse> getTicketInfo(@ApiParam(value = "查询票号条件", required = true)
                                            @RequestBody QueryTicketNoRequest request){

        return RestResponse.ok(ibeManager.getTicketNo(request.getAirlineCode() ,request.getPnrNo(),
                request.getDepCode(), request.getFlightNo(), request.getFlightDate(),
                request.getPassengerName(), request.getIdCard()));
    }
}
