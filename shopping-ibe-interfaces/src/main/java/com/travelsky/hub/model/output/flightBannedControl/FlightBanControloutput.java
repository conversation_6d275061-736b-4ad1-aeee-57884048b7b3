/*    */ package com.travelsky.hub.model.output.flightBannedControl;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightBanControloutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -150267495503575887L;
/*    */   private String resultCode;
/*    */   private String resultList;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 36 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 43 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultList()
/*    */   {
/* 50 */     return this.resultList;
/*    */   }
/*    */ 
/*    */   public void setResultList(String resultList)
/*    */   {
/* 57 */     this.resultList = resultList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.flightBannedControl.FlightBanControloutput
 * JD-Core Version:    0.6.0
 */