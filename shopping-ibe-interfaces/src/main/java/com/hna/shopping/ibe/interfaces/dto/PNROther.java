package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNROther extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = 972897501347039991L;
	private  int index;                                                            
	 private String other;                                                     
	 private  String psgrid;                                                            
	                                                                           
	 public PNROther()                                                         
	   throws Exception                                                        
	 {                                                                         
	 }                                                                         
	                                                                           
	 public PNROther(String other)                                             
	   throws Exception                                                        
	 {                                                                         
	   this.other = other;                                                     
	 }                                                                         
	                                                                                   
	 public String toString()                                                  
	 {                                                                         
	   try                                                                     
	   {                                                                       
	     return this.index + "." + this.other; } catch (Exception e) {     
	   }                                                                       
	   return super.toString();                                                
	 }                                                                         
	                                                                           
                                                                    

}
