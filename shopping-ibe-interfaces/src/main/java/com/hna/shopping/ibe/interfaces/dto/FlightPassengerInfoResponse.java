package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 高频 iTES 接口航班旅客信息返回封装
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlightPassengerInfoResponse extends BaseResponse implements Serializable {

	private static final long serialVersionUID = 4486985990364668112L;

	/**
	 * 旅客信息
	 */
	private List<FlightPassengerInfo> flightPassengerInfoList = new ArrayList<FlightPassengerInfo>();


	/**
	 * 航班信息
	 */
	@Data
	public static class FlightInfo {
		/**
		 * 航班号
		 */
		private String flightNo;
		/**
		 * 出发地
		 */
		private String depCode;
		/**
		 * 目的地
		 */
		private String arrCode;
		/**
		 * 机型
		 */
		private String planeType;
		/**
		 * 飞机号
		 */
		private String planeNo;
		/**
		 * 旅客人数
		 */
		private int passengerNum;
		/**
		 * 航班状态
		 */
		private String flightStatus;
		/**
		 * 预计起飞时间
		 */
		private String depTime;
		/**
		 * 预计到达时间
		 */
		private String arrTime;

	}

	/**
	 * 旅客信息
	 */
	@Data
	public static class FlightPassengerInfo {
		/**
		 * 旅客姓名(TravelerName)
		 */
		private String passengerName;

		/**
		 * 性别(Gender)
		 */
		private String gender;

		/**
		 * 旅客类型（@Type）
		 */
		private String passengerType;

		/**
		 * 出票时间(TktIssueDate TktIssueTime)
		 */
		private String issueTime;

		/**
		 * 编码(crsNo icsNo)
		 */
		private String pnr;

		/**
		 * 票号(TicketNumber)
		 */
		private String ticketNo;

		/**
		 * 证件类型（IDType）
		 */
		private String certType;

		/**
		 * 证件号(IDNumber 国际取 PassportNum)
		 */
		private String certNo;

		/**
		 * 舱位(fClass)
		 */
		private String cabin;

		/**
		 * 子舱位(cabinType)
		 */
		private String subCabin;

		/**
		 * 票价（Total）
		 */
		private String price;

		/**
		 * 卡号（FFNumber）
		 */
		private String ffCardNo;

		/**
		 * 客票状态（@Status）
		 */
		private String status;

		/**
		 * 备注（DepartureCode-ArrivalCode  经停航班的，取实际起降地）
		 */
		private String remark;


		/**
		 * 航班信息
		 */
		private FlightInfo flightInfo;

	}
}
