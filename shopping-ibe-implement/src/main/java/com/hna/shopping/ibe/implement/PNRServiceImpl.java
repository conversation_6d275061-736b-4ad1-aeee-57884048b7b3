package com.hna.shopping.ibe.implement;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.hna.eking.util.redis.lock.RedisSessionUtil;
import com.hna.shopping.ibe.common.constant.RedisKeyConstants;
import com.hna.shopping.ibe.common.util.ConvertUtil;
import com.hna.shopping.ibe.interfaces.PNRService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.PNRManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Set;

/**
 * pnr操作业务
 */
@Service
@Slf4j
public class PNRServiceImpl implements PNRService {

    @Autowired
    private PNRManager pnrManager;

    /**
     * delPnrItem操作
     *
     * @param delPnrItemReq
     * @return
     */
    @Override
    public BaseResponse delPnrItem(DelPnrItemRequest delPnrItemReq) {
        Boolean lock = false;
        try {
            // 参数校验
            delPnrItemParamsVerify(delPnrItemReq);
            // pnrNo加锁处理
            lock = executePnrNoLock(delPnrItemReq.getPnrNo(), 10);
            // 获取rt结果
            RTResult rtRes = executeRT(ConvertUtil.map(delPnrItemReq, RTRequest.class));
            // 筛选删除下标项
            BaseResponse baseRes = executeDelPnrItem(delPnrItemReq, rtRes);
            // 反馈结果
            return baseRes;
        } catch (Exception e) {
            log.error("service delPnrItem error,params:{},error!", JSONObject.toJSONString(delPnrItemReq), e);
            BaseResponse baseRes = new BaseResponse();
            baseRes.setSuccess(false);
            baseRes.setErrorCode(e.getClass().getSimpleName());
            baseRes.setErrorInfo(e.getMessage());
            return baseRes;
        } finally {
            executePnrNoUnlock(delPnrItemReq.getPnrNo(), lock);
        }
    }

    /**
     * cancelPnr操作
     *
     * @param cancelPnrReq
     * @return
     */
    @Override
    public BaseResponse cancelPnr(CancelPnrRequest cancelPnrReq) {
        BaseResponse baseRes = new BaseResponse();
        Boolean exception = false;
        Boolean lock = false;
        try {
            // 参数校验
            cancelPnrParamsVerify(cancelPnrReq);
            // pnrNo加锁处理
            lock = executePnrNoLock(cancelPnrReq.getPnrNo(), 10);
            // 请求操作cancelPnr
            baseRes = pnrManager.cancelPnr(cancelPnrReq);
            // 反馈结果
            return baseRes;
        } catch (Exception e) {
            exception = true;
            log.error("service cancelPnr error,params:{},error!", JSONObject.toJSONString(cancelPnrReq), e);
            baseRes = new BaseResponse();
            baseRes.setSuccess(false);
            baseRes.setErrorCode(e.getClass().getSimpleName());
            baseRes.setErrorInfo(e.getMessage());
            return baseRes;
        } finally {
            executePnrNoUnlock(cancelPnrReq.getPnrNo(), lock);
            if (exception) {
                throw new RuntimeException(baseRes.getErrorInfo());
            }
        }
    }

    /**
     * 加锁处理
     *
     * @param pnrNo
     * @param time
     * @return
     */
    private Boolean executePnrNoLock(String pnrNo, int time) {
        Boolean lock = RedisSessionUtil.tryLock(RedisKeyConstants.LOCK_PNR_PREFIX + pnrNo, time);
        log.info("Redis handle,lock pnrNo:{},lock result:{},lock time:{}", pnrNo, lock, System.currentTimeMillis());
        if (!lock) {
            throw new RuntimeException("Redis pnrNo lock error !");
        }
        return true;
    }

    /**
     * 解锁处理
     *
     * @param pnrNo
     * @param lock
     * @return
     */
    private void executePnrNoUnlock(String pnrNo, Boolean lock) {
        log.info("Redis handle,unlock pnrNo:{},lock result:{},unlock time:{}", pnrNo, lock, System.currentTimeMillis());
        if (lock) RedisSessionUtil.unlock(RedisKeyConstants.LOCK_PNR_PREFIX + pnrNo);
    }

    /**
     * 参数校验
     *
     * @param delPnrItemReq
     */
    private void delPnrItemParamsVerify(DelPnrItemRequest delPnrItemReq) {
        // 简单校验参数
        if (Objects.isNull(delPnrItemReq)) {
            throw new RuntimeException("Request params[object] must not be null !");
        }
        if (StringUtils.isBlank(delPnrItemReq.getPnrNo())) {
            throw new RuntimeException("Request params[PnrNo] must not be null !");
        }
        if (CollectionUtils.isEmpty(delPnrItemReq.getContents())) {
            throw new RuntimeException("Request params[contents] must not be null !");
        }
    }

    /**
     * 获取rt结果
     *
     * @param rtRequest
     * @return
     */
    private RTResult executeRT(RTRequest rtRequest) {
        // rt结果
        RTResult rtRes = pnrManager.rt(rtRequest);
        log.info("service executeRT , RT result:{}", JSONObject.toJSONString(rtRes));
        if (Objects.isNull(rtRes) || !rtRes.isSuccess()) {
            throw new RuntimeException(rtRes.getErrorInfo());
        }
        return rtRes;
    }

    /**
     * 筛选内容删除pnr下标项
     *
     * @param delPnrItemReq
     * @param rtRes
     * @return
     */
    private BaseResponse executeDelPnrItem(DelPnrItemRequest delPnrItemReq, RTResult rtRes) {
        // 根据参数内容循环筛选删除（会有相同的content）
        Set<Integer> delIndexes = Sets.newHashSet();
        for (String content : delPnrItemReq.getContents()) {
            // 筛选ssr项
            for (PNRSSR ssr : rtRes.getSsrs()) {
                if (!delIndexes.contains(ssr.getIndex()) && Objects.equals(content, ssr.getTextInPNR())) {
                    delIndexes.add(ssr.getIndex());
                    break;
                }
            }
            // 筛选rmk项
            for (PNRRMK rmk : rtRes.getRmks()) {
                if (!delIndexes.contains(rmk.getIndex()) && Objects.equals(content, rmk.getTextInPNR())) {
                    delIndexes.add(rmk.getIndex());
                    break;
                }
            }
        }
        // 没有可执行删除的pnr下标
        if (CollectionUtils.isEmpty(delIndexes)) {
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setSuccess(true);
            baseResponse.setErrorInfo("There are no execute elements !");
            return baseResponse;
        }
        // 删除pnr下标项
        delPnrItemReq.setIndexes(delIndexes.stream().mapToInt(Integer::intValue).toArray());
        log.info("manager delPnrItem , params:{}", JSONObject.toJSONString(delPnrItemReq));
        return pnrManager.delPnrItem(delPnrItemReq);
    }

    /**
     * 参数校验
     *
     * @param cancelPnrReq
     */
    private void cancelPnrParamsVerify(CancelPnrRequest cancelPnrReq) {
        // 简单校验参数
        if (Objects.isNull(cancelPnrReq)) {
            throw new RuntimeException("Request params[object] must not be null !");
        }
        if (StringUtils.isBlank(cancelPnrReq.getPnrNo())) {
            throw new RuntimeException("Request params[PnrNo] must not be null !");
        }
    }
}
