/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CheckPsgOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 17 */   private ErrorBean errorResponse = new ErrorBean();
/*    */   private CheckedPsgBean checkedPassenger;
/*    */ 
/*    */   public ErrorBean getErrorResponse()
/*    */   {
/* 27 */     return this.errorResponse;
/*    */   }
/*    */ 
/*    */   public void setErrorResponse(ErrorBean errorResponse)
/*    */   {
/* 34 */     this.errorResponse = errorResponse;
/*    */   }
/*    */ 
/*    */   public CheckedPsgBean getCheckedPassenger()
/*    */   {
/* 41 */     return this.checkedPassenger;
/*    */   }
/*    */ 
/*    */   public void setCheckedPassenger(CheckedPsgBean checkedPassenger)
/*    */   {
/* 48 */     this.checkedPassenger = checkedPassenger;
/*    */   }
/*    */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckPsgOutput
 * JD-Core Version:    0.6.0
 */