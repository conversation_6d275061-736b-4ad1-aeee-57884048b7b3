/*
 * create on 2008-5-13
 * Copy right (2008)
 * HNA System All rights reserved
 */
package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class CabinDTO implements Serializable {
    private static final long serialVersionUID = 5848840765858511689L;

    @ApiModelProperty(value = "仓位")
    private String cabinName;

    @ApiModelProperty(value = "仓位数量, 原始数据")
    private String quantity;

    @ApiModelProperty(value = "仓位数量, 按 ET 习惯转换")
    private Integer inventory;

    @ApiModelProperty(value = "开仓情况")
    private String status;
}
