/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class QryExpressBagsInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1362742352754769788L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String departureAirport;
/*     */   private String bagTagNumber;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  53 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  60 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  67 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  74 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  81 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  88 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  95 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 102 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 109 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 116 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 123 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 130 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getBagTagNumber()
/*     */   {
/* 138 */     return this.bagTagNumber;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumber(String bagTagNumber)
/*     */   {
/* 145 */     this.bagTagNumber = bagTagNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 153 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 161 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.QryExpressBagsInputBean
 * JD-Core Version:    0.6.0
 */