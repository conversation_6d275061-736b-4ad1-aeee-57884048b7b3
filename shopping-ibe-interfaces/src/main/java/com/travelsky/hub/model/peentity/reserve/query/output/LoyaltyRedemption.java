/*    */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class LoyaltyRedemption
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7913406793824460545L;
/*    */   private String serialNumber;
/*    */   private String ffpCardNumber;
/*    */   private String airlineID;
/*    */   private String ffpLevel;
/*    */ 
/*    */   public String getSerialNumber()
/*    */   {
/* 44 */     return this.serialNumber;
/*    */   }
/*    */ 
/*    */   public void setSerialNumber(String serialNumber)
/*    */   {
/* 51 */     this.serialNumber = serialNumber;
/*    */   }
/*    */ 
/*    */   public String getFfpCardNumber()
/*    */   {
/* 58 */     return this.ffpCardNumber;
/*    */   }
/*    */ 
/*    */   public void setFfpCardNumber(String ffpCardNumber)
/*    */   {
/* 65 */     this.ffpCardNumber = ffpCardNumber;
/*    */   }
/*    */ 
/*    */   public String getAirlineID()
/*    */   {
/* 72 */     return this.airlineID;
/*    */   }
/*    */ 
/*    */   public void setAirlineID(String airlineID)
/*    */   {
/* 79 */     this.airlineID = airlineID;
/*    */   }
/*    */ 
/*    */   public String getFfpLevel()
/*    */   {
/* 86 */     return this.ffpLevel;
/*    */   }
/*    */ 
/*    */   public void setFfpLevel(String ffpLevel)
/*    */   {
/* 93 */     this.ffpLevel = ffpLevel;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.LoyaltyRedemption
 * JD-Core Version:    0.6.0
 */