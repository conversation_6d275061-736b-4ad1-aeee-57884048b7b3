/*     */ package com.travelsky.hub.model.output.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class FlightInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2596546074043880344L;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String departureDate;
/*     */   private String aircraftType;
/*     */   private String aircraftVersion;
/*     */   private String gate;
/*     */   private String brdGateType;
/*     */   private String boardingTime;
/*     */   private String scheduledDepartureTime;
/*     */   private String availableSpAircraft;
/*     */   private String checkinPADCounts;
/*     */   private String estimatedDepartureTime;
/*     */   private String scheduledArrivalTime;
/*     */   private String flightDuration;
/*     */   private String flightRemarks;
/*     */   private String airportRemarks;
/*     */   private List<Passengers> passengers;
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  63 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  70 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getAircraftType()
/*     */   {
/*  76 */     return this.aircraftType;
/*     */   }
/*     */ 
/*     */   public void setAircraftType(String aircraftType)
/*     */   {
/*  83 */     this.aircraftType = aircraftType;
/*     */   }
/*     */ 
/*     */   public String getAircraftVersion()
/*     */   {
/*  90 */     return this.aircraftVersion;
/*     */   }
/*     */ 
/*     */   public void setAircraftVersion(String aircraftVersion)
/*     */   {
/*  97 */     this.aircraftVersion = aircraftVersion;
/*     */   }
/*     */ 
/*     */   public String getGate()
/*     */   {
/* 104 */     return this.gate;
/*     */   }
/*     */ 
/*     */   public void setGate(String gate)
/*     */   {
/* 111 */     this.gate = gate;
/*     */   }
/*     */ 
/*     */   public String getAirline()
/*     */   {
/* 117 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/* 124 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getBrdGateType()
/*     */   {
/* 131 */     return this.brdGateType;
/*     */   }
/*     */ 
/*     */   public void setBrdGateType(String brdGateType)
/*     */   {
/* 138 */     this.brdGateType = brdGateType;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 145 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 152 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getScheduledDepartureTime()
/*     */   {
/* 159 */     return this.scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public void setScheduledDepartureTime(String scheduledDepartureTime)
/*     */   {
/* 166 */     this.scheduledDepartureTime = scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public String getAvailableSpAircraft()
/*     */   {
/* 173 */     return this.availableSpAircraft;
/*     */   }
/*     */ 
/*     */   public void setAvailableSpAircraft(String availableSpAircraft)
/*     */   {
/* 180 */     this.availableSpAircraft = availableSpAircraft;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 186 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 193 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getCheckinPADCounts()
/*     */   {
/* 199 */     return this.checkinPADCounts;
/*     */   }
/*     */ 
/*     */   public void setCheckinPADCounts(String checkinPADCounts)
/*     */   {
/* 206 */     this.checkinPADCounts = checkinPADCounts;
/*     */   }
/*     */ 
/*     */   public String getEstimatedDepartureTime()
/*     */   {
/* 213 */     return this.estimatedDepartureTime;
/*     */   }
/*     */ 
/*     */   public void setEstimatedDepartureTime(String estimatedDepartureTime)
/*     */   {
/* 220 */     this.estimatedDepartureTime = estimatedDepartureTime;
/*     */   }
/*     */ 
/*     */   public String getScheduledArrivalTime()
/*     */   {
/* 227 */     return this.scheduledArrivalTime;
/*     */   }
/*     */ 
/*     */   public void setScheduledArrivalTime(String scheduledArrivalTime)
/*     */   {
/* 234 */     this.scheduledArrivalTime = scheduledArrivalTime;
/*     */   }
/*     */ 
/*     */   public String getFlightDuration()
/*     */   {
/* 241 */     return this.flightDuration;
/*     */   }
/*     */ 
/*     */   public void setFlightDuration(String flightDuration)
/*     */   {
/* 248 */     this.flightDuration = flightDuration;
/*     */   }
/*     */ 
/*     */   public String getFlightRemarks()
/*     */   {
/* 255 */     return this.flightRemarks;
/*     */   }
/*     */ 
/*     */   public void setFlightRemarks(String flightRemarks)
/*     */   {
/* 262 */     this.flightRemarks = flightRemarks;
/*     */   }
/*     */ 
/*     */   public String getAirportRemarks()
/*     */   {
/* 269 */     return this.airportRemarks;
/*     */   }
/*     */ 
/*     */   public void setAirportRemarks(String airportRemarks)
/*     */   {
/* 276 */     this.airportRemarks = airportRemarks;
/*     */   }
/*     */ 
/*     */   public List<Passengers> getPassengers()
/*     */   {
/* 283 */     return this.passengers;
/*     */   }
/*     */ 
/*     */   public void setPassengers(List<Passengers> passengers)
/*     */   {
/* 290 */     this.passengers = passengers;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 297 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 304 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.FlightInformation
 * JD-Core Version:    0.6.0
 */