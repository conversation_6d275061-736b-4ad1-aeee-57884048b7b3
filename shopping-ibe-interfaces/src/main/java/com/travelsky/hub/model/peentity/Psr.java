/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Psr
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String psrName;
/*     */   private String pnr;
/*     */   private String ticketNumber;
/*     */   private String tourIndex;
/*     */   private String certType;
/*     */   private String certNo;
/*     */   private String contactInfo;
/*     */   private String psrLevel;
/*     */   private String cabin;
/*     */   private String seat;
/*     */   private String cardNumber;
/*     */   private String boardingnum;
/*     */   private String nationality;
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/*  47 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/*  54 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/*  61 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/*  68 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/*  75 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/*  82 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/*  89 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/*  96 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 103 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 110 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 117 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 124 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/* 131 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/* 138 */     this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/* 145 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/* 152 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 159 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 166 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getSeat()
/*     */   {
/* 173 */     return this.seat;
/*     */   }
/*     */ 
/*     */   public void setSeat(String seat)
/*     */   {
/* 180 */     this.seat = seat;
/*     */   }
/*     */ 
/*     */   public String getCardNumber()
/*     */   {
/* 187 */     return this.cardNumber;
/*     */   }
/*     */ 
/*     */   public void setCardNumber(String cardNumber)
/*     */   {
/* 194 */     this.cardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingnum()
/*     */   {
/* 201 */     return this.boardingnum;
/*     */   }
/*     */ 
/*     */   public void setBoardingnum(String boardingnum)
/*     */   {
/* 208 */     this.boardingnum = boardingnum;
/*     */   }
/*     */ 
/*     */   public String getNationality()
/*     */   {
/* 216 */     return this.nationality;
/*     */   }
/*     */ 
/*     */   public void setNationality(String nationality)
/*     */   {
/* 224 */     this.nationality = nationality;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.Psr
 * JD-Core Version:    0.6.0
 */