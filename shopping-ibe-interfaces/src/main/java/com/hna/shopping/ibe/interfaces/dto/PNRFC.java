package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNRFC extends FareCalculation {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4996614226711678025L;
	
	private String psgrid = null;                                                                
    
	private int index = -1;                                                                      
	                                                                                     
	private String inputMode = "";                                                               
	                                                                                     
	private String textInPNR = "N/A";                                                    
	                                                                                                 
	                                                                                     
	public String makeString() {                                                         
	  return super.makeString() + "\r\n输入模式：" + this.inputMode;                     
	}                                                                                    
	                                                                                        

}
