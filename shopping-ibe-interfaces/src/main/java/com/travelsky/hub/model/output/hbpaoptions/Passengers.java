/*     */ package com.travelsky.hub.model.output.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Passengers
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7054337610134646499L;
/*     */   private String passengerNums;
/*     */   private String passengerName;
/*     */   private String passengerFullName;
/*     */   private String groupName;
/*     */   private String groupCount;
/*     */   private PassengerStatus passengerStatus;
/*     */   private IdentityInformation identityInformation;
/*     */   private List<SeatInformation> seatInformation;
/*     */   private List<ElectronicTicketInformation> electronicTicketInformation;
/*     */   private PassengerAttribute passengerAttribute;
/*     */   private MarketingInformation marketingInformation;
/*     */   private RemarksInformation remarksInformation;
/*     */   private List<ContactInformation> contactInformation;
/*     */   private List<CheckinRemarkInformation> checkinRemarkInformation;
/*     */   private SpecialMealsInformation specialMealsInformation;
/*     */   private List<FrequentFlyerInformation> frequentFlyerInformation;
/*     */   private List<StopOverInfo> stopOverInfoList;
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/*  63 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/*  70 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getPassengerFullName()
/*     */   {
/*  77 */     return this.passengerFullName;
/*     */   }
/*     */ 
/*     */   public void setPassengerFullName(String passengerFullName)
/*     */   {
/*  84 */     this.passengerFullName = passengerFullName;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/*  91 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/*  98 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getGroupCount()
/*     */   {
/* 105 */     return this.groupCount;
/*     */   }
/*     */ 
/*     */   public void setGroupCount(String groupCount)
/*     */   {
/* 112 */     this.groupCount = groupCount;
/*     */   }
/*     */ 
/*     */   public PassengerStatus getPassengerStatus()
/*     */   {
/* 119 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(PassengerStatus passengerStatus)
/*     */   {
/* 126 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public IdentityInformation getIdentityInformation()
/*     */   {
/* 133 */     return this.identityInformation;
/*     */   }
/*     */ 
/*     */   public void setIdentityInformation(IdentityInformation identityInformation)
/*     */   {
/* 140 */     this.identityInformation = identityInformation;
/*     */   }
/*     */ 
/*     */   public List<SeatInformation> getSeatInformation()
/*     */   {
/* 147 */     return this.seatInformation;
/*     */   }
/*     */ 
/*     */   public void setSeatInformation(List<SeatInformation> seatInformation)
/*     */   {
/* 154 */     this.seatInformation = seatInformation;
/*     */   }
/*     */ 
/*     */   public List<ElectronicTicketInformation> getElectronicTicketInformation()
/*     */   {
/* 161 */     return this.electronicTicketInformation;
/*     */   }
/*     */ 
/*     */   public void setElectronicTicketInformation(List<ElectronicTicketInformation> electronicTicketInformation)
/*     */   {
/* 168 */     this.electronicTicketInformation = electronicTicketInformation;
/*     */   }
/*     */ 
/*     */   public PassengerAttribute getPassengerAttribute()
/*     */   {
/* 175 */     return this.passengerAttribute;
/*     */   }
/*     */ 
/*     */   public void setPassengerAttribute(PassengerAttribute passengerAttribute)
/*     */   {
/* 182 */     this.passengerAttribute = passengerAttribute;
/*     */   }
/*     */ 
/*     */   public MarketingInformation getMarketingInformation()
/*     */   {
/* 189 */     return this.marketingInformation;
/*     */   }
/*     */ 
/*     */   public void setMarketingInformation(MarketingInformation marketingInformation)
/*     */   {
/* 196 */     this.marketingInformation = marketingInformation;
/*     */   }
/*     */ 
/*     */   public RemarksInformation getRemarksInformation()
/*     */   {
/* 203 */     return this.remarksInformation;
/*     */   }
/*     */ 
/*     */   public void setRemarksInformation(RemarksInformation remarksInformation)
/*     */   {
/* 210 */     this.remarksInformation = remarksInformation;
/*     */   }
/*     */ 
/*     */   public List<ContactInformation> getContactInformation()
/*     */   {
/* 217 */     return this.contactInformation;
/*     */   }
/*     */ 
/*     */   public void setContactInformation(List<ContactInformation> contactInformation)
/*     */   {
/* 224 */     this.contactInformation = contactInformation;
/*     */   }
/*     */ 
/*     */   public List<CheckinRemarkInformation> getCheckinRemarkInformation()
/*     */   {
/* 231 */     return this.checkinRemarkInformation;
/*     */   }
/*     */ 
/*     */   public void setCheckinRemarkInformation(List<CheckinRemarkInformation> checkinRemarkInformation)
/*     */   {
/* 238 */     this.checkinRemarkInformation = checkinRemarkInformation;
/*     */   }
/*     */ 
/*     */   public SpecialMealsInformation getSpecialMealsInformation()
/*     */   {
/* 245 */     return this.specialMealsInformation;
/*     */   }
/*     */ 
/*     */   public void setSpecialMealsInformation(SpecialMealsInformation specialMealsInformation)
/*     */   {
/* 252 */     this.specialMealsInformation = specialMealsInformation;
/*     */   }
/*     */ 
/*     */   public List<FrequentFlyerInformation> getFrequentFlyerInformation()
/*     */   {
/* 259 */     return this.frequentFlyerInformation;
/*     */   }
/*     */ 
/*     */   public void setFrequentFlyerInformation(List<FrequentFlyerInformation> frequentFlyerInformation)
/*     */   {
/* 266 */     this.frequentFlyerInformation = frequentFlyerInformation;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOverInfoList()
/*     */   {
/* 273 */     return this.stopOverInfoList;
/*     */   }
/*     */ 
/*     */   public void setStopOverInfoList(List<StopOverInfo> stopOverInfoList)
/*     */   {
/* 280 */     this.stopOverInfoList = stopOverInfoList;
/*     */   }
/*     */ 
/*     */   public String getPassengerNums()
/*     */   {
/* 287 */     return this.passengerNums;
/*     */   }
/*     */ 
/*     */   public void setPassengerNums(String passengerNums)
/*     */   {
/* 294 */     this.passengerNums = passengerNums;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.Passengers
 * JD-Core Version:    0.6.0
 */