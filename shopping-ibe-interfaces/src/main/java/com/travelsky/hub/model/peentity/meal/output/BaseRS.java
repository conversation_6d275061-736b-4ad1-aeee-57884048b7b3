/*    */ package com.travelsky.hub.model.peentity.meal.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BaseRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 5259520305819560362L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 26 */     return this.resultCode;
/*    */   }
/*    */   public void setResultCode(String resultCode) {
/* 29 */     this.resultCode = resultCode;
/*    */   }
/*    */   public String getResultMessage() {
/* 32 */     return this.resultMessage;
/*    */   }
/*    */   public void setResultMessage(String resultMessage) {
/* 35 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.BaseRS
 * JD-Core Version:    0.6.0
 */