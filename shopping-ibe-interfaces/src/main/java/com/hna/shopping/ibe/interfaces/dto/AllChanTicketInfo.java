package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 通过 IBE 获取的票面信息，全渠道专用
 */
@Data
public class AllChanTicketInfo implements Serializable {


    private static final long serialVersionUID = -633218572513217058L;
    /**
     * 出发地三字码
     */
    private String depCode;

    /**
     * 目的地三字码
     */
    private String arrCode;

    /**
     * 航班号
     */
    private String flightNo;

    /**
     * 航班日期
     */
    private Date flightDate;

    /**
     * 起飞时间
     */
    private Date depTime;

    /**
     * 到达时间
     */
    private Date arrTime;

    /**
     * 结算码
     */
    private String issCode;

    /**
     * 票号
     */
    private String ticketNo;

    /**
     * 客票状态
     */
    private String ticketStatus;

    /**
     * 编码
     */
    private String pnr;

    /**
     * 编码状态
     */
    private String pnrStatus;

    /**
     * 实付价
     */
    private BigDecimal netFare;

    /**
     * 票面价
     */
    private BigDecimal marketFare;

    /**
     * 燃油费
     */
    private BigDecimal fuelTax;

    /**
     * 基建费
     */
    private BigDecimal airportTax;

    /**
     * 舱位
     */
    private String cabin;

    /**
     *  参考舱位，用于算退票手续费
     */
    private String refCabin;

    /**
     * 预定时间
     */
    private Date bookTime;

    /**
     * 出票时间
     */
    private Date issueDate;


    /**
     * 清位时间
     */
    private Date clearDate;

    /**
     * 产品信息
     */
    private String productCode;

    /**
     * office号
     */
    private String office;

    /**
     * 航司，用于判断uni账号是否可以操作客票
     */
    private String airlineCode;
    /**
     * 旅客类型，用于计算手续费，IBE需要旅客类型
     */
    private String passengerType;

    /**
     * 旅客姓名
     */
    private String passengerName;

    /**
     * 证件号
     */
    private String passengerID;
    /**
     * ei项
     */
    private String ei;

    /**
     * 旅游代码
     */
    private String tourCode;

    /**
     * 航段号
     */
    private  int segmentIndex;

    /**
     * 表示婴儿的随行成人的票号（不带结算码，形如 ：2145281124）
     */
    private String accompaniedAuditTicketNo;

    /**
     * 表示婴儿的随行成人的票号（不带结算码，形如 ：2145281124）
     */
    private String accompaniedInfantTicketNo;

    /**
     * 历史票面信息(改签历史)（按从新到旧的顺序放入 List）
     */
    List<AllChanTicketInfo> historyTicketInfo;

    /**
     * rt结果
     */
    private List<DETRTax> taxs = new ArrayList<DETRTax>();

    /**
     * 到达航站楼
     */
    private String arrTerm;

    /**
     * 出发航站楼
     */
    private String depTerm;

    /**
     * EI项
     */
    private String signingInfo;

    /**
     * 优惠券信息
     */
    private String conponMsg;

    /**
     * 证件信息
     */
    DETRCreResult creResult;

    private Date birthDay;

    /**
     * 成人返回携带的婴儿
     */
    private AllChanTicketInfo infantInfo;

    /**
     * 婴儿返回随行的成人
     */
    private AllChanTicketInfo adtInfo;

    /**
     * 销售商渠道，10表示本票，12代理BSP票
     */
    private int ticketType;

    /**
     * 票价级别
     */
    private String rate;
    
    private boolean containIrr;//验证是否含有irr标识

    private boolean containInv;//验证是否含有ivv标识

}
