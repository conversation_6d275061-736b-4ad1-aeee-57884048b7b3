package com.hna.shopping.ibe.interfaces.dto.pricing.request;

import com.travelsky.ebuild.clientapi.axi.PIPassenger;
import lombok.Data;

import java.io.Serializable;
import java.util.List;



@Data
public class PricingRequest  implements Serializable {
    private static final long serialVersionUID = 1L;
    // 航段
    private List<PIFlightSegment> flightSegments;
    // 旅客
    private List<PIPassenger> passengers;
    // 是否私有价格
    private Boolean privatePrice = null;
    // 货币
    private String currency = "CNY";
    // 大客户编码 私有运价用到
    private String tourCode;



}