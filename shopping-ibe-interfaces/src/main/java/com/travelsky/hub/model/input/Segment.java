/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Segment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String index;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String suffix;
/*     */   private String deptCity;
/*     */   private String deptDate;
/*     */   private String deptTime;
/*     */   private String arrivalCity;
/*     */   private String arrivalDate;
/*     */   private String arrivalTime;
/*     */   private Boolean transferInd;
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  63 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  70 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  77 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  84 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  91 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  98 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getSuffix()
/*     */   {
/* 105 */     return this.suffix;
/*     */   }
/*     */ 
/*     */   public void setSuffix(String suffix)
/*     */   {
/* 112 */     this.suffix = suffix;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/* 119 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 126 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getDeptDate()
/*     */   {
/* 133 */     return this.deptDate;
/*     */   }
/*     */ 
/*     */   public void setDeptDate(String deptDate)
/*     */   {
/* 140 */     this.deptDate = deptDate;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 147 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 154 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalCity()
/*     */   {
/* 161 */     return this.arrivalCity;
/*     */   }
/*     */ 
/*     */   public void setArrivalCity(String arrivalCity)
/*     */   {
/* 168 */     this.arrivalCity = arrivalCity;
/*     */   }
/*     */ 
/*     */   public String getArrivalDate()
/*     */   {
/* 175 */     return this.arrivalDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalDate(String arrivalDate)
/*     */   {
/* 182 */     this.arrivalDate = arrivalDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 189 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String arrivalTime)
/*     */   {
/* 196 */     this.arrivalTime = arrivalTime;
/*     */   }
/*     */ 
/*     */   public Boolean getTransferInd()
/*     */   {
/* 203 */     return this.transferInd;
/*     */   }
/*     */ 
/*     */   public void setTransferInd(Boolean transferInd)
/*     */   {
/* 210 */     this.transferInd = transferInd;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.input.Segment
 * JD-Core Version:    0.6.0
 */