/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class DetrInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 15 */   private String certificateType = "NI";
/*    */   private String certificateNumber;
/*    */   private String isGroup;
/*    */ 
/*    */   public String getCertificateNumber()
/*    */   {
/* 26 */     return this.certificateNumber;
/*    */   }
/*    */ 
/*    */   public void setCertificateNumber(String certificateNumber)
/*    */   {
/* 34 */     this.certificateNumber = certificateNumber;
/*    */   }
/*    */ 
/*    */   public String getCertificateType()
/*    */   {
/* 41 */     return this.certificateType;
/*    */   }
/*    */ 
/*    */   public void setCertificateType(String certificateType)
/*    */   {
/* 49 */     this.certificateType = certificateType;
/*    */   }
/*    */ 
/*    */   public String getIsGroup()
/*    */   {
/* 56 */     return this.isGroup;
/*    */   }
/*    */ 
/*    */   public void setIsGroup(String isGroup)
/*    */   {
/* 64 */     this.isGroup = isGroup;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.DetrInputBean
 * JD-Core Version:    0.6.0
 */