/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FellowPassenger
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String surname;
/*     */   private String flightClass;
/*     */   private String gender;
/*     */   private String tktNumber;
/*     */   private String chd;
/*     */   private String index;
/*     */   private String hostNum;
/*     */   private String firstTktNumber;
/*     */   private String firstFlightClass;
/*     */ 
/*     */   public String getSurname()
/*     */   {
/*  56 */     return this.surname;
/*     */   }
/*     */ 
/*     */   public void setSurname(String surname)
/*     */   {
/*  63 */     this.surname = surname;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  70 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  77 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/*  84 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/*  91 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/*  98 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 105 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getIndex()
/*     */   {
/* 112 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/* 119 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 126 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 133 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 140 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 147 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getFirstTktNumber()
/*     */   {
/* 154 */     return this.firstTktNumber;
/*     */   }
/*     */ 
/*     */   public void setFirstTktNumber(String firstTktNumber)
/*     */   {
/* 161 */     this.firstTktNumber = firstTktNumber;
/*     */   }
/*     */ 
/*     */   public String getFirstFlightClass()
/*     */   {
/* 168 */     return this.firstFlightClass;
/*     */   }
/*     */ 
/*     */   public void setFirstFlightClass(String firstFlightClass)
/*     */   {
/* 175 */     this.firstFlightClass = firstFlightClass;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FellowPassenger
 * JD-Core Version:    0.6.0
 */