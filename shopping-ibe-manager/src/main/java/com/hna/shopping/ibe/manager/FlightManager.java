package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.AVRequest;
import com.hna.shopping.ibe.interfaces.dto.AVResponse;
import com.hna.shopping.ibe.interfaces.dto.FFRequest;
import com.hna.shopping.ibe.interfaces.dto.FFResponse;

import com.hna.shopping.ibe.interfaces.dto.pricing.request.PricingRequest;
import com.hna.shopping.ibe.interfaces.dto.pricing.response.PricingResponse;
import com.hna.shopping.ibe.interfaces.dto.searchone.request.SearchOneRequest;
import com.hna.shopping.ibe.interfaces.dto.searchone.response.SearchOneResponse;



public interface FlightManager {

	/**
	 * @param request
	 * @return
	 */
	AVResponse av(AVRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	FFResponse ff(FFRequest request);

	SearchOneResponse searchOne(SearchOneRequest request,String airline);

	PricingResponse pricing(PricingRequest request, String airline);
}
