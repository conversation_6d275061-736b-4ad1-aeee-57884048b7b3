package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class DtoTicketInfoResponse  extends BaseResponse  implements Serializable {

	private static final long serialVersionUID = 4486985990364668112L;
	private List<TicketInfo> ticketInfos=new ArrayList<TicketInfo>();
	private String source;

}
