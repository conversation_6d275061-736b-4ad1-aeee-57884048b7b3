/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Reference
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9077807027968764120L;
/*    */   private String sysID;
/*    */   private String recordValue;
/*    */ 
/*    */   public String getSysID()
/*    */   {
/* 41 */     return this.sysID;
/*    */   }
/*    */ 
/*    */   public void setSysID(String sysID)
/*    */   {
/* 48 */     this.sysID = sysID;
/*    */   }
/*    */ 
/*    */   public String getRecordValue()
/*    */   {
/* 55 */     return this.recordValue;
/*    */   }
/*    */ 
/*    */   public void setRecordValue(String recordValue)
/*    */   {
/* 62 */     this.recordValue = recordValue;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Reference
 * JD-Core Version:    0.6.0
 */