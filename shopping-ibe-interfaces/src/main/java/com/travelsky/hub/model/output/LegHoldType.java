/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class LegHoldType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 507163068777799465L;
/*    */   private String station;
/*    */   private List<HoldType> holdType;
/*    */ 
/*    */   public String getStation()
/*    */   {
/* 29 */     return this.station;
/*    */   }
/*    */ 
/*    */   public void setStation(String station)
/*    */   {
/* 36 */     this.station = station;
/*    */   }
/*    */ 
/*    */   public List<HoldType> getHoldType()
/*    */   {
/* 43 */     return this.holdType;
/*    */   }
/*    */ 
/*    */   public void setHoldType(List<HoldType> holdType)
/*    */   {
/* 50 */     this.holdType = holdType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.LegHoldType
 * JD-Core Version:    0.6.0
 */