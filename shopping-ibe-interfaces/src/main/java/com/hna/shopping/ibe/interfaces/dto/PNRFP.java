package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNRFP extends PNRObject {

	/**
	 * 
	 */
	private static final long serialVersionUID = -5239404448436271066L;
	private boolean infant = false;                                                                                                                                                  
    
	private String psgrid = null;                                                                                                                                                    
	private String currency;                                                                                                                                                         
	private String paytype;                                                                                                                                                          
	private int index;                                                                                                                                                           
	private String remark;                                                                                                                                                           
	                                                                                                                                                                   
	public String toString()                                                                                                                                                 
	{                                                                                                                                                                        
	  try                                                                                                                                                                    
	  {                                                                                                                                                                      
	    return String.valueOf(this.index) + ".FP " + ((this.infant) ? "/IN/" : "") + this.paytype +                                                                      
	      ((this.currency == null) ? "" : new StringBuilder(",").append(this.currency).toString()) + "  " + this.psgrid + " 原文:" + getRemark(); } catch (Exception e) {    
	  }                                                                                                                                                                      
	  return super.toString();                                                                                                                                               
	}                                                                                                                                                                        

}
