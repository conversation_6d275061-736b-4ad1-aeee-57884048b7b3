/*    */ package com.travelsky.hub.model.peentity.meal.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class MealStatusInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3680482986781195576L;
/*    */   private String mealStatus;
/*    */   private String mealInventory;
/*    */   private String mealControlUnit;
/*    */   private List<String> mealSubProducts;
/*    */ 
/*    */   public String getMealStatus()
/*    */   {
/* 49 */     return this.mealStatus;
/*    */   }
/*    */ 
/*    */   public void setMealStatus(String mealStatus)
/*    */   {
/* 56 */     this.mealStatus = mealStatus;
/*    */   }
/*    */ 
/*    */   public String getMealInventory()
/*    */   {
/* 63 */     return this.mealInventory;
/*    */   }
/*    */ 
/*    */   public void setMealInventory(String mealInventory)
/*    */   {
/* 70 */     this.mealInventory = mealInventory;
/*    */   }
/*    */ 
/*    */   public String getMealControlUnit()
/*    */   {
/* 77 */     return this.mealControlUnit;
/*    */   }
/*    */ 
/*    */   public void setMealControlUnit(String mealControlUnit)
/*    */   {
/* 84 */     this.mealControlUnit = mealControlUnit;
/*    */   }
/*    */ 
/*    */   public List<String> getMealSubProducts()
/*    */   {
/* 91 */     return this.mealSubProducts;
/*    */   }
/*    */ 
/*    */   public void setMealSubProducts(List<String> mealSubProducts)
/*    */   {
/* 98 */     this.mealSubProducts = mealSubProducts;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.MealStatusInfo
 * JD-Core Version:    0.6.0
 */