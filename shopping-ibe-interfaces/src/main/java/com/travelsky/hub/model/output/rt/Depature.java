/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Depature
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8754373176045096672L;
/*    */   private String depatureDayChange;
/*    */   private String aircraftScheduledDateTime;
/*    */   private String origin;
/*    */   private String terminalName;
/*    */ 
/*    */   public String getDepatureDayChange()
/*    */   {
/* 42 */     return this.depatureDayChange;
/*    */   }
/*    */ 
/*    */   public void setDepatureDayChange(String depatureDayChange)
/*    */   {
/* 49 */     this.depatureDayChange = depatureDayChange;
/*    */   }
/*    */ 
/*    */   public String getAircraftScheduledDateTime()
/*    */   {
/* 56 */     return this.aircraftScheduledDateTime;
/*    */   }
/*    */ 
/*    */   public void setAircraftScheduledDateTime(String aircraftScheduledDateTime)
/*    */   {
/* 63 */     this.aircraftScheduledDateTime = aircraftScheduledDateTime;
/*    */   }
/*    */ 
/*    */   public String getOrigin()
/*    */   {
/* 70 */     return this.origin;
/*    */   }
/*    */ 
/*    */   public void setOrigin(String origin)
/*    */   {
/* 77 */     this.origin = origin;
/*    */   }
/*    */ 
/*    */   public String getTerminalName()
/*    */   {
/* 84 */     return this.terminalName;
/*    */   }
/*    */ 
/*    */   public void setTerminalName(String terminalName)
/*    */   {
/* 91 */     this.terminalName = terminalName;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Depature
 * JD-Core Version:    0.6.0
 */