/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerAttribute
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -612780392351351927L;
/*    */   private String itemName;
/*    */   private String itemValue;
/*    */ 
/*    */   public String getItemName()
/*    */   {
/* 33 */     return this.itemName;
/*    */   }
/*    */ 
/*    */   public void setItemName(String itemName)
/*    */   {
/* 40 */     this.itemName = itemName;
/*    */   }
/*    */ 
/*    */   public String getItemValue()
/*    */   {
/* 47 */     return this.itemValue;
/*    */   }
/*    */ 
/*    */   public void setItemValue(String itemValue)
/*    */   {
/* 54 */     this.itemValue = itemValue;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.PassengerAttribute
 * JD-Core Version:    0.6.0
 */