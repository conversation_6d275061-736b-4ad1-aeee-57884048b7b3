/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PWResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private long errorCode;
/*    */   private String errorMsg;
/*    */ 
/*    */   public void clear()
/*    */   {
/* 30 */     this.errorCode = 0L;
/* 31 */     this.errorMsg = "";
/*    */   }
/*    */ 
/*    */   public long getErrorCode()
/*    */   {
/* 38 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(long errorCode)
/*    */   {
/* 45 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 52 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 59 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PWResult
 * JD-Core Version:    0.6.0
 */