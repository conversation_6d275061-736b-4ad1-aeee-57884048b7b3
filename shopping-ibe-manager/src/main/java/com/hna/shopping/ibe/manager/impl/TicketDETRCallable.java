package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.interfaces.dto.DETRTktResult;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETR2F;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;

/**
 * Zero
 * 2019/8/22 9:51
 **/
@Slf4j
public class TicketDETRCallable implements Callable<DETRTKTResult>{

    private CountDownLatch countDownLatch;
    private DETR2F detr2f;
    private String ticketNo;
    private String secondFactorCode;
    private String secondFactorValue;

    public TicketDETRCallable(CountDownLatch countDownLatch, DETR2F detr2f, String ticketNo, String secondFactorCode, String secondFactorValue){
        this.countDownLatch = countDownLatch;
        this.detr2f = detr2f;
        this.ticketNo = ticketNo;
        this.secondFactorCode = secondFactorCode;
        this.secondFactorValue = secondFactorValue;
    }

    @Override
    public DETRTKTResult call() throws Exception {
        long currentTime = System.currentTimeMillis();
        DETRTKTResult result = detr2f.getTicketInfoByTktNo2F(ticketNo, false, "", "N", secondFactorCode, secondFactorValue);
        countDownLatch.countDown();
        log.info("detr ticketNo:"+ticketNo+"|take time:" + (System.currentTimeMillis() - currentTime) + "ms");
        return result;
    }
}
