/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ApiUpdateOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private long errorCode;
/*    */   private String errorMsg;
/*    */ 
/*    */   public long getErrorCode()
/*    */   {
/* 32 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(long errorCode)
/*    */   {
/* 39 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 46 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 53 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 59 */     this.errorCode = 0L;
/* 60 */     this.errorMsg = "";
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ApiUpdateOutputBean
 * JD-Core Version:    0.6.0
 */