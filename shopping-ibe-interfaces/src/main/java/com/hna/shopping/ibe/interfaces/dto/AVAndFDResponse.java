package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

@Data
public class AVAndFDResponse {
    private String airline;
    private String depCode;
    private String arrCode;
    private String flightDate;
    private String flightNo;
    private String depTime;
    private String arrTime;
    private String depTerminal;
    private String arrTerminal;

    //默认Y舱
    private String cabin;
    //Y舱基础运价
    private Integer ybPrice;

    //成人税费
    private String adultFuelTax;
    private String adultAirportTax;
    //儿童税费
    private String childFuelTax;
    private String childAirportTax;
    //婴儿税费
    private String infFuelTax;
    private String infAirport;

    //公布运价最低价和舱位
    private String lowestCabin;
    private Integer lowestPrice;

    //经停次数
    private int stop;
    //跨天+1
    private String arriTimeModify;
    private String planeStyle;
}
