package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.IssueTicketPassenger;
import com.hna.shopping.ibe.interfaces.dto.IssueTicketSegment;
import com.hna.shopping.ibe.interfaces.dto.SegmentDTO;
import com.travelsky.ibe.exceptions.IBEException;

import java.util.Date;
import java.util.List;

/**
 * Ibe 接口
 *
 * <AUTHOR>
 */
public interface IbeManager {
    /**
     * 一次 av 查询，走缓存
     *
     * @param airline
     * @param orgCity
     * @param dstCity
     * @param flightDate
     * @param isDirect
     * @return
     */
    List<SegmentDTO> av(String airline, String orgCity, String dstCity, Date flightDate,
                        boolean isDirect, String sellerChannels, String allAirline);

    List<SegmentDTO> av(String supplier, String airline, String orgCity, String dstCity, Date flightDate,
                        boolean isDirect, String sellerChannels, String allAirline);

    void avAsync(String airline, String orgCity, String dstCity, Date flightDate,
                 boolean isDirect, String sellerChannels, String allAirline);

    //    List<SegmentDTO> avCache(String airline, String orgCity, String dstCity, Date flightDate,
//                        boolean isDirect);
    String getCacheKey(String airline, String orgCity, String dstCity, Date flightDate);

    List<IssueTicketPassenger> sellSeatAndIssueTicket(String airline, List<IssueTicketSegment> issueTicketSegmentList,
                                                      List<IssueTicketPassenger> passengerName, String contactInfo,
                                                      int timeLimitMinutes) throws IBEException;

    String getTicketNo(String airline ,String pnrNo, String depCode,String flightNo, String flightDate, String passengerName, String idNo);
}
