/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.ArrayList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class PsrDetailOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3980539238904371510L;
/* 22 */   private List passengers = new ArrayList();
/*    */ 
/*    */   public List getPassengers()
/*    */   {
/* 29 */     return this.passengers;
/*    */   }
/*    */ 
/*    */   public void setPassengers(List passengers)
/*    */   {
/* 36 */     this.passengers = passengers;
/*    */   }
/*    */ 
/*    */   public PRPsrBean getPassenger(int i)
/*    */   {
/* 44 */     return (PRPsrBean)this.passengers.get(i);
/*    */   }
/*    */ 
/*    */   public void addPassenger(PRPsrBean i)
/*    */   {
/* 51 */     this.passengers.add(i);
/*    */   }
/*    */ 
/*    */   public int getPassengersSize()
/*    */   {
/* 58 */     return this.passengers.size();
/*    */   }
/*    */ 
/*    */   public void clearPassengers()
/*    */   {
/* 64 */     this.passengers.clear();
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 70 */     clearPassengers();
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PsrDetailOutputBean
 * JD-Core Version:    0.6.0
 */