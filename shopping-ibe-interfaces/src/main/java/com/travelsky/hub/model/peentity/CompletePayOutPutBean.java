/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CompletePayOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String isSuccess;
/*    */   private String orderNum;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 22 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 29 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 38 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 45 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 54 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 61 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 70 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 77 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 86 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 93 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CompletePayOutPutBean
 * JD-Core Version:    0.6.0
 */