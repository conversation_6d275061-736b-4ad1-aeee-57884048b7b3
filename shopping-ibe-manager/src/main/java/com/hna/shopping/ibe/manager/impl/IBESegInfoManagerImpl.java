package com.hna.shopping.ibe.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.common.util.PassengerType;
import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.hna.shopping.ibe.interfaces.dto.DETRCreResult;
import com.hna.shopping.ibe.interfaces.dto.DETRTax;
import com.hna.shopping.ibe.manager.SegInfoManager;
import com.hna.shopping.ibe.manager.util.REMatchUtil;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.util.QDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;



@Component("ibeSegInfoManager")
@Slf4j
public class IBESegInfoManagerImpl implements SegInfoManager {

    @Override
    public AllChanTicketInfo getBaseAllChanTicketInfo(DETRTKTResult result, Integer segIndex, DETRTKTSegment detrtktSegment, DETR2F detr2f, String secondFactorCode, String secondFactorValue) {
        AllChanTicketInfo seg = new AllChanTicketInfo();
        Map<String, Object> hisinfo = new HashMap<>();
        //只要一个为空就获取历史明细
        if (null == result.getIssueDate() || result.getSegment(0) ==  null  || result.getSegment(0).getPnrNo() == null) {
            hisinfo = getInfoFromHistory(detr2f, result, detrtktSegment, result.getTicketNo(),segIndex, secondFactorCode, secondFactorValue);
            seg.setIssueDate((Date)(hisinfo.get("issueDate")));
            seg.setClearDate((Date)(hisinfo.get("clearDate")));
            if (StringUtils.isNotEmpty(detrtktSegment.getPnrNo())) {
                seg.setPnr(detrtktSegment.getPnrNo());
            } else {
                seg.setPnr((String)(hisinfo.get("pnr")));
            }
        }
        else{
            seg.setIssueDate(result.getIssueDate());
            seg.setPnr(detrtktSegment.getPnrNo());
        }
        seg.setDepCode(detrtktSegment.getDepAirportCode());//出发城市三字码
        seg.setArrCode(detrtktSegment.getArrAirportCode());//到达城市三字码
        seg.setFlightNo(detrtktSegment.getFlightNo());//航班号
        seg.setTicketType(result.getETicketType());
        if (detrtktSegment.getDepTime() != null) {
            seg.setFlightDate(DateUtil.changeDateToDate(detrtktSegment.getDepTime(),"yyyy-MM-dd"));//航班日期
            seg.setDepTime(detrtktSegment.getDepTime());//起飞时间
        }
        if (detrtktSegment.getArrTime() != null) {
            seg.setArrTime(detrtktSegment.getArrTime());//到达时间
        }

        if (seg.getFlightDate() == null || StringUtils.isEmpty(seg.getFlightNo())) {
            seg.setFlightDate((Date)hisinfo.get("flightDate"));
            seg.setFlightNo((String)hisinfo.get("flightNo"));
        }

        seg.setIssCode(result.getTicketNo().substring(0,3));//结算码
        String ticketNo = result.getTicketNo();
        if (ticketNo.length() > 10) {
            seg.setTicketNo(ticketNo.replaceAll("-","").substring(3));
        } else {
            seg.setTicketNo(result.getTicketNo());
        }
        seg.setTicketStatus(detrtktSegment.getTicketStatus());//客票状态

        seg.setCabin(detrtktSegment.getCabin()+"");//舱位
        seg.setAirlineCode(detrtktSegment.getAirline());//航司二字码
        seg.setEi(result.getSigningInfo());//EI项
        seg.setTourCode(result.getTourCode());//旅游代码
        getAbnormalFeature(detrtktSegment, seg);        
        //票面价以及实售价 往返程
        if(result.getSegmentCount()>1){
            //回程的价格  19SEP18PEK JD ERL457.00JD PEK330.00CNY787.00END
            if(segIndex==0){
                int segindex=result.getFareCompute().lastIndexOf(detrtktSegment.getDepAirportCode());
                String backFareStr=result.getFareCompute().substring(segindex+detrtktSegment.getDepAirportCode().length()).split("CNY")[0];
                //0的时候可能会不传，空按0处理
                Double backFare = getBackFair(backFareStr, result);
                seg.setMarketFare(new BigDecimal(result.getFare()-backFare));//票面价
                seg.setNetFare(seg.getMarketFare());//实付价
            }else{
                int segindex=result.getFareCompute().lastIndexOf(detrtktSegment.getArrAirportCode());
                String backFareStr=result.getFareCompute().substring(segindex+detrtktSegment.getDepAirportCode().length()).split("CNY")[0];
                Double backFare = getBackFair(backFareStr, result);
                seg.setMarketFare(new BigDecimal(backFare));//票面价
                seg.setNetFare(new BigDecimal(backFare));//实付价
            }
            for (int i = 0; i < result.getTaxCode().size(); i++) {
                if (result.getTaxCode(i).equals("CN")) {
                    seg.setAirportTax(new BigDecimal(result.getTaxAmount(i)/result.getSegmentCount()));//机建费
                } else if (result.getTaxCode(i).equals("YQ")) {
                    seg.setFuelTax(new BigDecimal(result.getTaxAmount(i)/result.getSegmentCount()));//燃油费
                }
            }
        }
        else {
            seg.setMarketFare(new BigDecimal(result.getFare()));//票面价
            seg.setNetFare(new BigDecimal(result.getFare()));//实付价
            for (int i = 0; i < result.getTaxCode().size(); i++) {
                if (result.getTaxCode(i).equals("CN")) {
                    seg.setAirportTax(new BigDecimal(result.getTaxAmount(i)));//机建费
                } else if (result.getTaxCode(i).equals("YQ")) {
                    seg.setFuelTax(new BigDecimal(result.getTaxAmount(i)));//燃油费
                }
            }
        }
        getTaxs(result, seg);
        String pcode="MZMK";//产品代码默认MZMK
        if(null!=detrtktSegment.getRate()&&!"".equals(detrtktSegment.getRate())){
            String[] pcodeArr=detrtktSegment.getRate().split("/");
            if(pcodeArr.length>1){
                pcode=pcodeArr[1];
            }
        }
        seg.setProductCode(pcode);
        try {
            //0 是成人 1是儿童 3是婴儿
            seg.setPassengerType(PassengerType.valueOf("PASSENGER_"+result.getPassengerType()).getAlias());//旅客类型
        }catch (Exception ex){
            log.error("passengerType  is error: "+ex.getMessage());
        }
        if (result.getPassengerType() !=0 ) { //婴儿需要处理一下名字
            int infPos = result.getPassengerName().indexOf(" INF");
            if (infPos == -1) {
                infPos = result.getPassengerName().indexOf(" CHD");
            }
            if (infPos == -1) {
                seg.setPassengerName(result.getPassengerName());
            } else {
                seg.setPassengerName(result.getPassengerName().substring(0,infPos));
            }
        } else {
            seg.setPassengerName(result.getPassengerName());//旅客姓名
        }
        seg.setSegmentIndex(seg.getSegmentIndex());//航段号

        if ("P".equals(seg.getCabin()) && detrtktSegment.getRate()!=null && detrtktSegment.getRate().length() > 2) {
            seg.setRefCabin(detrtktSegment.getRate().substring(1,2));
        } else {
            seg.setRefCabin(seg.getCabin());
        }
        seg.setSigningInfo(result.getSigningInfo());
        seg.setSegmentIndex(segIndex);
        seg.setRate(detrtktSegment.getRate().split("-")[0]);
        return seg;
    }
    
    private void getAbnormalFeature(DETRTKTSegment detrtktSegment, AllChanTicketInfo ticketInfo){
        String changeReason = detrtktSegment.getChangeReason();
        if (!StringUtils.isEmpty(changeReason)){
            changeReason = changeReason.trim();
            boolean isIRRSeg = changeReason.contains("IRR") || changeReason.contains("irr");
            boolean isINVSeg = changeReason.contains("INV") || changeReason.contains("inv");
            if (isINVSeg || isINVSeg){
                log.info("含有IRR或者INV标识:"+ticketInfo.getTicketNo() +" 航段信息"+JSONObject.toJSONString(detrtktSegment));
            }
            ticketInfo.setContainIrr(isIRRSeg);
            ticketInfo.setContainInv(isINVSeg);
        }
        
    }

    private void getTaxs(DETRTKTResult result, AllChanTicketInfo ticketInfo){
        for (int taxCount = 0; taxCount < result.getTaxLength(); taxCount++) {
            DETRTax tax = new DETRTax();
            if ("CN".equals(result.getTaxCode(taxCount)) || "YQ".equals(result.getTaxCode(taxCount))) {
                tax.setTaxAmount((float)result.getTaxAmount(taxCount)/result.getSegmentCount());//机建费或者燃油费，目前是按照总/段数处理，可能后续还会改
            } else {
                tax.setTaxAmount((float) result.getTaxAmount(taxCount));
            }

            tax.setTaxCode(result.getTaxCode(taxCount));
            tax.setTaxCurrency(result.getTaxCurrencyType(taxCount));
            ticketInfo.getTaxs().add(tax);
        }
    }

    private Double getBackFair(String backFair, DETRTKTResult result){
        try{
            return Double.parseDouble(backFair);
        }catch (Exception e){
            log.info("没有取到backFair，按空值处理:"+result.getTicketNo());
            return 0.00;
        }
    }

    @Override
    public void getOtherAllChanTicketInfo(DETRTKTResult result, DETR2F detr2f, AllChanTicketInfo seg, String secondFactorCode, String secondFactorValue) {
        //票里优惠券信息
        setConponResult(seg, getConponResult(result));
        //获取历史记录(此处不需要)
        //获取证件信息
        seg.setCreResult(getCreResult(detr2f, result.getTicketNo(), secondFactorCode, secondFactorValue));
        //先从这里获取证件号码，因为老代码(mapTicketInfoResult那的passengerid)不是在这，但是这里一般能取到，先放到seg，如果老代码段能取到就用老代码的(不过退票改期基本都是用creresult)
        seg.setPassengerID(seg.getCreResult().getIdNo());
    }

    @Override
    public Map<String, Object> getInfoFromHistory(DETR2F detr2f, DETRTKTResult detrtktResult, DETRTKTSegment detrtktSegment, String ticketNo,int segIndex, String secondFactorCode, String secondFactorValue) {
        try {
            Map<String, Object> map = new HashMap<>();
            DETRHistoryResult historyResult = detr2f.getTicketHistoryByTktNo2F(ticketNo, "N", secondFactorCode, secondFactorValue);
            map.put("issueDate", historyResult.getInfoItem(0).getOperTime());
            //从历史明细中获取pnr
            for (Object o : historyResult.getInfoItem()) {
                DETRHistoryInfoItem infoItem = (DETRHistoryInfoItem) o;
                if ("EOTU".equals(infoItem.getOperType())  && infoItem.getOperDesc().contains("CLEARED")) {
                    //
                    int position = infoItem.getOperDesc().indexOf(" RL ");
                    map.put("pnr", infoItem.getOperDesc().substring(position+4,position+10));
                    break;
                }
            }
            Date clearedDate = null;
            for(Object o: historyResult.getInfoItem()){
                DETRHistoryInfoItem infoItem = (DETRHistoryInfoItem) o;
                // 根据操作记录判断清位时间，往返程第一段couponNo=1
                if (infoItem.getOperDesc().contains("RES RL") && infoItem.getOperDesc().contains("CLEARED") && infoItem.getCouponNo()==segIndex+1) {
                    clearedDate = Calendar.getInstance().getTime();
                    Date retrieveTime = infoItem.getOperTime();

                    // 清位时间
                    Calendar retrieveCal = Calendar.getInstance();
                    retrieveCal.setTime(retrieveTime);
                    // 当前时间
                    Date now = new Date();
                    Calendar nowCal = Calendar.getInstance();
                    nowCal.setTime(now);

                    // 第一次判断
                    // 清位时间在当前系统日期之后，将年份换位当前年份
                    if (DateUtils.truncatedCompareTo(now,retrieveCal.getTime(),Calendar.DATE)<=0) {
                        retrieveCal.set(Calendar.YEAR,
                                nowCal.get(Calendar.YEAR));
                    }
                    // 第二次判断
                    // 如果清位时间依然在当前系统日期之后（跨年时可能会产生），将年份换位当前年份减一
                    if (DateUtils.truncatedCompareTo(now,retrieveCal.getTime(),Calendar.DATE)<0) {
                        retrieveCal.set(Calendar.YEAR,
                                nowCal.get(Calendar.YEAR) - 1);
                    }
                    retrieveTime = retrieveCal.getTime();
                    log.info("Change ticket {} clear date to {}" + ticketNo,retrieveTime);
                    if(retrieveTime.before(clearedDate)){
                        clearedDate = retrieveTime;
                    }
                    map.put("clearDate", clearedDate);
                    break;
                }
            }

            // 获取航班日期、航班号
            // 获取原客票信息
            DETRTKTResult originDetrtktResult = null;
            if (org.springframework.util.StringUtils.hasText(detrtktResult.getExchangeInfo())) {
                originDetrtktResult = detr2f.getTicketInfoByTktNo2F(detrtktResult.getExchangeInfo(), true, "", "N", "NM", detrtktResult.getPassengerName());
            }
                for (int i1 = historyResult.getInfoItem().size() - 1; i1 >= 0; i1--) {
                    boolean findEOTU = false;
                    boolean findCKIN = false;
                    boolean findNFMT = false;
                    DETRHistoryInfoItem detrHistoryInfoItem = historyResult.getInfoItem(i1);
                    // 有换开的场景 EOTU CHG FLT FROM JD5562/28SEP20/U/HAKXIY TO JDOPEN/OPEN/U/HAKXIY
                    if (originDetrtktResult != null) {
                        if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                            findEOTU = true;
                        }
                        if (!findEOTU) {
                            if ("CKIN".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode())) {
                                findCKIN = true;
                            }
                        }
                    } else if ("USED/FLOWN".equals(detrtktSegment.getTicketStatus()) || "OPEN FOR USE".equals(detrtktSegment.getTicketStatus())) {  // 客票已使用 CKIN O/C JD5277/26JUL20/U/HAKTNA
                        if ("CKIN".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode())) {
                            findCKIN = true;
                        }
                        if (!findCKIN) {
                            if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                findEOTU = true;
                            }
                        }
                    } else {
                        if ("EOTU".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                            findEOTU = true;
                        } else if (detrtktResult.getETicketType() == 11 || detrtktResult.getETicketType() == 13) {
                            if ("NFMT".equals(detrHistoryInfoItem.getOperType()) && detrHistoryInfoItem.getOperDesc().contains("/" + detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode() + " ")) {
                                findNFMT = true;
                            }
                        }
                    }
                    if (findEOTU ||findNFMT) {
                        String[] item = detrHistoryInfoItem.getOperDesc().split("/")[0].split(" ");
                        String flightNo = item[item.length - 1];
                        String ibeFlightDate = new DateTime(QDateTime.stringToDate(detrHistoryInfoItem.getOperDesc().split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                        map.put("flightDate", new DateTime(ibeFlightDate).toDate());
                        map.put("flightNo", flightNo);
                        break;
                    } else if (findCKIN) {
                        String[] item = detrHistoryInfoItem.getOperDesc().split(" ");
                        String itemStr = item[item.length - 1];
                        String flightNo = itemStr.split("/")[0];
                        String ibeFlightDate = new DateTime(QDateTime.stringToDate(itemStr.split("/")[1], "DDMMMYY")).toString("yyyy-MM-dd");
                        map.put("flightDate", new DateTime(ibeFlightDate).toDate());
                        map.put("flightNo", flightNo);
                        break;
                    }
                }

            return map;
        } catch (Exception e) {
            log.error("getInfoFromHistory" + e.getMessage());
            //暂未确定是那种异常
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, "获取历史客票信息失败", new RuntimeException("获取历史客票信息失败"));
        }
    }

    @Override
    public void setConponResult(AllChanTicketInfo seg, String conponResult){
        if (StringUtils.isNotEmpty(conponResult)) {
            log.info("findAllChanTicketInfo:" + seg.getTicketNo() + ",conpon" + conponResult);
            seg.setConponMsg(conponResult.substring(1));
        }
    }

    public String getConponResult(DETRTKTResult result) {
        return REMatchUtil.searchkey("-\\d+",result.getPayMethod());
    }

    @Override
    public DETRCreResult getCreResult(DETR2F detr2f, String ticketNo, String secondFactorCode, String secondFactorValue){
        DETRCreResult creResult = new DETRCreResult();
        try {
            DETRFoidResult result = detr2f.getCredentialByTktNo2F(ticketNo, secondFactorCode, secondFactorValue);
            BeanUtils.copyProperties(result, creResult);
            //如果有多条记录，优先获取身份证
            for(int i=0;i<result.getIdnoCount();i++) {
                if("NI".equals(result.getIdTypeAt(i))){
                    String idNo = result.getIdNoAt(i);
                    creResult.setIdNo(idNo);
                    creResult.setIdtype(result.getIdTypeAt(i));
                    creResult.getIdnos().add(idNo);

                }
            }
            log.info(" detrCredential ticketNo: {} , getTxnTraceKey: {} ",ticketNo ,detr2f.getTxnTraceKey());
        } catch (Exception e) {
            log.error(" detrCredential error ticketNo: {} , getTxnTraceKey: {} ",ticketNo ,detr2f.getTxnTraceKey());
        }
        return  creResult;
    }

    /**
     * 判断两个航段是否在同一时刻，同一航班上，成人携带婴儿需要返回婴儿所在的航段
     * @param seg
     * @param detrtktSegment
     * @return
     */
    public boolean testTwoSegOnOneFligh(AllChanTicketInfo seg, DETRTKTSegment detrtktSegment){
        if(seg.getDepCode().equals(detrtktSegment.getDepAirportCode()) && seg.getArrCode().equals(detrtktSegment.getArrAirportCode()) &&
                seg.getAirlineCode().equals(detrtktSegment.getAirline())){
            return true;
        }
        return false;
    }

}