package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

public class DETRTKTInfo implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -650467962023232403L;
	private String name;
	private String ticketNo;
	private String flightNo;
	private String departDate;
	private String segment;
	private String status;
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getTicketNo() {
		return ticketNo;
	}
	public void setTicketNo(String ticketNo) {
		this.ticketNo = ticketNo;
	}
	public String getFlightNo() {
		return flightNo;
	}
	public void setFlightNo(String flightNo) {
		this.flightNo = flightNo;
	}
	public String getDepartDate() {
		return departDate;
	}
	public void setDepartDate(String departDate) {
		this.departDate = departDate;
	}
	public String getSegment() {
		return segment;
	}
	public void setSegment(String segment) {
		this.segment = segment;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	@Override
	public String toString() {
		return "DETRTKTInfo [name=" + name + ", ticketNo=" + ticketNo + ", flightNo=" + flightNo + ", departDate="
				+ departDate + ", segment=" + segment + ", status=" + status + "]";
	}
}
