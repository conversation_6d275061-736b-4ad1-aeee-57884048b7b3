package com.hna.shopping.ibe.config.httpinvoke;

import org.springframework.remoting.httpinvoker.HttpInvokerClientConfiguration;
import org.springframework.remoting.httpinvoker.SimpleHttpInvokerRequestExecutor;
import org.springframework.remoting.support.RemoteInvocationResult;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.HttpURLConnection;

/**
 * Zero
 * 2020/10/26 11:31
 **/
public class CheckInHttpInvokerRequestExecutor extends SimpleHttpInvokerRequestExecutor{

    @Override
    protected RemoteInvocationResult doExecuteRequest(HttpInvokerClientConfiguration config, ByteArrayOutputStream baos) throws IOException, ClassNotFoundException {
        try{
            return super.doExecuteRequest(config, baos);
        }finally {
            CheckInConfigHolder.clear();
        }
    }

    @Override
    protected void prepareConnection(HttpURLConnection connection, int contentLength) throws IOException {
        super.prepareConnection(connection, contentLength);
        connection.setRequestProperty("user", CheckInConfigHolder.getUser());
    }
}
