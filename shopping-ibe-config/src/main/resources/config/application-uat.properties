server.port=${server.port}

# 引入公共配置
spring.profiles.include=common

# spring-boot-starter-actuator 测试环境访问不需要鉴权
management.security.enabled=false

# 每个系统都应该不一样
memcached.prefix=uni-offer-uat
memcached.serverHost[0]=*************:11211
memcached.userName[0]=memcached
memcached.password[0]=ENC(yawm/celfy5VE3Qdx+dDaroo5NzNptdG)


# logback 配置
logging.path=/app/logs/jd-ibe
#logging.path=./logs/uni-ibe
logging.level.com.hna.eking.basedemo=INFO
logging.level.root=INFO
logging.level.com.google.code.ssm=WARN

# eureka 注册中心地址
eureka.client.serviceUrl.defaultZone=http://*********:8761/eureka/,http://*********:8761/eureka/
#eureka.instance.ip-address=************
#eureka.client.serviceUrl.defaultZone=http://************:9013/eureka/

redisson.address=redis://*********:6379
redisson.password=Redis@123
redisson.config.open=true

# redis配置
spring.redis.database=1
# Redis服务器地址
spring.redis.host=************
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=ENC(BavCb+skeCZq9ID4BVlLiJxxFD8foz6An1tdi1whXZnaX42Q+3EO/QRcUd8mKAp6AHl+Xt10Qfx1vmuCjcrStA==)
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=20
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=10
# 连接池中的最小空闲连接
spring.redis.pool.min-idle=0
# 连接超时时间（毫秒）
spring.redis.timeout=3000



# jd 配置
ibe.jd.server.ip=*************
ibe.jd.server.backupIp=*************
ibe.jd.server.port=6891
ibe.jd.client.app=jdcrm
ibe.jd.client.office=xiy258
ibe.jd.client.customNo=0
ibe.jd.client.validationNo=64
ibe.jd.client.printNos=1,2,3,4,6,7,8

# jd 票务处理
ibe.jd.pw.server.ip=*************
ibe.jd.pw.server.backupIp=*************
ibe.jd.pw.server.port=6891
ibe.jd.pw.client.app=jdcrm
ibe.jd.pw.client.office=xiy258
ibe.jd.pw.client.customNo=0
ibe.jd.pw.client.validationNo=64
ibe.jd.pw.client.printNos=1,2,3,4,6,7,8

# jd IBE小工具 xiy255配置
ibe.jd.xiy255.server.ip=***********
ibe.jd.xiy255.server.backupIp=***********
ibe.jd.xiy255.server.port=6891
ibe.jd.xiy255.client.app=jdhaiwai
ibe.jd.xiy255.client.office=xiy255
ibe.jd.xiy255.client.customNo=0
ibe.jd.xiy255.client.validationNo=64
ibe.jd.xiy255.client.printNos=1,2,3,4,6,7,8

# jd IBE小工具 xiy258配置
ibe.jd.xiy258.server.ip=*************
ibe.jd.xiy258.server.backupIp=*************
ibe.jd.xiy258.server.port=6891
ibe.jd.xiy258.client.app=jdcrm
ibe.jd.xiy258.client.office=xiy258
ibe.jd.xiy258.client.customNo=0
ibe.jd.xiy258.client.validationNo=64
ibe.jd.xiy258.client.printNos=1,2,3,4,6,7,8

# jd 强k配置
ibe.jd.k.server.ip=**************
ibe.jd.k.server.backupIp=**************
ibe.jd.k.server.port=6891
ibe.jd.k.client.app=jdhaiwai
ibe.jd.k.client.office=xiy255
ibe.jd.k.client.customNo=0
ibe.jd.k.client.validationNo=64
ibe.jd.k.client.printNos=1,2,3,4,6,7,8

# jd 配置
ibe.jd.gw.server.ip=***********
ibe.jd.gw.server.backupIp=***********
ibe.jd.gw.server.port=6895
ibe.jd.gw.client.app=jdibe
ibe.jd.gw.client.office=xiy210
ibe.jd.gw.client.customNo=0
ibe.jd.gw.client.validationNo=64
ibe.jd.gw.client.printNos=1,2,3,4,6,7,8

#travel sky 配置
travelsky.service.url=http://*************:8087/TravelSkySvc/remoting/

# 股份 esb 接口配置信息
odsapi.url=https://odsuat.hnair.net/api
odsapi.serialization=fastjson2
odsapi.timeout=600000

spring.datasource.dynamic.primary=master
#type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.dynamic.datasource.master.url=****************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.username=jdmanager
spring.datasource.dynamic.datasource.master.password=ENC(mzU+w49iaWymnsrthDJKj6ZWnv/lawUW)
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.jdbc.Driver