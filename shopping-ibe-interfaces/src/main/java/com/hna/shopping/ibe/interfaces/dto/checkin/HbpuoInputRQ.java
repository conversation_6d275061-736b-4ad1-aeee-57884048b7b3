package com.hna.shopping.ibe.interfaces.dto.checkin; /**
 * <AUTHOR>
 * @date 2020/11/2
 */

import lombok.Data;

import java.io.Serializable;

@Data
public class HbpuoInputRQ implements Serializable {
    private String airlineCode = "";
    private String flightNumber = "";
    private String flightDate = "";
    private String fromCity = "";
    private String toCity = "";
    private String psrClass = "";
    private String passengerName = "";
    private String tktNumber = "";
    private String tourIndex = "";
    private String seatNumber = "";
    private String hostNumber = "";
    private Boolean isSnr;
    private HbpuoInputRQ throughPassenger;
    private String phoneNumber = "";
}
