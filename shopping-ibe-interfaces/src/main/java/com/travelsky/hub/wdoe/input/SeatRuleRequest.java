/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatRuleRequest extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String channel;
/*    */   private String paxLevel;
/*    */   private String etCode;
/*    */ 
/*    */   public String getChannel()
/*    */   {
/* 32 */     return this.channel;
/*    */   }
/*    */ 
/*    */   public void setChannel(String channel)
/*    */   {
/* 39 */     this.channel = channel;
/*    */   }
/*    */ 
/*    */   public String getPaxLevel()
/*    */   {
/* 46 */     return this.paxLevel;
/*    */   }
/*    */ 
/*    */   public void setPaxLevel(String paxLevel)
/*    */   {
/* 53 */     this.paxLevel = paxLevel;
/*    */   }
/*    */ 
/*    */   public String getEtCode()
/*    */   {
/* 61 */     return this.etCode;
/*    */   }
/*    */ 
/*    */   public void setEtCode(String etCode)
/*    */   {
/* 68 */     this.etCode = etCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.SeatRuleRequest
 * JD-Core Version:    0.6.0
 */