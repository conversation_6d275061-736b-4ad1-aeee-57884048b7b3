/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4577398244150513280L;
/*     */   private String seatNumber;
/*     */   private String extraSeatType;
/*     */   private String extraSeatNumber;
/*     */   private String extraSeatWeight;
/*     */   private String aecSeatNumber;
/*     */   private String reservedSeatType;
/*     */   private String seatAllocatedType;
/*     */   private String primaryClass;
/*     */   private String arrivalAirport;
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  61 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  68 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatType()
/*     */   {
/*  75 */     return this.extraSeatType;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatType(String extraSeatType)
/*     */   {
/*  82 */     this.extraSeatType = extraSeatType;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatNumber()
/*     */   {
/*  89 */     return this.extraSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatNumber(String extraSeatNumber)
/*     */   {
/*  96 */     this.extraSeatNumber = extraSeatNumber;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatWeight()
/*     */   {
/* 103 */     return this.extraSeatWeight;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatWeight(String extraSeatWeight)
/*     */   {
/* 110 */     this.extraSeatWeight = extraSeatWeight;
/*     */   }
/*     */ 
/*     */   public String getAecSeatNumber()
/*     */   {
/* 117 */     return this.aecSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setAecSeatNumber(String aecSeatNumber)
/*     */   {
/* 124 */     this.aecSeatNumber = aecSeatNumber;
/*     */   }
/*     */ 
/*     */   public String getReservedSeatType()
/*     */   {
/* 131 */     return this.reservedSeatType;
/*     */   }
/*     */ 
/*     */   public void setReservedSeatType(String reservedSeatType)
/*     */   {
/* 138 */     this.reservedSeatType = reservedSeatType;
/*     */   }
/*     */ 
/*     */   public String getSeatAllocatedType()
/*     */   {
/* 145 */     return this.seatAllocatedType;
/*     */   }
/*     */ 
/*     */   public void setSeatAllocatedType(String seatAllocatedType)
/*     */   {
/* 152 */     this.seatAllocatedType = seatAllocatedType;
/*     */   }
/*     */ 
/*     */   public String getPrimaryClass()
/*     */   {
/* 159 */     return this.primaryClass;
/*     */   }
/*     */ 
/*     */   public void setPrimaryClass(String primaryClass)
/*     */   {
/* 166 */     this.primaryClass = primaryClass;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 173 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 180 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatInformation
 * JD-Core Version:    0.6.0
 */