/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AdcError
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String errorCode;
/*    */   private String qualifier;
/*    */   private String errorDescription;
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 31 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 40 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getQualifier()
/*    */   {
/* 49 */     return this.qualifier;
/*    */   }
/*    */ 
/*    */   public void setQualifier(String qualifier)
/*    */   {
/* 58 */     this.qualifier = qualifier;
/*    */   }
/*    */ 
/*    */   public String getErrorDescription()
/*    */   {
/* 67 */     return this.errorDescription;
/*    */   }
/*    */ 
/*    */   public void setErrorDescription(String errorDescription)
/*    */   {
/* 76 */     this.errorDescription = errorDescription;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AdcError
 * JD-Core Version:    0.6.0
 */