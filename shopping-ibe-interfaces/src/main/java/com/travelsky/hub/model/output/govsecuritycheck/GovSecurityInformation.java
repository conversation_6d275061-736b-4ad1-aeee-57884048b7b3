/*     */ package com.travelsky.hub.model.output.govsecuritycheck;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class GovSecurityInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3689177602549254903L;
/*     */   private String checkType;
/*     */   private String checkStatus;
/*     */   private String estaStatus;
/*     */   private String checkDirection;
/*     */   private String checkCountry;
/*     */   private String checkRemark;
/*     */   private String isOveride;
/*     */   private String isAllowedCheckin;
/*     */   private String isAllowedCancelCheckin;
/*     */ 
/*     */   public String getCheckType()
/*     */   {
/*  58 */     return this.checkType;
/*     */   }
/*     */ 
/*     */   public void setCheckType(String checkType)
/*     */   {
/*  65 */     this.checkType = checkType;
/*     */   }
/*     */ 
/*     */   public String getCheckStatus()
/*     */   {
/*  72 */     return this.checkStatus;
/*     */   }
/*     */ 
/*     */   public void setCheckStatus(String checkStatus)
/*     */   {
/*  79 */     this.checkStatus = checkStatus;
/*     */   }
/*     */ 
/*     */   public String getEstaStatus()
/*     */   {
/*  86 */     return this.estaStatus;
/*     */   }
/*     */ 
/*     */   public void setEstaStatus(String estaStatus)
/*     */   {
/*  93 */     this.estaStatus = estaStatus;
/*     */   }
/*     */ 
/*     */   public String getCheckDirection()
/*     */   {
/* 100 */     return this.checkDirection;
/*     */   }
/*     */ 
/*     */   public void setCheckDirection(String checkDirection)
/*     */   {
/* 107 */     this.checkDirection = checkDirection;
/*     */   }
/*     */ 
/*     */   public String getCheckCountry()
/*     */   {
/* 114 */     return this.checkCountry;
/*     */   }
/*     */ 
/*     */   public void setCheckCountry(String checkCountry)
/*     */   {
/* 121 */     this.checkCountry = checkCountry;
/*     */   }
/*     */ 
/*     */   public String getCheckRemark()
/*     */   {
/* 128 */     return this.checkRemark;
/*     */   }
/*     */ 
/*     */   public void setCheckRemark(String checkRemark)
/*     */   {
/* 135 */     this.checkRemark = checkRemark;
/*     */   }
/*     */ 
/*     */   public String getIsOveride()
/*     */   {
/* 142 */     return this.isOveride;
/*     */   }
/*     */ 
/*     */   public void setIsOveride(String isOveride)
/*     */   {
/* 149 */     this.isOveride = isOveride;
/*     */   }
/*     */ 
/*     */   public String getIsAllowedCheckin()
/*     */   {
/* 156 */     return this.isAllowedCheckin;
/*     */   }
/*     */ 
/*     */   public void setIsAllowedCheckin(String isAllowedCheckin)
/*     */   {
/* 163 */     this.isAllowedCheckin = isAllowedCheckin;
/*     */   }
/*     */ 
/*     */   public String getIsAllowedCancelCheckin()
/*     */   {
/* 170 */     return this.isAllowedCancelCheckin;
/*     */   }
/*     */ 
/*     */   public void setIsAllowedCancelCheckin(String isAllowedCancelCheckin)
/*     */   {
/* 177 */     this.isAllowedCancelCheckin = isAllowedCancelCheckin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.govsecuritycheck.GovSecurityInformation
 * JD-Core Version:    0.6.0
 */