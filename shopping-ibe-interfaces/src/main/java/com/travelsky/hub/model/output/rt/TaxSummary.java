/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class TaxSummary
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7644204114811055192L;
/*     */   private String creditCardTaxAmount;
/*     */   private String creditCardTaxAmountCurCode;
/*     */   private String totalTaxAmount;
/*     */   private String totalTaxAmountCurCode;
/*     */   private List<Tax> originalTax;
/*     */   private List<Tax> tax;
/*     */ 
/*     */   public String getCreditCardTaxAmount()
/*     */   {
/*  47 */     return this.creditCardTaxAmount;
/*     */   }
/*     */ 
/*     */   public void setCreditCardTaxAmount(String creditCardTaxAmount)
/*     */   {
/*  54 */     this.creditCardTaxAmount = creditCardTaxAmount;
/*     */   }
/*     */ 
/*     */   public String getCreditCardTaxAmountCurCode()
/*     */   {
/*  61 */     return this.creditCardTaxAmountCurCode;
/*     */   }
/*     */ 
/*     */   public void setCreditCardTaxAmountCurCode(String creditCardTaxAmountCurCode)
/*     */   {
/*  68 */     this.creditCardTaxAmountCurCode = creditCardTaxAmountCurCode;
/*     */   }
/*     */ 
/*     */   public String getTotalTaxAmount()
/*     */   {
/*  75 */     return this.totalTaxAmount;
/*     */   }
/*     */ 
/*     */   public void setTotalTaxAmount(String totalTaxAmount)
/*     */   {
/*  82 */     this.totalTaxAmount = totalTaxAmount;
/*     */   }
/*     */ 
/*     */   public String getTotalTaxAmountCurCode()
/*     */   {
/*  89 */     return this.totalTaxAmountCurCode;
/*     */   }
/*     */ 
/*     */   public void setTotalTaxAmountCurCode(String totalTaxAmountCurCode)
/*     */   {
/*  96 */     this.totalTaxAmountCurCode = totalTaxAmountCurCode;
/*     */   }
/*     */ 
/*     */   public List<Tax> getTax()
/*     */   {
/* 104 */     return this.tax;
/*     */   }
/*     */ 
/*     */   public void setTax(List<Tax> tax)
/*     */   {
/* 111 */     this.tax = tax;
/*     */   }
/*     */ 
/*     */   public List<Tax> getOriginalTax()
/*     */   {
/* 118 */     return this.originalTax;
/*     */   }
/*     */ 
/*     */   public void setOriginalTax(List<Tax> originalTax)
/*     */   {
/* 125 */     this.originalTax = originalTax;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.TaxSummary
 * JD-Core Version:    0.6.0
 */