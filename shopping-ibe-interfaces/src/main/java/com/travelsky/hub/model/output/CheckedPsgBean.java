/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class CheckedPsgBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String pnr;
/*     */   private String passengerStatus;
/*     */   private List<CheckedDocBean> checkedDocuments;
/*     */   private List<CheckedSegbean> checkedSegments;
/*     */   private List<CheckedMsgBean> mainMessages;
/*     */   private List<CheckedMsgBean> extendMessages;
/*     */ 
/*     */   public String getPnr()
/*     */   {
/*  46 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/*  53 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/*  60 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/*  67 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public List<CheckedDocBean> getCheckedDocuments()
/*     */   {
/*  74 */     return this.checkedDocuments;
/*     */   }
/*     */ 
/*     */   public void setCheckedDocuments(List<CheckedDocBean> checkedDocuments)
/*     */   {
/*  81 */     this.checkedDocuments = checkedDocuments;
/*     */   }
/*     */ 
/*     */   public List<CheckedSegbean> getCheckedSegments()
/*     */   {
/*  88 */     return this.checkedSegments;
/*     */   }
/*     */ 
/*     */   public void setCheckedSegments(List<CheckedSegbean> checkedSegments)
/*     */   {
/*  95 */     this.checkedSegments = checkedSegments;
/*     */   }
/*     */ 
/*     */   public List<CheckedMsgBean> getMainMessages()
/*     */   {
/* 102 */     return this.mainMessages;
/*     */   }
/*     */ 
/*     */   public void setMainMessages(List<CheckedMsgBean> mainMessages)
/*     */   {
/* 109 */     this.mainMessages = mainMessages;
/*     */   }
/*     */ 
/*     */   public List<CheckedMsgBean> getExtendMessages()
/*     */   {
/* 116 */     return this.extendMessages;
/*     */   }
/*     */ 
/*     */   public void setExtendMessages(List<CheckedMsgBean> extendMessages)
/*     */   {
/* 123 */     this.extendMessages = extendMessages;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckedPsgBean
 * JD-Core Version:    0.6.0
 */