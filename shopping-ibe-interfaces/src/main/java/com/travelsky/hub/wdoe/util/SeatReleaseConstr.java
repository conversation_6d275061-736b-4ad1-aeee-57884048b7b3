package com.travelsky.hub.wdoe.util;

public final class SeatReleaseConstr
{
  public static final String SEATRELEASE_WDOE_ELEMENT = "WDoe";
  public static final String SEATRELEASE_WDOE_PASSENGERSEATINFO = "PassengerSeatInfo";
  public static final String SEATRELEASE_WDOE_AIRLINECODE = "AirlineCode";
  public static final String SEATRELEASE_WDOE_FLIGHTNUMBER = "FlightNumber";
  public static final String SEATRELEASE_WDOE_FLIGHTDATE = "FlightDate";
  public static final String SEATRELEASE_WDOE_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String SEATRELEASE_WDOE_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String SEATRELEASE_WDOE_CABINTYPE = "CabinType";
  public static final String SEATRELEASE_WDOE_SEATTYPE = "SeatType";
  public static final String SEATRELEASE_WDOE_SEATNUMBER = "SeatNumber";
  public static final String SEATRELEASE_WDOE_RARESULT = "RaResult";
  public static final String SEATRELEASE_WDOE_RESULTCODE = "ResultCode";
  public static final String SEATRELEASE_WDOE_RESULTMSG = "ResultMsg";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.SeatReleaseConstr
 * JD-Core Version:    0.6.0
 */