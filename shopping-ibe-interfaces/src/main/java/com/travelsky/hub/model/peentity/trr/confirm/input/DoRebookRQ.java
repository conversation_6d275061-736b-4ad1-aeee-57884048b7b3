/*     */ package com.travelsky.hub.model.peentity.trr.confirm.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class DoRebookRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5473001125131062089L;
/*     */   private String pnrNo;
/*     */   private String splitPnr;
/*     */ 
///*     */   @JSONField(name="psgPhone")
/*     */   private String contactInfo;
/*     */   private String airport;
/*     */   private String channel;
/*     */   private List<Segment> segments;
/*     */ 
/*     */   public String getPnrNo()
/*     */   {
/*  54 */     return this.pnrNo;
/*     */   }
/*     */ 
/*     */   public void setPnrNo(String pnrNo)
/*     */   {
/*  61 */     this.pnrNo = pnrNo;
/*     */   }
/*     */ 
/*     */   public String getSplitPnr()
/*     */   {
/*  68 */     return this.splitPnr;
/*     */   }
/*     */ 
/*     */   public void setSplitPnr(String splitPnr)
/*     */   {
/*  75 */     this.splitPnr = splitPnr;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/*  82 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/*  89 */     this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getAirport()
/*     */   {
/*  96 */     return this.airport;
/*     */   }
/*     */ 
/*     */   public void setAirport(String airport)
/*     */   {
/* 103 */     this.airport = airport;
/*     */   }
/*     */ 
/*     */   public String getChannel()
/*     */   {
/* 110 */     return this.channel;
/*     */   }
/*     */ 
/*     */   public void setChannel(String channel)
/*     */   {
/* 117 */     this.channel = channel;
/*     */   }
/*     */ 
/*     */   public List<Segment> getSegments()
/*     */   {
/* 124 */     return this.segments;
/*     */   }
/*     */ 
/*     */   public void setSegments(List<Segment> segments)
/*     */   {
/* 131 */     this.segments = segments;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.confirm.input.DoRebookRQ
 * JD-Core Version:    0.6.0
 */