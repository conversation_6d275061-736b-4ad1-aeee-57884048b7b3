
package com.hna.shopping.ibe.manager.esb;
import lombok.Data;

import java.util.Date;

/**
 * ESB航班动态查询结果 Data 对象
 */
@Data
public class StandardFocFlightInfo {

    private Date tsTamp;
    private String acOwn;
    private String depCity;
    private Date stdLocal;
    private Date datoplocal;
    private String arrStn;
    private Date ata;
    private Date atd;
    private Date tDwn;
    private Date eta;
    private Date etd;
    private Date datop;
    private String depStnCn;
    private String arrStnFourCode;
    private long id;
    private Date tDwnChn;
    private Date tOffChn;
    private Date sta;
    private Date std;
    private String ac;
    private String stc;
    private String depStnFourCode;
    private Date staChn;
    private Date datopChn;
    private int isStopOver;
    private Date ataChn;
    private String flightNo;
    private Date etdChn;
    private String status;
    private String arrStnCn;
    private Date datoplocal0;
    private String acLongNo;
    private String arrCity;
    private Date staLocal;
    private Date stdChn;
    private String origAcType;
    private String flightId;
    private String legNo;
    private Date atdChn;
    private String dappFlag;
    private String triFltid;
    private String fltType;
    private String airlineCode;
    private String acType;
    private Date etaChn;
    private Date etdLocal;
    private String delay1;
    private String delay2;
    private Date updateTime;
    private Date etaLocal;
    private Date tDwnLocal;
    private Date datopChn0;
    private String changeDelay;
    private Date tOff;
    private Date ataLocal;
    private String srcFlightNo;
    private Date atdLocal;
    private String depStn;
    private String delay3;
    private String delay4;
    private Date tOffLocal;
    private String dur4;
    private String dur3;
    private String dur2;
    private String dur1;
    private String divRecode;

}