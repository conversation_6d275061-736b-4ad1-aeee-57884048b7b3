package com.hna.shopping.ibe.manager.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by <PERSON><PERSON><PERSON> on 18/9/25.
 */
public class REMatchUtil {
    public static String searchkey(String patt,String line) {
        String result = "";
        Pattern r = Pattern.compile(patt);
        Matcher m = r.matcher(line);
        if (m.find()) {
            result = line.substring(m.start(),m.end());
        }
        return result;
    }

    public static void main(String[] args) {
        String result = REMatchUtil.searchkey("HGH\\d+","M 01OCT18HGH JD SJW860.00JD HGH450.00CNY1310.00END");
        System.out.println(result);
        result = REMatchUtil.searchkey("(PP|NI)\\d+[A-Z]{0,2}(/P)\\d","8.SSR FOID JD HK1 NI330219197304216855/P3");
        System.out.println(result);
        result = REMatchUtil.searchkey("\\d{4}\\s\\d{4}","001  2.  JD472  N   TH25OCT  TAOHGH\u001CNO1\u001D  1805 2000      E T1T3  LL(001)  HK(001)  RR(003)  NO(005");
        System.out.println(result);
        //2.  JD5211 N   MO22OCT  PEKERL NO1   2110 2240      E T1T1
        result = REMatchUtil.searchkey("\\s[A-Z]{2}\\d{2}[A-Z]{3}","2.  JD5211 N   MO22OCT  PEKERL NO1   2110 2240      E T1T1");
        System.out.println(result);
    }

}
