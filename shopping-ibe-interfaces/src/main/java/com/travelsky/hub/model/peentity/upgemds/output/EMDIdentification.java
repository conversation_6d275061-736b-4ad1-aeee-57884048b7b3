/*    */ package com.travelsky.hub.model.peentity.upgemds.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EMDIdentification
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2524193860661457713L;
/*    */   private String issueDate;
/*    */   private String recordLocator;
/*    */ 
/*    */   public String getIssueDate()
/*    */   {
/* 38 */     return this.issueDate;
/*    */   }
/*    */ 
/*    */   public void setIssueDate(String issueDate)
/*    */   {
/* 45 */     this.issueDate = issueDate;
/*    */   }
/*    */ 
/*    */   public String getRecordLocator()
/*    */   {
/* 52 */     return this.recordLocator;
/*    */   }
/*    */ 
/*    */   public void setRecordLocator(String recordLocator)
/*    */   {
/* 59 */     this.recordLocator = recordLocator;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.output.EMDIdentification
 * JD-Core Version:    0.6.0
 */