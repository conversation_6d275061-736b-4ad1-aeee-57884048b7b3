/*    */ package com.travelsky.hub.util;
/*    */ 
/*    */ public class MealServiceException extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String errMessage;
/*    */   private String errorCode;
/*    */ 
/*    */   public MealServiceException()
/*    */   {
/*    */   }
/*    */ 
/*    */   public MealServiceException(String errorCode, String errMessage)
/*    */   {
/* 32 */     this.errorCode = errorCode;
/* 33 */     this.errMessage = errMessage;
/*    */   }
/*    */ 
/*    */   public String getErrMessage()
/*    */   {
/* 41 */     return this.errMessage;
/*    */   }
/*    */ 
/*    */   public void setErrMessage(String errMessage)
/*    */   {
/* 49 */     this.errMessage = errMessage;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 57 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 65 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.util.MealServiceException
 * JD-Core Version:    0.6.0
 */