package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class SellSeatRequest extends BaseRequest {

	private List<IssueTicketSegment> issueTicketSegmentList;
	private List<IssueTicketPassenger> passengerIssueTicketList;
	private String contactInfo;
	private int timeLimitMinutes;
	private BigDecimal adultTripPrice;
	private BigDecimal childTripPrice;
	private BigDecimal adultBackTripPrice;
	private BigDecimal childBackTripPrice;
}
