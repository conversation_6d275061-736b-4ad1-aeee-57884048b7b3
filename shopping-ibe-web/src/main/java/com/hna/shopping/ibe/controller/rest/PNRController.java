/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.PNRService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.PNRManager;
import com.hna.shopping.ibe.manager.interceptor.logger.Logger;
import com.travelsky.ibe.client.pnr.BookFC;
import com.travelsky.ibe.client.pnr.BookFN;
import com.travelsky.ibe.client.pnr.BookFP;
import com.travelsky.ibe.client.pnr.BookOI;
import com.travelsky.ibe.exceptions.IBEException;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;
import java.util.Vector;

@Api("PNR操作接口")
@RestController
@RequestMapping("/api/pnr")
@Slf4j
public class PNRController {

    @Autowired
    private PNRManager pnrManager;

    @Autowired
    private PNRService pnrService;

    @ApiOperation(value = "sellseat")
    @RequestMapping(value = "ss", method = RequestMethod.POST)
    RestResponse<SSResult> ss(@ApiParam(value = "生成pnr参数对象", required = true)
                              @RequestBody SellSeat request) {
        String uuid = UUID.randomUUID().toString();
        Vector ois = request.getBif().getOis();
        Vector<BookOI> dest = new Vector<>();
        try {
            if (CollectionUtils.isNotEmpty(ois)) {
                for (Object item : ois) {
                    dest.add(JSONObject.parseObject(JSONObject.toJSONString(item), BookOI.class));
                }
                request.getBif().setOis(dest);
            }
            changePojo(request.getBif().getFp(), BookFP.class);
            changePojo(request.getBif().getFc(), BookFC.class);
            changePojo(request.getBif().getFn(), BookFN.class);
        } catch (Throwable e) {
            log.error("======入参转换失败:linkHashMap转换成BookOI======");
        }
        log.info("Start uuid: {} , ss: {}", uuid, JSON.toJSONString(request));
        SSResult avResponse = pnrManager.ss(request);
//       SSResult avResponse = new SSResult();
//        try {
//            avResponse.setPnrno("ABCDE");
//            avResponse.setSuccess(true);
//            Thread.sleep(2000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
        log.info("End uuid: {} , ss: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "addPNRInfo")
    @RequestMapping(value = "addPNRInfo", method = RequestMethod.POST)
    RestResponse<BaseResponse> addPNRInfo(@ApiParam(value = "addPNRInfo参数对象", required = true)
                                          @RequestBody AddPnrInfoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , addPNRInfo: {}", uuid, JSON.toJSONString(request));
        Vector<BookOI> dest = new Vector<>();
        try {
            if (CollectionUtils.isNotEmpty(request.getBookInfomation().getOis())) {
                for (Object item : request.getBookInfomation().getOis()) {
                    dest.add(JSONObject.parseObject(JSONObject.toJSONString(item), BookOI.class));
                }
                request.getBookInfomation().setOis(dest);
            }
            changePojo(request.getBookInfomation().getFp(), BookFP.class);
            changePojo(request.getBookInfomation().getFc(), BookFC.class);
            changePojo(request.getBookInfomation().getFn(), BookFN.class);
        } catch (Throwable e) {
            log.error("======入参转换失败:linkHashMap转换成BookOI======");
        }
        BaseResponse avResponse = pnrManager.addPNRInfo(request);
        log.info("End uuid: {} , addPNRInfo: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "delPnrItem")
    @RequestMapping(value = "delPnrItem", method = RequestMethod.POST)
    RestResponse<BaseResponse> delPnrItem(@ApiParam(value = "delPnrItem参数对象", required = true)
                                          @RequestBody DelPnrItemRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , delPnrItem: {}", uuid, JSON.toJSONString(request));
        BaseResponse avResponse = pnrManager.delPnrItem(request);
        log.info("End uuid: {} , delPnrItem: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }


    @ApiOperation(value = "匹配内容删除pnr下标项", notes = "匹配内容删除pnr下标项")
    @ApiImplicitParams({@ApiImplicitParam(name = "request", value = "参数", required = true, dataType = "DelPnrItemRequest", paramType = "body")})
    @Logger
    @RequestMapping(value = "newDelPnrItem", method = RequestMethod.POST)
    RestResponse<BaseResponse> newDelPnrItem(@RequestBody DelPnrItemRequest request) {
        return RestResponse.ok(pnrService.delPnrItem(request));
    }

    @ApiOperation(value = "rt")
    @RequestMapping(value = "rt", method = RequestMethod.POST)
    RestResponse<RTResult> rt(@ApiParam(value = "rt参数对象", required = true)
                              @RequestBody RTRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , rt: {}", uuid, JSON.toJSONString(request));
        RTResult avResponse = pnrManager.rt(request);
        log.info("End uuid: {} , rt: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "splitPnr")
    @RequestMapping(value = "splitPnr", method = RequestMethod.POST)
    RestResponse<SplitPNRResponse> splitPnr(@ApiParam(value = "splitPnr参数对象", required = true)
                                            @RequestBody SplitPnrRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , splitPnr: {}", uuid, JSON.toJSONString(request));
        SplitPNRResponse avResponse = pnrManager.splitPnr(request);
        log.info("End uuid: {} , splitPnr: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "cancelPnr")
    @RequestMapping(value = "cancelPnr", method = RequestMethod.POST)
    @Logger
    RestResponse<BaseResponse> cancelPnr(@ApiParam(value = "cancelPnr参数对象", required = true)
                                         @RequestBody CancelPnrRequest request) {
        return RestResponse.ok(pnrService.cancelPnr(request));
    }


    @ApiOperation(value = "cancelPnrK")
    @RequestMapping(value = "cancelPnrK", method = RequestMethod.POST)
    RestResponse<BaseResponse> cancelPnrK(@ApiParam(value = "cancelPnr参数对象", required = true)
                                          @RequestBody CancelPnrRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , cancelPnr: {}", uuid, JSON.toJSONString(request));
        //为空，不再去调IBE取消动作
        if (TextUtils.isEmpty(request.getPnrNo())) {
            BaseResponse response = new BaseResponse();
            response.setSuccess(true);
            log.info("End uuid: {} , cancelPnrK: {}", uuid, JSON.toJSONString(response));
            return RestResponse.ok(response);
        }
        BaseResponse avResponse = pnrManager.cancelPnrK(request);
        log.info("End uuid: {} , cancelPnrK: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "rtHistory")
    @RequestMapping(value = "rtHistory", method = RequestMethod.POST)
    RestResponse<RTResult> rtHistory(@ApiParam(value = "rtHistory参数对象", required = true)
                                     @RequestBody RTRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , rtHistory: {}", uuid, JSON.toJSONString(request));
        RTResult rtResponse = pnrManager.rtHistory(request);
        log.info("End uuid: {} , rtHistory: {}", uuid, JSON.toJSONString(rtResponse));
        return RestResponse.ok(rtResponse);
    }

    @ApiOperation(value = "changePaxInfo")
    @RequestMapping(value = "changePaxInfo", method = RequestMethod.POST)
    RestResponse<BaseResponse> changePaxInfo(@ApiParam(value = "changePaxInfo参数", required = true)
                                             @RequestBody ChangePaxInfoReq request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , changePaxInfo: {}", uuid, JSON.toJSONString(request));
        BaseResponse changeResponse = pnrManager.changePaxInfo(request);
        log.info("End uuid: {} , changePaxInfo: {}", uuid, JSON.toJSONString(changeResponse));
        return RestResponse.ok(changeResponse);
    }

    /**
     * 更新pnr中的信息
     *
     * @param updatePnrReq
     * @return
     */
    @ApiOperation(value = "updatePnr")
    @RequestMapping(value = "updatePnr", method = RequestMethod.POST)
    RestResponse<BaseResponse> updatePnr(@ApiParam(value = "updatePnr参数", required = true)
                                         @RequestBody UpdatePnrReq updatePnrReq) {
        String uuid = UUID.randomUUID().toString();
        log.info("updatePnr.Start uuid: {} , updatePnrReq: {}", uuid, JSON.toJSONString(updatePnrReq));
        changePojo(updatePnrReq.getBookInformation().getFc(), BookFC.class);
        changePojo(updatePnrReq.getBookInformation().getFn(), BookFN.class);
        changePojo(updatePnrReq.getBookInformation().getFp(), BookFP.class);
        changePojo(updatePnrReq.getBookInformation().getOis(), BookOI.class);
//        changePojo(updatePnrReq.getBookInformation().getFp(),BookFP.class);
//        changePojo(updatePnrReq.getBookInformation().getFp(),BookFP.class);
        BaseResponse updatePnrResponse = pnrManager.updatePnr(updatePnrReq);
        log.info("updatePnr.End uuid: {} , updatePnrRes: {}", uuid, JSON.toJSONString(updatePnrResponse));
        return RestResponse.ok(updatePnrResponse);
    }

    private <T> void changePojo(Vector pojo, Class<T> clazz) {
//        Vector fc = updatePnrReq.getBookInformation().getFc();
        if (CollectionUtils.isEmpty(pojo)) {
            return;
        }
        Vector<T> transfer = new Vector<>();
        for (Object item :
                pojo) {
            T bookFC = JSONObject.parseObject(JSONObject.toJSONString(item), clazz);
            transfer.add(bookFC);
        }
        pojo.clear();
        pojo.addAll(transfer);
    }

    /**
     * 强kpnr航段中的信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "confirmAirSeg")
    @RequestMapping(value = "confirmAirSeg", method = RequestMethod.POST)
    RestResponse<BaseResponse> confirmAirSeg(@ApiParam(value = "confirmAirSeg参数", required = true)
                                             @RequestBody ReconfirmAirSegRQ request) {
        String uuid = UUID.randomUUID().toString();
        log.info("confirmAirSeg.Start uuid: {} , reconfirmAirSegReq: {}", uuid, JSON.toJSONString(request));
        BaseResponse reconfirmAirSegResponse = pnrManager.confirmAirSeg(request);
        log.info("confirmAirSeg.End uuid: {} , reconfirmAirSegRes: {}", uuid, JSON.toJSONString(reconfirmAirSegResponse));
        return RestResponse.ok(reconfirmAirSegResponse);
    }

    /**
     * 确认pnr航段中的信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "reconfirmAirSeg")
    @RequestMapping(value = "reconfirmAirSeg", method = RequestMethod.POST)
    RestResponse<BaseResponse> reconfirmAirSeg(@ApiParam(value = "reconfirmAirSeg参数", required = true)
                                               @RequestBody ReconfirmAirSegReq request) {
        String uuid = UUID.randomUUID().toString();
        log.info("reconfirmAirSeg.Start uuid: {} , reconfirmAirSegReq: {}", uuid, JSON.toJSONString(request));
        BaseResponse reconfirmAirSegResponse = pnrManager.reconfirmAirSeg(request);
        log.info("reconfirmAirSeg.End uuid: {} , reconfirmAirSegRes: {}", uuid, JSON.toJSONString(reconfirmAirSegResponse));
        return RestResponse.ok(reconfirmAirSegResponse);
    }

    /**
     * 恢复pnr航段中的TKNE信息
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "addPnrTKNE")
    @RequestMapping(value = "addPnrTKNE", method = RequestMethod.POST)
    RestResponse<BaseResponse> addPnrTKNE(@ApiParam(value = "addPnrTKNE参数", required = true)
                                          @RequestBody AddPnrInfoRequest request) throws Exception {
        String uuid = UUID.randomUUID().toString();
        log.info("addPnrTKNE.Start uuid: {} , AddPnrInfoRequest: {}", uuid, JSON.toJSONString(request));
        BaseResponse res = pnrManager.addPnrTKNE(request);
        log.info("addPnrTKNE.End uuid: {} , AddPnrInfoResponse: {}", uuid, JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

    @ApiOperation(value = "checkAllSegmentHK")
    @RequestMapping(value = "checkAllSegmentHK", method = RequestMethod.POST)
    RestResponse<RTResult> checkAllSegmentHK(@ApiParam(value = "checkAllSegmentHK参数对象", required = true)
                                             @RequestBody RTRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , checkAllSegmentHK: {}", uuid, JSON.toJSONString(request));
        boolean result = false;
        try {
            result = pnrManager.checkAllSegmentHK(request);
        } catch (Exception e) {
            log.info("uuid: {} , checkAllSegmentHK fail: {}", uuid, JSON.toJSONString(request));
            return RestResponse.ok(false);
        }
        log.info("End uuid: {} , checkAllSegmentHK: {}", uuid, JSON.toJSONString(request));
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "checkAllSegmentHKByPnrs")
    @RequestMapping(value = "checkAllSegmentHKByPnrs", method = RequestMethod.POST)
    RestResponse<RTResult> checkAllSegmentHKByPnrs(@ApiParam(value = "checkAllSegmentHK参数对象", required = true)
                                             @RequestBody List<RTRequest> requests) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , checkAllSegmentHKByPnrs: {}", uuid, JSON.toJSONString(requests));
        boolean result = false;
        for(RTRequest rt : requests) {
            try {
                result = pnrManager.checkAllSegmentHK(rt);
                if(!result) {
                    break;
                }
            } catch (Exception e) {
                log.info("uuid: {} , checkAllSegmentHKByPnrs fail: {}", uuid, JSON.toJSONString(rt));
                RestResponse.ok(false);
            }
        }
        log.info("End uuid: {} , checkAllSegmentHKByPnrs: {}", uuid, JSON.toJSONString(requests));
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "ml")
    @RequestMapping(value = "ml", method = RequestMethod.POST)
    RestResponse<MLResponse> ml(@ApiParam(value = "ml参数对象", required = true)
                                @RequestBody MLRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , ml: {}", uuid, JSON.toJSONString(request));
        MLResponse result = pnrManager.ml(request);
        log.info("End uuid: {} , ml: {}", uuid, JSON.toJSONString(result));
        return RestResponse.ok(result);
    }

    @ApiOperation(value = "reConfirmAirSeg")
    @RequestMapping(value = "reConfirmAirSeg", method = RequestMethod.POST)
    RestResponse<BaseResponse> reConfirmAirSeg(@ApiParam(value = "reConfirmAirSeg参数", required = true)
                                               @RequestBody ReconfirmAirSegReq request) throws IBEException {
        String uuid = UUID.randomUUID().toString();
        log.info("reConfirmAirSeg.Start uuid: {} , ReconfirmAirSegRQ: {}", uuid, JSON.toJSONString(request));
        BaseResponse res = pnrManager.reConfirmAirSeg(request);
        log.info("reConfirmAirSeg.End uuid: {} , ReconfirmAirSegRQResponse: {}", uuid, JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

    @ApiOperation(value = "changeAirSeg")
    @RequestMapping(value = "changeAirSeg", method = RequestMethod.POST)
    RestResponse<BaseResponse> changeAirSeg(@RequestBody ChangeAirSegReq request) throws IBEException {
        String uuid = UUID.randomUUID().toString();
        log.info("changeAirSeg.Start uuid: {} , ReconfirmAirSegRQ: {}", uuid, JSON.toJSONString(request));
        BaseResponse res = pnrManager.changeAirSeg(request.getAirlineCode(), request.getPnrNo(), request.getOrg(), request.getDest());
        log.info("changeAirSeg.End uuid: {} , ReconfirmAirSegRQResponse: {}", uuid, JSON.toJSONString(res));
        return RestResponse.ok(res);
    }

}