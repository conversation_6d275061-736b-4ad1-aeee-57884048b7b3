package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
//@Data
//@EqualsAndHashCode(callSuper=true)
//@ApiModel
public class FareCalculation implements Serializable {

	public FareCalculation(){}

	/**
	 * 
	 */
	private static final long serialVersionUID = -7833137478479324101L;
	 public static final int IT = -2;
	 public static final int BT = -3;
	 static final DecimalFormat format = new DecimalFormat(
	   "0.00");
	
	 /** @deprecated */
protected double dfee = -1.0D;
	
	 protected String dseg = null;
	
	 protected String extraInfo = null;
	 protected String fc;
	 protected Date fcDate = null;
	
	 /** @deprecated */
	 protected double hfee = -1.0D;
	
	 protected String hseg = null;
	 protected boolean infant;
	 protected String moneytype = "CNY";
	
	 protected List<fctax> otax = new ArrayList();
	
	 /** @deprecated */
	 protected double pfee = -1.0D;
	
	 protected String pseg = null;
	 public static final double UNDEFINED = -1.0D;
	 public static final double UNDEFINEDROE = -10.0D;
	 protected double ROE = -10.0D;
	
	 protected List<fctax> rtax = new ArrayList();
	
	 protected List<fcitem> segments = new ArrayList();
	
	 protected List<fctax> tax = new ArrayList();
	
	 protected double total = -1.0D;
	
	 protected String freeText = null;
	
	 List<fcSurcharge> surcharges = new ArrayList<fcSurcharge>();
	 protected List<charge> charges;
	
	 public void addBranchFC(FareCalculation bookFC)
	 {
	   int j = bookFC.getSegmentCnt();
	
	   for (int i = 0; i < j; ++i) {
	     fcitem item = bookFC.getSegment(i);
	     if (i == 0) {
	       item.branchstart = true;
	     }
	     if (i == j - 1) {
	       item.branchend = true;
	     }
	     this.segments.add(item);
	   }
	 }
	
	 public void addFC(FareCalculation bookFC)
	 {
	   int j = bookFC.getSegmentCnt();
	
	   for (int i = 0; i < j; ++i) {
	     fcitem item = bookFC.getSegment(i);
	     if ((i != 0) ||
	       (i == j - 1));
	     this.segments.add(item);
	   }
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   this.segments.add(fc);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, int pcs, int pweight, Calendar invalidBefore, Calendar invalidAfter)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   fc.invalidBefore = invalidBefore;
	   fc.invalidAfter = invalidAfter;
	
	   fc.freePackageCount = pcs;
	   fc.freePackageWeight = pweight;
	   this.segments.add(fc);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, int pcs, int pweight, Calendar invalidBefore, Calendar invalidAfter, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg)
	 {
	   addFC(org, dst, airline, farebasis, price, pcs, pweight, invalidBefore, invalidAfter, E, stopOver,
	     traveldirect, milesurcharge, msseg, q, qseg, null, null, false);
	 }
	
	 /** @deprecated */
	 public void addFC(String org, String dst, String airline, String farebasis, double price, int pcs, int pweight, Calendar invalidBefore, Calendar invalidAfter, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, String mileIntermediatePoint, String fareseg)
	 {
	   addFC(org, dst, airline, farebasis, price, pcs, pweight, invalidBefore, invalidAfter, E, stopOver,
	     traveldirect, milesurcharge, msseg, q, qseg, mileIntermediatePoint, fareseg, false);
	 }
	
	 /** @deprecated */
	 public void addFC(String org, String dst, String airline, String farebasis, double price, int pcs, int pweight, Calendar invalidBefore, Calendar invalidAfter, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, String mileIntermediatePoint, String fareseg, boolean tktfrom)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   fc.invalidBefore = invalidBefore;
	   fc.invalidAfter = invalidAfter;
	
	   fc.freePackageCount = pcs;
	   fc.freePackageWeight = pweight;
	
	   fc.traveldirection = traveldirect;
	   fc.milesurcharge = milesurcharge;
	   fc.msseg = msseg;
	   fc.stopover = stopOver;
	   fc.e = E;
	   fc.q = q;
	   fc.qseg = qseg;
	   fc.mileIntermediatePoint = mileIntermediatePoint;
	   fc.fareseg = fareseg;
	   fc.tktFromFlag = tktfrom;
	   this.segments.add(fc);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, int pcs, int pweight, String pweightUnit, Calendar invalidBefore, Calendar invalidAfter, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, String mileIntermediatePoint, String fareseg, boolean tktfrom)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   fc.invalidBefore = invalidBefore;
	   fc.invalidAfter = invalidAfter;
	
	   fc.freePackageCount = pcs;
	   fc.freePackageWeight = pweight;
	   fc.freePackageWeightUnit = pweightUnit;
	   fc.traveldirection = traveldirect;
	   fc.milesurcharge = milesurcharge;
	   fc.msseg = msseg;
	   fc.stopover = stopOver;
	   fc.e = E;
	   fc.q = q;
	   fc.qseg = qseg;
	   fc.mileIntermediatePoint = mileIntermediatePoint;
	   fc.fareseg = fareseg;
	   fc.tktFromFlag = tktfrom;
	   this.segments.add(fc);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, String f, String NVB, String NVA, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, boolean branchStart, boolean branchEnd)
	 {
	   addFC(org, dst, airline, farebasis, price, f, NVB, NVA, E, stopOver,
	     traveldirect, milesurcharge, msseg, q, qseg, branchStart, branchEnd, null, null, false);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, String f, String NVB, String NVA, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, boolean branchStart, boolean branchEnd, String mileIntermediatePoint, String fareseg)
	 {
	   addFC(org, dst, airline, farebasis, price, f, NVB, NVA, E, stopOver,
	     traveldirect, milesurcharge, msseg, q, qseg, branchStart, branchEnd, mileIntermediatePoint, fareseg, false);
	 }
	
	 public void addFC(String org, String dst, String airline, String farebasis, double price, String f, String NVB, String NVA, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, boolean branchStart, boolean branchEnd, String mileIntermediatePoint, String fareseg, boolean tktfrom)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   Calendar invalidBefore = null;
	   try {
	     if (NVB.length() == 0) {
	       invalidBefore = null;  }
	     else if (NVB.equals("B-0")) {
	       invalidBefore = new GregorianCalendar(1990, 1, 1);
	     }
	     else invalidBefore = QDateTime.stringToCalendar(NVB.substring(2), "ddMMMyy"); } catch (Exception localException) {
	   }
	    fc.invalidBefore = invalidBefore;
	   Calendar invalidAfter = null;
	   try {
	     if (NVA.length() == 0) {
	       invalidAfter = null;  }
	     else
	     if (NVA.equals("A-0")) {
	       invalidAfter = new GregorianCalendar(1990, 1, 1);
	     }
	     else
	    	 invalidAfter = QDateTime.stringToCalendar(NVA.substring(2), "ddMMMyy"); } catch (Exception localException1) {
	   }
	    fc.invalidAfter = invalidAfter;
	
	   if (f.length() == 0) {
	     fc.freePackageCount = -1;
	     fc.freePackageWeight = -1;
	   } else if (f.equals("F-0")) {
	     fc.freePackageCount = -2;
	     fc.freePackageWeight = -1;
	   }
	   else
	   {
	     int t;
	     if (f.endsWith("PC")) {
	       t = Integer.parseInt(f.substring(2, f.length() - 2));
	       fc.freePackageCount = t;
	       fc.freePackageWeight = -1;
	     }
	     else if (f.endsWith("KG")) {
	       t = Integer.parseInt(f.substring(2, f.length() - 2));
	       fc.freePackageCount = -1;
	       fc.freePackageWeight = t;
	     } else if (f.endsWith("L")) {
	       t = Integer.parseInt(f.substring(2, f.length() - 1));
	       fc.freePackageCount = -1;
	       fc.freePackageWeight = t;
	       fc.freePackageWeightUnit = "L";
	     } else if (f.endsWith("UNK")) {
	       fc.freePackageCount = -3;
	       fc.freePackageWeight = -1;
	     }
	
	   }
	
	   fc.traveldirection = traveldirect;
	   fc.milesurcharge = milesurcharge;
	   fc.msseg = msseg;
	   fc.stopover = stopOver;
	   fc.e = E;
	   fc.q = q;
	   fc.qseg = qseg;
	   fc.mileIntermediatePoint = mileIntermediatePoint;
	   fc.fareseg = fareseg;
	   this.segments.add(fc);
	 }
	
	 public void addFCARNK()
	 {
	   fcitem fc = new fcitem();
	   fc.org = "";
	   fc.dst = "";
	   fc.amount = 0.0D;
	   fc.aircorp = "//";
	   fc.fltclass = "";
	
	   this.segments.add(fc);
	 }
	
	 public void addFCARNKwithGroundTx()
	 {
	   fcitem fc = new fcitem();
	   fc.org = "";
	   fc.dst = "";
	   fc.amount = 0.0D;
	   fc.aircorp = "//";
	   fc.fltclass = "";
	   this.segments.add(fc);
	 }
	
	 public void addFCARNKwithoutGroundTx()
	 {
	   fcitem fc = new fcitem();
	   fc.org = "";
	   fc.dst = "";
	   fc.amount = 0.0D;
	   fc.aircorp = "/-";
	   fc.fltclass = "";
	
	   this.segments.add(fc);
	 }
	
	 public String getAircorp(int i)
	   throws Exception
	 {
	   return getSegment(i).aircorp;
	 }
	
	 public boolean getBranchEnd(int i)
	 {
	   return getSegment(i).branchend;
	 }
	
	 public boolean getBranchStart(int i)
	 {
	   return getSegment(i).branchstart;
	 }
	
	 public String getCarrier(int i)
	   throws Exception
	 {
	   return getSegment(i).aircorp;
	 }
	
	 public String getCurrency()
	 {
	   return this.moneytype;
	 }
	
	 public double getDfee()
	 {
	   return this.dfee;
	 }
	
	 public String getDseg()
	 {
	   return this.dseg;
	 }
	
	 public String getDst(int i)
	   throws Exception
	 {
	   return getSegment(i).dst;
	 }
	
	 public boolean getE(int i)
	 {
	   return getSegment(i).e;
	 }
	
	 public double getExtraTaxAmount(int i)
	 {
	   return ((fctax)this.tax.get(i)).value;
	 }
	
	 public int getExtraTaxCnt()
	 {
	   return this.tax.size();
	 }
	
	 public String getExtraTaxCode(int i)
	 {
	   return ((fctax)this.tax.get(i)).code;
	 }
	
	 public String getExtraTaxCurrency(int i)
	 {
	   return ((fctax)this.tax.get(i)).currency;
	 }
	
	 public String getFareBasis(int i)
	   throws Exception
	 {
	   return getSegment(i).fltclass;
	 }
	
	 public String getFc()
	 {
	   return this.fc;
	 }
	
	 public Date getFCDate()
	 {
	   return this.fcDate;
	 }
	
	 public double getHfee()
	 {
	   return this.hfee;
	 }
	
	 public String getHseg()
	 {
	   return this.hseg;
	 }
	
	 public String getInvalidateAfter(int i)
	   throws Exception
	 {
	   fcitem item = (fcitem)this.segments.get(i);
	   if (item.invalidAfter == null) {
	     return "";
	   }
	   if (item.invalidAfter.get(1) < 2000) {
	     return "A-0";
	   }
	   return "A-" +
	     QDateTime.calendarToString(item.invalidAfter, "DDMMMYY");
	 }
	
	 public String getInvalidateBefore(int i)
	   throws Exception
	 {
	   fcitem item = (fcitem)this.segments.get(i);
	   if (item.invalidBefore == null) {
	     return "";
	   }
	   if (item.invalidBefore.get(1) < 2000) {
	     return "B-0";
	   }
	   return "B-" +
	     QDateTime.calendarToString(item.invalidBefore, "DDMMMYY");
	 }
	
	 public int getMileSurcharge(int i)
	 {
	   return getSegment(i).milesurcharge;
	 }
	
	 public String getMileSurchargeSeg(int i)
	 {
	   return getSegment(i).msseg;
	 }
	
	 public String getOrg(int i)
	   throws Exception
	 {
	   return getSegment(i).org;
	 }
	
	 public double getOriginalExtraTaxAmount(int i)
	 {
	   return ((fctax)this.otax.get(i)).value;
	 }
	
	 public int getOriginalExtraTaxCnt()
	 {
	   return this.otax.size();
	 }
	
	 public String getOriginalExtraTaxCode(int i)
	 {
	   return ((fctax)this.otax.get(i)).code;
	 }
	
	 public String getOriginalExtraTaxCurrency(int i)
	 {
	   return ((fctax)this.otax.get(i)).currency;
	 }
	
	 public String getPackage(int i)
	   throws Exception
	 {
	   fcitem item = (fcitem)this.segments.get(i);
	   if (item.freePackageCount == -3) {
	     return "F-UNK";
	   }
	   if (item.freePackageCount == -2) {
	     return "F-0";
	   }
	   if (item.freePackageCount > 0) {
	     return "F-" + item.freePackageCount + "PC";
	   }
	   if (item.freePackageCount == 0) {
	     return "F-00PC";
	   }
	   if (item.freePackageWeight >= 10) {
	     return "F-" + item.freePackageWeight +
	       (((item.freePackageWeightUnit == null) || (item.freePackageWeightUnit.length() == 0)) ? "KG" : item.freePackageWeightUnit);
	   }
	   if ((item.freePackageWeight >= 0) && (item.freePackageWeight < 10)) {
	     return "F-0" + item.freePackageWeight +
	       (((item.freePackageWeightUnit == null) || (item.freePackageWeightUnit.length() == 0)) ? "KG" : item.freePackageWeightUnit);
	   }
	   return "";
	 }
	
	 public double getPfee()
	 {
	   return this.pfee;
	 }
	
	 public double getPrice(int i)
	   throws Exception
	 {
	   return getSegment(i).amount;
	 }
	
	 public String getPseg()
	 {
	   return this.pseg;
	 }
	
	 /** @deprecated */
	 public double getQ(int i)
	   throws Exception
	 {
	   return getSegment(i).q;
	 }
	
	 /** @deprecated */
	 public String getQSeg(int i)
	   throws Exception
	 {
	   return getSegment(i).qseg;
	 }
	
	 public double getRefundExtraTaxAmount(int i)
	 {
	   return ((fctax)this.rtax.get(i)).value;
	 }
	
	 public int getRefundExtraTaxCnt()
	 {
	   return this.rtax.size();
	 }
	
	 public String getRefundExtraTaxCode(int i)
	 {
	   return ((fctax)this.rtax.get(i)).code;
	 }
	
	 public String getRefundExtraTaxCurrency(int i)
	 {
	   return ((fctax)this.rtax.get(i)).currency;
	 }
	
	 public double getROE()
	 {
	   return this.ROE;
	 }
	 
	 public List<fcitem> getSegments(){
		 return this.segments;
	 }
	
	 public fcitem getSegment(int index)
	 {
	   return ((fcitem)this.segments.get(index));
	 }
	
	 public int getSegmentCnt()
	 {
	   return this.segments.size();
	 }
	
	 public boolean getStopOver(int i)
	 {
	   return getSegment(i).stopover;
	 }
	
	 public String getMileIntermediatePoint(int i)
	 {
	   return getSegment(i).mileIntermediatePoint;
	 }
	
	 public String getFareseg(int i)
	 {
	   return getSegment(i).fareseg;
	 }
	
	 public String getTravelDirection(int i)
	 {
	   return getSegment(i).traveldirection;
	 }
	
	 public void insertTax(String currency, double value, String code)
	 {
	   if ((code == null) || (code.length() == 0)) {
	     return;
	   }
	   if ((currency == null) || (currency.length() == 0)) {
	     currency = "";
	   }
	   if (value <= -0.0001D) {
	     return;
	   }
	   this.tax.add(new fctax(currency, value, code));
	 }
	
	 public void insertTax(int index, String currency, double value, String code)
	 {
	   if ((code == null) || (code.length() == 0)) {
	     return;
	   }
	   if ((currency == null) || (currency.length() == 0)) {
	     currency = "";
	   }
	   if (value <= -0.0001D) {
	     return;
	   }
	   this.tax.add(index, new fctax(currency, value, code));
	 }
	
	 public void insertOTax(String currency, double value, String code) {
	   if ((code == null) || (code.length() == 0)) {
	     return;
	   }
	   if ((currency == null) || (currency.length() == 0)) {
	     currency = "";
	   }
	   if (value <= -0.0001D) {
	     return;
	   }
	   this.otax.add(new fctax(currency, value, code));
	 }
	
	 public void insertRTax(String currency, double value, String code) {
	   if ((code == null) || (code.length() == 0)) {
	     return;
	   }
	   if ((currency == null) || (currency.trim().length() == 0)) {
	     currency = "";
	   }
	   if (value <= -0.0001D) {
	     return;
	   }
	   this.rtax.add(new fctax(currency, value, code));
	 }
	
	 public void insertTaxes(String currecny, Map<String, Double> map)
	 {
	   if (map == null) {
	     return;
	   }
	   String theCurrency = currecny;
	   if ((currecny == null) || (currecny.trim().length() == 0)) {
	     theCurrency = "";
	   }
	   theCurrency = theCurrency.trim();
	   Iterator iter = map.keySet().iterator();
	   while (iter.hasNext()) {
	     String key = (String)iter.next();
	     this.tax.add(new fctax(theCurrency, ((Double)map.get(key)).doubleValue(), key));
	   }
	 }
	
	 public void setExtraTaxCurrency(String currency)
	 {
	   if ((currency == null) || (currency.trim().length() != 3)) {
	     return;
	   }
	   for (int i = 0; i < this.tax.size(); ++i)
	     if ("".equals(((fctax)this.tax.get(i)).currency))
	       ((fctax)this.tax.get(i)).currency = currency;
	 }
	
	 public boolean isInfant()
	 {
	   return this.infant;
	 }
	
	 public void setFc(String newFc)
	 {
	   this.fc = newFc;
	 }
	
	 public void setInfant(boolean newInfant)
	 {
	   this.infant = newInfant;
	 }
	
	 public String makeString()
	 {
	   DecimalFormat format = new DecimalFormat("0.00");
	   try
	   {
//	     int j;
	     StringBuffer sb = new StringBuffer("原文：" + getFc());
	     if (getFCDate() != null) {
	       sb.append("\r\nFC日期：");
	       sb.append(QDateTime.dateToString(getFCDate(), "ddmmmyy"));
	     }
	
	     int sc = 0;
	     for (int i = 0; i < getSegmentCnt(); ++i) {
	       sb.append("\r\nSegment: ");
	       if (isTktFromFlag(i)) {
	         sb.append("自此航段开始出票(T-)\r\n");
	       }
	       sb.append((getBranchEnd(i)) ? "到此航段结束为旁岔程终止。\r\n" : (getBranchStart(i)) ? "自此航段始为旁岔程。\r\n" : "\r\n");
	       sb.append("       起始:" + getOrg(i));
	       if (getPackage(i).length() > 0) {
	         sb.append("   行李限制：" + getPackage(i));
	       }
	       if (getInvalidateAfter(i).length() > 0) {
	         sb.append("   NVA：" + getInvalidateAfter(i));
	       }
	       if (getInvalidateBefore(i).length() > 0) {
	         sb.append("   NVB：" + getInvalidateBefore(i));
	       }
	       sb.append("  承运人:" + getAircorp(i));
	
	       if ((getTravelDirection(i) != null) && (getTravelDirection(i).length() > 0)) {
	         sb.append("  适用" + getTravelDirection(i) + "方向运价");
	       }
	
	       if (getSegment(i).getStopoverSurcharge() > 0.0D) {
	         sb.append("经停附加费：" + format.format(getSegment(i).getStopoverSurcharge()));
	       }
	
	       if (getE(i)) {
	         sb.append(" 经由此终到点有忧惠");
	       }
	       if (!(getStopOver(i))) {
	         sb.append(" 此终到点为非经停点");
	       }
	       sb.append("  终到:" + getDst(i));
	
	       for (int j = 0; j < getSegment(i).qs.size(); ++j)
	       {
	         if ((getQSeg(i, j) != null) && (getQSeg(i, j).length() > 0))
	           sb.append(" 航段:" + getQSeg(i, j));
	         else {
	           sb.append(" ");
	         }
	         sb.append(" Q附加：" + format.format(getQValue(i, j)));
	       }
	
	       if (getMileSurcharge(i) >= 0) {
	         sb.append(" 至此航段使用里程制运价");
	         if ((getMileIntermediatePoint(i) != null) && (getMileIntermediatePoint(i).length() > 0)) {
	           sb.append(" 里程中间点：" + getMileIntermediatePoint(i));
	         }
	       }
	       if ((getMileSurchargeSeg(i) != null) && (getMileSurchargeSeg(i).length() > 0)) {
	         if (!(getE(i)))
	           sb.append(" 票价提高至此段（" + getMileSurchargeSeg(i) + "）价格");
	         else {
	           sb.append(" 此段（" + getMileSurchargeSeg(i) + "）价格有优惠");
	         }
	       }
	       else if ((((getMileSurchargeSeg(i) == null) || (getMileSurchargeSeg(i).length() == 0))) && (getMileSurcharge(i) > 0))
	       {
	         boolean E = false;
	         for (int j = i; j > 0; --j) {
	           if ((j < i) && (getPrice(j) > -0.0001D)) {
	             break;
	           }
	           if (getE(j)) {
	             E = true;
	             break;
	           }
	         }
	         if ((!(getE(i))) && (!(E))) {
	           sb.append(" 票价提高");
	         } else {
	           sb.append(" 价格优惠");
	           if (getSegment(i).isE_xxx()) {
	             sb.append(",优惠点未知(XXX)");
	           }
	         }
	       }
	
	       if (getMileSurcharge(i) > 0) {
	         sb.append(" 比例" + getMileSurcharge(i) + "%");
	       }
	       if ((getSegment(i).fareseg != null) && (getSegment(i).fareseg.length() > 0)) {
	         sb.append(" 按" + getSegment(i).fareseg + "段计算运价 ");
	       }
	       if (getPrice(i) > -0.001D) {
	         if ((sc > 0) && (getMileSurcharge(i) < 0)) {
	           sb.append(" 至此航段为联程运价");
	         }
	         sb.append("  运价：" + format.format(getPrice(i)));
	         sc = 0;
	       } else {
	         ++sc;
	       }
	       if ((getFareBasis(i) != null) && (getFareBasis(i).length() > 0)) {
	         sb.append("  运价基础：" + getFareBasis(i));
	       }
	     }
	
	     sb.append("\r\n");
	
	     if (getSurchargesCount() > 0) {
	       for (int i = 0; i < getSurchargesCount(); ++i) {
	         fcSurcharge surcharge = getSurcharge(i);
	         sb.append("   费用：" + surcharge.getCode() + " " + format.format(surcharge.getValue()));
	       }
	       sb.append("\r\n");
	     }
	     if (getCharge(null).size() > 0) {
	       List list = getCharge(null);
	      int j = getCharge(null).size();
	       for (int i = 0; i < j; ++i) {
	         charge othcharge = (charge)list.get(i);
	         sb.append("  其他费用（D/H/P等）：" + othcharge.getType() + " " + othcharge.getSegInfo() + " " + format.format(othcharge.getAmount()));
	         sb.append("\r\n");
	       }
	       sb.append("\r\n");
	     }
	
	     sb.append("总运价:" + getCurrency() + "   " + format.format(getTotal()));
	     if (getROE() > 0.0D)
	       sb.append("  ROE:" + new DecimalFormat("0.000000").format(getROE()));
	     if (getTax().size() > 0) {
	       sb.append("\r\nEXTRA Tax：");
	       for (int i = 0; i < this.tax.size(); ++i) {
	         sb.append("  " + this.tax.get(i));
	       }
	     }
	     if (getRtax().size() > 0) {
	       sb.append("\r\nEXTRA Refund Tax：");
	       for (int i = 0; i < this.rtax.size(); ++i) {
	         sb.append("  " + this.rtax.get(i));
	       }
	     }
	     if (getOtax().size() > 0) {
	       sb.append("\r\nEXTRA Orignal Tax：");
	       for (int i = 0; i < this.otax.size(); ++i) {
	         sb.append("  " + this.otax.get(i));
	       }
	     }
	     if ((getExtraInfo() != null) && (getExtraInfo().length() > 0)) {
	       sb.append("\r\n附加信息：");
	       sb.append(getExtraInfo());
	     }
	     if ((getFreeText() != null) && (getFreeText().length() > 0)) {
	       sb.append("\r\n自由附加文本：");
	       sb.append(getFreeText());
	     }
	     return sb.toString();
	   } catch (Exception e) {
	     e.printStackTrace();
	   }
	   return "";
	 }
	
	 private boolean isTktFromFlag(int i)
	 {
	   return getSegment(i).isTktFromFlag();
	 }
	
	 public double getTotal()
	 {
	   return this.total;
	 }
	
	 public void setTotal(double total)
	 {
	   this.total = total;
	 }
	
	 public String getExtraInfo()
	 {
	   return this.extraInfo;
	 }
	
	 public List<fctax> getOtax() {
	   return this.otax;
	 }
	
	 public void setOtax(List<fctax> otax) {
	   this.otax = otax;
	 }
	
	 public List<fctax> getRtax() {
	   return this.rtax;
	 }
	
	 public void setRtax(List<fctax> rtax) {
	   this.rtax = rtax;
	 }
	
	 public List<fctax> getTax() {
	   return this.tax;
	 }
	
	 public void setTax(List<fctax> tax) {
	   this.tax = tax;
	 }
	
	 public Date getFcDate() {
	   return this.fcDate;
	 }
	
	 public void setFcDate(Date fcDate) {
	   this.fcDate = fcDate;
	 }
	
	 public String getMoneytype() {
	   return this.moneytype;
	 }
	
	 public void setMoneytype(String moneytype) {
	   this.moneytype = moneytype;
	 }
	
	 public void setDfee(double dfee) {
	   this.dfee = dfee;
	 }
	
	 public void setDseg(String dseg) {
	   this.dseg = dseg;
	 }
	
	 public void setPfee(double pfee) {
	   this.pfee = pfee;
	 }
	
	 public void setPseg(String pseg) {
	   this.pseg = pseg;
	 }
	
	 public void setROE(double roe) {
	   this.ROE = roe;
	 }
	
	 public String getFreeText()
	 {
	   return this.freeText;
	 }
	
	 public void setFreeText(String freeText)
	 {
	   this.freeText = freeText;
	 }
	
	 public void setExtraInfo(String extraInfo)
	 {
	   this.extraInfo = extraInfo;
	 }
	
	 public void setHfee(double hfee)
	 {
	   this.hfee = hfee;
	 }
	
	 public void setHseg(String hseg)
	 {
	   this.hseg = hseg;
	 }
	
	 public void setSegments(List<fcitem> segments)
	 {
//		 if(null==segments)
//			 return;
//		 for(Object object:segments) {
//			 fcitem item = new fcitem();
//			 BeanUtils.copyProperties(object, item);
//			 this.segments.add(item);
//		 }
	   this.segments = segments;
	 }
	
	 public void modifyExtraTax(String code, double amount) {
	   for (int i = 0; i < this.tax.size(); ++i) {
	     if (((fctax)this.tax.get(i)).code.equalsIgnoreCase(code))
	       ((fctax)this.tax.get(i)).value = amount;
	     if (amount == -1.0D)
	       ((fctax)this.tax.get(i)).currency = "";
	   }
	 }
	
	 public void removeExtraTax(String code) {
	   for (int i = 0; i < this.tax.size(); ++i)
	     if (((fctax)this.tax.get(i)).code.equalsIgnoreCase(code))
	       this.tax.remove(i);
	 }
	
	 public void insertSegment(int i, String org, String dst, String airline, String farebasis, double price, String f, String NVB, String NVA, boolean E, boolean stopOver, String traveldirect, int milesurcharge, String msseg, double q, String qseg, boolean branchStart, boolean branchEnd, String mileIntermediatePoint, String fareseg)
	 {
	   fcitem fc = new fcitem();
	   fc.org = org;
	   fc.dst = dst;
	   fc.amount = price;
	   fc.aircorp = airline;
	   fc.fltclass = farebasis;
	
	   Calendar invalidBefore = null;
	   try {
	     if (NVB.length() == 0) {
	       invalidBefore = null;
	     }  else
	     if (NVB.equals("B-0")) {
	       invalidBefore = new GregorianCalendar(1990, 1, 1);
	     }
	     else
	     invalidBefore = QDateTime.stringToCalendar(NVB.substring(2), "ddMMMyy");
	   } catch (Exception localException) {
	   }
	    fc.invalidBefore = invalidBefore;
	   Calendar invalidAfter = null;
	   try {
	     if (NVA.length() == 0) {
	       invalidAfter = null;
	     }        else
	     if (NVA.equals("A-0")) {
	       invalidAfter = new GregorianCalendar(1990, 1, 1);
	     }   else
	
	     invalidAfter = QDateTime.stringToCalendar(NVA.substring(2), "ddMMMyy");
	   } catch (Exception localException1) {
	   }
	    fc.invalidAfter = invalidAfter;
	
	   if (f.length() == 0) {
	     fc.freePackageCount = -1;
	     fc.freePackageWeight = -1;
	   } else if (f.equals("F-0")) {
	     fc.freePackageCount = -2;
	     fc.freePackageWeight = -1;
	   }
	   else
	   {
	     int t;
	     if (f.endsWith("PC")) {
	       t = Integer.parseInt(f.substring(2, f.length() - 2));
	       fc.freePackageCount = t;
	       fc.freePackageWeight = -1;
	     }
	     else if (f.endsWith("KG")) {
	       t = Integer.parseInt(f.substring(2, f.length() - 2));
	       fc.freePackageCount = -1;
	       fc.freePackageWeight = t;
	     }
	
	   }
	
	   fc.traveldirection = traveldirect;
	   fc.milesurcharge = milesurcharge;
	   fc.msseg = msseg;
	   fc.stopover = stopOver;
	   fc.e = E;
	   fc.q = q;
	   fc.qseg = qseg;
	   fc.mileIntermediatePoint = mileIntermediatePoint;
	   fc.fareseg = fareseg;
	   this.segments.add(i, fc);
	 }
	 
	 public void insertSegment(int i, fcitem fc)
	 {
		 this.segments.add(i, fc);
	 }
	
	 public fcitem newFcItem() {
		 fcitem fc = new fcitem();
		 return fc;
	 }
	 public int getSurchargesCount()
	 {
	   return this.surcharges.size();
	 }
	
	 public void addSurcharge(String code, double amount)
	 {
	   this.surcharges.add(new fcSurcharge(code, amount));
	 }
	
	 public fcSurcharge getSurcharge(int i)
	 {
	   if ((i >= 0) && (i < this.surcharges.size())) {
	     return ((fcSurcharge)this.surcharges.get(i));
	   }
	   return null;
	 }
	
	 public void addCharge(String type, String segInfo, double amount)
	 {
	   if (this.charges == null) {
	     this.charges = new ArrayList();
	   }
	   if ((type == null) || (segInfo == null) || (amount < 0.0D)) {
	     return;
	   }
	   this.charges.add(new charge(type, segInfo, amount));
	 }
	
	 public List getCharge(String type)
	 {
	   ArrayList list = new ArrayList();
	   if (this.charges == null) {
	     return list;
	   }
	   if (type == null) {
	     return Collections.unmodifiableList(this.charges);
	   }
	   for (int i = 0; i < this.charges.size(); ++i)
	   {
	     if (((charge)this.charges.get(i)).type.equals(type))
	       list.add(this.charges.get(i));
	   }
	   return list;
	 }
	
	 public String getQSeg(int segmentidx, int index)
	 {
	   return getSegment(segmentidx).getQSeg(index);
	 }
	
	 public double getQValue(int segmentidx, int index)
	 {
	   return getSegment(segmentidx).getQValue(index);
	 }
	
	 public void addQ(int segmentidx, double value, String seg)
	 {
	   getSegment(segmentidx).addQ(value, seg);
	 }
//	 @Data
	 public class charge
	   implements Serializable
	 {
	   private static final long serialVersionUID = -6923865167170106944L;
	   protected String type = "";
	   protected String segInfo = "";
	   protected double amount = 0.0D;
	
	   public charge() {  }
	
	   public charge(String paramString1, String paramString2, double paramDouble) { this.type = paramString1;
	     this.segInfo = paramString2;
	     this.amount = paramDouble; }
	
	   charge(charge paramcharge) {
	     if (paramcharge == null) {
	       return;
	     }
	     this.type = paramcharge.type;
	     this.segInfo = paramcharge.segInfo;
	     this.amount = paramcharge.amount;
	   }
	
	   public double getAmount()
	   {
	     return this.amount;
	   }
	
	   public void setAmount(double amount)
	   {
	     this.amount = amount;
	   }
	
	   public String getSegInfo()
	   {
	     return this.segInfo;
	   }
	
	   public void setSegInfo(String segInfo)
	   {
	     this.segInfo = segInfo;
	   }
	
	   public String getType()
	   {
	     return this.type;
	   }
	
	   public void setType(String type)
	   {
	     this.type = type;
	   }
	 }
	
	 public class fcSurcharge
	 {
	   protected String code;
	   protected double value;
	
	   public fcSurcharge(String paramString, double paramDouble)
	   {
	     this.code = paramString;
	     this.value = paramDouble;
	   }
	
	   public String getCode()
	   {
	     return this.code;
	   }
	
	   public void setCode(String code)
	   {
	     this.code = code;
	   }
	
	   public double getValue()
	   {
	     return this.value;
	   }
	
	   public void setValue(double value)
	   {
	     this.value = value;
	   }
	 }
//	 @Data
	 public static class fcitem
	   implements Serializable
	 {
	   private static final long serialVersionUID = 7348683484826845722L;
	   protected String aircorp;
	   protected double amount = -1.0D;
	
	   protected boolean branchend = false;
	
	   protected boolean branchstart = false;
	
	   protected boolean cancelA = false; protected boolean cancelB = false; protected boolean cancelF = false;
	   protected String dst;
	   protected boolean e = false;
	
	   protected boolean e_xxx = false;
	
	   protected String fltclass = "";
	
	   protected int freePackageCount = -1; protected int freePackageWeight = -1;
	
	   protected String freePackageWeightUnit = "KG";
	
	   protected Calendar invalidAfter = null; protected Calendar invalidBefore = null;
	
	   protected int milesurcharge = -1;
	
	   protected String msseg = null;
	   protected String org;
	
	   /** @deprecated */
	   protected double q = -1.0D;
	
	   /** @deprecated */
	   protected String qseg = null;
	
	   protected boolean stopover = true;
	
	   protected String traveldirection = null;
	
	   protected String fareseg = null;
	
	   protected String mileIntermediatePoint = null;
	
	   protected double stopoverSurcharge = -1.0D;
	
	   protected boolean tktFromFlag = false;
	
	   List<FareCalculation.fcq> qs = new ArrayList<FareCalculation.fcq> ();
	
	   public double getQValue(int i)
	   {
	     if ((i < 0) || (i > this.qs.size()) || (this.qs.get(i) == null)) {
	       return -1.0D;
	     }
	     return ((FareCalculation.fcq)this.qs.get(i)).getValue();
	   }
	
	   public int getQCount()
	   {
	     return this.qs.size();
	   }
	
	   public String getQSeg(int i)
	   {
	     if ((i < 0) || (i > this.qs.size()) || (this.qs.get(i) == null)) {
	       return "";
	     }
	     return ((FareCalculation.fcq)this.qs.get(i)).getSeg();
	   }
	
	   public void addQ(double value, String seg)
	   {
	     FareCalculation.fcq q = new FareCalculation.fcq();       //  FareCalculation.this
	     q.value = value;
	     q.seg = seg;
	     this.qs.add(q);
	   }
	
	   public String toString()
	   {
	     return this.org + ' ' + this.aircorp + ' ' + this.dst + ' ' +
	       FareCalculation.format.format(this.amount) + this.fltclass + ' ';
	   }
	
	   public fcitem()
	   {
	   }
	
	   fcitem(fcitem paramfcitem)
	   {
	     if (paramfcitem == null)
	       return;
	     this.aircorp = paramfcitem.aircorp;
	     this.amount = paramfcitem.amount;
	     this.branchend = paramfcitem.branchend;
	     this.branchstart = paramfcitem.branchstart;
	     this.cancelA = paramfcitem.cancelA;
	     this.cancelB = paramfcitem.cancelB;
	     this.cancelF = paramfcitem.cancelF;
	     this.dst = paramfcitem.dst;
	     this.e = paramfcitem.e;
	     this.e_xxx = paramfcitem.e_xxx;
	     this.fareseg = paramfcitem.fareseg;
	     this.fltclass = paramfcitem.fltclass;
	     this.freePackageCount = paramfcitem.freePackageCount;
	     this.freePackageWeight = paramfcitem.freePackageWeight;
	     this.freePackageWeightUnit = paramfcitem.freePackageWeightUnit;
	     if (paramfcitem.invalidAfter != null) {
	       this.invalidAfter = new GregorianCalendar();
	       this.invalidAfter.setTimeInMillis(paramfcitem.invalidAfter.getTimeInMillis());
	     }
	     if (paramfcitem.invalidBefore != null) {
	       this.invalidBefore = new GregorianCalendar();
	       this.invalidBefore.setTimeInMillis(paramfcitem.invalidBefore.getTimeInMillis());
	     }
	     this.milesurcharge = paramfcitem.milesurcharge;
	     this.mileIntermediatePoint = paramfcitem.mileIntermediatePoint;
	     this.msseg = paramfcitem.msseg;
	     this.org = paramfcitem.org;
	     this.q = paramfcitem.q;
	     this.qseg = paramfcitem.qseg;
	     this.qs = new ArrayList();
	     this.qs.addAll(paramfcitem.qs);
	     this.stopover = paramfcitem.stopover;
	     this.traveldirection = paramfcitem.traveldirection;
	     this.tktFromFlag = paramfcitem.tktFromFlag;
	     this.stopoverSurcharge = paramfcitem.stopoverSurcharge;
	   }
	
	   public String getFareseg()
	   {
	     return this.fareseg;
	   }
	
	   public void setFareseg(String fareseg)
	   {
	     this.fareseg = fareseg;
	   }
	
	   public String getMileIntermediatePoint()
	   {
	     return this.mileIntermediatePoint;
	   }
	
	   public void setMileIntermediatePoint(String mileIntermediatePoint)
	   {
	     this.mileIntermediatePoint = mileIntermediatePoint;
	   }
	
	   public String getAircorp()
	   {
	     return this.aircorp;
	   }
	
	   public void setAircorp(String aircorp)
	   {
	     this.aircorp = aircorp;
	   }
	
	   public double getAmount()
	   {
	     return this.amount;
	   }
	
	   public void setAmount(double amount)
	   {
	     this.amount = amount;
	   }
	
	   public boolean isBranchend()
	   {
	     return this.branchend;
	   }
	
	   public void setBranchend(boolean branchend)
	   {
	     this.branchend = branchend;
	   }
	
	   public boolean isBranchstart()
	   {
	     return this.branchstart;
	   }
	
	   public void setBranchstart(boolean branchstart)
	   {
	     this.branchstart = branchstart;
	   }
	
	   public boolean isCancelA()
	   {
	     return this.cancelA;
	   }
	
	   public void setCancelA(boolean cancelA)
	   {
	     this.cancelA = cancelA;
	   }
	
	   public boolean isCancelB()
	   {
	     return this.cancelB;
	   }
	
	   public void setCancelB(boolean cancelB)
	   {
	     this.cancelB = cancelB;
	   }
	
	   public boolean isCancelF()
	   {
	     return this.cancelF;
	   }
	
	   public void setCancelF(boolean cancelF)
	   {
	     this.cancelF = cancelF;
	   }
	
	   public String getDst()
	   {
	     return this.dst;
	   }
	
	   public void setDst(String dst)
	   {
	     this.dst = dst;
	   }
	
	   public boolean isE()
	   {
	     return this.e;
	   }
	
	   public boolean isE_xxx()
	   {
	     return this.e_xxx;
	   }
	
	   public void setE_xxx(boolean e_xxx)
	   {
	     this.e_xxx = e_xxx;
	   }
	
	   public void setE(boolean e)
	   {
	     this.e = e;
	   }
	
	   public String getFltclass()
	   {
	     return this.fltclass;
	   }
	
	   public void setFltclass(String fltclass)
	   {
	     this.fltclass = fltclass;
	   }
	
	   public int getFreePackageCount()
	   {
	     return this.freePackageCount;
	   }
	
	   public void setFreePackageCount(int freePackageCount)
	   {
	     this.freePackageCount = freePackageCount;
	   }
	
	   public int getFreePackageWeight()
	   {
	     return this.freePackageWeight;
	   }
	
	   public void setFreePackageWeight(int freePackageWeight)
	   {
	     this.freePackageWeight = freePackageWeight;
	   }
	
	   public Calendar getInvalidAfter()
	   {
	     return this.invalidAfter;
	   }
	
	   public void setInvalidAfter(Calendar invalidAfter)
	   {
	     this.invalidAfter = invalidAfter;
	   }
	
	   public Calendar getInvalidBefore()
	   {
	     return this.invalidBefore;
	   }
	
	   public void setInvalidBefore(Calendar invalidBefore)
	   {
	     this.invalidBefore = invalidBefore;
	   }
	
	   public int getMilesurcharge()
	   {
	     return this.milesurcharge;
	   }
	
	   public void setMilesurcharge(int milesurcharge)
	   {
	     this.milesurcharge = milesurcharge;
	   }
	
	   public String getMsseg()
	   {
	     return this.msseg;
	   }
	
	   public void setMsseg(String msseg)
	   {
	     this.msseg = msseg;
	   }
	
	   public String getOrg()
	   {
	     return this.org;
	   }
	
	   public void setOrg(String org)
	   {
	     this.org = org;
	   }
	
	   /** @deprecated */
	   public double getQ()
	   {
	     return this.q;
	   }
	
	   /** @deprecated */
	   public void setQ(double q)
	   {
	     this.q = q;
	   }
	
	   /** @deprecated */
	   public String getQseg()
	   {
	     return this.qseg;
	   }
	
	   /** @deprecated */
	   public void setQseg(String qseg)
	   {
	     this.qseg = qseg;
	   }
	
	   public boolean isStopover()
	   {
	     return this.stopover;
	   }
	
	   public void setStopover(boolean stopover)
	   {
	     this.stopover = stopover;
	   }
	
	   public String getTraveldirection()
	   {
	     return this.traveldirection;
	   }
	
	   public void setTraveldirection(String traveldirection)
	   {
	     this.traveldirection = traveldirection;
	   }
	
	   public boolean isTktFromFlag()
	   {
	     return this.tktFromFlag;
	   }
	
	   public void setTktFromFlag(boolean tktFromFlag)
	   {
	     this.tktFromFlag = tktFromFlag;
	   }
	
	   public double getStopoverSurcharge()
	   {
	     return this.stopoverSurcharge;
	   }
	
	   public void setStopoverSurcharge(double stopoverSurcharge)
	   {
	     this.stopoverSurcharge = stopoverSurcharge;
	   }
	
	   public String getFreePackageWeightUnit()
	   {
	     return this.freePackageWeightUnit;
	   }
	
	   public void setFreePackageWeightUnit(String freePackageWeightUnit)
	   {
	     this.freePackageWeightUnit = freePackageWeightUnit;
	   }
	 }
//	 @Data
	 public static class fcq
	 {
	   protected String seg;
	   protected double value;
	
	   public String getSeg()
	   {
	     return this.seg;
	   }
	
	   public void setSeg(String seg)
	   {
	     this.seg = seg;
	   }
	
	   public double getValue()
	   {
	     return this.value;
	   }
	
	   public void setValue(double value)
	   {
	     this.value = value;
	   }
	 }
//	 @Data
	public static class fctax
	   implements Serializable
	 {
	   private static final long serialVersionUID = 2990242460423314161L;
	   protected String code = "";
	
	   protected String currency = "CNY";
	
	   protected double value = -1.0D;
		public fctax(){}
	   fctax(String paramString1, double paramDouble, String paramString2) {
	     this.currency = paramString1;
	     this.value = paramDouble;
	     this.code = paramString2;
	   }
	
	   public String toString()
	   {
	     return this.currency + FareCalculation.format.format(this.value) + this.code;
	   }

	public String getCode() {
		return code;
	}

	public String getCurrency() {
		return currency;
	}

	public double getValue() {
		return value;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public void setValue(double value) {
		this.value = value;
	}
	 }

}
