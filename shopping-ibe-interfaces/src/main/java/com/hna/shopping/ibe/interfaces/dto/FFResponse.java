package com.hna.shopping.ibe.interfaces.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel

public class FFResponse extends BaseResponse {
	/**
	* 
	*/
	private static final long serialVersionUID = -7398667277806570191L;
	private String flightNo;
	private Date departDate;
	private String planeModel;

	private List<FFSegment> segments = new ArrayList<FFSegment>();
	private String remark;

}
