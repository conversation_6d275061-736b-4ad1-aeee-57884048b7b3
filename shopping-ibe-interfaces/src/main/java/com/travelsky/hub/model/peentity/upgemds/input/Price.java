/*     */ package com.travelsky.hub.model.peentity.upgemds.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Price
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6295417247324593326L;
/*     */   private String amount;
/*     */   private String currencyCode;
/*     */   private String autoIndicator;
/*     */   private String equivAmount;
/*     */   private String equivCurrency;
/*     */   private String baseCurrency;
/*     */   private String baseFare;
/*     */   private String additionalFee;
/*     */   private String additionalFeeCurrency;
/*     */   private String sellAmount;
/*     */   private String sellCurrencyCode;
/*     */   private String ffpMileage;
/*     */   private Commission commission;
/*     */   private Tax tax;
/*     */   private Fee fee;
/*     */ 
/*     */   public Commission getCommission()
/*     */   {
/*  89 */     return this.commission;
/*     */   }
/*     */ 
/*     */   public void setCommission(Commission commission)
/*     */   {
/*  96 */     this.commission = commission;
/*     */   }
/*     */ 
/*     */   public String getEquivAmount()
/*     */   {
/* 103 */     return this.equivAmount;
/*     */   }
/*     */ 
/*     */   public void setEquivAmount(String equivAmount)
/*     */   {
/* 110 */     this.equivAmount = equivAmount;
/*     */   }
/*     */ 
/*     */   public String getEquivCurrency()
/*     */   {
/* 117 */     return this.equivCurrency;
/*     */   }
/*     */ 
/*     */   public void setEquivCurrency(String equivCurrency)
/*     */   {
/* 124 */     this.equivCurrency = equivCurrency;
/*     */   }
/*     */ 
/*     */   public String getBaseCurrency()
/*     */   {
/* 131 */     return this.baseCurrency;
/*     */   }
/*     */ 
/*     */   public void setBaseCurrency(String baseCurrency)
/*     */   {
/* 138 */     this.baseCurrency = baseCurrency;
/*     */   }
/*     */ 
/*     */   public String getBaseFare()
/*     */   {
/* 145 */     return this.baseFare;
/*     */   }
/*     */ 
/*     */   public void setBaseFare(String baseFare)
/*     */   {
/* 152 */     this.baseFare = baseFare;
/*     */   }
/*     */ 
/*     */   public String getSellAmount()
/*     */   {
/* 159 */     return this.sellAmount;
/*     */   }
/*     */ 
/*     */   public void setSellAmount(String sellAmount)
/*     */   {
/* 166 */     this.sellAmount = sellAmount;
/*     */   }
/*     */ 
/*     */   public String getSellCurrencyCode()
/*     */   {
/* 173 */     return this.sellCurrencyCode;
/*     */   }
/*     */ 
/*     */   public void setSellCurrencyCode(String sellCurrencyCode)
/*     */   {
/* 180 */     this.sellCurrencyCode = sellCurrencyCode;
/*     */   }
/*     */ 
/*     */   public String getAmount()
/*     */   {
/* 187 */     return this.amount;
/*     */   }
/*     */ 
/*     */   public void setAmount(String amount)
/*     */   {
/* 194 */     this.amount = amount;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/* 201 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/* 208 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ 
/*     */   public String getAutoIndicator()
/*     */   {
/* 215 */     return this.autoIndicator;
/*     */   }
/*     */ 
/*     */   public void setAutoIndicator(String autoIndicator)
/*     */   {
/* 222 */     this.autoIndicator = autoIndicator;
/*     */   }
/*     */ 
/*     */   public String getAdditionalFee()
/*     */   {
/* 229 */     return this.additionalFee;
/*     */   }
/*     */ 
/*     */   public void setAdditionalFee(String additionalFee)
/*     */   {
/* 236 */     this.additionalFee = additionalFee;
/*     */   }
/*     */ 
/*     */   public String getAdditionalFeeCurrency()
/*     */   {
/* 243 */     return this.additionalFeeCurrency;
/*     */   }
/*     */ 
/*     */   public void setAdditionalFeeCurrency(String additionalFeeCurrency)
/*     */   {
/* 250 */     this.additionalFeeCurrency = additionalFeeCurrency;
/*     */   }
/*     */ 
/*     */   public String getFfpMileage()
/*     */   {
/* 257 */     return this.ffpMileage;
/*     */   }
/*     */ 
/*     */   public void setFfpMileage(String ffpMileage)
/*     */   {
/* 264 */     this.ffpMileage = ffpMileage;
/*     */   }
/*     */ 
/*     */   public Tax getTax()
/*     */   {
/* 271 */     return this.tax;
/*     */   }
/*     */ 
/*     */   public void setTax(Tax tax)
/*     */   {
/* 278 */     this.tax = tax;
/*     */   }
/*     */ 
/*     */   public Fee getFee()
/*     */   {
/* 285 */     return this.fee;
/*     */   }
/*     */ 
/*     */   public void setFee(Fee fee)
/*     */   {
/* 292 */     this.fee = fee;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Price
 * JD-Core Version:    0.6.0
 */