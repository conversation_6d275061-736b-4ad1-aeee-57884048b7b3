package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class DtoTicketInfoRequest implements Serializable {

	private static final long serialVersionUID = -876864144246838384L;
	@ApiModelProperty(value = "根据票号提客票记录")
	private String ticketNo;

	@ApiModelProperty(value = "根据pnr提客票记录")
	private String pnrNo;

	@ApiModelProperty(value = "旅客证件类型")
	private String passengerType;

	@ApiModelProperty(value = "旅客证件号")
	private String passengerNo;

	@ApiModelProperty(value = "指定航空公司配置，非必填")
	private String airlineCode;

	@ApiModelProperty(value = "是否需要获取编码预定时间，不为空时多RT一次，获取预定时间")
	private String needPnrBookTime;

	@ApiModelProperty(value = "是否需要获取客票改期历史，不为空时获取")
	private String needTicketChangeHis;

	private String depCode;

	/**
	 * 旅客姓名。
	 */
	private String passengerName;

	// 航班日期。如：2023-09-10
	private String flightDate;
}
