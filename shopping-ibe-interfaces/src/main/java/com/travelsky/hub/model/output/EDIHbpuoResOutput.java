/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class EDIHbpuoResOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<String> boardStreams;
/*    */ 
/*    */   public List<String> getBoardStreams()
/*    */   {
/* 25 */     return this.boardStreams;
/*    */   }
/*    */ 
/*    */   public void setBoardStreams(List<String> boardStreams)
/*    */   {
/* 32 */     this.boardStreams = boardStreams;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EDIHbpuoResOutput
 * JD-Core Version:    0.6.0
 */