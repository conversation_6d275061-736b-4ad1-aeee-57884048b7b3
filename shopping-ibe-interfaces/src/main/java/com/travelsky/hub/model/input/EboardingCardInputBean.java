/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class EboardingCardInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String ticketNumber;
/*     */   private String airlineCode;
/*     */   private String boardingNumber;
/*     */   private String cabin;
/*     */   private String flightDate;
/*     */   private String tourIndex;
/*     */   private String psrName;
/*     */   private String fromCity;
/*     */   private String flightNumber;
/*     */   private String toCity;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  37 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  45 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  53 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  62 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  69 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  77 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  84 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  92 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 100 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 107 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 114 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 122 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 129 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 137 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 145 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 153 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 161 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 168 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 175 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 182 */     return this.psrName;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.EboardingCardInputBean
 * JD-Core Version:    0.6.0
 */