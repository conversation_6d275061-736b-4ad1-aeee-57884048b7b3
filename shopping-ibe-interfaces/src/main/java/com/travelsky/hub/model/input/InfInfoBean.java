/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class InfInfoBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String info;
/*     */   private String etNo;
/*     */   private String index;
/*     */   private String passengerWeight;
/*     */   private String IFBA;
/*     */ 
/*     */   public String getPassengerWeight()
/*     */   {
/*  36 */     return this.passengerWeight;
/*     */   }
/*     */ 
/*     */   public void setPassengerWeight(String passengerWeight)
/*     */   {
/*  43 */     this.passengerWeight = passengerWeight;
/*     */   }
/*     */ 
/*     */   public String getInfo()
/*     */   {
/*  55 */     return this.info;
/*     */   }
/*     */ 
/*     */   public void setInfo(String info)
/*     */   {
/*  62 */     this.info = info;
/*     */   }
/*     */ 
/*     */   public String getEtNo()
/*     */   {
/*  69 */     return this.etNo;
/*     */   }
/*     */ 
/*     */   public void setEtNo(String etNo)
/*     */   {
/*  76 */     this.etNo = etNo;
/*     */   }
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  83 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  90 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getIFBA()
/*     */   {
/*  98 */     return this.IFBA;
/*     */   }
/*     */ 
/*     */   public void setIFBA(String IFBA)
/*     */   {
/* 106 */     this.IFBA = IFBA;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.InfInfoBean
 * JD-Core Version:    0.6.0
 */