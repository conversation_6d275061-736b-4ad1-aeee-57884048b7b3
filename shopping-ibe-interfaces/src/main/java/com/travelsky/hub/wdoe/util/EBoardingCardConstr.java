package com.travelsky.hub.wdoe.util;

public final class EBoardingCardConstr
{
  public static final String EBOARDINGCARD_WDOE = "WDoe";
  public static final String EBOARDINGCARD_REQUEST = "DBppRequest";
  public static final String EBOARDINGCARD_DEPARTUREDATE = "DepartureDate";
  public static final String EBOARDINGCARD_AIRLINECODE = "AirlineCode";
  public static final String EBOARDINGCARD_FLIGHTNUMBER = "FlightNumber";
  public static final String EBOARDINGCARD_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String EBOARDINGCARD_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String EBOARDINGCARD_TICKETID = "TicketID";
  public static final String EBOARDINGCARD_BOARDINGNUMBER = "BoardingNumber";
  public static final String EBOARDINGCARD_CABINTYPE = "CabinType";
  public static final String EBOARDINGCARD_SEQUENCENUMBER = "SequenceNumber";
  public static final String EBOARDINGCARD_SURNAME = "Surname";
  public static final String EBOARDINGCARD_RESPONSE = "DBppResponse";
  public static final String EBOARDINGCARD_DATASTREAM = "DataStream";
  public static final String EBOARDINGCARD_EBPSTR = "DataStreamText";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.EBoardingCardConstr
 * JD-Core Version:    0.6.0
 */