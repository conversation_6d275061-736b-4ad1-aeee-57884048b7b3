/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EbpInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6759484700813881839L;
/*    */   private String dataStream;
/*    */ 
/*    */   public String getDataStream()
/*    */   {
/* 24 */     return this.dataStream;
/*    */   }
/*    */ 
/*    */   public void setDataStream(String dataStream)
/*    */   {
/* 31 */     this.dataStream = dataStream;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.EbpInfo
 * JD-Core Version:    0.6.0
 */