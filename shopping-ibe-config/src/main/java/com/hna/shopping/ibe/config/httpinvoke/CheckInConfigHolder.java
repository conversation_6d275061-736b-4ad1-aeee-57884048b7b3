package com.hna.shopping.ibe.config.httpinvoke;

/**
 * Zero
 * 2020/10/26 11:25
 **/
public class CheckInConfigHolder {
    private static final ThreadLocal<String> user = new ThreadLocal<>();

    public static void setUser(String value){
        user.set(value);
    }

    public static String getUser(){
        return user.get();
    }

    public static void clear(){
        user.remove();
    }
}
