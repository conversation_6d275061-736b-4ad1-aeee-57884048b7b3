/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SpecialRequestService
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3883587713715584656L;
/*     */   private String serviceType;
/*     */   private String airline;
/*     */   private String status;
/*     */   private String text;
/*     */   private String seatIndis;
/*     */   private FrequentFlyerInfo frequentFlyerInfo;
/*     */ 
/*     */   public String getServiceType()
/*     */   {
/*  46 */     return this.serviceType;
/*     */   }
/*     */ 
/*     */   public void setServiceType(String serviceType)
/*     */   {
/*  53 */     this.serviceType = serviceType;
/*     */   }
/*     */ 
/*     */   public String getAirline()
/*     */   {
/*  60 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/*  67 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getStatus()
/*     */   {
/*  74 */     return this.status;
/*     */   }
/*     */ 
/*     */   public void setStatus(String status)
/*     */   {
/*  81 */     this.status = status;
/*     */   }
/*     */ 
/*     */   public String getText()
/*     */   {
/*  88 */     return this.text;
/*     */   }
/*     */ 
/*     */   public void setText(String text)
/*     */   {
/*  95 */     this.text = text;
/*     */   }
/*     */ 
/*     */   public String getSeatIndis()
/*     */   {
/* 102 */     return this.seatIndis;
/*     */   }
/*     */ 
/*     */   public void setSeatIndis(String seatIndis)
/*     */   {
/* 109 */     this.seatIndis = seatIndis;
/*     */   }
/*     */ 
/*     */   public FrequentFlyerInfo getFrequentFlyerInfo()
/*     */   {
/* 116 */     return this.frequentFlyerInfo;
/*     */   }
/*     */ 
/*     */   public void setFrequentFlyerInfo(FrequentFlyerInfo frequentFlyerInfo)
/*     */   {
/* 123 */     this.frequentFlyerInfo = frequentFlyerInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.SpecialRequestService
 * JD-Core Version:    0.6.0
 */