/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FareBox
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5616079819439065105L;
/*     */   private String commission;
/*     */   private String equivAmount;
/*     */   private String groupInd;
/*     */   private String itInd;
/*     */   private String btInd;
/*     */   private String overflowInd;
/*     */   private String reissueInd;
/*     */   private String sellingAmount;
/*     */   private String trrInd;
/*     */   private String creditCardAmount;
/*     */   private String creditCardAmountCurCode;
/*     */   private TaxSummary taxSummary;
/*     */   private String fareAmount;
/*     */   private String indicators;
/*     */   private String isManInput;
/*     */   private String text;
/*     */   private String totalAmount;
/*     */ 
/*     */   public String getCommission()
/*     */   {
/*  90 */     return this.commission;
/*     */   }
/*     */ 
/*     */   public void setCommission(String commission)
/*     */   {
/*  97 */     this.commission = commission;
/*     */   }
/*     */ 
/*     */   public String getEquivAmount()
/*     */   {
/* 104 */     return this.equivAmount;
/*     */   }
/*     */ 
/*     */   public void setEquivAmount(String equivAmount)
/*     */   {
/* 111 */     this.equivAmount = equivAmount;
/*     */   }
/*     */ 
/*     */   public String getGroupInd()
/*     */   {
/* 118 */     return this.groupInd;
/*     */   }
/*     */ 
/*     */   public void setGroupInd(String groupInd)
/*     */   {
/* 125 */     this.groupInd = groupInd;
/*     */   }
/*     */ 
/*     */   public String getItInd()
/*     */   {
/* 132 */     return this.itInd;
/*     */   }
/*     */ 
/*     */   public void setItInd(String itInd)
/*     */   {
/* 139 */     this.itInd = itInd;
/*     */   }
/*     */ 
/*     */   public String getBtInd()
/*     */   {
/* 146 */     return this.btInd;
/*     */   }
/*     */ 
/*     */   public void setBtInd(String btInd)
/*     */   {
/* 153 */     this.btInd = btInd;
/*     */   }
/*     */ 
/*     */   public String getOverflowInd()
/*     */   {
/* 160 */     return this.overflowInd;
/*     */   }
/*     */ 
/*     */   public void setOverflowInd(String overflowInd)
/*     */   {
/* 167 */     this.overflowInd = overflowInd;
/*     */   }
/*     */ 
/*     */   public String getReissueInd()
/*     */   {
/* 174 */     return this.reissueInd;
/*     */   }
/*     */ 
/*     */   public void setReissueInd(String reissueInd)
/*     */   {
/* 181 */     this.reissueInd = reissueInd;
/*     */   }
/*     */ 
/*     */   public String getSellingAmount()
/*     */   {
/* 188 */     return this.sellingAmount;
/*     */   }
/*     */ 
/*     */   public void setSellingAmount(String sellingAmount)
/*     */   {
/* 195 */     this.sellingAmount = sellingAmount;
/*     */   }
/*     */ 
/*     */   public String getTrrInd()
/*     */   {
/* 202 */     return this.trrInd;
/*     */   }
/*     */ 
/*     */   public void setTrrInd(String trrInd)
/*     */   {
/* 209 */     this.trrInd = trrInd;
/*     */   }
/*     */ 
/*     */   public String getCreditCardAmount()
/*     */   {
/* 216 */     return this.creditCardAmount;
/*     */   }
/*     */ 
/*     */   public void setCreditCardAmount(String creditCardAmount)
/*     */   {
/* 223 */     this.creditCardAmount = creditCardAmount;
/*     */   }
/*     */ 
/*     */   public String getCreditCardAmountCurCode()
/*     */   {
/* 230 */     return this.creditCardAmountCurCode;
/*     */   }
/*     */ 
/*     */   public void setCreditCardAmountCurCode(String creditCardAmountCurCode)
/*     */   {
/* 237 */     this.creditCardAmountCurCode = creditCardAmountCurCode;
/*     */   }
/*     */ 
/*     */   public TaxSummary getTaxSummary()
/*     */   {
/* 244 */     return this.taxSummary;
/*     */   }
/*     */ 
/*     */   public void setTaxSummary(TaxSummary taxSummary)
/*     */   {
/* 251 */     this.taxSummary = taxSummary;
/*     */   }
/*     */ 
/*     */   public String getFareAmount()
/*     */   {
/* 258 */     return this.fareAmount;
/*     */   }
/*     */ 
/*     */   public void setFareAmount(String fareAmount)
/*     */   {
/* 265 */     this.fareAmount = fareAmount;
/*     */   }
/*     */ 
/*     */   public String getIndicators()
/*     */   {
/* 272 */     return this.indicators;
/*     */   }
/*     */ 
/*     */   public void setIndicators(String indicators)
/*     */   {
/* 279 */     this.indicators = indicators;
/*     */   }
/*     */ 
/*     */   public String getIsManInput()
/*     */   {
/* 286 */     return this.isManInput;
/*     */   }
/*     */ 
/*     */   public void setIsManInput(String isManInput)
/*     */   {
/* 293 */     this.isManInput = isManInput;
/*     */   }
/*     */ 
/*     */   public String getText()
/*     */   {
/* 300 */     return this.text;
/*     */   }
/*     */ 
/*     */   public void setText(String text)
/*     */   {
/* 307 */     this.text = text;
/*     */   }
/*     */ 
/*     */   public String getTotalAmount()
/*     */   {
/* 314 */     return this.totalAmount;
/*     */   }
/*     */ 
/*     */   public void setTotalAmount(String totalAmount)
/*     */   {
/* 321 */     this.totalAmount = totalAmount;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FareBox
 * JD-Core Version:    0.6.0
 */