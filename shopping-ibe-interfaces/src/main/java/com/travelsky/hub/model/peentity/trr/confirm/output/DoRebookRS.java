/*    */ package com.travelsky.hub.model.peentity.trr.confirm.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class DoRebookRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7651494968956169651L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */   private String isSuccess;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 38 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 45 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 52 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 59 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 66 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 73 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.confirm.output.DoRebookRS
 * JD-Core Version:    0.6.0
 */