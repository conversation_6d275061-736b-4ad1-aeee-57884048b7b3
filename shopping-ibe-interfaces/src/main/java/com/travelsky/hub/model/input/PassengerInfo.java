/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String cabin;
/*     */   private String etCode;
/*     */   private String tourIndex;
/*     */   private String seatNo;
/*     */   private String cardAirline;
/*     */   private String cardID;
/*     */   private PassengerInfo connPsgInfo;
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  48 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  55 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/*  62 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/*  69 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/*  76 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/*  83 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/*  91 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/*  98 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getCardAirline()
/*     */   {
/* 105 */     return this.cardAirline;
/*     */   }
/*     */ 
/*     */   public void setCardAirline(String cardAirline)
/*     */   {
/* 112 */     this.cardAirline = cardAirline;
/*     */   }
/*     */ 
/*     */   public String getCardID()
/*     */   {
/* 119 */     return this.cardID;
/*     */   }
/*     */ 
/*     */   public void setCardID(String cardID)
/*     */   {
/* 126 */     this.cardID = cardID;
/*     */   }
/*     */ 
/*     */   public PassengerInfo getConnPsgInfo()
/*     */   {
/* 133 */     return this.connPsgInfo;
/*     */   }
/*     */ 
/*     */   public void setConnPsgInfo(PassengerInfo connPsgInfo)
/*     */   {
/* 140 */     this.connPsgInfo = connPsgInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PassengerInfo
 * JD-Core Version:    0.6.0
 */