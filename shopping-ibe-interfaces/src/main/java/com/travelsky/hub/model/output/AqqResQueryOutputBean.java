/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AqqResQueryOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String deptCity;
/*     */   private String aqqFlag;
/*     */   private String estaFlag;
/*     */   private String hostNBR;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  53 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  61 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  68 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  75 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  82 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/*  89 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getAqqFlag()
/*     */   {
/*  96 */     return this.aqqFlag;
/*     */   }
/*     */ 
/*     */   public void setAqqFlag(String aqqFlag)
/*     */   {
/* 103 */     this.aqqFlag = aqqFlag;
/*     */   }
/*     */ 
/*     */   public String getEstaFlag()
/*     */   {
/* 110 */     return this.estaFlag;
/*     */   }
/*     */ 
/*     */   public void setEstaFlag(String estaFlag)
/*     */   {
/* 117 */     this.estaFlag = estaFlag;
/*     */   }
/*     */ 
/*     */   public String getHostNBR()
/*     */   {
/* 124 */     return this.hostNBR;
/*     */   }
/*     */ 
/*     */   public void setHostNBR(String hostNBR)
/*     */   {
/* 131 */     this.hostNBR = hostNBR;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AqqResQueryOutputBean
 * JD-Core Version:    0.6.0
 */