/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ContactInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2952565075198408506L;
/*    */   private String contactType;
/*    */   private String contactContent;
/*    */ 
/*    */   public String getContactType()
/*    */   {
/* 28 */     return this.contactType;
/*    */   }
/*    */ 
/*    */   public void setContactType(String contactType)
/*    */   {
/* 35 */     this.contactType = contactType;
/*    */   }
/*    */ 
/*    */   public void setContactContent(String contactContent)
/*    */   {
/* 45 */     this.contactContent = contactContent;
/*    */   }
/*    */ 
/*    */   public String getContactContent()
/*    */   {
/* 52 */     return this.contactContent;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ContactInformation
 * JD-Core Version:    0.6.0
 */