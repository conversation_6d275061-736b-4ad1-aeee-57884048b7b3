/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class LegInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String segmentNumber;
/*    */   private EquipConfig equipConfig;
/*    */   private TerminalInformation terminalInfomation;
/*    */   private String isNoselection;
/*    */   private List<CabinGroup> cabinGroups;
/*    */ 
/*    */   public String getSegmentNumber()
/*    */   {
/* 35 */     return this.segmentNumber;
/*    */   }
/*    */ 
/*    */   public void setSegmentNumber(String segmentNumber)
/*    */   {
/* 42 */     this.segmentNumber = segmentNumber;
/*    */   }
/*    */ 
/*    */   public EquipConfig getEquipConfig()
/*    */   {
/* 49 */     return this.equipConfig;
/*    */   }
/*    */ 
/*    */   public void setEquipConfig(EquipConfig equipConfig)
/*    */   {
/* 56 */     this.equipConfig = equipConfig;
/*    */   }
/*    */ 
/*    */   public TerminalInformation getTerminalInfo()
/*    */   {
/* 63 */     return this.terminalInfomation;
/*    */   }
/*    */ 
/*    */   public void setTerminalInfo(TerminalInformation terminalInfomation)
/*    */   {
/* 71 */     this.terminalInfomation = terminalInfomation;
/*    */   }
/*    */ 
/*    */   public String getIsNoselection()
/*    */   {
/* 78 */     return this.isNoselection;
/*    */   }
/*    */ 
/*    */   public void setIsNoselection(String isNoselection)
/*    */   {
/* 85 */     this.isNoselection = isNoselection;
/*    */   }
/*    */ 
/*    */   public List<CabinGroup> getCabinGroups()
/*    */   {
/* 92 */     return this.cabinGroups;
/*    */   }
/*    */ 
/*    */   public void setCabinGroups(List<CabinGroup> cabinGroups)
/*    */   {
/* 99 */     this.cabinGroups = cabinGroups;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.LegInfo
 * JD-Core Version:    0.6.0
 */