/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SegmentInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String asm;
/*     */   private String configuration;
/*     */   private String aircraftVersion;
/*     */   private String equipmentCode;
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  43 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  54 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  63 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  74 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getAsm()
/*     */   {
/*  83 */     return this.asm;
/*     */   }
/*     */ 
/*     */   public void setAsm(String asm)
/*     */   {
/*  94 */     this.asm = asm;
/*     */   }
/*     */ 
/*     */   public String getConfiguration()
/*     */   {
/* 102 */     return this.configuration;
/*     */   }
/*     */ 
/*     */   public void setConfiguration(String configuration)
/*     */   {
/* 109 */     this.configuration = configuration;
/*     */   }
/*     */ 
/*     */   public String getAircraftVersion()
/*     */   {
/* 116 */     return this.aircraftVersion;
/*     */   }
/*     */ 
/*     */   public void setAircraftVersion(String aircraftVersion)
/*     */   {
/* 123 */     this.aircraftVersion = aircraftVersion;
/*     */   }
/*     */ 
/*     */   public String getEquipmentCode()
/*     */   {
/* 130 */     return this.equipmentCode;
/*     */   }
/*     */ 
/*     */   public void setEquipmentCode(String equipmentCode)
/*     */   {
/* 137 */     this.equipmentCode = equipmentCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SegmentInfo
 * JD-Core Version:    0.6.0
 */