package com.travelsky.hub.wdoe.util;

public final class TTLToolsConStr
{
  public static final String WDOE = "WDoe";
  public static final String WDOE_SUCCESS = "Success";
  public static final String WDOE_ERROR = "Error";
  public static final String WDOE_ERROCODE = "Code";
  public static final String WDOE_MSG = "Msg";
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String SERVICENAME = "TTL_ANGELTOOLS";
  public static final String FUNC_AIRPORTINFO = "AIRPORTINFO";
  public static final String FUNC_QUERYCKIPSR = "QUERYCKIPSR";
  public static final String FUNC_FFP = "CAFFP";
  public static final String FUNC_WEATHER = "WF";
  public static final String FUNC_LOGINMANAGER = "CUSSADM_LOGINMANAGER";
  public static final String FUNC_UPDATEAGENTINFO = "CUSSADM_UPDATEAGENTINFO";
  public static final String CUSS_LICFUNCTIONLIST = "CUSSADM_GETLICFUNCTIONLIST";
  public static final String CUSS_AUTHCODEVALIDATE = "CUSSADM_AUTHCODEVALIDATE";
  public static final String CUSS_CHANGEPASSWORD = "CUSSADM_CHANGEPASSWORD";
  public static final String CUSS_LOGOUT = "CUSSADM_LOGOUTMANAGER";
  public static final String CUSS_GETAGENTLIST = "CUSSADM_GETAGENTLIST";
  public static final String CUSS_RESETPASSWORD = "CUSSADM_RESETPASSWORD";
  public static final String CUSS_RESETSTATUS = "CUSSADM_RESETSTATUS";
  public static final String FUNC_TIME = "TIME";
  public static final String ELEMENT_REQUESTPARA = "RequestPara";
  public static final String ELEMENT_AIRPORT = "Airport";
  public static final String ELEMENT_AIRLINE = "Airline";
  public static final String NODE_STATIONINFO = "StationInfo";
  public static final String ELEMENT_STACODE = "StationCode";
  public static final String ELEMENT_STACOUNTRY = "StationCountry";
  public static final String ELEMENT_APPENABLE = "AppEnabled";
  public static final String ELEMENT_APIENABLE = "ApiEnabled";
  public static final String ELEMENT_EDIENABLE = "EdiiEnabled";
  public static final String USERINFO_ELEMENT = "UserInfo";
  public static final String SESSIONCODE_USERINFO = "SessionCode";
  public static final String NAME_USERINFO = "Name";
  public static final String AIRLINE_USERINFO = "Airline";
  public static final String AIRPORT_USERINFO = "Airport";
  public static final String APPFLAG_USERINFO = "AppFlag";
  public static final String IPADDRESS_USERINFO = "IpAddress";
  public static final String TRANSACTIONNO_USERINFO = "TransactionNo";
  public static final String TICKETS_ANGEL = "Tickets";
  public static final String TICKET_ELEMENT = "Ticket";
  public static final String PASSENGERNAME_TICKET = "PassengerName";
  public static final String TICKETID_TICKET = "TicketID";
  public static final String ISRECEIPTPRINTED_TICKET = "IsReceiptPrinted";
  public static final String TOURS_TICKET = "Tours";
  public static final String TOUR_TICKET = "Tour";
  public static final String SEQUENCENUMBER_TOUR = "SequenceNumber";
  public static final String FROMCITY_TOUR = "FromCity";
  public static final String TOCITY_TOUR = "ToCity";
  public static final String PNR_TOUR = "PNR";
  public static final String AIRLINECODE_TOUR = "AirlineCode";
  public static final String FLIGHTNUMBER_TOUR = "FlightNumber";
  public static final String DEPARTUREDATE_TOUR = "DepartureDate";
  public static final String DEPARTURETIME_TOUR = "DepartureTime";
  public static final String CABINTYPE_TOUR = "CabinType";
  public static final String CHECKINSTATUS_TOUR = "CheckInStatus";
  public static final String TOOLSRQ_ELEMENT = "TTL_ToolsRQ";
  public static final String TOOLSRS_ELEMENT = "TTL_ToolsRS";
  public static final String QUERYCKIPSRINFO_TOOLSRQ = "QueryCkiPSRInfo";
  public static final String CKITYPE_QUERYCKIPSRINFO = "CkiType";
  public static final String CERTIFICATETYPE_QUERYCKIPSRINFO = "CertificateType";
  public static final String CERTIFICATENUMBER_QUERYCKIPSRINFO = "CertificateNumber";
  public static final String CITY_WEATHER = "City";
  public static final String LANG_WEATHER = "Lang";
  public static final String WEATHER_ELEMENT = "WfInput";
  public static final String ELEMENT_AGENTLIST = "AgentList";
  public static final String TOTALPAGE_AGENTLIST = "totalPage";
  public static final String TOTALNUMBER_AGENTLIST = "totalnumber";
  public static final String ELEMENT_AGENTDETAIL = "AgentDetail";
  public static final String ELEMENT_AGENTDETAIL_AGENTNAME = "AgtName";
  public static final String ELEMENT_AGENTDETAIL_PASSWORD = "aPassword";
  public static final String ELEMENT_AGENTDETAIL_MEMO = "memo";
  public static final String ELEMENT_AGENTDETAIL_LEVEL = "level";
  public static final String ELEMENT_AGENTDETAIL_STATUS = "status";
  public static final String ELEMENT_AGENTDETAIL_AIRPORT = "Airport";
  public static final String ELEMENT_AGENTDETAIL_AIRLINE = "Airline";
  public static final String ELEMENT_AGENTDETAIL_LICENSEKEY = "licenseKey";
  public static final String WS_PWDTKT_AGENTINFO = "AgentInfo";
  public static final String WS_PWDTKT_AGENTNAME = "AgtName";
  public static final String WS_PWDTKT_PASSWORD = "aPasswd";
  public static final String WS_PWDTKT_OLDPASSWORD = "oldPasswd";
  public static final String WS_PWDTKT_AIRPORT = "Airport";
  public static final String WS_PWDTKT_AIRLINE = "Airline";
  public static final String WS_PWDTKT_SESSIONCODE = "SID";
  public static final String WS_PWDTKT_AGENTDETAIL = "AgentDetail";
  public static final String WS_PWDTKT_MEMO = "memo";
  public static final String WS_PWDTKT_LEVEL = "level";
  public static final String WS_PWDTKT_STATUS = "status";
  public static final String WS_PWDTKT_LICENSEKEY = "licenseKey";
  public static final String WS_PWDTKT_AGENTLIST = "AgentList";
  public static final String WS_PWDTKT_NAME = "agentName";
  public static final String WS_PWDTKT_APASSWORD = "aPassword";
  public static final String WS_PWDTKT_USED = "1";
  public static final String WS_PWDTKT_AIRLINE_ALL = "ALL";
  public static final String WS_PWDTKT_SUCCESS = "success";
  public static final String WS_PWDTKT_ISUCCESS = "Y";
  public static final String WS_PWDTKT_REQUESTPARA = "RequestPara";
  public static final String WS_PWDTKT_PWD = "password";
  public static final String WS_PWDTKT_VALUE = "value";
  public static final String WS_PWDTKT_PAGEINDEX = "pageindex";
  public static final String WS_PWDTKT_START = "start";
  public static final String TIME_SRCCITY = "SrcCity";
  public static final String TIME_DESTCITY = "DestCity";
  public static final String TIME_CURTIME = "CurTime";
  public static final String TIME_CITY = "City";
  public static final String TIME_TIMEDIFF = "TimeDiff";
  public static final String TIME_HR12 = "12HR";
  public static final String TIME_HR24 = "24HR";
  public static final String TIME_DATE = "Date";
  public static final String WS_P_WF_IN_CITY = "City";
  public static final String WS_P_WF_OUT_WEATHERS = "Weathers";
  public static final String WS_P_WF_OUT_CITY = "City";
  public static final String WS_P_WF_OUT_LANG = "Lang";
  public static final String WS_P_WF_OUT_CHNCITY = "LangCity";
  public static final String WS_P_WF_OUT_INDEX = "index";
  public static final String WS_P_WF_OUT_DATE = "date";
  public static final String WS_P_WF_OUT_DAY = "Day";
  public static final String WS_P_WF_OUT_L_TEMPERATURE = "L_Temperature";
  public static final String WS_P_WF_OUT_H_TEMPERATURE = "H_Temperature";
  public static final String WS_P_WF_OUT_DESCRIPTION = "Description";
  public static final String WS_P_WF_OUT_WIND = "Wind";
  public static final String WS_P_WF_OUT_WEATHER = "Weather";
  public static final String TIMEINFOS_ELEMENT = "TimeInfos";
  public static final String TIMEINFO_ELEMENT = "TimeInfo";
  public static final String CITY_TIMEINFO = "City";
  public static final String TIMEDIFF_TIMEINFO = "TimeDiff";
  public static final String HR12_TIMEINFO = "12HR";
  public static final String HR24_TIMEINFO = "24HR";
  public static final String DATE_TIMEINFO = "Date";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.TTLToolsConStr
 * JD-Core Version:    0.6.0
 */