/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import com.travelsky.hub.model.output.BoundInfo;
/*     */ import com.travelsky.hub.model.output.TextMsg;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class HbOptionsOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3977174188179676047L;
/*     */   private String resultList;
/*  28 */   private String bordingTime = "";
/*     */ 
/*  33 */   private String seatNumber = "";
/*     */   private List bordingStreams;
/*     */   private String boardingGateNumber;
/*     */   private String departureTime;
/*     */   private String arrivalTime;
/*     */   private String marketingAirline;
/*     */   private String marketingFlightNumber;
/*     */   private String fareClass;
/*     */   private List<BoundInfo> boundInfos;
/*     */   private String surName;
/*     */   private String docType;
/*     */   private String docID;
/*     */   private String passengerStatus;
/*     */   private String hostNumber;
/*     */   private String cabinType;
/*     */   private String ticketID;
/*     */   private String groupCode;
/*     */   private String groupNumber;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private String contactText;
/*     */   private List<TextMsg> textMsgList;
/*     */ 
/*     */   public String getResultList()
/*     */   {
/* 131 */     return this.resultList;
/*     */   }
/*     */ 
/*     */   public void setResultList(String resultList)
/*     */   {
/* 139 */     this.resultList = resultList;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 147 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String bordingTime)
/*     */   {
/* 155 */     this.bordingTime = bordingTime;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 163 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 171 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public List getBordingStreams()
/*     */   {
/* 179 */     return this.bordingStreams;
/*     */   }
/*     */ 
/*     */   public void setBordingStreams(List bordingStreams)
/*     */   {
/* 187 */     this.bordingStreams = bordingStreams;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 194 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 201 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 208 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/* 215 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getMarketingAirline()
/*     */   {
/* 221 */     return this.marketingAirline;
/*     */   }
/*     */ 
/*     */   public void setMarketingAirline(String marketingAirline)
/*     */   {
/* 228 */     this.marketingAirline = marketingAirline;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 234 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String arrivalTime)
/*     */   {
/* 241 */     this.arrivalTime = arrivalTime;
/*     */   }
/*     */ 
/*     */   public String getMarketingFlightNumber()
/*     */   {
/* 247 */     return this.marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMarketingFlightNumber(String marketingFlightNumber)
/*     */   {
/* 254 */     this.marketingFlightNumber = marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public List<BoundInfo> getBoundInfos()
/*     */   {
/* 260 */     return this.boundInfos;
/*     */   }
/*     */ 
/*     */   public void setBoundInfos(List<BoundInfo> boundInfos)
/*     */   {
/* 267 */     this.boundInfos = boundInfos;
/*     */   }
/*     */ 
/*     */   public String getFareClass()
/*     */   {
/* 273 */     return this.fareClass;
/*     */   }
/*     */ 
/*     */   public void setFareClass(String fareClass)
/*     */   {
/* 280 */     this.fareClass = fareClass;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 286 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 293 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 300 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 307 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 314 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 321 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 328 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 335 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 342 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 349 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 356 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 363 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 370 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 377 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 384 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 391 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getGroupCode()
/*     */   {
/* 398 */     return this.groupCode;
/*     */   }
/*     */ 
/*     */   public void setGroupCode(String groupCode)
/*     */   {
/* 405 */     this.groupCode = groupCode;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 412 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String groupNumber)
/*     */   {
/* 419 */     this.groupNumber = groupNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 426 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 433 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getContactText()
/*     */   {
/* 439 */     return this.contactText;
/*     */   }
/*     */ 
/*     */   public void setContactText(String contactText)
/*     */   {
/* 446 */     this.contactText = contactText;
/*     */   }
/*     */ 
/*     */   public List<TextMsg> getTextMsgList()
/*     */   {
/* 453 */     return this.textMsgList;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 460 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 467 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setTextMsgList(List<TextMsg> textMsgList)
/*     */   {
/* 474 */     this.textMsgList = textMsgList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.HbOptionsOutputBean
 * JD-Core Version:    0.6.0
 */