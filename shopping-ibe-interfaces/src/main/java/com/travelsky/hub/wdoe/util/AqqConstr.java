package com.travelsky.hub.wdoe.util;

public final class AqqConstr
{
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String HAQQINFO_ELEMENT = "HaqqInfo";
  public static final String AQQCONFINFO_ELEMENT = "AqqConfInfo";
  public static final String AQQRESINFO_ELEMENT = "AqqResInfo";
  public static final String AQQRES_ELEMENT = "AQQRES";
  public static final String AQQCONFRES_ELEMENT = "AqqConfRS";
  public static final String HAQQRS_ELEMENT = "HaqqRS";
  public static final String AQQ_AIRLINECODE = "AirlineCode";
  public static final String AQQ_FLIGHTNUMBER = "FlightNumber";
  public static final String AQQ_FLIGHTDATE = "FlightDate";
  public static final String AQQ_FROMCITY = "FromCity";
  public static final String AQQ_DEPTCITY = "DeptCity";
  public static final String AQQ_DESTCITY = "DestCity";
  public static final String AQQ_HOSTID = "HostID";
  public static final String AQQ_TICKETID = "TicketID";
  public static final String AQQ_GOV = "GovSecurityInformation";
  public static final String AQQ_OPTIONTYPE = "OptionType";
  public static final String AQQ_FLIGHTCLASS = "FlightClass";
  public static final String AQQ_HOSTNBR = "HostNBR";
  public static final String AQQ_AQQFLAG = "AqqFlag";
  public static final String AQQ_ESTAFLAG = "EstaFlag";
  public static final String AQQ_PID = "Pid";
  public static final String AQQ_MSGTYPE = "MsgType";
  public static final String AQQ_CKISTATUS = "CkiStatus";
  public static final String AQQ_RESULT = "Result";
  public static final String AQQ_CHECKTYPE = "CheckType";
  public static final String AQQ_CHECK_TYPE = "CheckType";
  public static final String AQQ_CHECK_STATUS = "CheckStatus";
  public static final String AQQ_CHECK_DIRECTION = "CheckDirection";
  public static final String AQQ_CHECK_COUNTRY = "CheckCountry";
  public static final String AQQ_CHECK_REMARK = "CheckRemark";
  public static final String AQQ_ESTA_STATUS = "EstaStatus";
  public static final String AQQ_IS_OVERIDE = "IsOveride";
  public static final String AQQ_IS_ALLOWED_CHECKIN = "IsAllowedCheckin";
  public static final String AQQ_IS_ALLOWED_CANCEL_CHECKIN = "IsAllowedCancelCheckin";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.AqqConstr
 * JD-Core Version:    0.6.0
 */