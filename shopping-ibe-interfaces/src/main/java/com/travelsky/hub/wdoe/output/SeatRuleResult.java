/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SeatRuleResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String ruleType;
/*    */   private List<String> seatType;
/*    */   private List<String> specifySeat;
/*    */   private List<String> specifyRow;
/*    */ 
/*    */   public String getRuleType()
/*    */   {
/* 37 */     return this.ruleType;
/*    */   }
/*    */ 
/*    */   public void setRuleType(String ruleType)
/*    */   {
/* 44 */     this.ruleType = ruleType;
/*    */   }
/*    */ 
/*    */   public List<String> getSeatType()
/*    */   {
/* 51 */     return this.seatType;
/*    */   }
/*    */ 
/*    */   public void setSeatType(List<String> seatType)
/*    */   {
/* 58 */     this.seatType = seatType;
/*    */   }
/*    */ 
/*    */   public List<String> getSpecifySeat()
/*    */   {
/* 65 */     return this.specifySeat;
/*    */   }
/*    */ 
/*    */   public void setSpecifySeat(List<String> specifySeat)
/*    */   {
/* 72 */     this.specifySeat = specifySeat;
/*    */   }
/*    */ 
/*    */   public List<String> getSpecifyRow()
/*    */   {
/* 79 */     return this.specifyRow;
/*    */   }
/*    */ 
/*    */   public void setSpecifyRow(List<String> specifyRow)
/*    */   {
/* 86 */     this.specifyRow = specifyRow;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.SeatRuleResult
 * JD-Core Version:    0.6.0
 */