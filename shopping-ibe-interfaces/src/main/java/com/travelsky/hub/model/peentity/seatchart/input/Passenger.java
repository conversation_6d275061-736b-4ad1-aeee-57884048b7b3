/*     */ package com.travelsky.hub.model.peentity.seatchart.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Passenger
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 29097315719324591L;
/*     */   private String passengerType;
/*     */   private Integer passengerAge;
/*     */   private Integer passengerOccurrence;
/*     */   private String passengerAttribute;
/*     */   private String ethnicity;
/*     */   private String passengerFOIDType;
/*     */   private String gender;
/*     */   private Integer passengerValue;
/*     */   private String passengerNationality;
/*     */   private Integer passengerPsptAge;
/*     */   private Integer passengerPocsAge;
/*     */   private String passengerStatus;
/*     */   private List<String> purchasedPros;
/*     */ 
/*     */   public String getPassengerType()
/*     */   {
/*  90 */     return this.passengerType;
/*     */   }
/*     */ 
/*     */   public void setPassengerType(String passengerType)
/*     */   {
/*  97 */     this.passengerType = passengerType;
/*     */   }
/*     */ 
/*     */   public Integer getPassengerAge()
/*     */   {
/* 104 */     return this.passengerAge;
/*     */   }
/*     */ 
/*     */   public void setPassengerAge(Integer passengerAge)
/*     */   {
/* 111 */     this.passengerAge = passengerAge;
/*     */   }
/*     */ 
/*     */   public Integer getPassengerOccurrence()
/*     */   {
/* 118 */     return this.passengerOccurrence;
/*     */   }
/*     */ 
/*     */   public void setPassengerOccurrence(Integer passengerOccurrence)
/*     */   {
/* 125 */     this.passengerOccurrence = passengerOccurrence;
/*     */   }
/*     */ 
/*     */   public String getPassengerAttribute()
/*     */   {
/* 132 */     return this.passengerAttribute;
/*     */   }
/*     */ 
/*     */   public void setPassengerAttribute(String passengerAttribute)
/*     */   {
/* 139 */     this.passengerAttribute = passengerAttribute;
/*     */   }
/*     */ 
///*     */   @JSONField(name="ethnic")
/*     */   public String getEthnicity()
/*     */   {
/* 147 */     return this.ethnicity;
/*     */   }
/*     */ 
/*     */   public void setEthnicity(String ethnicity)
/*     */   {
/* 154 */     this.ethnicity = ethnicity;
/*     */   }
/*     */ 
///*     */   @JSONField(name="passengerIDType")
/*     */   public String getPassengerFOIDType()
/*     */   {
/* 162 */     return this.passengerFOIDType;
/*     */   }
/*     */ 
/*     */   public void setPassengerFOIDType(String passengerFOIDType)
/*     */   {
/* 169 */     this.passengerFOIDType = passengerFOIDType;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 176 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 183 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public Integer getPassengerValue()
/*     */   {
/* 190 */     return this.passengerValue;
/*     */   }
/*     */ 
/*     */   public void setPassengerValue(Integer passengerValue)
/*     */   {
/* 197 */     this.passengerValue = passengerValue;
/*     */   }
/*     */ 
/*     */   public String getPassengerNationality()
/*     */   {
/* 204 */     return this.passengerNationality;
/*     */   }
/*     */ 
/*     */   public void setPassengerNationality(String passengerNationality)
/*     */   {
/* 211 */     this.passengerNationality = passengerNationality;
/*     */   }
/*     */ 
/*     */   public Integer getPassengerPsptAge()
/*     */   {
/* 218 */     return this.passengerPsptAge;
/*     */   }
/*     */ 
/*     */   public void setPassengerPsptAge(Integer passengerPsptAge)
/*     */   {
/* 225 */     this.passengerPsptAge = passengerPsptAge;
/*     */   }
/*     */ 
/*     */   public Integer getPassengerPocsAge()
/*     */   {
/* 232 */     return this.passengerPocsAge;
/*     */   }
/*     */ 
/*     */   public void setPassengerPocsAge(Integer passengerPocsAge)
/*     */   {
/* 239 */     this.passengerPocsAge = passengerPocsAge;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 246 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 253 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public List<String> getPurchasedPros()
/*     */   {
/* 260 */     return this.purchasedPros;
/*     */   }
/*     */ 
/*     */   public void setPurchasedPros(List<String> purchasedPros)
/*     */   {
/* 267 */     this.purchasedPros = purchasedPros;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.Passenger
 * JD-Core Version:    0.6.0
 */