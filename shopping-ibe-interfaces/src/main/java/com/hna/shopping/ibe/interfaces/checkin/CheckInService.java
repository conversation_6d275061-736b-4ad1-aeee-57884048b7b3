package com.hna.shopping.ibe.interfaces.checkin;

import com.hna.shopping.ibe.interfaces.dto.checkin.*;

import java.util.List;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:19
 * @Version 1.0
 */
public interface CheckInService {

    boolean hasInitialized(FlightInitRQ req);

    FlightCheckInInfoRS queryCheckInFlightInfo(FlightInitRQ req);

    String printBoarding(PrintBoardingRQ req);

    WeatherRS weather(WeatherRQ req);

    EbpRS qrEbp(EbpRQ req);
    EbpRS pdfEbp(EbpRQ req);
    EbpRS barEbp(EbpRQ req);

    SingleCheckInRS singleCheckIn(SingleCheckInRQ req);

    CancelCheckInRS cancelCheckIn(CancelCheckInRQ req);

    PUOutputRS connectingFlightCheckIn(HbpuoInputRQ req);

    List<RetrieveRS> retrieve(RetrieveRQ rq);

    SeatMapRS seatMap(SeatMapRQ rq);

    List<RetrievePassengerRS> retrievePassenger(RetrievePassengerRQ rq);

    RetrieveForeignerPassengerRS retrieveForeignerPassenger(RetrieveForeignerPassengerRQ rq);

    boolean bookingSeat(BookingSeatRQ rq);

    SeatChartRS getSeatChart(SeatChartRQ rq);

    SeatReleaseRS seatRelease(SeatReleaseRQ rq);
}
