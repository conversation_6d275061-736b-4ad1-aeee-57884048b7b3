/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatChartInBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2682845075551793374L;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String flightClass;
/*     */   private String psrLevel;
/*     */   private String option;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  40 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  47 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  54 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  61 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  68 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  75 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  82 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  89 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  96 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 103 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 110 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 117 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/* 124 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/* 131 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ 
/*     */   public String getOption()
/*     */   {
/* 138 */     return this.option;
/*     */   }
/*     */ 
/*     */   public void setOption(String option)
/*     */   {
/* 145 */     this.option = option;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatChartInBean
 * JD-Core Version:    0.6.0
 */