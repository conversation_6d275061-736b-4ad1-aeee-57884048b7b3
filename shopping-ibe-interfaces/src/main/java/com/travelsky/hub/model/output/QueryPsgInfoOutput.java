/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class QueryPsgInfoOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String deptAptCode;
/*     */   private String arvAptCode;
/*     */   private String flightClass;
/*     */   private String psrName;
/*     */   private String psrCkiStatus;
/*     */   private String seatNo;
/*     */   private String boardingNumber;
/*     */   private String hostNum;
/*     */   private String bagQuantity;
/*     */   private String bagWeight;
/*     */   private String bagWeightUnit;
/*     */   private List<BagTag> bagTags;
/*     */   private String flightStatus;
/*     */   private String fba;
/*     */   private String fbaUnit;
/*     */   private FfBasicInfo ffBasicInfo;
/*     */   private List<ASVC> aseASVCList;
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  97 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 104 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 111 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 118 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptAptCode()
/*     */   {
/* 125 */     return this.deptAptCode;
/*     */   }
/*     */ 
/*     */   public void setDeptAptCode(String deptAptCode)
/*     */   {
/* 132 */     this.deptAptCode = deptAptCode;
/*     */   }
/*     */ 
/*     */   public String getArvAptCode()
/*     */   {
/* 139 */     return this.arvAptCode;
/*     */   }
/*     */ 
/*     */   public void setArvAptCode(String arvAptCode)
/*     */   {
/* 146 */     this.arvAptCode = arvAptCode;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 153 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 160 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getPsrCkiStatus()
/*     */   {
/* 168 */     return this.psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPsrCkiStatus(String psrCkiStatus)
/*     */   {
/* 175 */     this.psrCkiStatus = psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 182 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 189 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 196 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 203 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 210 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 217 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 224 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 231 */     this.hostNum = hostNum;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/* 238 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/* 245 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 252 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 259 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getBagWeightUnit()
/*     */   {
/* 266 */     return this.bagWeightUnit;
/*     */   }
/*     */ 
/*     */   public void setBagWeightUnit(String bagWeightUnit)
/*     */   {
/* 273 */     this.bagWeightUnit = bagWeightUnit;
/*     */   }
/*     */ 
/*     */   public List<BagTag> getBagTags()
/*     */   {
/* 280 */     return this.bagTags;
/*     */   }
/*     */ 
/*     */   public void setBagTags(List<BagTag> bagTags)
/*     */   {
/* 287 */     this.bagTags = bagTags;
/*     */   }
/*     */ 
/*     */   public String getFba()
/*     */   {
/* 294 */     return this.fba;
/*     */   }
/*     */ 
/*     */   public void setFba(String fba)
/*     */   {
/* 302 */     this.fba = fba;
/*     */   }
/*     */ 
/*     */   public FfBasicInfo getFfBasicInfo()
/*     */   {
/* 309 */     return this.ffBasicInfo;
/*     */   }
/*     */ 
/*     */   public void setFfBasicInfo(FfBasicInfo ffBasicInfo)
/*     */   {
/* 316 */     this.ffBasicInfo = ffBasicInfo;
/*     */   }
/*     */ 
/*     */   public String getFlightStatus()
/*     */   {
/* 324 */     return this.flightStatus;
/*     */   }
/*     */ 
/*     */   public void setFlightStatus(String flightStatus)
/*     */   {
/* 332 */     this.flightStatus = flightStatus;
/*     */   }
/*     */ 
/*     */   public List<ASVC> getAseASVCList()
/*     */   {
/* 340 */     return this.aseASVCList;
/*     */   }
/*     */ 
/*     */   public void setAseASVCList(List<ASVC> aseASVCList)
/*     */   {
/* 348 */     this.aseASVCList = aseASVCList;
/*     */   }
/*     */ 
/*     */   public String getFbaUnit()
/*     */   {
/* 355 */     return this.fbaUnit;
/*     */   }
/*     */ 
/*     */   public void setFbaUnit(String fbaUnit)
/*     */   {
/* 362 */     this.fbaUnit = fbaUnit;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.QueryPsgInfoOutput
 * JD-Core Version:    0.6.0
 */