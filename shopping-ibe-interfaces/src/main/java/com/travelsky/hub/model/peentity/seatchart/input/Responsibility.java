/*     */ package com.travelsky.hub.model.peentity.seatchart.input;
/*     */ 
///*     */ import com.alibaba.fastjson.annotation.JSONField;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Responsibility
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8179315910648207426L;
/*     */   private String responsibilityCity;
/*     */   private Boolean travelAgentInd;
/*     */   private String carrierGDS;
/*     */   private String dutyFunctionCode;
/*     */   private String officeID;
/*     */   private String departmentIdentifier;
/*     */   private String departmentCode;
/*     */   private String erspNumCode;
/*     */   private String terminalID;
/*     */   private String airlineSpecificCode;
/*     */ 
///*     */   @JSONField(name="pointOfSaleLocation")
/*     */   public String getResponsibilityCity()
/*     */   {
/*  75 */     return this.responsibilityCity;
/*     */   }
/*     */ 
/*     */   public void setResponsibilityCity(String responsibilityCity)
/*     */   {
/*  82 */     this.responsibilityCity = responsibilityCity;
/*     */   }
/*     */ 
///*     */   @JSONField(name="isTravelAgent")
/*     */   public Boolean getTravelAgentInd()
/*     */   {
/*  90 */     return this.travelAgentInd;
/*     */   }
/*     */ 
/*     */   public void setTravelAgentInd(Boolean travelAgentInd)
/*     */   {
/*  97 */     this.travelAgentInd = travelAgentInd;
/*     */   }
/*     */ 
///*     */   @JSONField(name="carrierGds")
/*     */   public String getCarrierGDS()
/*     */   {
/* 105 */     return this.carrierGDS;
/*     */   }
/*     */ 
/*     */   public void setCarrierGDS(String carrierGDS)
/*     */   {
/* 112 */     this.carrierGDS = carrierGDS;
/*     */   }
/*     */ 
/*     */   public String getDutyFunctionCode()
/*     */   {
/* 119 */     return this.dutyFunctionCode;
/*     */   }
/*     */ 
/*     */   public void setDutyFunctionCode(String dutyFunctionCode)
/*     */   {
/* 126 */     this.dutyFunctionCode = dutyFunctionCode;
/*     */   }
/*     */ 
///*     */   @JSONField(name="travelAgencyCode")
/*     */   public String getOfficeID()
/*     */   {
/* 134 */     return this.officeID;
/*     */   }
/*     */ 
/*     */   public void setOfficeID(String officeID)
/*     */   {
/* 141 */     this.officeID = officeID;
/*     */   }
/*     */ 
/*     */   public String getDepartmentIdentifier()
/*     */   {
/* 148 */     return this.departmentIdentifier;
/*     */   }
/*     */ 
/*     */   public void setDepartmentIdentifier(String departmentIdentifier)
/*     */   {
/* 155 */     this.departmentIdentifier = departmentIdentifier;
/*     */   }
/*     */ 
/*     */   public String getDepartmentCode()
/*     */   {
/* 162 */     return this.departmentCode;
/*     */   }
/*     */ 
/*     */   public void setDepartmentCode(String departmentCode)
/*     */   {
/* 169 */     this.departmentCode = departmentCode;
/*     */   }
/*     */ 
/*     */   public String getErspNumCode()
/*     */   {
/* 176 */     return this.erspNumCode;
/*     */   }
/*     */ 
/*     */   public void setErspNumCode(String erspNumCode)
/*     */   {
/* 183 */     this.erspNumCode = erspNumCode;
/*     */   }
/*     */ 
/*     */   public String getTerminalID()
/*     */   {
/* 190 */     return this.terminalID;
/*     */   }
/*     */ 
/*     */   public void setTerminalID(String terminalID)
/*     */   {
/* 197 */     this.terminalID = terminalID;
/*     */   }
/*     */ 
/*     */   public String getAirlineSpecificCode()
/*     */   {
/* 204 */     return this.airlineSpecificCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineSpecificCode(String airlineSpecificCode)
/*     */   {
/* 211 */     this.airlineSpecificCode = airlineSpecificCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.input.Responsibility
 * JD-Core Version:    0.6.0
 */