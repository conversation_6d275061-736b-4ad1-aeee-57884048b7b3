/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AqqResInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1063638166415838912L;
/*    */   private String hostNBR;
/*    */   private String msgType;
/*    */ 
/*    */   public String getHostNBR()
/*    */   {
/* 27 */     return this.hostNBR;
/*    */   }
/*    */ 
/*    */   public void setHostNBR(String hostNBR)
/*    */   {
/* 34 */     this.hostNBR = hostNBR;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 41 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 48 */     this.msgType = msgType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.AqqResInputBean
 * JD-Core Version:    0.6.0
 */