/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Individual
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2869248470479247693L;
/*     */   private String fullName;
/*     */   private SupplementaryName supplementaryName;
/*     */   private String birthday;
/*     */   private String gender;
/*     */   private String firstName;
/*     */   private String lastName;
/*     */   private String chineseName;
/*     */ 
/*     */   public String getFullName()
/*     */   {
/*  50 */     return this.fullName;
/*     */   }
/*     */ 
/*     */   public void setFullName(String fullName)
/*     */   {
/*  57 */     this.fullName = fullName;
/*     */   }
/*     */ 
/*     */   public SupplementaryName getSupplementaryName()
/*     */   {
/*  64 */     return this.supplementaryName;
/*     */   }
/*     */ 
/*     */   public void setSupplementaryName(SupplementaryName supplementaryName)
/*     */   {
/*  71 */     this.supplementaryName = supplementaryName;
/*     */   }
/*     */ 
/*     */   public String getBirthday()
/*     */   {
/*  78 */     return this.birthday;
/*     */   }
/*     */ 
/*     */   public void setBirthday(String birthday)
/*     */   {
/*  85 */     this.birthday = birthday;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/*  92 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/*  99 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getFirstName()
/*     */   {
/* 106 */     return this.firstName;
/*     */   }
/*     */ 
/*     */   public void setFirstName(String firstName)
/*     */   {
/* 113 */     this.firstName = firstName;
/*     */   }
/*     */ 
/*     */   public String getLastName()
/*     */   {
/* 120 */     return this.lastName;
/*     */   }
/*     */ 
/*     */   public void setLastName(String lastName)
/*     */   {
/* 127 */     this.lastName = lastName;
/*     */   }
/*     */ 
/*     */   public String getChineseName()
/*     */   {
/* 134 */     return this.chineseName;
/*     */   }
/*     */ 
/*     */   public void setChineseName(String chineseName)
/*     */   {
/* 141 */     this.chineseName = chineseName;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Individual
 * JD-Core Version:    0.6.0
 */