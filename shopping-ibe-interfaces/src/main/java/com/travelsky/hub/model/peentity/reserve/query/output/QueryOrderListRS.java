/*    */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class QueryOrderListRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5571573900881310089L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */   private List<ReverseOrderInfo> resultList;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 41 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 48 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 55 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 62 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ 
/*    */   public List<ReverseOrderInfo> getResultList()
/*    */   {
/* 69 */     return this.resultList;
/*    */   }
/*    */ 
/*    */   public void setResultList(List<ReverseOrderInfo> resultList)
/*    */   {
/* 76 */     this.resultList = resultList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.QueryOrderListRS
 * JD-Core Version:    0.6.0
 */