/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ChdPartner
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  16 */   private String hostNumber = "";
/*     */ 
/*  18 */   private String index = "";
/*     */ 
/*  20 */   private String surname = "";
/*     */ 
/*  22 */   private String ticketID = "";
/*     */ 
/*  24 */   private String cabinType = "";
/*     */ 
/*  26 */   private String chd = "";
/*     */ 
/*  28 */   private String ffpCardNumber = "";
/*     */ 
/*  30 */   private String ffpAirlineCode = "";
/*     */ 
/*  32 */   private String gender = "";
/*     */   private InfInfoBean infInfo;
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/*  41 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/*  48 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  55 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  62 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getSurname()
/*     */   {
/*  69 */     return this.surname;
/*     */   }
/*     */ 
/*     */   public void setSurname(String surname)
/*     */   {
/*  76 */     this.surname = surname.toUpperCase();
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  83 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  90 */     this.cabinType = cabinType.toUpperCase();
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/*  97 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 104 */     this.chd = chd.toUpperCase();
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 111 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 118 */     this.ffpAirlineCode = ffpAirlineCode.toUpperCase();
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 125 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 132 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 139 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 146 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 153 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 160 */     this.infInfo = infInfo;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 167 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 174 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ChdPartner
 * JD-Core Version:    0.6.0
 */