/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Fee
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4233001992210952167L;
/*    */   private String serviceID;
/*    */   private String currencyCode;
/*    */   private String amount;
/*    */ 
/*    */   public String getServiceID()
/*    */   {
/* 41 */     return this.serviceID;
/*    */   }
/*    */ 
/*    */   public void setServiceID(String serviceID)
/*    */   {
/* 48 */     this.serviceID = serviceID;
/*    */   }
/*    */ 
/*    */   public String getCurrencyCode()
/*    */   {
/* 55 */     return this.currencyCode;
/*    */   }
/*    */ 
/*    */   public void setCurrencyCode(String currencyCode)
/*    */   {
/* 62 */     this.currencyCode = currencyCode;
/*    */   }
/*    */ 
/*    */   public String getAmount()
/*    */   {
/* 69 */     return this.amount;
/*    */   }
/*    */ 
/*    */   public void setAmount(String amount)
/*    */   {
/* 76 */     this.amount = amount;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Fee
 * JD-Core Version:    0.6.0
 */