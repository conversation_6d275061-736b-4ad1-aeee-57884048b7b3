package com.hna.shopping.ibe.implement.checkin;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.hna.eking.domain.exception.BusinessException;
import com.hna.shopping.ibe.common.enums.LineAttrEnum;
import com.hna.shopping.ibe.common.enums.SeatCharEnum;
import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.exception.PEException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.responsecode.CodePE;
import com.hna.shopping.ibe.common.util.CnToSpellNew;
import com.hna.shopping.ibe.common.util.ConvertUtil;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.config.httpinvoke.CheckInConfigHolder;
import com.hna.shopping.ibe.interfaces.PNRService;
import com.hna.shopping.ibe.interfaces.checkin.CheckInService;
import com.hna.shopping.ibe.interfaces.checkin.pe.ICheckInService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.interfaces.dto.checkin.*;
import com.hna.shopping.ibe.manager.PNRManager;
import com.hna.shopping.ibe.manager.util.IBEUtils;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.hub.model.input.*;
import com.travelsky.hub.model.output.*;
import com.travelsky.hub.model.peentity.bookingSeat.output.BookingSeatRS;
import com.travelsky.hub.model.peentity.seatchart.input.*;
import com.travelsky.hub.model.peentity.seatchart.output.LineInfo;
import com.travelsky.hub.model.peentity.seatchart.output.SeatChartRs;
import com.travelsky.hub.model.peentity.seatchart.output.SeatInfo;
import com.travelsky.hub.util.APISvcException;
import com.travelsky.hub.util.HubServiceException;
import com.travelsky.hub.wdoe.input.HbpuoInputBean;
import com.travelsky.hub.wdoe.output.PUOutputBean;
import com.travelsky.ibe.client.pnr.RT;
import com.travelsky.ibe.exceptions.IBEException;
import com.travelsky.ibe.exceptions.RTNoPNRException;
import com.travelsky.ibe.exceptions.RTNotAuthorizedException;
import com.travelsky.ibe.exceptions.RTPNRCancelledException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Author: deyi
 * @Date: 2019/11/19 16:34
 * @Version 1.0
 */
@Slf4j
@Service("IBECheckInService")
public class CheckInServiceImpl implements CheckInService {

    @Resource(name = "checkInService")
    private ICheckInService checkInService;
    @Autowired
    private PNRManager pnrManager;
    @Autowired
    private PNRService pnrService;

    @Override
    public boolean hasInitialized(FlightInitRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());
        //提取航班信息新接口
        boolean initialized = false;
        log.debug("queryCheckInFlightInfo begin ,req:{} ", req);
        try {
            BoardInfoResult result = queryFlightBoardFromPE(req);
            if (null != result) {
                initialized = true;
            }
        } catch (HubServiceException e) {
            throw new PEException(e.getMessage(), e.getErrorCode());
        }
        return initialized;
    }

    @Override
    public FlightCheckInInfoRS queryCheckInFlightInfo(FlightInitRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());
        //提取航班信息新接口
        log.info("queryCheckInFlightInfo begin... ");
        FlightCheckInInfoRS result = new FlightCheckInInfoRS();
        try {
            BoardInfoResult boardInfoReusult = queryFlightBoardFromPE(req);
            BeanUtils.copyProperties(boardInfoReusult, result);
        } catch (HubServiceException e) {
            log.error(this.getClass().getName() + ".GetFltDetail error:" + e.getMessage(), e);
        }
        return result;
    }


    @Override
    public SingleCheckInRS singleCheckIn(SingleCheckInRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());
        PsrCheckInBean psrCheckInBean = new PsrCheckInBean();
        BeanUtils.copyProperties(req, psrCheckInBean);
        if(StringUtils.isBlank(psrCheckInBean.getEtCode())){
            if(StringUtils.isNotBlank(req.getTkNo())){
                psrCheckInBean.setEtCode(req.getTkNo());
            }
        }
        //InfInfoBean infInfoBean = new InfInfoBean();
        //BeanUtils.copyProperties(req.getInfInfo(), infInfoBean);

        PsrCheckInResult psrCheckInResult = null;
        try{
            psrCheckInResult = checkInService.doPsrCheckin(psrCheckInBean);
        }catch (HubServiceException e){
            throw new PEException(e.getMessage(), e.getErrorCode());
        }
        SingleCheckInRS singleCheckInRS = new SingleCheckInRS();
        BeanUtils.copyProperties(psrCheckInResult, singleCheckInRS);
        return singleCheckInRS;
    }

    @Override
    public CancelCheckInRS cancelCheckIn(CancelCheckInRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());
        PsgPwInfoBean psgPwInfoBean = new PsgPwInfoBean();
        BeanUtils.copyProperties(req, psgPwInfoBean);
        PWResult pwResult = null;
        try{
            pwResult = checkInService.doDelPsr(psgPwInfoBean);
        }catch (HubServiceException e){
            throw new PEException(e.getMessage(), e.getErrorCode());
        }
        CancelCheckInRS cancelCheckInRS = new CancelCheckInRS();
        BeanUtils.copyProperties(pwResult, cancelCheckInRS);
        return cancelCheckInRS;
    }

    @Override
    public PUOutputRS connectingFlightCheckIn(HbpuoInputRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());
        HbpuoInputBean hbpuoInputBean = getHbpuoInputBean(req);
        PUOutputBean puOutputBean = checkInService.connectingFlightCheckIn(hbpuoInputBean);
        PUOutputRS puOutputRS = new PUOutputRS();
        puOutputRS.setArrivalTime(puOutputBean.getArrivalTime());
        puOutputRS.setBoardingGateNumber(puOutputBean.getBoardingGateNumber());
        puOutputRS.setBordingTime(puOutputBean.getBordingTime());
        puOutputRS.setDepartureTime(puOutputBean.getDepartureTime());
        puOutputRS.setPassengers(puOutputBean.getPassengers());
        return puOutputRS;
    }


    //查询PE 获取航班信息
    private BoardInfoResult queryFlightBoardFromPE(FlightInitRQ req) {
        FlightBoardQueryBean boardQueryBean = new FlightBoardQueryBean();
        boardQueryBean.setDeptAirport(req.getDepCity());
        boardQueryBean.setFlightDate(req.getFlightDate());
        boardQueryBean.setFlightNo(req.getFlightNo());
        BoardInfoResult boardInfoReusult = checkInService.queryFlightBoardInAirport(boardQueryBean);
        return boardInfoReusult;
    }

    @Override
    public String printBoarding(PrintBoardingRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());

        RePrintInputBean rePrintInputBean = new RePrintInputBean();
        rePrintInputBean.setAirlineCode(req.getAirlineCode());
        //rePrintInputBean.setBoardingNumber(req.getBoardingNumber());
        rePrintInputBean.setFlightClass(req.getCabin());
        rePrintInputBean.setFlightDate(DateUtil.dateToString(req.getDepTime(), "yyyyMMdd"));
        rePrintInputBean.setFlightNumber(req.getFlightNo().substring(2));
        rePrintInputBean.setFromCity(req.getDepCode());
        rePrintInputBean.setToCity(req.getArrCode());
        rePrintInputBean.setGroupName(req.getGroupName());
        rePrintInputBean.setLocal(req.getLocal());
        rePrintInputBean.setPassengerName(CnToSpellNew.getPinYin(req.getPassengerName()).toUpperCase());
        rePrintInputBean.setReissue(req.getReissue());
        rePrintInputBean.setTktNumber(req.getTicketNo().replace("-", ""));
        rePrintInputBean.setTourIndex(req.getTourIndex());
        RePrintOutputBean rePrintResult = checkInService.rePrint(rePrintInputBean);
        return (String) rePrintResult.getDataFlows().get(0);
    }

    @Override
    public WeatherRS weather(WeatherRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());

        WeatherQueryBean weatherQueryInputBean = new WeatherQueryBean();
        weatherQueryInputBean.setAirportCode(req.getCity());
        WeatherInfoResult weatherInfoResult = checkInService.queryCityWeather(weatherQueryInputBean);
        ;
        if (weatherInfoResult != null) {
            WeatherRS rs = new WeatherRS();
            rs.setCity(weatherInfoResult.getCity());
            rs.setChnCity(weatherInfoResult.getChnCity());
            List<WeatherItem> itemList = new ArrayList<>();
            if (weatherInfoResult.getWhether() != null) {
                for (DayWhetherBean dayWhetherBean : weatherInfoResult.getWhether()) {
                    WeatherItem item = new WeatherItem();
                    item.setIndex(dayWhetherBean.getIndex());
                    item.setDescription(dayWhetherBean.getDescription());
                    item.setLowTemperature(dayWhetherBean.getL_Temperature());
                    item.setHighTemperature(dayWhetherBean.getH_Temperature());
                    item.setWind(dayWhetherBean.getWind());
                    itemList.add(item);
                }
                rs.setWeather(itemList);
            }
            return rs;
        }
        return null;
    }

    @Override
    public EbpRS qrEbp(EbpRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());

        EbpInfo ebpInfo = new EbpInfo();
        ebpInfo.setDataStream(req.getDataStream());
        EbpOutputBean ebpOutputBean = checkInService.getEboardingPass(ebpInfo);
        return buildEbsRS(ebpOutputBean);
    }

    private EbpRS buildEbsRS(EbpOutputBean ebpOutputBean) {
        if (ebpOutputBean != null) {
            EbpRS rs = new EbpRS();
            rs.setEbpImgByteStr(ebpOutputBean.getEbpImgByteStr());
            rs.setEbpStr(ebpOutputBean.getEbpStr());
            return rs;
        }
        return null;
    }

    @Override
    public EbpRS pdfEbp(EbpRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());

        EbpInfo ebpInfo = new EbpInfo();
        ebpInfo.setDataStream(req.getDataStream());
        EbpOutputBean ebpOutputBean = checkInService.getPdf417Ebp(ebpInfo);
        return buildEbsRS(ebpOutputBean);
    }

    @Override
    public EbpRS barEbp(EbpRQ req) {
        CheckInConfigHolder.setUser(req.getAirlineCode());

        EbpInfo ebpInfo = new EbpInfo();
        ebpInfo.setDataStream(req.getDataStream());
        EbpOutputBean ebpOutputBean = checkInService.getBarCodeEbp(ebpInfo);
        return buildEbsRS(ebpOutputBean);
    }


    private HbpuoInputBean getHbpuoInputBean(HbpuoInputRQ req) {
        HbpuoInputBean hbpuoInputBean = new HbpuoInputBean();
        BeanUtils.copyProperties(req, hbpuoInputBean);
        if (hbpuoInputBean.getThroughPassenger() != null) {
            hbpuoInputBean.setThroughPassenger(getHbpuoInputBean(req.getThroughPassenger()));
        }
        return hbpuoInputBean;
    }

    @Override
    public List<RetrieveRS>  retrieve(RetrieveRQ rq) {
        List<RetrieveRS> retrieveRS= Lists.newArrayList();
        try{
            for (RetrieveRQ.Identity identity : rq.getIdentitys()) {
                CheckInConfigHolder.setUser(rq.getAirlineCode());
                DetrInputBean checkInReq=new DetrInputBean();
                checkInReq.setCertificateNumber(identity.getCertificateNumber());
                checkInReq.setCertificateType(identity.getCertificateType());

                DetrOutPutBean result= Optional.ofNullable(checkInService.detrTicket(checkInReq)).orElseThrow(()->new BusinessException("提取行程失败!"));

                Map<String, String> passengerTypeMap = new HashMap<>();
                List<DetrTicketBean> tickets = result.getTickets();
                for(DetrTicketBean ticket: tickets) {
                    List<DetrTourBean> tours = ticket.getTours();
                    for (DetrTourBean tour : tours) {
                        RetrieveRS item = new RetrieveRS();
                        item.setKey(identity.getCertificateType()+"_"+identity.getCertificateNumber());
                        item.setTktNumber(ticket.getTKTNumber());
                        item.setPName(ticket.getPassengerName());
                        //TODO 城市是否可以值机
                        item.setCityIsCheckIn(false);
                        item.setTourIndex(tour.getTourIndex());
                        item.setFromCity(tour.getFromCity());
                        item.setToCity(tour.getToCity());
                        item.setPnr(tour.getPNR());
                        item.setAirlineCode(tour.getAirlineCode());
                        item.setFlightNumber(tour.getFlightNumber());
                        item.setTourDate(tour.getTourDate());
                        item.setTourTime(tour.getTourTime());
                        item.setTourClass(tour.getTourClass());
                        item.setStatus(tour.getStatus());
                        item.setCarrAirlineCode(tour.getAirlineCode().substring(0,2));
                        item.setFromCityStatus(tour.getFromCityStatus());
                        item.setToCityStatus(tour.getToCityStatus());
                        //TODO 是否北美航线
                        item.setNorthAmerica(false);
                        String ptype = passengerTypeMap.get(ticket.getPassengerName());
                        if(ptype == null){
                            ptype = getPassengerType(tour.getPNR(), tour.getAirlineCode(), ticket.getPassengerName());
                            passengerTypeMap.put(ticket.getPassengerName(), ptype);
                        }
                        item.setPType(ptype);
                        retrieveRS.add(item);
                    }
                }
            }
        }catch (HubServiceException e){
            String msg = e.getMessage();
            if(-300005 == e.getErrorCode()){
                msg = "旅客行程未到可办理时间";
            }
            throw new PEException(msg, e.getErrorCode());
        }
        return retrieveRS;
    }

    private String getPassengerType(String pnrNo, String airlineCode, String passengerName){
        RTRequest req = new RTRequest();
        req.setPnrNo(pnrNo);
        req.setAirlineCode(airlineCode);
        RTResult rtResult = pnrManager.rt(req);
        if(rtResult != null){
            for(PNRPassenger p:rtResult.getPassengers()){
                if(p.getName().equalsIgnoreCase(passengerName)){
                    return p.getPasstype()== BasePassenger.ADULT?"ADT":
                            p.getPasstype()== BasePassenger.CHILD?"CHD":"INF";
                }
            }
        }
        return null;
    }

    @Override
    public SeatMapRS seatMap(SeatMapRQ rq) {
        CheckInConfigHolder.setUser(rq.getAirlineCode());
        SeatChartQueryBean seatChartQueryBean=new SeatChartQueryBean();
        seatChartQueryBean.setFlightNo(rq.getFlightNo());
        seatChartQueryBean.setFlightDate(rq.getFlightDate());
        seatChartQueryBean.setFromCity(rq.getFromCity());
        seatChartQueryBean.setToCity(rq.getToCity());
        seatChartQueryBean.setFlightClass(rq.getFlightClass());
        SeatChartResultBean seatChartResultBean= Optional.ofNullable(checkInService.querySeatChart(seatChartQueryBean)).orElseThrow(()->new BusinessException("座位图失败!"));
        return assembleSeatMapRS(seatChartResultBean);
    }

    @Override
    public List<RetrievePassengerRS> retrievePassenger(RetrievePassengerRQ rq) {
        List<RetrievePassengerRS> result = null;
        try{
            result=rq.passengerInfs.stream().map(passenger->{

                //查询登机信息
                CheckInConfigHolder.setUser(rq.getAirlineCode());
                FlightBoardQueryBean query=assembleFlightBoardQueryBean(passenger);
                BoardInfoResult boardInfoResult = checkInService.queryFlightBoardInAirport(query);

                //查询乘客
                CheckInConfigHolder.setUser(rq.getAirlineCode());
                SYPRQueryBean psrInput = buildSYPRQueryBean(passenger);
                SYPRResultBean psrOutput = checkInService.doSYPR(psrInput);

                //航班是否初始化
                boolean flightOpened = isFlightOpened(boardInfoResult, psrOutput);

                RetrievePassengerRS retrievePassengerRS=assemblePassenger(boardInfoResult, psrOutput, passenger,flightOpened);
                return retrievePassengerRS;
            }).collect(Collectors.toList());
        }catch (HubServiceException e){
            throw new PEException(e.getMessage(), e.getErrorCode());
        }
        return result;
    }

    @Override
    public RetrieveForeignerPassengerRS retrieveForeignerPassenger(RetrieveForeignerPassengerRQ req) {
        //查询登机信息
        CheckInConfigHolder.setUser(req.getAirlineCode());
        FlightBoardQueryBean query=assembleFlightBoardQueryBean(req);
        BoardInfoResult boardInfoResult = checkInService.queryFlightBoardInAirport(query);

        //查询乘客
        CheckInConfigHolder.setUser(req.getAirlineCode());
        SYPRQueryBean psrInput = buildSYPRQueryBean(req);
        SYPRResultBean psrOutput = checkInService.doSYPR(psrInput);

        //航班是否初始化
        boolean flightOpened = isFlightOpened(boardInfoResult, psrOutput);

        RetrieveForeignerPassengerRS retrievePassengerRS=assembleForeignerPassenger(req);
        return retrievePassengerRS;
    }

    @Override
    public boolean bookingSeat(BookingSeatRQ req) {
        try{
            CheckInConfigHolder.setUser(req.getOfficeInfo().getAirline());
            com.travelsky.hub.model.peentity.bookingSeat.input.BookingSeatRQ rq = ConvertUtil.map(req, new com.travelsky.hub.model.peentity.bookingSeat.input.BookingSeatRQ());
            BookingSeatRS rs = checkInService.bookingSeat(rq);
            if(rs != null && "0".equals(rs.getResultCode())){
                return true;
            }
            throw new BusinessException(rs.getResultMessage());
        }catch (APISvcException e){
            boolean booked = StringUtils.isNotBlank(e.getMessage()) && e.getMessage().contains("The passengers already bought a seat");
            if(booked){
                try{
                    String pnrNo = req.getPnrCode();
                    String passengerName = req.getPassenger().getPassengerName();
                    String fromCity = req.getFlightSegment().getDepartureAirport();
                    String toCity = req.getFlightSegment().getArrivalAirport();
                    String seatNumber = req.getServiceItem().getSeatInfo().getSeatNumber();
                    com.travelsky.ibe.client.pnr.PNRSSR ssr = getBookingSeatSsr(req.getOfficeInfo().getAirline(), pnrNo, passengerName, fromCity, toCity, seatNumber);
                    if(ssr != null){
                        log.info("pnrNo:{},passengerName:{},citypair:{},seatNumber:{},has booked", pnrNo, passengerName, fromCity+toCity, seatNumber);
                        return true;
                    }
                }catch (Exception e1){
                    log.info("getBookingSeatSsr error:{}", e1.getMessage());
                }
            }
            throw new GlobalException(CodePE.INVOKE_PE_ERROR, e);
        }
    }

    @Override
    public SeatChartRS getSeatChart(SeatChartRQ rq) {
        try{
            String airlineCode = rq.getAirlineCode();
            if(StringUtils.isBlank(airlineCode) && rq.getFlightSegment() != null){
                airlineCode = rq.getFlightSegment().getMcAirlineCode();
            }
            CheckInConfigHolder.setUser(airlineCode);
            SeatChartRq seatChartRq = buildSeatChartRq(rq);
            SeatChartRs seatChartRs= Optional.ofNullable(checkInService.getSeatChart(seatChartRq)).orElseThrow(()->new BusinessException("座位图失败!"));
            return assembleSeatChartRS(seatChartRs);
        }catch (HubServiceException e){
            throw new PEException(e.getMessage(), e.getErrorCode());
        }catch (APISvcException e){
            throw new GlobalException(CodePE.INVOKE_PE_ERROR, e);
        }
    }

    @Override
    public SeatReleaseRS seatRelease(SeatReleaseRQ rq) {
        if(rq.isOnlyRelease()){
            return releaseSeat(rq);
        }else{
            return cancelSeat(rq);
        }
    }

    private SeatReleaseRS cancelSeat(SeatReleaseRQ rq){
        if(StringUtils.isBlank(rq.getPnrNo()) || StringUtils.isBlank(rq.getPassengerName())
                || StringUtils.isBlank(rq.getFromCity()) || StringUtils.isBlank(rq.getToCity())
                || StringUtils.isBlank(rq.getSeatNumber())){
            throw new GlobalException(CodeDefault.ILLEGAL_ARGUMENT, "");
        }
        SeatReleaseRS rs = new SeatReleaseRS();
        DelPnrItemRequest delPnrItemRequest = buildDelPnrItemRequest(rq);
        if(delPnrItemRequest != null){
            BaseResponse baseResponse = pnrService.delPnrItem(delPnrItemRequest);
            rs.setResultCode(baseResponse.isSuccess()?"0":baseResponse.getErrorCode());
            rs.setResultMsg(baseResponse.getErrorInfo());
        }else{
            rs.setResultCode("0");
        }
        return rs;
    }

    private DelPnrItemRequest buildDelPnrItemRequest(SeatReleaseRQ rq){
        String airline = rq.getAirlineCode();
        String pnrNo = rq.getPnrNo();
        String passengerName = rq.getPassengerName();
        String cityPair = rq.getFromCity() + rq.getToCity();
        log.info("del pnrNo:{},passengerName:{},cityPair:{},seatNumber:{}", pnrNo, passengerName, cityPair, rq.getSeatNumber());

        com.travelsky.ibe.client.pnr.PNRSSR ssr = getBookingSeatSsr(airline, pnrNo, passengerName, rq.getFromCity(), rq.getToCity(), rq.getSeatNumber());
        if(ssr != null){
            String ssrSeat = ssr.getTextInPNR();
            DelPnrItemRequest req = new DelPnrItemRequest();
            req.setPnrNo(pnrNo);
            req.setAirlineCode(airline);
            req.setContents(Arrays.asList(ssrSeat));
            log.info("DelPnrItemRequest:{}", JSON.toJSONString(req));
            return req;
        }
        return null;
    }

    private com.travelsky.ibe.client.pnr.PNRSSR getBookingSeatSsr(String airline, String pnrNo, String passengerName, String fromCity, String toCity, String seatNumber){
        String SSR_TYPE_SEAT = "SEAT";
        String ACTION_CODE_BOOK_STATUS = "HK";
        String cityPair = fromCity + toCity;
        seatNumber = leftPadSeatNumber(seatNumber);
        log.info("getBookingSeatSsr pnrNo:{},passengerName:{},cityPair:{},seatNumber:{}", pnrNo, passengerName, cityPair, seatNumber);
        String[][] containsTexts = {
                {" "+seatNumber+"N/"}
        };

        RT rt = IbeCmd.getIbeClient(airline, RT.class);
        com.travelsky.ibe.client.pnr.RTResult rr = null;
        try{
            rr = rt.retrieve(pnrNo);
        }catch (Exception e){
            log.error("rt error ,pnr : {} ,airCode : {} ,error :{}", pnrNo, airline, e.getMessage());
            if(e instanceof RTPNRCancelledException || e instanceof RTNoPNRException || e instanceof RTNotAuthorizedException){
                return null;
            }
        }
        if(rr == null){
            throw new RuntimeException("rt fail");
        }

        List<Integer> indexes = new ArrayList<>();
        com.travelsky.ibe.client.pnr.PNRSSR ssr = IBEUtils.getSSRFirst(rr, SSR_TYPE_SEAT, ACTION_CODE_BOOK_STATUS, passengerName, cityPair, Arrays.asList(containsTexts), indexes);
        return ssr;
    }

    private static String leftPadSeatNumber(String seatNumber){
        int index = StringUtils.indexOfAnyBut(seatNumber, "0123456789");
        if(index < 0){
            return seatNumber;
        }
        String rowNo = StringUtils.substring(seatNumber, 0, index);
        String col = StringUtils.substring(seatNumber, index);
        return StringUtils.leftPad(rowNo, 2, "0") + col;
    }

    private SeatReleaseRS releaseSeat(SeatReleaseRQ rq) {
        try{
            CheckInConfigHolder.setUser(rq.getAirlineCode());
            RaInputBean raInputBean = ConvertUtil.map(rq, new RaInputBean());
            RaOutputBean raOutputBean = checkInService.seatRelease(raInputBean);
            return ConvertUtil.map(raOutputBean, new SeatReleaseRS());
        }catch (APISvcException e){
            throw new GlobalException(CodePE.INVOKE_PE_ERROR, e);
        }
    }

    private SeatChartRq buildSeatChartRq(SeatChartRQ req){
        RequestCriteria requestCriteria = new RequestCriteria();
        requestCriteria.setFlightSegment(ConvertUtil.map(req.getFlightSegment(), new FlightSegment()));
        if(req.getPassenger() != null){
            requestCriteria.setPassenger(ConvertUtil.map(req.getPassenger(), new Passenger()));
        }

        Responsibility responsibility = new Responsibility();
        responsibility.setResponsibilityCity("BJS");

        SeatChartRq rq = new SeatChartRq();
        rq.setTicketingDate(req.getTicketingDate());
        rq.setVcAirline(req.getVcAirline());
        rq.setRequestCriteria(requestCriteria);
        rq.setResponsibility(responsibility);
        return rq;
    }

    private RetrieveForeignerPassengerRS assembleForeignerPassenger(RetrieveForeignerPassengerRQ req) {
        RetrieveForeignerPassengerRS result= new RetrieveForeignerPassengerRS();
        result.setAirlineCode(req.getAirlineCode());
        result.setFlightNumber(req.getFlightNumber());
        result.setDepartureAirport(req.getFromCity());
        result.setArrivalAirport(req.getToCity());
        result.setDepartureDate(req.getFlightDate());
        result.setHostNumber(req.getHostNumber());
        result.setDeniedBoardingVolunteerInd(false);
        assembleApiInfo(result);

        //TODO  assemble  obj
        return result;
    }

    private void assembleApiInfo(RetrieveForeignerPassengerRS result) {
        RetrieveForeignerPassengerRS.ApiInfo apiInfo=new RetrieveForeignerPassengerRS.ApiInfo();
        //TODO assemble apiifno
//        apiInfo.setSurName();
//        apiInfo.setBirthDate();
//        apiInfo.setGender();
//        apiInfo.setTransferInd();
//        apiInfo.setPrimaryHolderInd();
//        apiInfo.setDocType();
//        apiInfo.setDocHolderNationality();
//        apiInfo.setExpireDate();
//        apiInfo.setDocIssueCountry();
//        apiInfo.setGivenName();
//        apiInfo.setMiddleName();
//        apiInfo.setBirthLocation();
//        apiInfo.setEffectiveDate();
//        apiInfo.setResidenceCountry();
//        apiInfo.setVisaInfo();
//        apiInfo.setOtherDocInfo();
//        apiInfo.setHomeAddress();
//        apiInfo.setDestAddress();
        result.setApiInfo(apiInfo);
    }

    private RetrievePassengerRS assemblePassenger(BoardInfoResult boardInfoResult, SYPRResultBean psrOutput, RetrievePassengerRQ.PassengerInf req, boolean flightOpened) {
        RetrievePassengerRS result= new RetrievePassengerRS();
        result.setPName(psrOutput.getPsrName());
        result.setPEnName(psrOutput.getPsrEnName());
        result.setPCiStatus(psrOutput.getPstCkiStatus());
        result.setFfLevel(psrOutput.getFfLevel());
        result.setAsrSeat(psrOutput.getAsrSeat());
        result.setCardLevel(psrOutput.getCardLevel());
        result.setCardId(psrOutput.getCardID());
        result.setCardAirline(psrOutput.getCardAirline());
        result.setCabinType(psrOutput.getCabinType());
        result.setSpeicialSvc(psrOutput.getSpeicialSvc());
        result.setSchDeptTime(psrOutput.getSchDeptTime());
        result.setFromCity(req.getFromCity());
        result.setToCity(req.getToCity());
        result.setExpDeptTime(boardInfoResult.getDeptTime());
        result.setFlightOpened(flightOpened);
        result.setBoardingTime(boardInfoResult.getBoardingTime());
        result.setBoardingGateNumber(boardInfoResult.getBoardingGateNumber());
        result.setPlaneType(psrOutput.getPlaneType());
        result.setCarrFlightNo(psrOutput.getCarrFlightNo());
        result.setCkiInChannel(psrOutput.getCkiInChannel());
        result.setAsrStatus(psrOutput.getAsrStatus());
        result.setHostNum(psrOutput.getHostNum());
        result.setChdFlag(psrOutput.getChdFlag());
        result.setChd(psrOutput.getChd());
        //TODO 没填充
        result.setAsvcInfo(Lists.newArrayList());
        return result;
    }

    private SYPRQueryBean buildSYPRQueryBean( RetrievePassengerRQ.PassengerInf req) {
        SYPRQueryBean result=new SYPRQueryBean();
        result.setFlightDate(req.getFlightDate());
        result.setFlightNo(req.getFlightNo());
        result.setDeptAptCode(req.getFromCity());
        result.setArvAptCode(req.getToCity());
        result.setEtCode(StringUtils.isNotBlank(req.getTkNo())?req.getTkNo().replace("-", ""):req.getTkNo());
        return result;
    }

    private SYPRQueryBean buildSYPRQueryBean( RetrieveForeignerPassengerRQ req) {
        SYPRQueryBean result=new SYPRQueryBean();
        result.setFlightDate(req.getFlightDate());
        result.setFlightNo(req.getFlightNumber());
        result.setDeptAptCode(req.getFromCity());
        result.setArvAptCode(req.getToCity());
        result.setEtCode(req.getTkNo());
        return result;
    }

    private boolean isFlightOpened(BoardInfoResult boardInfoResult, SYPRResultBean psrOutput) {
        //航班是否初始化
        boolean flightOpened=true;
        if(null== boardInfoResult) {
            flightOpened=false;
        }
        if(psrOutput == null) {
            flightOpened=false;
        }
        return flightOpened;
    }

    private FlightBoardQueryBean assembleFlightBoardQueryBean( RetrievePassengerRQ.PassengerInf req) {
        FlightBoardQueryBean queryBean=new FlightBoardQueryBean();
        queryBean.setFlightDate(req.getFlightDate());
        queryBean.setFlightNo(req.getFlightNo());
        queryBean.setDeptAirport(req.getFromCity());
        return queryBean;
    }

    private FlightBoardQueryBean assembleFlightBoardQueryBean(RetrieveForeignerPassengerRQ req) {
        FlightBoardQueryBean queryBean=new FlightBoardQueryBean();
        queryBean.setFlightDate(req.getFlightDate());
        queryBean.setFlightNo(req.getFlightNumber());
        queryBean.setDeptAirport(req.getFromCity());
        return queryBean;
    }

    private SeatMapRS assembleSeatMapRS(SeatChartResultBean seatChartResultBean) {
        SeatMapRS result= ConvertUtil.map(seatChartResultBean, new SeatMapRS());
        return result;
    }

    private SeatChartRS assembleSeatChartRS(SeatChartRs seatChartRs) {
        SeatChartRS result= ConvertUtil.map(seatChartRs, new SeatChartRS());
        for(SeatChartRS.LineInfo lineInfo:result.getLineInfos()){
            for(SeatChartRS.SeatInfo seatInfo:lineInfo.getSeatInfos()){
                seatInfo.setRow(lineInfo.getLineNo());
                seatInfo.setCol(seatInfo.getSeatNumber().replace(lineInfo.getLineNo(), ""));
            }
        }
        SeatMap seatMapInfo = convertSeatMapInfo(seatChartRs);
        result.setSeatMapInfo(seatMapInfo);
        return result;
    }

    private SeatMap convertSeatMapInfo(SeatChartRs seatChartRs){
        List<List<SeatMap.SeatInfo>> lines = new ArrayList<>();
        List<String> exitRows = new ArrayList<>();
        LineInfo maxLenLineInfo = null;
        for(LineInfo lineInfo:seatChartRs.getLineInfos()){
            List<SeatMap.SeatInfo> lineSeats = new ArrayList<>();
            if(isExitLine(lineInfo.getLineAttr())){
                exitRows.add(lineInfo.getLineNo());
            }
            int size = lineInfo.getSeatInfos().size();
            if(maxLenLineInfo == null || size > maxLenLineInfo.getSeatInfos().size()){
                maxLenLineInfo = lineInfo;
            }
            for(int i=0;i<size;i++){
                SeatInfo seatInfo = lineInfo.getSeatInfos().get(i);
                SeatMap.SeatInfo seat = new SeatMap.SeatInfo();
                seat.setRow(lineInfo.getLineNo());
                seat.setCol(seatInfo.getSeatNumber().replace(lineInfo.getLineNo(), ""));
                seat.setPrice(seatInfo.getFareAmount()==null?null:new BigDecimal(seatInfo.getFareAmount()));
                seat.setSeatStatus(getSeatStatus(seatInfo));
                lineSeats.add(seat);
            }
            lines.add(lineSeats);
        }
        List<List<String>> arrange = initArrange(maxLenLineInfo);
        int cols = arrange.stream().flatMapToInt(item -> IntStream.of(item.size())).sum();
        SeatMap.ExitInfo exit = new SeatMap.ExitInfo();
        exit.setRow(exitRows);
        SeatMap seatMap = new SeatMap();
        seatMap.setSeat(initSeats(lines, arrange));
        seatMap.setRows(seatChartRs.getLineInfos().size());
        seatMap.setCols(cols);
        seatMap.setExit(exit);
        seatMap.setArrange(arrange);
        return seatMap;
    }

    private boolean isExitLine(String lineAttr){
        if(StringUtils.isNotBlank(lineAttr)){
            return Arrays.asList(lineAttr.split(",")).contains(LineAttrEnum.EXT.name());
        }
        return false;
    }

    private List<List<String>> initArrange(LineInfo maxLenLineInfo){
        List<List<String>> arrange = new ArrayList<>();
        if(maxLenLineInfo != null){
            List<String> arr = new ArrayList<>();
            for(SeatInfo seatInfo:maxLenLineInfo.getSeatInfos()){
                arr.add(seatInfo.getSeatNumber().replace(maxLenLineInfo.getLineNo(), ""));
                if(StringUtils.isNotBlank(seatInfo.getSeatChar())){
                    if(Arrays.asList(seatInfo.getSeatChar().split(",")).contains(SeatCharEnum.AIR.name())){
                        arrange.add(arr);
                        arr = new ArrayList<>();
                    }
                }
            }
            arrange.add(arr);
        }
        return arrange;
    }

    private List<SeatMap.SeatInfo> initSeats(List<List<SeatMap.SeatInfo>> lines, List<List<String>> arrange){
        List<List<SeatMap.SeatInfo>> newlines = new ArrayList<>();
        if(arrange != null){
            //填充无座位
            List<String> cols = arrange.stream().reduce(new ArrayList<>(), (all, item)->{
                all.addAll(item);
                return all;
            });
            for(List<SeatMap.SeatInfo> line:lines){
                List<SeatMap.SeatInfo> newline = fillEmptySeat(line, cols);
                newlines.add(newline);
            }
        }else{
            newlines.addAll(lines);
        }
        return newlines.stream().reduce(new ArrayList<>(), (all, item)->{
            all.addAll(item);
            return all;
        });
    }

    private List<SeatMap.SeatInfo> fillEmptySeat(List<SeatMap.SeatInfo> line, List<String> cols){
        List<SeatMap.SeatInfo> newline = new ArrayList<>();
        if(line == null){
            return newline;
        }
        if(cols != null && cols.size() > line.size() && line.size() > 0){
            String row = line.get(0).getRow();
            for(String col:cols){
                boolean exist = false;
                for(SeatMap.SeatInfo seat:line){
                    if(col.equals(seat.getCol())){
                        exist = true;
                        newline.add(seat);
                        break;
                    }
                }
                if(!exist){
                    SeatMap.SeatInfo empty = new SeatMap.SeatInfo();
                    empty.setRow(row);
                    empty.setCol(col);
                    empty.setSeatStatus("");
                    newline.add(empty);
                }
            }
        }else{
            newline.addAll(line);
        }
        return newline;
    }

    //座位状态：*-可值机，A-可值机，.-已选座位，""-无座位（空字符串），N-不可选座位（目前没表示出来）
    private String getSeatStatus(SeatInfo seatInfo){
        String[] reservedSeatChars = {
                SeatCharEnum.BLS.name(),
                SeatCharEnum.RES.name(),
                SeatCharEnum.OCC.name(),

                SeatCharEnum.IRS.name(),
                SeatCharEnum.OSS.name(),
                SeatCharEnum.SES.name(),
                SeatCharEnum.ORS.name(),
                SeatCharEnum.GRS.name(),
                SeatCharEnum.VRS.name(),
                SeatCharEnum.ARS.name(),
                SeatCharEnum.ABR.name(),
                SeatCharEnum.APS.name(),
                SeatCharEnum.TBS.name(),
                SeatCharEnum.SRS.name(),
                SeatCharEnum.OCC.name(),
                SeatCharEnum.TPS.name(),
                SeatCharEnum.LRI.name(),
                SeatCharEnum.RRI.name(),
                SeatCharEnum.LRK.name(),
                SeatCharEnum.RRK.name(),
                SeatCharEnum.LRB.name(),
                SeatCharEnum.RRB.name(),
                SeatCharEnum.ASR.name(),
                SeatCharEnum.ABP.name(),
                SeatCharEnum.CRS.name(),
                SeatCharEnum.NML.name(),
                SeatCharEnum.SPC.name(),
                SeatCharEnum.VIP.name(),
                SeatCharEnum.IMP.name(),

                //FIXME 未知
                SeatCharEnum.BZS.name()
        };
        String seatStatus = "";
        if(StringUtils.isNotBlank(seatInfo.getSeatChar())){
            boolean reserved = CollectionUtils.intersection(Arrays.asList(reservedSeatChars), Arrays.asList(seatInfo.getSeatChar().split(","))).size() > 0;
            seatStatus = reserved?".":"*";
        }
        return seatStatus;
    }
}
