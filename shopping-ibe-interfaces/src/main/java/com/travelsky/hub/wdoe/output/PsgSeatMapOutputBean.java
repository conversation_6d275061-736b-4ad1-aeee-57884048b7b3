/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgSeatMapOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6732106995672460302L;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String preAssignSNO;
/*     */   private String typeV;
/*     */   private String aircraftType;
/*     */   private String flightLayout;
/*     */   private String flightPlan;
/*     */ 
/*     */   public String getAirline()
/*     */   {
/*  44 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/*  51 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getFlightPlan()
/*     */   {
/*  63 */     return this.flightPlan;
/*     */   }
/*     */ 
/*     */   public void setFlightPlan(String flightPlan)
/*     */   {
/*  70 */     this.flightPlan = flightPlan;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  78 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  85 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getPreAssignSNO()
/*     */   {
/*  92 */     return this.preAssignSNO;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 101 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 108 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setPreAssignSNO(String preAssignSNO)
/*     */   {
/* 115 */     this.preAssignSNO = preAssignSNO;
/*     */   }
/*     */ 
/*     */   public String getTypeV()
/*     */   {
/* 122 */     return this.typeV;
/*     */   }
/*     */ 
/*     */   public void setTypeV(String typeV)
/*     */   {
/* 129 */     this.typeV = typeV;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 136 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 143 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getAircraftType()
/*     */   {
/* 150 */     return this.aircraftType;
/*     */   }
/*     */ 
/*     */   public void setAircraftType(String aircraftType)
/*     */   {
/* 157 */     this.aircraftType = aircraftType;
/*     */   }
/*     */ 
/*     */   public String getFlightLayout()
/*     */   {
/* 164 */     return this.flightLayout;
/*     */   }
/*     */ 
/*     */   public void setFlightLayout(String flightLayout)
/*     */   {
/* 171 */     this.flightLayout = flightLayout;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PsgSeatMapOutputBean
 * JD-Core Version:    0.6.0
 */