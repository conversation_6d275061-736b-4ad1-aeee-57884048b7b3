package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.ibetools.*;
import com.travelsky.ibe.client.pnr.DETRHistoryResult;
import com.travelsky.ibe.client.pnr.DETRTKTResult;
import com.travelsky.ibe.client.pnr.RTResult;

/**
 * IBE 工具
 */
public interface IBEToolsManager {

    /**
     * 航信免流量费--定编码、出票
     * @param req
     * @return
     */
    IssueForTravelSkyRes issueForTravelSky(IssueForTravelSkyReq req);

    /**
     * 航信免流量费--rt获取结果
     * @param req
     * @return
     */
    RTResult rt(RtReq req);

    /**
     * 航信免流量费--退票、清位
     * @param req
     * @return
     */
    Boolean refundForTravelSky(RefundForTravelSkyReq req);

    /**
     * IBE小工具--rt历史获取结果
     * @param req
     * @return
     */
    RTResult rtHistory(RtReq req);

    /**
     * IBE小工具--detr获取票面信息
     * @param req
     * @return
     */
    DETRTKTResult detr(DETRReq req);

    /**
     * IBE小工具--detr获取票面历史信息
     * @param req
     * @return
     */
    DETRHistoryResult detrHistory(DETRReq req);


}
