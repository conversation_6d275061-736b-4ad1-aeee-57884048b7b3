/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import com.travelsky.hub.model.output.StopOverInfo;
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AcceptPsrOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4406029198331450810L;
/*  23 */   private String bordingTime = "";
/*     */ 
/*  28 */   private String departureTime = "";
/*     */ 
/*  34 */   private String arrivalTime = "";
/*     */ 
/*  40 */   private String boardingGateNumber = "";
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private List<StopOverInfo> stopOverInfos;
/*  52 */   private List passengers = new ArrayList();
/*     */ 
/*     */   public List getPassengers()
/*     */   {
/*  58 */     return this.passengers;
/*     */   }
/*     */ 
/*     */   public void setPassengers(List passengers)
/*     */   {
/*  65 */     this.passengers = passengers;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/*  74 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String i)
/*     */   {
/*  81 */     this.boardingGateNumber = i;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/*  90 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String i)
/*     */   {
/*  98 */     this.departureTime = i;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 105 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String i)
/*     */   {
/* 113 */     this.bordingTime = i;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 120 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String i)
/*     */   {
/* 128 */     this.arrivalTime = i;
/*     */   }
/*     */ 
/*     */   public AcceptedPsrBean getPassenger(int i)
/*     */   {
/* 137 */     return (AcceptedPsrBean)this.passengers.get(i);
/*     */   }
/*     */ 
/*     */   public void addPassenger(AcceptedPsrBean i)
/*     */   {
/* 144 */     this.passengers.add(i);
/*     */   }
/*     */ 
/*     */   public int getPassengersSize()
/*     */   {
/* 151 */     return this.passengers.size();
/*     */   }
/*     */   public String getFfpCardNumber() {
/* 154 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber) {
/* 158 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void clearPassengers()
/*     */   {
/* 164 */     this.passengers.clear();
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior() {
/* 168 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior) {
/* 172 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOverInfos() {
/* 176 */     return this.stopOverInfos;
/*     */   }
/*     */ 
/*     */   public void setStopOverInfos(List<StopOverInfo> stopOverInfos) {
/* 180 */     this.stopOverInfos = stopOverInfos;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 186 */     clearPassengers();
/* 187 */     this.bordingTime = "";
/* 188 */     this.boardingGateNumber = "";
/* 189 */     this.departureTime = "";
/* 190 */     this.arrivalTime = "";
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 195 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode) {
/* 199 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.AcceptPsrOutputBean
 * JD-Core Version:    0.6.0
 */