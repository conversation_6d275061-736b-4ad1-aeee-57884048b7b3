/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class HoldType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1333229798081581186L;
/*    */   private String hoType;
/*    */   private String hoScope;
/*    */   private String operationType;
/*    */ 
/*    */   public String getHoType()
/*    */   {
/* 36 */     return this.hoType;
/*    */   }
/*    */ 
/*    */   public void setHoType(String hoType)
/*    */   {
/* 43 */     this.hoType = hoType;
/*    */   }
/*    */ 
/*    */   public String getHoScope()
/*    */   {
/* 50 */     return this.hoScope;
/*    */   }
/*    */ 
/*    */   public void setHoScope(String hoScope)
/*    */   {
/* 57 */     this.hoScope = hoScope;
/*    */   }
/*    */ 
/*    */   public String getOperationType()
/*    */   {
/* 64 */     return this.operationType;
/*    */   }
/*    */ 
/*    */   public void setOperationType(String operationType)
/*    */   {
/* 71 */     this.operationType = operationType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.HoldType
 * JD-Core Version:    0.6.0
 */