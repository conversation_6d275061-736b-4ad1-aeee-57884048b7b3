/*    */ package com.travelsky.hub.model.peentity.reserve.cancel.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CancelReserveUpgRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9109020910451504264L;
/*    */   private String resultCode;
/*    */   private String reverseNumber;
/*    */   private String resultMessage;
/*    */   private String reverseStatus;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 42 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 49 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 56 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 63 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setReverseNumber(String reverseNumber)
/*    */   {
/* 70 */     this.reverseNumber = reverseNumber;
/*    */   }
/*    */ 
/*    */   public String getReverseNumber()
/*    */   {
/* 77 */     return this.reverseNumber;
/*    */   }
/*    */ 
/*    */   public void setReverseStatus(String reverseStatus)
/*    */   {
/* 84 */     this.reverseStatus = reverseStatus;
/*    */   }
/*    */ 
/*    */   public String getReverseStatus()
/*    */   {
/* 91 */     return this.reverseStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.cancel.output.CancelReserveUpgRS
 * JD-Core Version:    0.6.0
 */