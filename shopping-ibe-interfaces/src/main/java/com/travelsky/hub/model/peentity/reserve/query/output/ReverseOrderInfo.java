/*     */ package com.travelsky.hub.model.peentity.reserve.query.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ReverseOrderInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8315847551345928647L;
/*     */   private String reserveNumber;
/*     */   private String reserveStatus;
/*     */   private String ocAirlineCode;
/*     */   private String ocFlightNumber;
/*     */   private String createTime;
/*     */   private String orderNo;
/*     */   private String ocFlightSuffix;
/*     */   private String mcFlightSuffix;
/*     */   private String flightDate;
/*     */   private String mcAirlineCode;
/*     */   private String mcFlightNumber;
/*     */   private String departureAirport;
/*     */   private String pnrNumber;
/*     */   private String ticketNumber;
/*     */   private String arrivalAirport;
/*     */   private String psrName;
/*     */   private String tourIndex;
/*     */   private String certNo;
/*     */   private String contactInfo;
/*     */   private String certType;
/*     */   private String priority;
/*     */   private UpgInfo upgInfo;
/*     */   private String subcode;
/*     */   private PaymentDetail paymentDetail;
/*     */ 
/*     */   public String getReserveNumber()
/*     */   {
/* 124 */     return this.reserveNumber;
/*     */   }
/*     */ 
/*     */   public void setReserveNumber(String reserveNumber)
/*     */   {
/* 131 */     this.reserveNumber = reserveNumber;
/*     */   }
/*     */ 
/*     */   public String getCreateTime()
/*     */   {
/* 138 */     return this.createTime;
/*     */   }
/*     */ 
/*     */   public void setCreateTime(String createTime)
/*     */   {
/* 145 */     this.createTime = createTime;
/*     */   }
/*     */ 
/*     */   public String getReserveStatus()
/*     */   {
/* 152 */     return this.reserveStatus;
/*     */   }
/*     */ 
/*     */   public void setReserveStatus(String reserveStatus)
/*     */   {
/* 159 */     this.reserveStatus = reserveStatus;
/*     */   }
/*     */ 
/*     */   public String getOrderNo()
/*     */   {
/* 166 */     return this.orderNo;
/*     */   }
/*     */ 
/*     */   public void setOrderNo(String orderNo)
/*     */   {
/* 173 */     this.orderNo = orderNo;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 180 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/* 187 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/* 194 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/* 201 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 208 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 215 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getMcFlightNumber()
/*     */   {
/* 222 */     return this.mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMcFlightNumber(String mcFlightNumber)
/*     */   {
/* 229 */     this.mcFlightNumber = mcFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getMcAirlineCode()
/*     */   {
/* 236 */     return this.mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMcAirlineCode(String mcAirlineCode)
/*     */   {
/* 243 */     this.mcAirlineCode = mcAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getMcFlightSuffix()
/*     */   {
/* 250 */     return this.mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setMcFlightSuffix(String mcFlightSuffix)
/*     */   {
/* 257 */     this.mcFlightSuffix = mcFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 264 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 271 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 278 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 285 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 292 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 299 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getPnrNumber()
/*     */   {
/* 306 */     return this.pnrNumber;
/*     */   }
/*     */ 
/*     */   public void setPnrNumber(String pnrNumber)
/*     */   {
/* 313 */     this.pnrNumber = pnrNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 320 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 327 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 334 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 341 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 348 */     return this.certNo;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 355 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 362 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 369 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getContactInfo()
/*     */   {
/* 376 */     return this.contactInfo;
/*     */   }
/*     */ 
/*     */   public void setContactInfo(String contactInfo)
/*     */   {
/* 383 */     this.contactInfo = contactInfo;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 390 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 397 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getSubcode()
/*     */   {
/* 404 */     return this.subcode;
/*     */   }
/*     */ 
/*     */   public void setSubcode(String subcode)
/*     */   {
/* 411 */     this.subcode = subcode;
/*     */   }
/*     */ 
/*     */   public PaymentDetail getPaymentDetail()
/*     */   {
/* 418 */     return this.paymentDetail;
/*     */   }
/*     */ 
/*     */   public void setPaymentDetail(PaymentDetail paymentDetail)
/*     */   {
/* 425 */     this.paymentDetail = paymentDetail;
/*     */   }
/*     */ 
/*     */   public UpgInfo getUpgInfo()
/*     */   {
/* 432 */     return this.upgInfo;
/*     */   }
/*     */ 
/*     */   public void setUpgInfo(UpgInfo upgInfo)
/*     */   {
/* 439 */     this.upgInfo = upgInfo;
/*     */   }
/*     */ 
/*     */   public String getPriority()
/*     */   {
/* 446 */     return this.priority;
/*     */   }
/*     */ 
/*     */   public void setPriority(String priority)
/*     */   {
/* 453 */     this.priority = priority;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.query.output.ReverseOrderInfo
 * JD-Core Version:    0.6.0
 */