package com.hna.shopping.ibe.manager.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.hna.shopping.ibe.common.responsecode.CodeIbe;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.esb.ESBService;
import com.hna.shopping.ibe.manager.esb.EsbApiResponse;
import com.hna.shopping.ibe.manager.esb.StandardFocFlightInfo;
import com.hna.shopping.ibe.manager.util.REMatchUtil;
import com.hnair.opcnet.api.v2.ApiRequest;
import com.hnair.opcnet.api.v2.ApiResponse;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.client.pnr.DETRHistoryInfoItem;
import com.travelsky.ibe.client.pnr.DETRHistoryResult;
import com.travelsky.ibe.client.pnr.ETDZResult;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.common.util.PassengerType;
import com.travelsky.ibe.client.pnr.PNRPassenger;
import com.travelsky.ibe.client.pnr.PNRSSR;
import com.travelsky.ibe.client.pnr.PNRTktNo;
import com.travelsky.ibe.client.pnr.RTResult;

import com.travelsky.ibe.exceptions.IBEException;
import io.swagger.annotations.Api;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.apache.http.client.utils.DateUtils;

import com.hna.shopping.ibe.config.ApplicationContextProvider;
import com.hna.shopping.ibe.config.ibe.IBEConfig;
import com.hna.shopping.ibe.manager.TicketManager;
import com.hna.shopping.ibe.manager.ibe.IBERetry;
import com.hna.shopping.ibe.manager.util.IbeCmd;

import lombok.extern.slf4j.Slf4j;
import sun.security.krb5.internal.Ticket;

@Component("ibeTicketManager")
@Slf4j
public class IBETicketManager implements TicketManager {

    private final static int CYCLE_MAX_NUM = 4;

    @Autowired(required = false)
    private IBERetry ibeRetry;

    @Autowired
    private ESBService esbService;

    private static ListeningExecutorService EXECUTOR_SERVICE =
            MoreExecutors.listeningDecorator(Executors.newFixedThreadPool(200));

    @Override
    public DETRResponse detr(DETRRequest request) {
        DETRResponse response = new DETRResponse();
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            long time = System.currentTimeMillis();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getPnrNo())) {
                Vector<DETRTKTResult> vector = detr.getTicketInfoByPNR(request.getPnrNo());
                if (!vector.isEmpty()) {
                    for (DETRTKTResult result : vector) {
                        response.getTicketInfos().add(mapTicketResult(result));
                    }
                }
            } else if (null != request.getTicketNo() && !"".equals(request.getTicketNo())) {
                DETRTKTResult result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "N", secondFactorCode, secondFactorValue);
                response.getTicketInfos().add(mapTicketResult(result));
            } else {
                //根据证件类型、证件号获取旅客票面信息
                boolean isOnlyopen = true;//默认true，兼容未传值情况
                if (request.getOnlyOpen() != null && !request.getOnlyOpen().booleanValue()) {
                    isOnlyopen = false;
                }
                Vector<DETRDigestResult> vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), isOnlyopen, airline, null);
                if (vector != null && !vector.isEmpty()) {
                    if (!isOnlyopen) {
                        List<String> ticketNos = new ArrayList<>();
                        for (DETRDigestResult digestResult : vector) {
                            if (digestResult != null) {
                                boolean refundAll = true;
                                List<DETRDigestSegment> segs = digestResult.getSegs();
                                if (CollectionUtils.isNotEmpty(segs)) {
                                    for (DETRDigestSegment seg : segs) {
                                        if (seg.getStatus() != null && !seg.getStatus().trim().contains("REFU")) {
                                            refundAll = false;
                                        }
                                    }
                                }
                                if (!refundAll) {
                                    ticketNos.add(digestResult.getTktno());
                                }
                            }
                        }
                        if (ticketNos.size() > 0) {
                            //多线程
                            List<ListenableFuture> results = new ArrayList<>();
                            CountDownLatch countDownLatch = new CountDownLatch(ticketNos.size());
                            for (String ticketNo : ticketNos) {
                                TicketDETRCallable callable = new TicketDETRCallable(countDownLatch, detr2f, ticketNo, secondFactorCode, secondFactorValue);
                                ListenableFuture<DETRTKTResult> result = EXECUTOR_SERVICE.submit(callable);
                                results.add(result);
                            }
                            countDownLatch.await(15, TimeUnit.SECONDS);
                            for (ListenableFuture<DETRTKTResult> listListenableFuture : results) {
                                DETRTKTResult result = listListenableFuture.get();
                                if (result != null) {
                                    response.getTicketInfos().add(mapTicketResult(result));
                                }
                            }
                        }
                    } else {
                        for (DETRDigestResult digestResult : vector) {
                            DETRTKTResult result = detr2f.getTicketInfoByTktNo2F(digestResult.getTktno(), false, "", "N", secondFactorCode, secondFactorValue);
                            response.getTicketInfos().add(mapTicketResult(result));
                        }
                    }
                }
                log.info(" detr passengerNo: {}  passengerType: {},getTxnTraceKey: {} ", request.getPassengerNo(), request.getPassengerType(), detr.getTxnTraceKey());
            }
            log.info(" detr request: {} , take times: {} , getTxnTraceKey: {} ", JSON.toJSONString(request), (System.currentTimeMillis() - time), detr.getTxnTraceKey());

            response.setSuccess(true);
            // response.setOk(ok);
        } catch (Exception e) {
            log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        // response.setSuccess(true);
        return response;
    }

    @Override
    public DETRHistoryResponse getTicketHistory(DETRHistoryRequest request) {
        // TODO Auto-generated method stub
        DETRHistoryResponse response = new DETRHistoryResponse();
        // return null;


        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            DETRHistoryResult result = detr2f.getTicketHistoryByTktNo2F(request.getTicketNo(), "N", secondFactorCode, secondFactorValue);
            com.hna.shopping.ibe.interfaces.dto.DETRHistoryResult historyResult = new com.hna.shopping.ibe.interfaces.dto.DETRHistoryResult();
            BeanUtils.copyProperties(result, historyResult);
            for (int i = 0; i < result.getInfoItemNum(); i++) {
                DETRHistoryInfoItem infoItem = result.getInfoItem(i);
                com.hna.shopping.ibe.interfaces.dto.DETRHistoryInfoItem infoItem2 = new com.hna.shopping.ibe.interfaces.dto.DETRHistoryInfoItem();
                BeanUtils.copyProperties(infoItem, infoItem2);
                historyResult.getInfoItems().add(infoItem2);
            }

            log.info(" getTicketHistory ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());

            response.setDetrHistoryResult(historyResult);
            response.setSuccess(true);
            // response.setOk(ok);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error("", e);
            log.error(" getTicketHistory error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        // response.setSuccess(true);
        return response;
    }

    /**
     * 根据输入参数获取客票记录
     *
     * @param request
     * @return
     */
    @Override
    public DtoTicketInfoResponse getTicketInfo(DtoTicketInfoRequest request) {
        DtoTicketInfoResponse response = new DtoTicketInfoResponse();

//		if (StringUtils.isEmpty(request.getTicketNo())
//				|| StringUtils.isEmpty(request.getPassengerNo())
//				|| StringUtils.isEmpty(request.getFlightDate())
//		) {
//			response.setSuccess(false);
//			response.setErrorCode("-1");
//			response.setErrorInfo("票号、证件号、航班日期不能为空");
//			return response;
//		}

        // 先查高频，无结果时，再查IBE
        ApiResponse apiResponse = new ApiResponse();
        try {
            if (StringUtils.isNotEmpty(request.getTicketNo())) {      // 根据票号查询
                apiResponse = esbService.queryIETSByTkne(request.getTicketNo());
            } else {  // 根据证件号证件类型查询
                apiResponse = esbService.queryIETSByIdinfoAndFltDate(request.getPassengerNo(), request.getFlightDate() != null ? DateUtil.toStringYMD(DateUtil.toDateYYYY_MM_DD(request.getFlightDate())) : "");
            }
            response.setTicketInfos(mapTicketInfoResultByHSD(apiResponse));
            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                log.info("getTicketInfo from IETS: esb parse result is null. IETS result: {}", JSON.toJSONString(apiResponse));
            }
            // 匹配过滤航班日期、证件号和票号
            Iterator<TicketInfo> iterator = response.getTicketInfos().iterator();
            while (iterator.hasNext()) {
                TicketInfo ticketInfo = iterator.next();
                if (!request.getPassengerNo().equals(ticketInfo.getPassengerID())
                        || !request.getFlightDate().equals(ticketInfo.getFlightDate() != null ? DateUtil.toStringYYYY_MM_DD(ticketInfo.getFlightDate()) : "")
                ) {
                    iterator.remove();
                }
            }

            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                response.setSuccess(true);
                response.setSource("IETS");
                log.info(JSON.toJSONString(response));
                return response;
            } else {
                log.info("getTicketInfo from IETS: certNo or flightDate not match");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("getTicketInfo from IETS exception: {}, IETS result: {}" + e.getMessage(), JSON.toJSONString(apiResponse));
        }
        // 先查高频，无结果时，再查IBE

        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            if (null != request.getTicketNo() && !"".equals(request.getTicketNo())) {
                DETRTKTResult result = null;
                try {
                    result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "N", secondFactorCode, secondFactorValue);
                } catch (Exception e) {
                    log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ,next step retry", request.getTicketNo(), detr.getTxnTraceKey());
                    //重试一次
                    result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "N", secondFactorCode, secondFactorValue);
                }
                response.setTicketInfos(mapTicketInfoResult(result, detr2f, 0, null, request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), 0, response, secondFactorCode, secondFactorValue));
                log.info(" detr ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getPnrNo())) {
                //按PNR来提取票号信息
                Vector<DETRTKTResult> vector = detr.getTicketInfoByPNR(request.getPnrNo());
                if (!vector.isEmpty()) {
                    for (DETRTKTResult result : vector) {
                        response.getTicketInfos().addAll(mapTicketInfoResult(result, detr2f, 0, null, "true", "true", 0, response, secondFactorCode, secondFactorValue));
                    }
                }
                log.info(" detr  pnrNo: {},getTxnTraceKey: {} ", request.getPnrNo(), detr.getTxnTraceKey());
            } else {
                //根据证件类型、证件号获取旅客票面信息
                Vector<DETRDigestResult> vector = null;
                if (org.springframework.util.StringUtils.hasText(request.getDepCode())) {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), request.getDepCode().toLowerCase());
                    } catch (com.travelsky.ibe.exceptions.IBEException e) {
                        log.error("request.getPassengerNo() 1:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), request.getDepCode().toLowerCase());
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 2:" + e1.getMessage());
                        }
                    }
                } else {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), true, request.getAirlineCode(), null);
                    } catch (IBEException e) {
                        log.error("request.getPassengerNo() 3:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), true, request.getAirlineCode(), null);
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 4:" + e1.getMessage());
                        }
                    }
                }

                //没有命中，都查一次
                if (vector == null || vector.isEmpty()) {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), null);
                    } catch (IBEException e) {
                        log.error("request.getPassengerNo() 5:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), null);
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 6:" + e1.getMessage());
                        }
                    }
                }

                if (vector != null && !vector.isEmpty()) {
                    log.info("ticketlist.size():" + vector.size() + "begin");
                    CountDownLatch countDownLatch = new CountDownLatch(vector.size());
                    List<ListenableFuture<List<TicketInfo>>> listenableFutureList = new ArrayList<ListenableFuture<List<TicketInfo>>>();
                    for (DETRDigestResult result : vector) {
//						DETRTKTResult  detrResult = detr.getTicketInfoByTktNo(result.getTktno());
//						response.getTicketInfos().addAll(mapTicketInfoResult(detrResult, detr, 0, null,request.getNeedPnrBookTime(),  request.getNeedTicketChangeHis()));
                        TicketInfoSearchCallable callable = new TicketInfoSearchCallable(countDownLatch, detr2f, result.getTktno(), request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), response, secondFactorCode, secondFactorValue);
                        ListenableFuture<List<TicketInfo>> ticketInfoList = EXECUTOR_SERVICE.submit(callable);
                        listenableFutureList.add(ticketInfoList);
                    }
                    countDownLatch.await(15, TimeUnit.SECONDS);
                    for (ListenableFuture<List<TicketInfo>> listListenableFuture : listenableFutureList) {
                        List<TicketInfo> list = listListenableFuture.get();
                        if (list != null) {
                            response.getTicketInfos().addAll(list);
                        }
                    }
                    log.info("ticketlist.size():" + vector.size() + "end");
                }
                log.info(" detr passengerNo: {}  passengerType: {},getTxnTraceKey: {} ", request.getPassengerNo(), request.getPassengerType(), detr.getTxnTraceKey());
            }

            // 匹配过滤航班日期、证件号和票号
            Iterator<TicketInfo> iterator = response.getTicketInfos().iterator();
            while (iterator.hasNext()) {
                TicketInfo ticketInfo = iterator.next();
                if (!request.getPassengerNo().equals(ticketInfo.getPassengerID())
                        || !request.getFlightDate().equals(DateUtil.toStringYYYY_MM_DD(ticketInfo.getFlightDate()))
                ) {
                    iterator.remove();
                }
            }
            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                response.setSuccess(true);
            } else {
                response.setSuccess(false);
            }
            response.setSource("IBE");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }


    /**
     * 根据输入参数获取客票记录。V1版本，不校验过滤证件号、航班日期
     *
     * @param request
     * @return
     */
    @Override
    public DtoTicketInfoResponse getTicketInfoV1(DtoTicketInfoRequest request) {
        DtoTicketInfoResponse response = new DtoTicketInfoResponse();

        // 先查高频，无结果时，再查IBE
        ApiResponse apiResponse = new ApiResponse();
        try {
            if (StringUtils.isNotEmpty(request.getTicketNo())) {      // 根据票号查询
                apiResponse = esbService.queryIETSByTkne(request.getTicketNo());
            } else {  // 根据证件号证件类型查询
                apiResponse = esbService.queryIETSByIdinfoAndFltDate(request.getPassengerNo(), request.getFlightDate() != null ? DateUtil.toStringYMD(DateUtil.toDateYYYY_MM_DD(request.getFlightDate())) : "");
            }
            response.setTicketInfos(mapTicketInfoResultByHSD(apiResponse));
            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                log.info("getTicketInfoV1 from IETS: esb parse result is null. IETS result: {}", JSON.toJSONString(apiResponse));
            }

            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                response.setSuccess(true);
                response.setSource("IETS");
                log.info(JSON.toJSONString(response));
                return response;
            } else {
                log.info("getTicketInfoV1 from IETS: certNo or flightDate not match");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("getTicketInfoV1 from IETS exception: {}, IETS result: {}" + e.getMessage(), JSON.toJSONString(apiResponse));
        }
        // 先查高频，无结果时，再查IBE

        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            if (null != request.getTicketNo() && !"".equals(request.getTicketNo())) {
                DETRTKTResult result = null;
                try {
                    result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "N", secondFactorCode, secondFactorValue);
                } catch (Exception e) {
                    log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ,next step retry", request.getTicketNo(), detr.getTxnTraceKey());
                    //重试一次
                    result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "N", secondFactorCode, secondFactorValue);
                }
                response.setTicketInfos(mapTicketInfoResult(result, detr2f, 0, null, request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), 0, response, secondFactorCode, secondFactorValue));
                log.info(" detr ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            } else if (org.apache.commons.lang3.StringUtils.isNotBlank(request.getPnrNo())) {
                //按PNR来提取票号信息
                Vector<DETRTKTResult> vector = detr.getTicketInfoByPNR(request.getPnrNo());
                if (!vector.isEmpty()) {
                    for (DETRTKTResult result : vector) {
                        response.getTicketInfos().addAll(mapTicketInfoResult(result, detr2f, 0, null, request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), 0, response, secondFactorCode, secondFactorValue));
                    }
                }
                log.info(" detr  pnrNo: {},getTxnTraceKey: {} ", request.getPnrNo(), detr.getTxnTraceKey());
            } else {
                //根据证件类型、证件号获取旅客票面信息
                Vector<DETRDigestResult> vector = null;
                if (org.springframework.util.StringUtils.hasText(request.getDepCode())) {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), request.getDepCode().toLowerCase());
                    } catch (com.travelsky.ibe.exceptions.IBEException e) {
                        log.error("request.getPassengerNo() 1:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), request.getDepCode().toLowerCase());
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 2:" + e1.getMessage());
                        }
                    }
                } else {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), true, request.getAirlineCode(), null);
                    } catch (IBEException e) {
                        log.error("request.getPassengerNo() 3:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), true, request.getAirlineCode(), null);
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 4:" + e1.getMessage());
                        }
                    }
                }

                //没有命中，都查一次
                if (vector == null || vector.isEmpty()) {
                    try {
                        vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), null);
                    } catch (IBEException e) {
                        log.error("request.getPassengerNo() 5:" + e.getMessage());
                        try {
                            vector = detr.getTicketDigestsByCert(request.getPassengerType(), request.getPassengerNo(), false, request.getAirlineCode(), null);
                        } catch (IBEException e1) {
                            log.error("request.getPassengerNo() 6:" + e1.getMessage());
                        }
                    }
                }

                if (vector != null && !vector.isEmpty()) {
                    log.info("ticketlist.size():" + vector.size() + "begin");
                    CountDownLatch countDownLatch = new CountDownLatch(vector.size());
                    List<ListenableFuture<List<TicketInfo>>> listenableFutureList = new ArrayList<ListenableFuture<List<TicketInfo>>>();
                    for (DETRDigestResult result : vector) {
//						DETRTKTResult  detrResult = detr.getTicketInfoByTktNo(result.getTktno());
//						response.getTicketInfos().addAll(mapTicketInfoResult(detrResult, detr, 0, null,request.getNeedPnrBookTime(),  request.getNeedTicketChangeHis()));
                        TicketInfoSearchCallable callable = new TicketInfoSearchCallable(countDownLatch, detr2f, result.getTktno(), request.getNeedPnrBookTime(), request.getNeedTicketChangeHis(), response, secondFactorCode, secondFactorValue);
                        ListenableFuture<List<TicketInfo>> ticketInfoList = EXECUTOR_SERVICE.submit(callable);
                        listenableFutureList.add(ticketInfoList);
                    }
                    countDownLatch.await(15, TimeUnit.SECONDS);
                    for (ListenableFuture<List<TicketInfo>> listListenableFuture : listenableFutureList) {
                        List<TicketInfo> list = listListenableFuture.get();
                        if (list != null) {
                            response.getTicketInfos().addAll(list);
                        }
                    }
                    log.info("ticketlist.size():" + vector.size() + "end");
                }
                log.info(" detr passengerNo: {}  passengerType: {},getTxnTraceKey: {} ", request.getPassengerNo(), request.getPassengerType(), detr.getTxnTraceKey());
            }

            if (response.getTicketInfos() != null && response.getTicketInfos().size() > 0) {
                response.setSuccess(true);
            } else {
                response.setSuccess(false);
            }
            response.setSource("IBE");
        } catch (Exception e) {
            e.printStackTrace();
            log.error(" detr error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }


    /**
     * 根据输入参数获取客票记录(高频数据源)
     *
     * @param request
     * @return
     */
    @Override
    public DtoTicketInfoResponse getTicketInfoByHSD(DtoTicketInfoRequest request) {
        DtoTicketInfoResponse response = new DtoTicketInfoResponse();
        ApiResponse apiResponse = new ApiResponse();
        if (StringUtils.isNotEmpty(request.getTicketNo())) {      // 根据票号查询
            apiResponse = esbService.queryIETSByTkne(request.getTicketNo());
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {  // 根据编码查询
            apiResponse = esbService.queryIETSByIcsnoAndFltDate(request.getPnrNo(), null);
        } else {  // 根据证件号证件类型查询
            apiResponse = esbService.queryIETSByIdinfoAndFltDate(request.getPassengerType() + request.getPassengerNo(), null);
        }
        response.setTicketInfos(mapTicketInfoResultByHSD(apiResponse));
        return response;
    }

    /**
     * 根据航班号、航班日期、旅客姓名提取客票信息(高频数据源)
     *
     * @param request
     * @return
     */
    @Override
    public DtoTicketInfoResponse getTicketInfoByFlightInfoAndName(DtoTicketInfoByFlightInfoAndNameRequest request) {
        DtoTicketInfoResponse response = new DtoTicketInfoResponse();
        ApiResponse apiResponse = esbService.queryIETSByFlightNo(request.getFlightDate()
                , request.getFlightNo(),
                "",
                "",
                ""
        );
        response.setTicketInfos(mapTicketInfoResultByHSD(apiResponse));
        Iterator<TicketInfo> iterator = response.getTicketInfos().iterator();
        while (iterator.hasNext()) {
            TicketInfo ticketInfo = iterator.next();
            if (org.springframework.util.StringUtils.hasText(request.getPassengerName()) && !request.getPassengerName().equals(ticketInfo.getPassengerName())) {
                iterator.remove();
            }
        }
        return response;
    }

    /**
     * 数据格式转换
     *
     * @return
     */
    private List<TicketInfo> mapTicketInfoResultByHSD(ApiResponse apiResponse) {
        List<TicketInfo> ticketInfoList = new ArrayList<>();

        JSONObject result = JSONObject.parseObject((String) apiResponse.getData().get(0).get("result"));

        JSONArray dataList = result.getJSONArray("data");

        for (Object data : dataList) {
            try {
                JSONObject dataObj = (JSONObject) data;
                JSONObject extObj = dataObj.getJSONObject("EXT");
                JSONObject datObj = dataObj.getJSONObject("DAT");
                JSONObject ticketImageObj = datObj.getJSONObject("TicketImage");
                JSONObject tktTypeObj = ticketImageObj.getJSONObject("TktType");
                JSONObject travelerObj = ticketImageObj.getJSONObject("Traveler");
                JSONObject passportInfoObj = ticketImageObj.getJSONObject("PassportInfo");
                JSONObject idInfoObj = ticketImageObj.getJSONObject("IDInfo");
                JSONObject itineraryObj = ticketImageObj.getJSONObject("Itinerary");
                JSONObject ticketIdentificationObj = ticketImageObj.getJSONObject("TicketIdentification");
                JSONObject fareGroupObj = ticketImageObj.getJSONObject("FareGroup");
                JSONObject travelerGroupObj = fareGroupObj == null ? null : fareGroupObj.getJSONObject("TravelerGroup");
                JSONObject fareRulesObj = travelerGroupObj == null ? null : travelerGroupObj.getJSONObject("FareRules");
                JSONObject priceObj = travelerGroupObj == null ? null : travelerGroupObj.getJSONObject("Price");
                JSONObject taxesObj = priceObj == null ? null : priceObj.getJSONObject("Taxes");
                JSONArray taxList = taxesObj == null ? null : taxesObj.getJSONArray("Tax");

                JSONArray ticketCouponList = itineraryObj == null ? new JSONArray() : itineraryObj.getJSONArray("TicketCoupon");
                JSONArray segList = extObj == null ? new JSONArray() : extObj.getJSONArray("Segs");
                for (Object seg : segList) {
                    JSONObject segObj = (JSONObject) seg;
                    TicketInfo ticketInfo = new TicketInfo();

                    ticketInfo.setAirlineCode(segObj.getString("oc"));
                    ticketInfo.setFlightNo(segObj.getString("rteeFlightNo"));
                    ticketInfo.setIssCode(extObj.getString("tkne").substring(0, 3));
                    ticketInfo.setTicketNo(extObj.getString("tkne").substring(3));
                    ticketInfo.setTicketType(tktTypeObj == null ? "" : tktTypeObj.getString("@InterIndicator"));  // 国内国际票标识
                    ticketInfo.setDepCode(segObj.getString("orig"));
                    ticketInfo.setArrCode(segObj.getString("dest"));
                    ticketInfo.setCabin(segObj.getString("ClassOfService"));
                    ticketInfo.setProductCode(segObj.getString("FareBasisCode"));
                    ticketInfo.setPnr(segObj.getString("icsNo"));
                    ticketInfo.setPnrStatus("");
                    ticketInfo.setSegmentIndex(Integer.parseInt(segObj.getString("CouponNumber")) - 1);
                    ticketInfo.setTourCode(fareRulesObj == null ? "" : fareRulesObj.getString("TourCode"));
                    ticketInfo.setOffice(ticketIdentificationObj.getString("TicketOfficeID"));
                    ticketInfo.setIssuedBy(segObj.getString("oc"));  // 承运人
                    ticketInfo.setPassengerType(travelerObj.getString("@Type"));
                    ticketInfo.setPassengerName(travelerObj.getJSONObject("TravelerName").getString("NativeGivenName"));
                    if (StringUtils.isEmpty(ticketInfo.getPassengerName())) {
                        ticketInfo.setPassengerName(travelerObj.getJSONObject("TravelerName").getString("Surname")
                                + "/"
                                + travelerObj.getJSONObject("TravelerName").getString("GivenName")
                        );
                    }
                    String idNo = "";
                    if (passportInfoObj != null && StringUtils.isNotEmpty(passportInfoObj.getString("PassportNum"))) {
                        idNo = passportInfoObj.getString("PassportNum");
                    } else if (idInfoObj != null && StringUtils.isNotEmpty(idInfoObj.getString("IDNumber"))) {
                        idNo = idInfoObj.getString("IDNumber");
                    }
                    ticketInfo.setPassengerID(idNo);
                    /**
                     * CouponStatus对应的航段票面状态如下：
                     *
                     *
                     * 序号	CouponStatus	DETR票面中的航段状态	 解释说明
                     * 1	O	OPEN FOR USE	客票有效未使用
                     * 2	F	USED/FLOWN	已使用
                     * 3	C	CHECKED IN	已办理值机手续
                     * 4	N	CPN NOTE	客票信息不明确，客票控制权在对方
                     *
                     * 5	L	LIFT/BOARDED	已登机
                     * 6	R	REFUNDED	已退票
                     * 7	S	SUSPENDED	系统处理中，或人为挂起禁止使用该票
                     * 8	G	FIM EXCH	已签转航空公司，机场在发生不正常航班时，使用FIM单（Flight Interrupt Manifest 中断飞行舱单）进行航空公司间签转手续
                     *
                     * 9	E	EXCHANGED	 电子客票已换开为其他客票
                     * 10	A	AIRP CNTL/YY	 表明控制权不在本航司，需要通过订座系统向对方（YY）申请控制权
                     * 11	V	VOID	 已作废
                     * 12	X	PRINT/EXCH	电子客票PET打印换开纸票
                     * 13	P	PRINTED	？？可认为是已使用
                     */
                    String ticketStatus = segObj.getString("CouponStatus");
                    if (ticketStatus.equals("O")) {
                        ticketInfo.setTicketStatus("OPEN FOR USE");
                    } else if (ticketStatus.equals("F")) {
                        ticketInfo.setTicketStatus("USED/FLOWN");
                    } else if (ticketStatus.equals("C")) {
                        ticketInfo.setTicketStatus("CHECKED IN");
                    } else if (ticketStatus.equals("N")) {
                        ticketInfo.setTicketStatus("CPN NOTE");
                    } else if (ticketStatus.equals("L")) {
                        ticketInfo.setTicketStatus("LIFT/BOARDED");
                    } else if (ticketStatus.equals("R")) {
                        ticketInfo.setTicketStatus("REFUNDED");
                    } else if (ticketStatus.equals("S")) {
                        ticketInfo.setTicketStatus("SUSPENDED");
                    } else if (ticketStatus.equals("G")) {
                        ticketInfo.setTicketStatus("FIM EXCH");
                    } else if (ticketStatus.equals("E")) {
                        ticketInfo.setTicketStatus("EXCHANGED");
                    } else if (ticketStatus.equals("A")) {
                        ticketInfo.setTicketStatus("AIRP CNTL/YY");
                    } else if (ticketStatus.equals("V")) {
                        ticketInfo.setTicketStatus("VOID");
                    } else if (ticketStatus.equals("X")) {
                        ticketInfo.setTicketStatus("PRINT/EXCH");
                    } else if (ticketStatus.equals("P")) {
                        ticketInfo.setTicketStatus("PRINTED");
                    }


                    String fareCalculation = fareRulesObj.getString("FareCalculation");
                    // 处理票面实售机建燃油
                    if (segList.size() > 1) {
                        if (!ticketInfo.getTicketType().equals("I")) { //国际票往返程是价格总和，不支持分开计算
                            //回程的价格  19SEP18PEK JD ERL457.00JD PEK330.00CNY787.00END

                            if ((ticketInfo.getSegmentIndex()) != (segList.size() - 1)) { // M 19JUL23PKX JD SYX1290.00JD PKX1770.00CNY3060.00END
                                // 多段的情况，目的地取下一段的出发地
                                String depCode = ((JSONObject) segList.get(ticketInfo.getSegmentIndex() + 1)).getString("dest");
                                int segCodeIndex = fareCalculation.lastIndexOf(depCode);
                                String backFare = fareCalculation.substring(segCodeIndex + depCode.length()).split("CNY")[0];
                                ticketInfo.setMarketFare(priceObj == null ? null : priceObj.getBigDecimal("@PsgrMask").subtract(new BigDecimal(backFare)));//票面价
                                ticketInfo.setNetFare(ticketInfo.getMarketFare());//实付价
                            } else {
                                //最后一段就取
                                int segCodeIndex = fareCalculation.lastIndexOf(ticketInfo.getArrCode());
                                String backFare = fareCalculation.substring(segCodeIndex + ticketInfo.getDepCode().length()).split("CNY")[0];
                                ticketInfo.setMarketFare(new BigDecimal(backFare));//票面价
                                ticketInfo.setNetFare(new BigDecimal(backFare));//实付价
                            }
                            if (taxList != null) {
                                for (Object tax : taxList) {
                                    JSONObject taxObj = (JSONObject) tax;
                                    if (taxObj.getString("Nature").equals("CN")) {
                                        ticketInfo.setAirportTax(taxObj.getBigDecimal("@Amount").divide(new BigDecimal(segList.size()))); //机建费
                                    } else if (taxObj.getString("Nature").equals("YQ")) {
                                        ticketInfo.setFuelTax(taxObj.getBigDecimal("@Amount").divide(new BigDecimal(segList.size())));//燃油费
                                    }
                                }
                            }

                        }
                    } else {
                        try {
                            ticketInfo.setMarketFare(priceObj == null ? null : priceObj.getBigDecimal("@BulkMask"));//票面价
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        try {
                            ticketInfo.setNetFare(priceObj == null ? null : priceObj.getBigDecimal("@PsgrMask"));//实付价
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        if (taxList != null) {
                            for (Object tax : taxList) {
                                JSONObject taxObj = (JSONObject) tax;
                                if (taxObj.getString("Nature").equals("CN")) {
                                    ticketInfo.setAirportTax(taxObj.getBigDecimal("@Amount")); //机建费
                                } else if (taxObj.getString("Nature").equals("YQ")) {
                                    ticketInfo.setFuelTax(taxObj.getBigDecimal("@Amount"));    //燃油费
                                }
                            }
                        }
                    }

                    try {
                        SimpleDateFormat fullTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                        if (StringUtils.isNotEmpty(segObj.getString("fltDate")) && StringUtils.isNotEmpty(segObj.getString("dptm"))) {
                            ticketInfo.setDepTime(timeFormat.parse(segObj.getString("fltDate") + " " + segObj.getString("dptm")));
                        }
                        if (StringUtils.isNotEmpty(segObj.getString("arrivalDate")) && StringUtils.isNotEmpty(segObj.getString("attm"))) {
                            ticketInfo.setArrTime(timeFormat.parse(segObj.getString("arrivalDate") + " " + segObj.getString("attm")));
                        }
                        if (StringUtils.isNotEmpty(segObj.getString("fltDate"))) {
                            ticketInfo.setFlightDate(dateFormat.parse(segObj.getString("fltDate")));
                        }
                        ticketInfo.setIssueDate(timeFormat.parse(ticketIdentificationObj.getString("TktIssueDate") + " " + ticketIdentificationObj.getString("TktIssueTime")));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }

                    for (Object ticketCoupon : ticketCouponList) {
                        JSONObject ticketCouponObj = (JSONObject) ticketCoupon;
                        if (segObj.getString("CouponNumber").equals(ticketCouponObj.getString("@CouponNumber"))) {
                            JSONObject openObj = ticketCouponObj.getJSONObject("Open");
                            JSONObject openDepartureObj = openObj == null ? null : openObj.getJSONObject("OpenDeparture");
                            JSONObject openArrivalObj = openObj == null ? null : openObj.getJSONObject("OpenArrival");
                            String depTerm = openDepartureObj == null ? "" : openDepartureObj.getString("Terminal");
                            String arrTerm = openArrivalObj == null ? "" : openArrivalObj.getString("Terminal");
                            String boardingNumber = ticketCouponObj.getString("@BoardingNumber");
                            ticketInfo.setBoardingNumber(boardingNumber); // 值机序号
                            ticketInfo.setDepTerm(depTerm); // 航站楼
                            ticketInfo.setArrTerm(arrTerm); // 航站楼
                        }
                    }

                    ticketInfoList.add(ticketInfo);
                }
            } catch (Exception e) {

            }
        }

        return ticketInfoList;
    }


    /**
     * 数据格式转换
     *
     * @return
     */
    private FlightPassengerInfoResponse mapFlightPassengerResultByHSD(FlightPassengerInfoRequest request, ApiResponse hsdResponse, EsbApiResponse<StandardFocFlightInfo> focResponse) {
        FlightPassengerInfoResponse flightPassengerInfoResponse = new FlightPassengerInfoResponse();
        FlightPassengerInfoResponse.FlightInfo flightInfo = new FlightPassengerInfoResponse.FlightInfo();
        List<FlightPassengerInfoResponse.FlightPassengerInfo> flightPassengerInfoList = new ArrayList<>();

        for (StandardFocFlightInfo datum : focResponse.getData()) {
            flightInfo.setFlightNo(datum.getFlightNo());
            flightInfo.setDepCode(datum.getDepStn());
            flightInfo.setArrCode(datum.getArrStn());
            flightInfo.setDepTime(DateUtil.toStringYMDHM(datum.getEtdChn()));
            flightInfo.setArrTime(DateUtil.toStringYMDHM(datum.getEtaChn()));
            flightInfo.setPlaneType(datum.getAcType());
            flightInfo.setPlaneNo(datum.getAcLongNo());
            flightInfo.setFlightStatus(datum.getStatus());
        }

        JSONObject result = JSONObject.parseObject((String) hsdResponse.getData().get(0).get("result"));
        JSONArray dataList = result.getJSONArray("data");

        for (Object data : dataList) {
            try {
                JSONObject dataObj = (JSONObject) data;
                JSONObject extObj = dataObj.getJSONObject("EXT");
                JSONObject datObj = dataObj.getJSONObject("DAT");
                JSONObject ticketImageObj = datObj.getJSONObject("TicketImage");
                JSONObject travelerObj = ticketImageObj.getJSONObject("Traveler");
                JSONArray frequentTravelerGroupObj = ticketImageObj.getJSONArray("FrequentTravelerGroup");
                JSONObject passportInfoObj = ticketImageObj.getJSONObject("PassportInfo");
                JSONObject idInfoObj = ticketImageObj.getJSONObject("IDInfo");
                JSONObject ticketIdentificationObj = ticketImageObj.getJSONObject("TicketIdentification");
                JSONObject fareGroupObj = ticketImageObj.getJSONObject("FareGroup");
                JSONObject travelerGroupObj = fareGroupObj == null ? null : fareGroupObj.getJSONObject("TravelerGroup");
                JSONObject priceObj = travelerGroupObj == null ? null : travelerGroupObj.getJSONObject("Price");
                JSONObject currencyCodeObj = fareGroupObj == null ? null : fareGroupObj.getJSONObject("CurrencyCode");

                JSONArray segList = extObj == null ? new JSONArray() : extObj.getJSONArray("Segs");

                FlightPassengerInfoResponse.FlightPassengerInfo flightPassengerInfo = new FlightPassengerInfoResponse.FlightPassengerInfo();

                flightPassengerInfo.setPassengerName(travelerObj.getJSONObject("TravelerName").getString("NativeGivenName"));
                if (StringUtils.isEmpty(flightPassengerInfo.getPassengerName())) {
                    flightPassengerInfo.setPassengerName(travelerObj.getJSONObject("TravelerName").getString("Surname")
                            + "/"
                            + travelerObj.getJSONObject("TravelerName").getString("GivenName")
                    );
                }
                // 性别 F,M   TODO 无值
                flightPassengerInfo.setGender(travelerObj.getJSONObject("TravelerName").getString("Gender"));
                // 旅客类型  可为：ADT、INF、CHD等
                flightPassengerInfo.setPassengerType(travelerObj.getString("@Type"));

                flightPassengerInfo.setIssueTime(ticketIdentificationObj.getString("TktIssueDate") + " " + ticketIdentificationObj.getString("TktIssueTime"));

                flightPassengerInfo.setTicketNo(extObj.getString("tkne"));

                String idNo = "";
                if (passportInfoObj != null && StringUtils.isNotEmpty(passportInfoObj.getString("PassportNum"))) {
                    idNo = passportInfoObj.getString("PassportNum");
                    flightPassengerInfo.setCertType("Passport");
                } else if (idInfoObj != null && StringUtils.isNotEmpty(idInfoObj.getString("IDNumber"))) {
                    idNo = idInfoObj.getString("IDNumber");
                    flightPassengerInfo.setCertType(idInfoObj.getString("IDType"));
                }
                flightPassengerInfo.setCertNo(idNo);

                flightPassengerInfo.setPrice(priceObj.getString("@Total") + currencyCodeObj.getString("Value"));

                if (frequentTravelerGroupObj != null) {
                    for (Object o : frequentTravelerGroupObj) {
                        JSONObject ffObj = (JSONObject) o;
                        flightPassengerInfo.setFfCardNo(ffObj.getString("FFNumber"));
                    }
                }

                flightPassengerInfo.setStatus(ticketImageObj.getString("@Status"));

                for (Object seg : segList) {
                    JSONObject segObj = (JSONObject) seg;

                    if (request.getFlightNo().equals(segObj.getString("rteeFlightNo"))) {
                        flightPassengerInfo.setPnr(segObj.getString("icsNo"));
                        flightPassengerInfo.setCabin(segObj.getString("ClassOfService"));
                        flightPassengerInfo.setSubCabin(segObj.getString("fClass"));
                        flightPassengerInfo.setRemark(segObj.getString("orig") + "-" + segObj.getString("dest"));
                        flightPassengerInfo.setFlightInfo(flightInfo);
                        flightPassengerInfoList.add(flightPassengerInfo);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        int passengerNum = (int) flightPassengerInfoList.stream()
                .filter(passenger -> "Ticketed".equals(passenger.getStatus()))
                .count();

        for (FlightPassengerInfoResponse.FlightPassengerInfo flightPassengerInfo : flightPassengerInfoList) {
            flightPassengerInfo.getFlightInfo().setPassengerNum(passengerNum);
        }
        flightPassengerInfoResponse.setFlightPassengerInfoList(flightPassengerInfoList);
        return flightPassengerInfoResponse;
    }


    /**
     * @param result
     * @param detr
     * @param index
     * @param segInfo
     * @param needBookTime
     * @param needTicketChangeHis 是否需要提取客票历史
     * @param cycleNum            历史客票最多递归次数（改期次数太多导致耗时太久无法返回数据）
     * @param response            为了即要返回数据，又要提示异常（或者做前台限制）
     * @return
     * @throws Exception
     */
    public List<TicketInfo> mapTicketInfoResult(DETRTKTResult result, DETR2F detr2f, Integer index, String segInfo, String needBookTime, String needTicketChangeHis, int cycleNum, DtoTicketInfoResponse response, String secondFactorCode, String secondFactorValue) throws Exception {
        long ticketStart = new Date().getTime();
        log.info("ticketNo:" + result.getTicketNo() + " mapTicketInfoResult start date:" + ticketStart);
        List<TicketInfo> ticket = new ArrayList<>();
        if (cycleNum++ > CYCLE_MAX_NUM) {
            response.setErrorCode(CodeIbe.TICKET_HIS_LIMIT_CYCLE_NUM.getCode() + "");
            response.setErrorInfo(CodeIbe.TICKET_HIS_LIMIT_CYCLE_NUM.getName());
            return ticket;
        }
        Date issueDate = result.getIssueDate();
        String airline = "";
        //查询历史客票信息 出票时间为空的时候需要提取客票历史记录
        DETRHistoryResult historyResult = null;
        if (null == result.getIssueDate() || result.getSegment(0) == null || result.getSegment(0).getPnrNo() == null) {
            //获取出票时间
            try {
                historyResult = detr2f.getTicketHistoryByTktNo2F(result.getTicketNo(), "N", secondFactorCode, secondFactorValue);
                log.info("historyResult passengerName:" + historyResult.getPassengerName()
                        + ",passengerType:" + historyResult.getPassengerType()
                        + ",ticketNo:" + historyResult.getTicketNo()
                        + ",issueDate:" + historyResult.getIssueDate());

                issueDate = historyResult.getInfoItem(0).getOperTime();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        for (int i = 0; i < result.getSegmentCount(); ++i) {
            DETRTKTSegment detrtktSegment = result.getSegment(i);
            airline = detrtktSegment.getAirline();
            if (!"JD".equals(airline)) {
                airline = "JD";
            }
            //非正常状态不进行查询
//			if (index == 0 && !detrtktSegment.getTicketStatus().equals("OPEN FOR USE")) {
//				continue;
//			}
            //票面历史信息要比对航段信息去提取客票信息 只会返回同航段的记录信息
            if (index == 1 && !(detrtktSegment.getDepAirportCode() + detrtktSegment.getArrAirportCode()).equals(segInfo)) {
                continue;
            }
            TicketInfo seg = getTicketInfo(result, issueDate, i, detrtktSegment, historyResult);
            seg.setSigningInfo(result.getSigningInfo());
            seg.setSegmentIndex(i);
            //票里优惠券信息
            String conponResult = REMatchUtil.searchkey("-\\d+", result.getPayMethod());
            if (org.apache.commons.lang.StringUtils.isNotEmpty(conponResult)) {
                log.info("findTicketInfo:" + seg.getTicketNo() + ",conpon" + conponResult);
                seg.setConponMsg(conponResult.substring(1));
            }
            //OI换开的
            String oldTicket = result.getExchangeInfo();
            //历史记录信息
            String[] arr = null;
            if (null != result.getSigningInfo()) {
                arr = result.getSigningInfo().split(",");
            }
            //改升的
            if (null != oldTicket && "".equals(oldTicket)) {
                if (null != arr && arr.length > 1) {
                    oldTicket = arr[1];
                }
            }
            if (StringUtils.isNotEmpty(needTicketChangeHis)) {
                Boolean checkResult = ticketCheck(oldTicket);
                log.info("oldTicket:" + oldTicket + ",checkres:" + checkResult);
                //提取到的客票号符合客票号的规则去提取历史信息
                if (null != oldTicket && !"".equals(oldTicket) && checkResult) {
                    //历史信息
                    try {
                        DETRTKTResult hisresult = detr2f.getTicketInfoByTktNo2F(oldTicket, false, "", "N", secondFactorCode, secondFactorValue);
                        Long startTime = new Date().getTime();
                        log.info("oldTicket:" + oldTicket + " find history start date:" + new Date().getTime());
                        seg.setHistoryTicketInfo(mapTicketInfoResult(hisresult, detr2f, 1, seg.getDepCode() + seg.getArrCode(), "1"/*改期需要查历史编码*/, needTicketChangeHis, cycleNum, response, secondFactorCode, secondFactorValue));
                        log.info("oldTicket:" + oldTicket + " find history end date:" + (new Date().getTime() - startTime));
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw e;
                    }
                }
            }
            if (airline.equals("") && airline.length() > 2) {
                airline = "JD";
            }

            ticket.add(seg);
        }
        //
        com.travelsky.ibe.client.pnr.RT rt = IbeCmd.getIbeClient(airline, com.travelsky.ibe.client.pnr.RT.class);
        com.travelsky.ibe.client.pnr.RTResult rtResult = null;//票号里的编码相同，rt一次即可
        boolean isCompletelyPnr = false;//提取的历史 TODO 需要重构
        Date bookTime = null;
        String passengerId = "";
        String birthDay = "";
        Map<String, Date> arriveTimeMap = new HashMap<>();
        Map<String, String> pnrStatusMap = new HashMap<>();
        if (ticket.size() > 0) {
            for (TicketInfo ticketInfo : ticket) { //放入承运人
                ticketInfo.setIssuedBy(issuedToAir(result.getIssueAirline()));
            }
            TicketInfo seg = ticket.get(0);
            log.info("1:" + result.getTicketNo() + ",pnrno:" + seg.getPnr());
            if (null != seg.getPnr() && !"".equals(seg.getPnr())) {
                //提取pnr状态信息
                try {
                    try {
                        rtResult = rt.retrieve(seg.getPnr());
                    } catch (Exception e) {
                        log.info(" rt pnr: {} , getTxnTraceKey: {}, message: {} ", seg.getPnr() + result.getTicketNo(), rt.getTxnTraceKey(), e.getMessage());
                        if (e.getMessage() != null && e.getMessage().indexOf("SocketException") > -1) {
                            //网络异常，重试一次
                            try {
                                rtResult = rt.retrieve(seg.getPnr());
                            } catch (Exception e1) {
                                log.info(" rt pnr: {} , getTxnTraceKey: {}, message: {} ", seg.getPnr() + result.getTicketNo(), rt.getTxnTraceKey(), e1.getMessage());
                                if (e1.getMessage() != null && e1.getMessage().indexOf("SocketException") > -1) {
                                    //网络异常，重试第二次
                                    try {
                                        rtResult = rt.retrieve(seg.getPnr());
                                    } catch (Exception e2) {
                                        log.info(" rt pnr: {} , getTxnTraceKey: {}, message: {} ", seg.getPnr() + result.getTicketNo(), rt.getTxnTraceKey(), e2.getMessage());
                                    }
                                }
                            }
                        }
                    }
                    if (rtResult == null) {
                        //pnr编号无相应信息或已经删除,只能通过历史记录
                        rtResult = rt.retrieveCompletely(seg.getPnr());
                        //提取预定时间
                        if (org.springframework.util.StringUtils.hasText(needBookTime)) {
                            parsePnrHis(rtResult, seg);
                            bookTime = seg.getBookTime();
                        }
                        isCompletelyPnr = true;
                    }
                    if (org.springframework.util.StringUtils.hasText(needBookTime) && !isCompletelyPnr) {
                        com.travelsky.ibe.client.pnr.RTResult rtResult1 = rt.retrieveCompletely(seg.getPnr());
                        //获取PNR预定时间
                        parsePnrHis(rtResult1, seg);
                        bookTime = seg.getBookTime();
                        // isCompletelyPnr = true;
                    }

                    if (!isCompletelyPnr) { //编码还存在的解析逻辑，并且能读取都内容

                        int segIndex = 0;
                        for (Object obj : rtResult.getAirSegs()) {
                            if (segIndex >= ticket.size()) {
                                //往返程退来第二段，ticket只有一个，rtResult.getAirSegs()是2个
                                break;
                            }
                            com.travelsky.ibe.client.pnr.PNRAirSeg airSeg = (com.travelsky.ibe.client.pnr.PNRAirSeg) obj;
                            log.info("检查出的pnr是" + airline + "的航班" + airSeg.getAirNo());
                            TicketInfo segTem = ticket.get(segIndex++);
                            if (!segTem.getDepCode().equals(airSeg.getOrgCity())) {
                                //往返程退了第一段时，ticket只有一个，rtResult.getAirSegs()是2个
                                segIndex--;
                                continue;
                            }
                            segTem.setFlightNo(airSeg.getAirNo());
                            segTem.setArrTerm(airSeg.getArrivalTerm());
                            segTem.setDepTerm(airSeg.getDepartureTerm());
                            if (segTem.getDepTime() == null) segTem.setDepTime(airSeg.getDepartureTime());
                            if (segTem.getFlightDate() == null) segTem.setFlightDate(airSeg.getDepartureTime());
                            if (airSeg.getAirNo().substring(0, 2).equals(airline)) {
                                pnrStatusMap.put(airSeg.getOrgCity() + airSeg.getDesCity(), airSeg.getActionCode());
                            }
                        }

                        //读取旅客证件号
                        int paxIndex = 0;
                        for (int j = 0; j < rtResult.getPassengerNumber(); j++) {
                            PNRPassenger p = rtResult.getPassengerAt(j);
                            if (p.getName() != null && p.getName().equals(seg.getPassengerName())) {
                                paxIndex = p.getIndex();
                                break;
                            }
                        }
                        for (int j = 0; j < rtResult.getSSRsCount(); j++) {
                            PNRSSR ssr = rtResult.getSSRAt(j);
                            if ("FOID".equals(ssr.getSSRType()) && ("P" + paxIndex).equals(ssr.getPsgrid())) {
                                passengerId = ((com.travelsky.ibe.client.pnr.PNRSSR_FOID) ssr).getIdNo();
                                break;
                            }
                        }

                        for (int j = 0; j < rtResult.getSSRsCount(); j++) {
                            PNRSSR ssr = rtResult.getSSRAt(j);
                            if ("DOCS".equals(ssr.getSSRType()) && ("P" + paxIndex).equals(ssr.getPsgrid())) {
                                birthDay = ((com.travelsky.ibe.client.pnr.PNRSSR_DOCS) ssr).getBirth();
                                break;
                            }
                        }

                        if (!org.springframework.util.StringUtils.hasText(passengerId)) {
                            //找护照号
                            for (int j = 0; j < rtResult.getSSRsCount(); j++) {
                                PNRSSR ssr = rtResult.getSSRAt(j);
                                if ("DOCS".equals(ssr.getSSRType()) && ("P" + paxIndex).equals(ssr.getPsgrid())) {
                                    passengerId = ((com.travelsky.ibe.client.pnr.PNRSSR_DOCS) ssr).getDoc_No();
                                    break;
                                }
                            }
                        }

                        //读取到达时间
                        for (int j = 0; j < rtResult.getAirSegsCount(); j++) {
                            PNRAirSeg pnrSeg = rtResult.getAirSegAt(j);
                            arriveTimeMap.put(pnrSeg.getOrgCity() + pnrSeg.getDesCity(), pnrSeg.getArrivalTime());
                        }
                        com.travelsky.ibe.client.pnr.PNRResp pnrResp = rtResult.getResp();
                        for (TicketInfo ticketInfo : ticket) {
                            ticketInfo.setBookTime(bookTime);
                            ticketInfo.setPassengerID(passengerId);
                            ticketInfo.setBirthDay(birthDay);
                            ticketInfo.setArrTime(arriveTimeMap.get(ticketInfo.getDepCode() + ticketInfo.getArrCode()));
                            ticketInfo.setOffice(pnrResp.getOfficecode());
                            ticketInfo.setPnrStatus(pnrStatusMap.get(ticketInfo.getDepCode() + ticketInfo.getArrCode()));
                            //婴儿备注一下成人客票
                            if (ticketInfo.getPassengerType() != null && ticketInfo.getPassengerType().equals(PassengerType.PASSENGER_3.getAlias())) {
                                Vector<PNRTktNo> ticketNos = rtResult.getTktnos();
                                String psgrID = "";
                                for (PNRTktNo o : ticketNos) {
                                    String tktNo = o.getTktNo().split("-")[1];
                                    if (ticketInfo.getTicketNo().equals(tktNo) || (ticketInfo.getIssCode() + "-" + ticketInfo.getTicketNo()).equals(tktNo)) {
                                        //取一下儿童所在的组
                                        psgrID = o.getPsgrID();
                                    }
                                }
                                for (PNRTktNo o : ticketNos) {
                                    String tktNo = o.getTktNo().split("-")[1];
                                    if (!ticketInfo.getTicketNo().equals(tktNo) && psgrID.equals(o.getPsgrid())) {
                                        //取成人客票
                                        ticketInfo.setAccompaniedAuditTicketNo(o.getTktNo().substring(4));//不要前三位结算码和－
                                    }
                                }
                            }
                        }
                    } else {
                        com.travelsky.ibe.client.pnr.PNRResp pnrResp = rtResult.getResp();
                        //如果是历史编码
                        for (TicketInfo ticketInfo : ticket) {
                            ticketInfo.setBookTime(bookTime);
                            ticketInfo.setOffice(pnrResp.getOfficecode());
                            if (rtResult != null) {
                                Vector vector = rtResult.getAirSegs();
                                Iterator<PNRAirSeg> it = vector.iterator();
                                while (it.hasNext()) {
                                    PNRAirSeg pnrAirSeg = it.next();
                                    String desCity = pnrAirSeg.getDesCity();
                                    String orgCity = pnrAirSeg.getOrgCity();
                                    if (ticketInfo.getArrCode().equals(desCity) || ticketInfo.getDepCode().equals(orgCity)) {
                                        ticketInfo.setPnrStatus(pnrAirSeg.getActionCode());
                                    }
                                }
                            } else {
                                ticketInfo.setPnrStatus("RR");
                            }
                            searchMessageFromOringinalRT(rtResult, ticketInfo);
                        }
                    }
                } catch (Exception e) {
                    log.error("mapTicketInfoResult" + seg.getTicketNo(), e);
                    throw e;
                }

            }
        }

        if (StringUtils.isEmpty(passengerId)) {
            log.error("passengerId is null:" + result.getTicketNo());
        } else {
            log.error("passengerId is not null:" + result.getTicketNo());
        }

        log.info("ticketNo:" + result.getTicketNo() + " mapTicketInfoResult end date:" + (new Date().getTime() - ticketStart));
        return ticket;
    }

    /**
     * 从编码里获取编码生成的月日时分
     *
     * @param rtResult
     * @param seg
     * @return
     * @throws ParseException
     */
    private void parsePnrHis(RTResult rtResult, TicketInfo seg) throws ParseException {
        String operateHis = rtResult.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        //读取pnr预定时间
        Map<String, String> map = new HashMap<String, String>();
        String pnrbookTime = "";
        for (String one : list) {
            one = one.replaceAll("\\s+", " ");
            if ((one.startsWith("001") || one.startsWith("002") || one.startsWith("003")) && (one.split(" ").length == 5 || one.split(" ").length == 6)) {
                log.info(one + ":" + one.split(" ").length);
                String[] details = one.split(" ");
                if (one.indexOf(" IK ") > 0) { //找到操作配置
                    pnrbookTime = map.get(details[1] + details[2]);
                    break;
                }
                map.put(details[1] + details[2], details[3] + details[4]);//key是配置 value是时间
            }
        }

        //格式化日期
        try {
            if (StringUtils.isNotEmpty(pnrbookTime)) {
                Calendar c = Calendar.getInstance();
                if (pnrbookTime.length() == 9) {//只有月日时分
                    Date bookTime = DateUtils.parseDate(pnrbookTime, new String[]{"HHmmddMMM"});
                    int year = c.get(Calendar.YEAR);
                    c.setTime(bookTime);
                    c.set(Calendar.YEAR, year);
                    if (bookTime.compareTo(seg.getIssueDate()) > 0) {//年份原因导致生编码时间大于出票时间
                        c.add(Calendar.YEAR, -1);
                    }
                    seg.setBookTime(c.getTime());

                } else {
                    Date bookTime = DateUtils.parseDate(pnrbookTime, new String[]{"HHmmddMMMyy"});
                    c.setTime(bookTime);
                    seg.setBookTime(bookTime);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    //查找到达时间和证件号
    private void searchMessageFromOringinalRT(RTResult rtResult, TicketInfo ticketInfo) {

        String operateHis = rtResult.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        //判断编码是否和票中人一致
        if (operateHis.contains(ticketInfo.getPassengerName()) && operateHis.contains(ticketInfo.getDepCode() + ticketInfo.getArrCode())) {
            if (ticketInfo.getDepTime() != null) {
                //1.1先知道起飞时间
                Calendar c = Calendar.getInstance();
                c.setTime(ticketInfo.getDepTime());
                int hour = c.get(Calendar.HOUR_OF_DAY);
                int minutes = c.get(Calendar.MINUTE);
                String depTime = " " + (hour > 9 ? hour : "0" + hour) + (minutes > 9 ? minutes : "0" + minutes) + " ";

                //1.2把内容解析出来
                String arrTime = "";
                for (String one : list) {
                    one = one.replaceAll("\\s+", " ");
                    int depTimeIndex = one.indexOf(depTime);
                    if (depTimeIndex > 0 && one.indexOf(ticketInfo.getDepCode() + ticketInfo.getArrCode()) > -1) {
                        //找到落地时间字符串
                        arrTime = one.substring(depTimeIndex + depTime.length(), depTimeIndex + depTime.length() + 4);
                        break;
                    }
                }

                //1.3设置到达时间
                if (arrTime != null) {
                    c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(arrTime.substring(0, 2)));
                    c.set(Calendar.MINUTE, Integer.parseInt(arrTime.substring(2, 4)));
                    ticketInfo.setArrTime(c.getTime());
                }
            } else {
                String arrTime = "";
                for (String one : list) {
                    String time = REMatchUtil.searchkey("\\s\\d{4}\\s\\d{4}", one); //特例  2.  JD5675 L   FR15FEB  PEKHGH NO1   2320 0140+1    E T1T3
                    String fltDateStr = REMatchUtil.searchkey("\\s[A-Z]{2}\\d{2}[A-Z]{3}\\s", one);

                    if (StringUtils.isNotEmpty(time) && StringUtils.isNotEmpty(fltDateStr)) {
                        Calendar c = Calendar.getInstance();

                        Date fltDate = DateUtils.parseDate(fltDateStr.substring(3), new String[]{"ddMMM"});
                        //TODO year
                        int year = c.get(Calendar.YEAR);
                        c.setTime(fltDate);
                        c.set(Calendar.YEAR, year);
                        c.set(Calendar.HOUR_OF_DAY, 0);
                        c.set(Calendar.MINUTE, 0);
                        c.set(Calendar.SECOND, 0);
                        ticketInfo.setFlightDate(c.getTime());

                        String[] timeArray = time.substring(0, 10).split("\\s");
                        c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeArray[1].substring(0, 2)));
                        c.set(Calendar.MINUTE, Integer.parseInt(timeArray[1].substring(2, 4)));
                        ticketInfo.setDepTime(c.getTime());
                        c.set(Calendar.HOUR_OF_DAY, Integer.parseInt(timeArray[2].substring(0, 2)));
                        c.set(Calendar.MINUTE, Integer.parseInt(timeArray[2].substring(2, 4)));
                        ticketInfo.setArrTime(c.getTime());
                        if (StringUtils.isEmpty(ticketInfo.getFlightNo())) {
                            ticketInfo.setFlightNo(REMatchUtil.searchkey("\\s[A-Z]{2}\\d{3,4}\\s", one).trim());
                        }

                        break;
                    }
                }

            }

            //2.1检索乘客的索引
            String paxIndex = "";
            Map<String, String> paxIDMap = new HashMap<>();
            String paxTicket = "TN/" + ticketInfo.getTicketNo() + "/P";
            for (String one : list) {
                one = one.replaceAll("\\s+", " ");
                int paxTicketIndex = one.indexOf(paxTicket);
                //SSR FOID JD HK1 NI330219197304216855/P1
                if (one.indexOf("FOID") > -1) {
                    String paxId = REMatchUtil.searchkey("(PP|NI)\\d+[A-Z]{0,2}(/P)\\d", one);
                    if (!"".equals(paxId)) {
                        //P1->NI330219197304216855/P1
                        paxIDMap.put(paxId.substring(paxId.length() - 2), paxId.substring(2, paxId.length() - 3));
                    }
                }
                if (paxTicketIndex > 0) {
                    paxIndex = "P" + one.substring(paxTicketIndex + paxTicket.length(), paxTicketIndex + paxTicket.length() + 1);
                    break;
                }
            }
            ticketInfo.setPassengerID(paxIDMap.get(paxIndex));
        }
    }

    //客票正则
    private boolean ticketCheck(String ticketNo) {
        String regEx = "[0-9]{3}(-)?[0-9]{10}";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(ticketNo);
        // 字符串是否与正则表达式相匹配
        boolean rs = matcher.matches();
        return rs;
    }

    /**
     * 设置客票票面信息
     *
     * @param result
     * @param issueDate
     * @param segIndex
     * @param detrtktSegment
     * @return
     */
    private TicketInfo getTicketInfo(DETRTKTResult result, Date issueDate,
                                     Integer segIndex, DETRTKTSegment detrtktSegment, DETRHistoryResult historyResult) {
        TicketInfo seg = new TicketInfo();
        seg.setDepCode(detrtktSegment.getDepAirportCode());//出发城市三字码
        seg.setArrCode(detrtktSegment.getArrAirportCode());//到达城市三字码
        seg.setFlightNo(detrtktSegment.getFlightNo());//航班号
        if (detrtktSegment.getDepTime() != null) {
            seg.setFlightDate(DateUtil.changeDateToDate(detrtktSegment.getDepTime(), "yyyy-MM-dd"));//航班日期
            seg.setDepTime(detrtktSegment.getDepTime());//起飞时间
        }
        if (detrtktSegment.getArrTime() != null) {
            seg.setArrTime(detrtktSegment.getArrTime());//到达时间
        }
        seg.setIssCode(result.getTicketNo().substring(0, 3));//结算码
        String ticketNo = result.getTicketNo();
        if (ticketNo.length() > 10) {
            seg.setTicketNo(ticketNo.replaceAll("-", "").substring(3));
        } else {
            seg.setTicketNo(result.getTicketNo());
        }
        seg.setTicketStatus(detrtktSegment.getTicketStatus());//客票状态
        if (StringUtils.isNotEmpty(detrtktSegment.getPnrNo())) {
            seg.setPnr(detrtktSegment.getPnrNo());
        } else {
            for (int i = 0; i < historyResult.getInfoItemNum(); i++) {
                DETRHistoryInfoItem infoItem = historyResult.getInfoItem(i);
                if ("EOTU".equals(infoItem.getOperType()) && infoItem.getOperDesc().contains("CLEARED")) {
                    //
                    int position = infoItem.getOperDesc().indexOf(" RL ");
                    seg.setPnr(infoItem.getOperDesc().substring(position + 4, position + 10));
                    break;
                }
            }

        }
        seg.setCabin(detrtktSegment.getCabin() + "");//舱位
        seg.setIssueDate(issueDate);//出票时间
        seg.setAirlineCode(detrtktSegment.getAirline());//航司二字码
        seg.setEi(result.getSigningInfo());//EI项
        seg.setTourCode(result.getTourCode());//旅游代码


        if (result.getSegmentCount() > 1) {

            if (result.getETicketType() != 11 && result.getETicketType() != 13) { //国际票往返程是价格总和，不支持分开计算
                //回程的价格  19SEP18PEK JD ERL457.00JD PEK330.00CNY787.00END
                if (!org.springframework.util.StringUtils.hasText(seg.getTicketType())) {
                    seg.setTicketType("D");//国内票标识
                }

                if (segIndex != (result.getSegmentCount() - 1)) { // M 19JUL23PKX JD SYX1290.00JD PKX1770.00CNY3060.00END
                    // 多段的情况，目的地取下一段的出发地
                    String depCode = result.getSegment(segIndex + 1).getArrAirportCode();
                    int segCodeIndex = result.getFareCompute().lastIndexOf(depCode);
                    String backFare = result.getFareCompute().substring(segCodeIndex + depCode.length()).split("CNY")[0];
                    seg.setMarketFare(new BigDecimal(result.getFare() - Double.parseDouble(backFare)));//票面价
                    seg.setNetFare(seg.getMarketFare());//实付价
                } else {
                    //最后一段就取
                    int segCodeIndex = result.getFareCompute().lastIndexOf(detrtktSegment.getArrAirportCode());
                    String backFare = result.getFareCompute().substring(segCodeIndex + detrtktSegment.getDepAirportCode().length()).split("CNY")[0];
                    seg.setMarketFare(new BigDecimal(backFare));//票面价
                    seg.setNetFare(new BigDecimal(backFare));//实付价
                }
                for (int i = 0; i < result.getTaxCode().size(); i++) {
                    if (result.getTaxCode(i).equals("CN")) {
                        seg.setAirportTax(new BigDecimal(result.getTaxAmount(i) / result.getSegmentCount()));//机建费
                    } else if (result.getTaxCode(i).equals("YQ")) {
                        seg.setFuelTax(new BigDecimal(result.getTaxAmount(i) / result.getSegmentCount()));//燃油费
                    }
                }
            } else {
                //TODO 联程时需要判断国内段
                String depCode = detrtktSegment.getDepAirportCode();
                String arrCode = detrtktSegment.getArrAirportCode();
                log.info("international:" + depCode + "-" + arrCode);

                seg.setTicketType("I"); //国际票标识
            }

        } else {
            seg.setMarketFare(new BigDecimal(result.getFare()));//票面价
            seg.setNetFare(new BigDecimal(result.getFare()));//实付价
            for (int i = 0; i < result.getTaxCode().size(); i++) {
                if (result.getTaxCode(i).equals("CN")) {
                    seg.setAirportTax(new BigDecimal(result.getTaxAmount(i)));//机建费
                } else if (result.getTaxCode(i).equals("YQ")) {
                    seg.setFuelTax(new BigDecimal(result.getTaxAmount(i)));//燃油费
                }
            }

            log.info(seg.getTicketNo() + ",ETicketType:" + result.getETicketType());

            if (result.getETicketType() != 11 && result.getETicketType() != 13) { //国际票往返程是价格总和，不支持分开计算
                if (!org.springframework.util.StringUtils.hasText(seg.getTicketType())) {
                    seg.setTicketType("D");//国内票标识
                }
            } else {
                seg.setTicketType("I");//国内票标识
            }

        }
        String pcode = "MZMK";//产品代码默认MZMK，如果第二段或者旅客找不到则按照
        if (null != detrtktSegment.getRate() && !"".equals(detrtktSegment.getRate())) {
            String[] pcodeArr = detrtktSegment.getRate().split("/");
            if (pcodeArr.length > 1) {
                pcode = pcodeArr[1];
            } else if (pcodeArr.length == 1) {
                pcode = pcodeArr[0];
            } else {
                pcode = "";
            }
        }
        seg.setProductCode(pcode);
        try {
            //0 是成人 1是儿童 3是婴儿
            seg.setPassengerType(PassengerType.valueOf("PASSENGER_" + result.getPassengerType()).getAlias());//旅客类型
        } catch (Exception ex) {
            log.error("passengerType  is error: " + ex.getMessage());
            seg.setPassengerType(PassengerType.valueOf("PASSENGER_4").getAlias());//旅客类型
        }
        if (result.getPassengerType() != 0) { //婴儿需要处理一下名字
            int infPos = result.getPassengerName().indexOf(" INF");
            if (infPos == -1) {
                infPos = result.getPassengerName().indexOf(" CHD");
            }
            if (infPos == -1) {
                seg.setPassengerName(result.getPassengerName());
            } else {
                seg.setPassengerName(result.getPassengerName().substring(0, infPos));
            }
        } else {
            seg.setPassengerName(result.getPassengerName());//旅客姓名
        }
        seg.setSegmentIndex(seg.getSegmentIndex());//航段号

        if ("P".equals(seg.getCabin()) && detrtktSegment.getRate() != null && detrtktSegment.getRate().length() > 2) {
            seg.setRefCabin(detrtktSegment.getRate().substring(1, 2));
        } else {
            seg.setRefCabin(seg.getCabin());
        }
        return seg;
    }

    private DETRTktResult mapTicketResult(DETRTKTResult result) {
        DETRTktResult ticket = new DETRTktResult();
        BeanUtils.copyProperties(result, ticket);
        for (int i = 0; i < result.getSegmentCount(); ++i) {
            DETRTKTSegment detrtktSegment = result.getSegment(i);
            DETRSeg seg = new DETRSeg();
            BeanUtils.copyProperties(detrtktSegment, seg);
            ticket.getSegs().add(seg);
        }
        for (int taxCount = 0; taxCount < result.getTaxLength(); taxCount++) {
            DETRTax tax = new DETRTax();
            tax.setTaxAmount((float) result.getTaxAmount(taxCount));
            tax.setTaxCode(result.getTaxCode(taxCount));
            tax.setTaxCurrency(result.getTaxCurrencyType(taxCount));
            ticket.getTaxs().add(tax);
        }
        return ticket;
    }

    @Override
    public BaseResponse etrf(RefundTicketRequest request) {
        // TODO Auto-generated method stub
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        ETRF etrf = IbeCmd.getIbeClient(airline, ETRF.class);
        if (StringUtils.isBlank(request.getPrntNo())) {
            request.setPrntNo("" + getPrinter(airline));
        }
        try {
            String ok = etrf.refundETkt(request.getTicketNo(), request.getSegID(), request.getCurrType(), request.getAmount(), request.getPrntNo(), request.getRemark());
            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
            log.info(" etrf ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), etrf.getTxnTraceKey());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" etrf error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), etrf.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public BaseResponse etdz(IssueTicketRequest request) {
        // TODO Auto-generated method stub
//		return null;
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        BaseResponse response = new BaseResponse();
        ETDZ etdz = IbeCmd.getIbeClient(airline, ETDZ.class);
        if (request.getPrinterNo() == 0) {
            request.setPrinterNo(getPrinter(airline));
        }
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String ok;
            ok = pnrManage.reconfirmAirSeg(request.getPnrno(), null, PnrManage.RECONFIRM_CHECK_STRICTLY);
            if (!"OK".equalsIgnoreCase(ok) && org.apache.commons.lang3.StringUtils.isNotBlank(ok)) {
                response.setErrorInfo(ok);
                return response;
            }
            ok = etdz.issueTicket(request.getPnrno(), request.getPrinterNo(), request.getPsgrID(), request.getPasstype());
            if ("OK".equalsIgnoreCase(ok)) {
                response.setSuccess(true);
            } else {
                response.setErrorInfo(ok);
            }
            log.info(" etdz pnrNo: {} , getTxnTraceKey: {} ", request.getPnrno(), pnrManage.getTxnTraceKey());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" etdz error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrno(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    @Override
    public IssueTicketResponse issueTicketWithTN(IssueTicketRequest request) {
        // TODO Auto-generated method stub
//		return null;
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        IssueTicketResponse response = new IssueTicketResponse();
        ETDZ etdz = IbeCmd.getIbeClient(airline, ETDZ.class);
        if (request.getPrinterNo() == 0) {
            request.setPrinterNo(getPrinter(airline));
        }
        PnrManage pnrManage = IbeCmd.getIbeClient(airline, PnrManage.class);
        try {
            String ok;
            ok = pnrManage.reconfirmAirSeg(request.getPnrno(), null, PnrManage.RECONFIRM_ALL_POSSIBLE_ACTION);
            if (!"OK".equalsIgnoreCase(ok) && org.apache.commons.lang3.StringUtils.isNotBlank(ok)) {
                response.setErrorInfo(ok);
                return response;
            }

            ETDZResult[] etdzResults = etdz.issueTicketWithTN(request.getPnrno(), request.getPrinterNo(), request.getPsgrID(), request.getPasstype());
            if (null != etdzResults) {
                response.setEtdzResults(new com.hna.shopping.ibe.interfaces.dto.ETDZResult[etdzResults.length]);
                int i = 0;
                for (ETDZResult etdzResult : etdzResults) {
                    com.hna.shopping.ibe.interfaces.dto.ETDZResult e0 = new com.hna.shopping.ibe.interfaces.dto.ETDZResult();
                    BeanUtils.copyProperties(etdzResult, e0);
                    response.getEtdzResults()[i++] = e0;
                }
                response.setSuccess(true);
            } else {

            }
            log.info(" issueTicketWithTN pnrNo: {} , getTxnTraceKey: {} ", request.getPnrno(), pnrManage.getTxnTraceKey());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(" issueTicketWithTN error,  pnrNo: {} , getTxnTraceKey: {} ", request.getPnrno(), pnrManage.getTxnTraceKey());
            log.error("", e);
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }

    private int getPrinter(String airline) {
//		if(request.getPrinterNo()==0) {
        IBEConfig ibeConfig = ApplicationContextProvider.getBean(String.format("IBEConfig%s",
                StringUtils.upperCase(airline)), IBEConfig.class);
        if (StringUtils.isNoneBlank(ibeConfig.getClient().getPrintNos())) {
            String[] printers = ibeConfig.getClient().getPrintNos().split("[,/]");
            int index = RandomUtils.nextInt(printers.length);
//				request.setPrinterNo(Integer.parseInt(printers[index].trim()));
            return Integer.parseInt(printers[index].trim());
//			}
        }
        return 1;
    }

    @Override
    public DETRCreResponse detrCredential(DETRRequest request) {
        DETRCreResponse response = new DETRCreResponse();
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline)) airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            DETRFoidResult result = detr2f.getCredentialByTktNo2F(request.getTicketNo(), secondFactorCode, secondFactorValue);
            DETRCreResult creResult = new DETRCreResult();
            BeanUtils.copyProperties(result, creResult);
            for (int i = 0; i < result.getIdnoCount(); i++) {
                if ("NI".equals(result.getIdTypeAt(i))) {
                    String idNo = result.getIdNoAt(i);
                    String idType = result.getIdTypeAt(i);
                    creResult.setIdNo(idNo);
                    creResult.setIdtype(idType);
                    creResult.getIdnos().add(idNo);
                }
            }
            response.setDetrCreResult(creResult);
            log.info(" detrCredential ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setSuccess(true);
        } catch (Exception e) {
            log.error(" detrCredential error ticketNo: {} , getTxnTraceKey: {} ", request.getTicketNo(), detr.getTxnTraceKey());
            response.setErrorCode(e.getClass().getSimpleName());
            response.setErrorInfo(e.getMessage());
        }
        return response;
    }


    /**
     * 旅服系统回迁。通过高频接口获取航班旅客信息
     *
     * @param request
     * @return
     */
    @Override
    public FlightPassengerInfoResponse getFlightPassengerInfoByHSD(FlightPassengerInfoRequest request) {
        String flightNo = request.getFlightNo().substring(2).length() == 3 ? request.getFlightNo().substring(0, 2) + "0" + request.getFlightNo().substring(2) : request.getFlightNo();

        ApiRequest apiRequest = new ApiRequest();
        apiRequest.setOption("fltNo", flightNo);
        apiRequest.setOption("datop", request.getFlightDate());
        EsbApiResponse<StandardFocFlightInfo> focResponse = esbService.getStandardFocFlightInfo(apiRequest);

        ApiResponse hsdResponse;
        if (StringUtils.isNotEmpty(request.getTicketNo())) {      // 根据票号查询
            hsdResponse = esbService.queryIETSByTkne(request.getTicketNo());
        } else {
            String flightDate = request.getFlightDate().replaceAll("-", "");
            hsdResponse = esbService.queryIETSByFlightNo(flightDate, flightNo, request.getDepCode(), request.getArrCode(), null);
        }
        request.setFlightNo(flightNo);
        return mapFlightPassengerResultByHSD(request, hsdResponse, focResponse);
    }


    private String issuedToAir(String issuedMsg) {
        if (!StringUtils.isEmpty(issuedMsg)) {
            if (issuedMsg.indexOf("YANGTZE") > -1) {
                return "Y8";
            }
            if (issuedMsg.indexOf("CHANG_AN") > -1) {
                return "9H";
            }
            if (issuedMsg.indexOf("URUMQI") > -1) {
                return "UQ";
            }
            if (issuedMsg.indexOf("FUZHOU") > -1) {
                return "FU";
            }
            if (issuedMsg.indexOf("BEIBU") > -1) {
                return "GX";
            }
            if (issuedMsg.indexOf("WEST") > -1) {
                return "PN";
            }
            if (issuedMsg.indexOf("XIANG PENG") > -1) {
                return "8L";
            }
            if (issuedMsg.indexOf("HAINAN") > -1) {
                return "HU";
            }
            if (issuedMsg.indexOf("TIANJIN") > -1) {
                return "GS";
            }
            if (issuedMsg.indexOf("GRAND CHINA AIR CO.LTD") > -1) {
                return "CN";
            }
            if (issuedMsg.indexOf("CAPITAL") > -1) {
                return "JD";
            }
        }
        return "";
    }


    @Override
    public List<Date> getFixDepartureDate(DETRRequest request) {
        String airline = request.getAirlineCode();
        if (StringUtils.isEmpty(airline))
            airline = "JD";
        DETR detr = IbeCmd.getIbeClient(airline, DETR.class);
        DETR2F detr2f = IbeCmd.getIbeClient(airline, DETR2F.class);

        /**
         * 组织双因素。
         * 双因素参数接口。#getTicketInfoByTktNo(String, boolean, String, String) 提取客票数据的接口，除了票号外，还需录入第二因素信息。 目前支持第二因素代码为 NI（身份证）、PP（护照）、UU（无法识别的证件）、ID（士官证、军官证、武警警官证、海员证等）、CN（PNR）、NM（姓名）
         * CN,PNR记录编码，票面任一CPN的任一记录编码均可
         * NI 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括NI类型且匹配的证件号则可以提取客票信息
         * ID 一般是出票前以SSR FOID 类型NI录入的证件号。如客票内存储的证件信息包括ID类型且匹配的证件号则可以提取客票信息
         * UU 一般是出票前以SSR FOID 类型UU录入的证件号。如客票内存储的证件信息包括UU类型且匹配的证件号则可以提取客票信息
         * PP 一般是出票前以SSR DOCS录入的任何类型的证件号，如客票内存储的证件信息包括PP类型且匹配的证件号则可以提取客票信息
         * 以上代码择一录入，后台与此票号中存储的数据比对，后台根据比对结果给出客票相应数据或不提供客票信息。 如后台支持NM作为第二因素时，姓名匹配规则由后台规则决定 如特殊客票，如代码使用CN，但PNR中所有CPN均不关联任何记录编码 或以任一证件代码作为第二因素，但客票中没有四种证件代码的证件号时，由后台决定是否提供数据。
         */
        String secondFactorCode = "";
        String secondFactorValue = "";
        // 有证件号，默认使用证件类型+证件号作为双因素
        if (StringUtils.isNotEmpty(request.getPassengerNo())) {
            secondFactorCode = StringUtils.isEmpty(request.getPassengerType()) ? "NI" : request.getPassengerType();
            secondFactorValue = request.getPassengerNo();
        } else if (StringUtils.isNotEmpty(request.getPassengerName())) {
            secondFactorCode = "NM";
            secondFactorValue = request.getPassengerName();
            // 双因素处理婴儿姓名，使用正则表达式匹配 INF( 后面跟任意字符的模式
            secondFactorValue = secondFactorValue.replaceAll("INF\\(.*?\\)", "").trim();
            // 双因素处理无陪儿童
            secondFactorValue = secondFactorValue.replaceAll("\\(UM\\d+\\)", "").trim();
        } else if (StringUtils.isNotEmpty(request.getPnrNo())) {
            secondFactorCode = "CN";
            secondFactorValue = request.getPnrNo();
        }

        try {
            DETRTKTResult result = detr2f.getTicketInfoByTktNo2F(request.getTicketNo(), false, "", "", secondFactorCode, secondFactorValue);
            DETRHistoryResult historyResult = detr2f.getTicketHistoryByTktNo2F(request.getTicketNo(),  "N", secondFactorCode, secondFactorValue);
            return detr.fixDepartureDate(result, historyResult);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public Vector<String> getTicketListByCert(Map<String, String> request) {
        DETR detr = IbeCmd.getIbeClient("JD", DETR.class);
        String certType = request.get("certType");
        String idCardNo = request.get("idCardNo");

        // IBE 按证件号提取票号列表
        try {
            return detr.getTicketListByCert(certType, idCardNo, false, "JD", null, 1);
        } catch (IBEException e) {
            e.printStackTrace();
        }
        return null;
    }
}
