/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ErrorBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String errorCode;
/*    */   private String errorMsg;
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 27 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 34 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 41 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 48 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.ErrorBean
 * JD-Core Version:    0.6.0
 */