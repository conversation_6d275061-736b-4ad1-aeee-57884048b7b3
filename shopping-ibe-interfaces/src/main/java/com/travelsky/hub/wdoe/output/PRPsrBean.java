/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.ArrayList;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PRPsrBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2104318322382078408L;
/*  20 */   private String passengerName = "";
/*     */ 
/*  26 */   private String passengerChnName = "";
/*     */ 
/*  31 */   private String boardingNumber = "";
/*     */ 
/*  36 */   private String seatNumber = "";
/*     */ 
/*  41 */   private String toCity = "";
/*     */ 
/*  46 */   private String pnr = "";
/*     */ 
/*  51 */   private String passengerStatus = "";
/*     */ 
/*  56 */   private String ffpAirlineCode = "";
/*     */ 
/*  61 */   private String ffpCardNumber = "";
/*     */ 
/*  66 */   private String groupName = "";
/*     */ 
/*  72 */   private String groupNumber = "";
/*     */ 
/*  77 */   private String outBoundInfo = "";
/*     */ 
/*  82 */   private List outBoundInfoList = new ArrayList();
/*     */ 
/*  87 */   private String inBoundInfo = "";
/*     */ 
/*  92 */   private List inBoundInfoList = new ArrayList();
/*     */ 
/*  97 */   private String specialFood = "";
/*     */ 
/* 102 */   private String isUM = "";
/*     */ 
/* 107 */   private String infantName = "";
/*     */ 
/* 112 */   private String isJumpSeat = "";
/*     */ 
/* 117 */   private String isStretcher = "";
/*     */ 
/* 122 */   private String isExtraSeat = "";
/*     */ 
/* 127 */   private String extraSeatNumber = "";
/*     */ 
/* 132 */   private String agentID = "";
/*     */ 
/* 137 */   private String ckiTime = "";
/*     */ 
/* 142 */   private String ckiPid = "";
/*     */ 
/* 147 */   private String psptInfo = "";
/*     */ 
/* 152 */   private String additionalInfo = "";
/*     */ 
/* 157 */   private String marketingFltNumber = "";
/*     */ 
/* 162 */   private String marketingFareClass = "";
/*     */ 
/* 167 */   private String operationFltNumber = "";
/*     */ 
/* 172 */   private String dcsClass = "";
/*     */ 
/* 177 */   private String hostNbr = "";
/*     */ 
/* 182 */   private String isHasBag = "";
/*     */   private boolean vip;
/*     */   private String paxLevel;
/*     */   private String cabType;
/*     */   private String sequenceNumber;
/*     */   private String parentCabinType;
/*     */   private String appStatus;
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 211 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 218 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 225 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String i)
/*     */   {
/* 232 */     this.passengerName = i;
/*     */   }
/*     */ 
/*     */   public String getPassengerChnName()
/*     */   {
/* 239 */     return this.passengerChnName;
/*     */   }
/*     */ 
/*     */   public void setPassengerChnName(String i)
/*     */   {
/* 246 */     this.passengerChnName = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 253 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String i)
/*     */   {
/* 260 */     this.boardingNumber = i;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 267 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String i)
/*     */   {
/* 274 */     this.seatNumber = i;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 281 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String i)
/*     */   {
/* 288 */     this.toCity = i;
/*     */   }
/*     */ 
/*     */   public String getPNR()
/*     */   {
/* 295 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public void setPNR(String i)
/*     */   {
/* 302 */     this.pnr = i;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 309 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String i)
/*     */   {
/* 316 */     this.passengerStatus = i;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 323 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String i)
/*     */   {
/* 330 */     this.ffpAirlineCode = i;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 337 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String i)
/*     */   {
/* 344 */     this.ffpCardNumber = i;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/* 351 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String i)
/*     */   {
/* 358 */     this.groupName = i;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 365 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String i)
/*     */   {
/* 372 */     this.groupNumber = i;
/*     */   }
/*     */ 
/*     */   public String getOutBoundInfo()
/*     */   {
/* 379 */     return this.outBoundInfo;
/*     */   }
/*     */ 
/*     */   public void setOutBoundInfo(String i)
/*     */   {
/* 386 */     this.outBoundInfo = i;
/*     */   }
/*     */ 
/*     */   public String getInBoundInfo()
/*     */   {
/* 393 */     return this.inBoundInfo;
/*     */   }
/*     */ 
/*     */   public void setInBoundInfo(String i)
/*     */   {
/* 400 */     this.inBoundInfo = i;
/*     */   }
/*     */ 
/*     */   public String getSpecialFood()
/*     */   {
/* 407 */     return this.specialFood;
/*     */   }
/*     */ 
/*     */   public void setSpecialFood(String i)
/*     */   {
/* 414 */     this.specialFood = i;
/*     */   }
/*     */ 
/*     */   public String getIsUM()
/*     */   {
/* 421 */     return this.isUM;
/*     */   }
/*     */ 
/*     */   public void setIsUM(String i)
/*     */   {
/* 428 */     this.isUM = i;
/*     */   }
/*     */ 
/*     */   public String getInfantName()
/*     */   {
/* 435 */     return this.infantName;
/*     */   }
/*     */ 
/*     */   public void setInfantName(String i)
/*     */   {
/* 442 */     this.infantName = i;
/*     */   }
/*     */ 
/*     */   public String getIsJumpSeat()
/*     */   {
/* 449 */     return this.isJumpSeat;
/*     */   }
/*     */ 
/*     */   public void setIsJumpSeat(String i)
/*     */   {
/* 456 */     this.isJumpSeat = i;
/*     */   }
/*     */ 
/*     */   public String getIsStretcher()
/*     */   {
/* 463 */     return this.isStretcher;
/*     */   }
/*     */ 
/*     */   public void setIsStretcher(String i)
/*     */   {
/* 470 */     this.isStretcher = i;
/*     */   }
/*     */ 
/*     */   public String getIsExtraSeat()
/*     */   {
/* 477 */     return this.isExtraSeat;
/*     */   }
/*     */ 
/*     */   public void setIsExtraSeat(String i)
/*     */   {
/* 484 */     this.isExtraSeat = i;
/*     */   }
/*     */ 
/*     */   public String getExtraSeatNumber()
/*     */   {
/* 491 */     return this.extraSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setExtraSeatNumber(String i)
/*     */   {
/* 498 */     this.extraSeatNumber = i;
/*     */   }
/*     */ 
/*     */   public String getAgentID()
/*     */   {
/* 505 */     return this.agentID;
/*     */   }
/*     */ 
/*     */   public void setAgentID(String i)
/*     */   {
/* 512 */     this.agentID = i;
/*     */   }
/*     */ 
/*     */   public String getCkiTime()
/*     */   {
/* 519 */     return this.ckiTime;
/*     */   }
/*     */ 
/*     */   public void setCkiTime(String i)
/*     */   {
/* 526 */     this.ckiTime = i;
/*     */   }
/*     */ 
/*     */   public String getCkiPid()
/*     */   {
/* 533 */     return this.ckiPid;
/*     */   }
/*     */ 
/*     */   public void setCkiPid(String i)
/*     */   {
/* 540 */     this.ckiPid = i;
/*     */   }
/*     */ 
/*     */   public String getPSPTInfo()
/*     */   {
/* 547 */     return this.psptInfo;
/*     */   }
/*     */ 
/*     */   public void setPSPTInfo(String i)
/*     */   {
/* 554 */     this.psptInfo = i;
/*     */   }
/*     */ 
/*     */   public String getAdditionalInfo()
/*     */   {
/* 561 */     return this.additionalInfo;
/*     */   }
/*     */ 
/*     */   public void setAdditionalInfo(String i)
/*     */   {
/* 568 */     this.additionalInfo = i;
/*     */   }
/*     */ 
/*     */   public String getMarketingFltNumber()
/*     */   {
/* 575 */     return this.marketingFltNumber;
/*     */   }
/*     */ 
/*     */   public void setMarketingFltNumber(String i)
/*     */   {
/* 582 */     this.marketingFltNumber = i;
/*     */   }
/*     */ 
/*     */   public String getMarketingFareClass()
/*     */   {
/* 589 */     return this.marketingFareClass;
/*     */   }
/*     */ 
/*     */   public void setMarketingFareClass(String i)
/*     */   {
/* 596 */     this.marketingFareClass = i;
/*     */   }
/*     */ 
/*     */   public String getOperationFltNumber()
/*     */   {
/* 603 */     return this.operationFltNumber;
/*     */   }
/*     */ 
/*     */   public void setOperationFltNumber(String i)
/*     */   {
/* 610 */     this.operationFltNumber = i;
/*     */   }
/*     */ 
/*     */   public String getDcsClass()
/*     */   {
/* 617 */     return this.dcsClass;
/*     */   }
/*     */ 
/*     */   public void setDcsClass(String i)
/*     */   {
/* 624 */     this.dcsClass = i;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 630 */     this.passengerName = "";
/* 631 */     this.passengerChnName = "";
/* 632 */     this.boardingNumber = "";
/* 633 */     this.seatNumber = "";
/* 634 */     this.toCity = "";
/* 635 */     this.pnr = "";
/* 636 */     this.passengerStatus = "";
/* 637 */     this.ffpAirlineCode = "";
/* 638 */     this.ffpCardNumber = "";
/* 639 */     this.groupName = "";
/* 640 */     this.groupNumber = "";
/* 641 */     this.outBoundInfo = "";
/* 642 */     this.inBoundInfo = "";
/* 643 */     this.specialFood = "";
/* 644 */     this.isUM = "";
/* 645 */     this.infantName = "";
/* 646 */     this.isJumpSeat = "";
/* 647 */     this.isStretcher = "";
/* 648 */     this.isExtraSeat = "";
/* 649 */     this.extraSeatNumber = "";
/* 650 */     this.agentID = "";
/* 651 */     this.ckiTime = "";
/* 652 */     this.ckiPid = "";
/* 653 */     this.psptInfo = "";
/* 654 */     this.additionalInfo = "";
/*     */ 
/* 656 */     this.marketingFltNumber = "";
/*     */ 
/* 658 */     this.marketingFareClass = "";
/*     */ 
/* 660 */     this.operationFltNumber = "";
/* 661 */     this.dcsClass = "";
/*     */   }
/*     */ 
/*     */   public String getHostNbr()
/*     */   {
/* 668 */     return this.hostNbr;
/*     */   }
/*     */ 
/*     */   public void setHostNbr(String hostNbr)
/*     */   {
/* 675 */     this.hostNbr = hostNbr;
/*     */   }
/*     */ 
/*     */   public String getIsHasBag()
/*     */   {
/* 683 */     return this.isHasBag;
/*     */   }
/*     */ 
/*     */   public void setIsHasBag(String isHasBag)
/*     */   {
/* 691 */     this.isHasBag = isHasBag;
/*     */   }
/*     */ 
/*     */   public List getOutBoundInfoList()
/*     */   {
/* 699 */     return this.outBoundInfoList;
/*     */   }
/*     */ 
/*     */   public void setOutBoundInfoList(List outBoundInfoList)
/*     */   {
/* 707 */     this.outBoundInfoList = outBoundInfoList;
/*     */   }
/*     */ 
/*     */   public List getInBoundInfoList()
/*     */   {
/* 715 */     return this.inBoundInfoList;
/*     */   }
/*     */ 
/*     */   public void setInBoundInfoList(List inBoundInfoList)
/*     */   {
/* 723 */     this.inBoundInfoList = inBoundInfoList;
/*     */   }
/*     */ 
/*     */   public boolean getVip()
/*     */   {
/* 730 */     return this.vip;
/*     */   }
/*     */ 
/*     */   public void setVip(boolean vip)
/*     */   {
/* 737 */     this.vip = vip;
/*     */   }
/*     */ 
/*     */   public void setPaxLevel(String paxLevel)
/*     */   {
/* 744 */     this.paxLevel = paxLevel;
/*     */   }
/*     */ 
/*     */   public String getCabType()
/*     */   {
/* 751 */     return this.cabType;
/*     */   }
/*     */ 
/*     */   public void setCabType(String cabType)
/*     */   {
/* 758 */     this.cabType = cabType;
/*     */   }
/*     */ 
/*     */   public String getPaxLevel()
/*     */   {
/* 765 */     return this.paxLevel;
/*     */   }
/*     */ 
/*     */   public String getParentCabinType()
/*     */   {
/* 772 */     return this.parentCabinType;
/*     */   }
/*     */ 
/*     */   public void setParentCabinType(String parentCabinType)
/*     */   {
/* 779 */     this.parentCabinType = parentCabinType;
/*     */   }
/*     */ 
/*     */   public String getAppStatus()
/*     */   {
/* 786 */     return this.appStatus;
/*     */   }
/*     */ 
/*     */   public void setAppStatus(String appStatus)
/*     */   {
/* 793 */     this.appStatus = appStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PRPsrBean
 * JD-Core Version:    0.6.0
 */