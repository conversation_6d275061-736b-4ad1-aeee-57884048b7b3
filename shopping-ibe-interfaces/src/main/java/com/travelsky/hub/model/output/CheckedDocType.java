/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CheckedDocType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private Integer docID;
/*    */   private String docName;
/*    */   private String description;
/*    */ 
/*    */   public Integer getDocID()
/*    */   {
/* 31 */     return this.docID;
/*    */   }
/*    */ 
/*    */   public void setDocID(Integer docID)
/*    */   {
/* 38 */     this.docID = docID;
/*    */   }
/*    */ 
/*    */   public String getDocName()
/*    */   {
/* 45 */     return this.docName;
/*    */   }
/*    */ 
/*    */   public void setDocName(String docName)
/*    */   {
/* 52 */     this.docName = docName;
/*    */   }
/*    */ 
/*    */   public String getDescription()
/*    */   {
/* 59 */     return this.description;
/*    */   }
/*    */ 
/*    */   public void setDescription(String description)
/*    */   {
/* 66 */     this.description = description;
/*    */   }
/*    */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckedDocType
 * JD-Core Version:    0.6.0
 */