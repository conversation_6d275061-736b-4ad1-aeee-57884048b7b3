/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FcTaxSummary
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2572997964111262690L;
/*    */   private FcTax fcTax;
/*    */   private FcTax fcOriginalTax;
/*    */   private FcTax fcRefundTax;
/*    */ 
/*    */   public FcTax getFcTax()
/*    */   {
/* 35 */     return this.fcTax;
/*    */   }
/*    */ 
/*    */   public void setFcTax(FcTax fcTax)
/*    */   {
/* 42 */     this.fcTax = fcTax;
/*    */   }
/*    */ 
/*    */   public FcTax getFcOriginalTax()
/*    */   {
/* 49 */     return this.fcOriginalTax;
/*    */   }
/*    */ 
/*    */   public void setFcOriginalTax(FcTax fcOriginalTax)
/*    */   {
/* 56 */     this.fcOriginalTax = fcOriginalTax;
/*    */   }
/*    */ 
/*    */   public FcTax getFcRefundTax()
/*    */   {
/* 63 */     return this.fcRefundTax;
/*    */   }
/*    */ 
/*    */   public void setFcRefundTax(FcTax fcRefundTax)
/*    */   {
/* 70 */     this.fcRefundTax = fcRefundTax;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FcTaxSummary
 * JD-Core Version:    0.6.0
 */