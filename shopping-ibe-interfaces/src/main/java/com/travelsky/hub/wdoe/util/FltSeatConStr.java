package com.travelsky.hub.wdoe.util;

public final class FltSeatConStr
{
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String CSL_HOSTAIRLINE = "HostAirlineCode";
  public static final String CSL_HOSTFLTNUM = "HostFlightNumber";
  public static final String CSL_SHAREAIRLINE = "SharedAirlineCode";
  public static final String CSL_SHAREFLTNUM = "SharedFlightNumber";
  public static final String SYPRINFO_ELEMENT = "SyPrInfo";
  public static final String TKTNUMBER_SYPRINFO = "TKTNumber";
  public static final String AIRLINECODE_SYPRINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_SYPRINFO = "FlightNumber";
  public static final String FLIGHTDATE_SYPRINFO = "FlightDate";
  public static final String DEPARTUREAIRPORT_SYPRINFO = "DepartureAirport";
  public static final String ARRIVALAIRPORT_SYPRINFO = "ArrivalAirport";
  public static final String BOARDINGNUMBER = "BoardingNumber";
  public static final String ERROR_ELEMENT = "Error";
  public static final String CODE_ERROR = "Code";
  public static final String MSG_ERROR = "Msg";
  public static final String PASSENGERS_ELEMENT = "Passengers";
  public static final String PASSENGER_ELEMENT = "Passenger";
  public static final String SURNAME_PASSENGER = "Surname";
  public static final String CHNNAME_PASSENGER = "ChnName";
  public static final String BOARDINGNUMBER_PASSENGER = "BoardingNumber";
  public static final String SEATNUMBER_PASSENGER = "SeatNumber";
  public static final String TICKETID_PASSENGER = "TicketId";
  public static final String SEQUENCENUMBER_PASSENGER = "SequenceNumber";
  public static final String DEPARTUREAIRPORT_PASSENGER = "DepartureAirport";
  public static final String ARRIVALAIRPORT_PASSENGER = "ArrivalAirport";
  public static final String MARKETINGAIRLINE_PASSENGER = "MarketingAirlineCode";
  public static final String MARKETINGFLTNUMBER_PASSENGER = "MarketingFlightNumber";
  public static final String FARECLASS_PASSENGER = "FareClass";
  public static final String PNR_PASSENGER = "PNR";
  public static final String PASSENGERSTATUS_PASSENGER = "PassengerStatus";
  public static final String CABINTYPE_PASSENGER = "CabinType";
  public static final String PARENTCABINTYPE_PASSENGER = "ParentCabinType";
  public static final String PROGRAMID_PASSENGER = "ProgramID";
  public static final String MEMBERSHIPID_PASSENGER = "MembershipID";
  public static final String LAYOLLEVEL_PASSENGER = "LayolLevel";
  public static final String GROUPCODE_PASSENGER = "GroupCode";
  public static final String GROUPNUMBER_PASSENGER = "GroupNumber";
  public static final String FAMILYTAG_PASSENGER = "FamilyTag";
  public static final String CHD_PASSENGER = "CHD";
  public static final String SPECIALSVCINFO_PASSENGER = "SpecialSvcInfo";
  public static final String AIRLINECODE_PASSENGER = "AirlineCode";
  public static final String FLIGHTNUMBER_PASSENGER = "FlightNumber";
  public static final String HOSTNUMBER_PASSENGER = "HostNumber";
  public static final String CHDFLAG_PASSENGER = "ChdFlag";
  public static final String ISASRSEAT_PASSENGER = "IsAsrSeat";
  public static final String ASRSTATUS_PASSENGER = "AsrStatus";
  public static final String INF_ELEMENT = "INF";
  public static final String INFINFO_INF = "INFInfo";
  public static final String INFETNO_INF = "INFEtNo";
  public static final String INFETSEQUENCE_INF = "INFEtSequence";
  public static final String INFANT_ELEMENT = "Infant";
  public static final String BAGTAGS_ELEMENT = "BagTags";
  public static final String BAGTAG_ELEMENT = "BagTag";
  public static final String INDEX_BAGTAG = "Index";
  public static final String BAGTAGNO_BAGTAG = "BagTagNo";
  public static final String BAGARRIVALAIRPORT_BAGTAG = "BagArrivalAirport";
  public static final String BAGSTATUS_BAGTAG = "BagStatus";
  public static final String EXTRASEAT_ELEMENT = "Extraseat";
  public static final String SEATNUMBER_EXTRASEAT = "SeatNumber";
  public static final String EXTRATYPE_EXTRASEAT = "ExtraType";
  public static final String EXTRAWEIGHT_EXTRASEAT = "ExtraWeight";
  public static final String SEATCHANGES_ELEMENT = "SeatChanges";
  public static final String SEATCHANGE_ELEMENT = "SeatChange";
  public static final String SEATNUMBER_SEATCHANGE = "SeatNumber";
  public static final String CABINTYPE_SEATCHANGE = "CabinType";
  public static final String ARRIVALAIRPORT_SEATCHANGE = "ArrivalAirport";
  public static final String TEXTMSGS_ELEMENT = "TextMSGS";
  public static final String TEXTMSG_ELEMENT = "TextMSG";
  public static final String MSGTYPE_TEXTMSG = "MSGType";
  public static final String MSGTEXT_TEXTMSG = "MSGText";
  public static final String BKOABSTRACTINFO_ELEMENT = "BkoAbstractInfo";
  public static final String BAGCOUNT_BKOABSTRACTINFO = "BagCount";
  public static final String CUSSFLAG_BKOABSTRACTINFO = "CussFlag";
  public static final String XRESFLAG_BKOABSTRACTINFO = "XresFlag";
  public static final String APPINFO_ELEMENT = "AppInfo";
  public static final String APPSTATUS_APPINFO = "AppStatus";
  public static final String CKININFOS_ELEMENT = "CkinInfos";
  public static final String CKININFO_ELEMENT = "CkinInfo";
  public static final String CKINCONTENT_CKININFO = "CkinContent";
  public static final String CKINCHANNEL_CKININFO = "CkinChannel";
  public static final String ASVCINFOS_ELEMENT = "AsvcInfos";
  public static final String ASVCINFO_ELEMENT = "AsvcInfo";
  public static final String COUPONNUM_ASVCINFO = "CouponNum";
  public static final String DOCUMENTNUM_ASVCINFO = "DocumentNum";
  public static final String SSRCODE_ASVCINFO = "SsrCode";
  public static final String ASVCSTATUS_ASVCINFO = "AsvcStatus";
  public static final String COMSERVICENAME_ASVCINFO = "ComServiceName";
  public static final String HISTORYINFOS_ELEMENT = "HistoryInfos";
  public static final String HISTORYINFO_ELEMENT = "HistoryInfo";
  public static final String HISCODE_HISTORYINFO = "hisCode";
  public static final String PSPTINFO_ELEMENT = "PsptInfo";
  public static final String PSPTNUMBER_PSPTINFO = "PSPTNumber";
  public static final String COUNTRYNAME_PSPTINFO = "CountryName";
  public static final String BIRTHDATE_PSPTINFO = "BirthDate";
  public static final String GENDER_PSPTINFO = "Gender";
  public static final String MULTIPASSENGER_PSPTINFO = "Multipassenger";
  public static final String APIINFO_ELEMENT = "ApiInfo";
  public static final String SURNAME_APIINFO = "SurName";
  public static final String GIVENNAME_APIINFO = "GivenName";
  public static final String GENDER_APIINFO = "Gender";
  public static final String DOCHOLDERNATIONALITY_APIINFO = "DocHolderNationality";
  public static final String BIRTHDATE_APIINFO = "BirthDate";
  public static final String DOCTYPE_APIINFO = "DocType";
  public static final String DOCID_APIINFO = "DocID";
  public static final String DOCISSUECOUNTRY_APIINFO = "DocIssueCountry";
  public static final String INFANTIND_APIINFO = "InfantInd";
  public static final String PRIMARYHOLDERIND_APIINFO = "PrimaryHolderInd";
  public static final String EXPIREDATE_APIINFO = "ExpireDate";
  public static final String BOUNDINFOS_ELEMENT = "BoundInfos";
  public static final String BOUNDINFO_ELEMENT = "BoundInfo";
  public static final String BOUNDTYPE_BOUNDINFO = "BoundType";
  public static final String AIRLINECODE_BOUNDINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_BOUNDINFO = "FlightNumber";
  public static final String FLIGHTDATE_BOUNDINFO = "FlightDate";
  public static final String CABINTYPE_BOUNDINFO = "CabinType";
  public static final String SEATINFO_BOUNDINFO = "SeatInfo";
  public static final String ARRIVALAIRPORT_BOUNDINFO = "ArrivalAirport";
  public static final String EDI_BOUNDINFO = "EDI";
  public static final String SEATMAP_ELEMENT = "SeInput";
  public static final String SEATMAP_ELEMENT_NEW = "FlightInfo";
  public static final String SEATMAP_AIRLINE = "AirlineCode";
  public static final String SEATMAP_FLIGHT = "FlightNumber";
  public static final String SEATMAP_FLTSUFFIX = "FlightSuffix";
  public static final String SEATMAP_FLIGHTORICITY = "FlightOriCity";
  public static final String SEATMAP_FLIGHTDESCITY = "FlightDesCity";
  public static final String SEATMAP_DATE = "FlightDate";
  public static final String SEATMAP_AIRPORT = "AirPort";
  public static final String SEATMAP_CABIN = "CabinType";
  public static final String SEATMAP_PAXELEMENT = "PaxInfo";
  public static final String SEATMAP_PAXORICITY = "PaxOriCity";
  public static final String SEATMAP_PAXDESCITY = "PaxDesCity";
  public static final String SEATMAP_PAXLEVEL = "PaxLevel";
  public static final String SEATMAP_PAXNUM = "PaxNum";
  public static final String SEATMAP_PAXCLASS = "Class";
  public static final String SEATMAP_PAXCHANNEL = "Channel";
  public static final String SEATMAP_PAXOPTTYPE = "OpType";
  public static final String SEATMAP_PAXMODE = "IsVerticalMode";
  public static final String SEATMAP_PAXFOCUSONTYPE = "focusOnType";
  public static final String SEATMAP_PAXPICFLAG = "Picflag";
  public static final String SEATMAP_PSGELEMENT = "PsgSeInput";
  public static final String SEATMAP_TKTNO = "TicketID";
  public static final String SEATMAP_SEQUENCE = "SequenceNumber";
  public static final String SEATMAP_OUTPUT = "SeatMap";
  public static final String SEATMAP_AIRLINECODE = "Airline";
  public static final String SEATMAP_DEPTCITY = "DepartureAirport";
  public static final String SEATMAP_DESTCITY = "ArrivalAirport";
  public static final String SEATMAP_LAYOUT = "FlightLayout";
  public static final String SEATMAP_TYPE = "AircraftType";
  public static final String SEATMAP_VERSION = "AircraftVersion";
  public static final String SEATMAP_PLAN = "FlightPlan";
  public static final String SEATMAP_PREASSIGNSNO = "preAssignSNO";
  public static final String PSGSEATMAP_OUTPUT = "FlightInfo";
  public static final String PSGSEATMAP_VERSION = "TypeV";
  public static final String PREFERSEAT_ELEMENT = "FlightInfo";
  public static final String PREFERSEAT_AIRLINE = "AirlineCode";
  public static final String PREFERSEAT_FLIGHT = "FlightNumber";
  public static final String PREFERSEAT_FLTSUFFIX = "FlightSuffix";
  public static final String PREFERSEAT_DATE = "FlightDate";
  public static final String PREFERSEAT_FLIGHTORICITY = "FlightOriCity";
  public static final String PREFERSEAT_FLIGHTDESCITY = "FlightDesCity";
  public static final String PREFERSEAT_AIRPORT = "AirPort";
  public static final String PREFERSEAT_CABIN = "CabinType";
  public static final String PREFERSEAT_PAXELEMENT = "PaxInfo";
  public static final String PREFERSEAT_PAXORICITY = "PaxOriCity";
  public static final String PREFERSEAT_PAXDESCITY = "PaxDesCity";
  public static final String PREFERSEAT_PAXLEVEL = "PaxLevel";
  public static final String PREFERSEAT_PAXCLASS = "Class";
  public static final String PREFERSEAT_SEATPREFER = "SeatPrefer";
  public static final String PREFERSEAT_OUTPUT = "TTL_OpenSeatRS";
  public static final String PREFERSEAT_ASSIGNNO = "assignNo";
  public static final String PREFERSEAT_PREASSIGNSNO = "preAssignNO";
  public static final String FLIGHTDETAILINFO_ELEMENT = "FlightDetailInfo";
  public static final String ISFLIGHTOPENED_FLIGHTDETAILINFO = "IsFlightOpened";
  public static final String BORDINGTIME_FLIGHTDETAILINFO = "BordingTime";
  public static final String SCHEDULEDDEPARTURETIME_FLIGHTDETAILINFO = "ScheduledDepartureTime";
  public static final String EXPECTEDDEPARTURETIME_FLIGHTDETAILINFO = "ExpectedDepartureTime";
  public static final String BOARDINGGATENUMBER_FLIGHTDETAILINFO = "BoardingGateNumber";
  public static final String PLANETYPE_FLIGHTDETAILINFO = "PlaneType";
  public static final String SLLISTINFO_ELEMENT = "SLListInfo";
  public static final String SLINFO_ELEMENT = "SLInfo";
  public static final String NAME_SLINFO = "Name";
  public static final String LIMITDESC_SLINFO = "LimitDesc";
  public static final String HLLISTINFO_ELEMENT = "HLListInfo";
  public static final String HLINFO_ELEMENT = "HLInfo";
  public static final String NAME_HLINFO = "Name";
  public static final String LIMITDESC_HLINFO = "LimitDesc";
  public static final String FLIGHTINFO_ELEMENT = "FlightInfo";
  public static final String FILINGAIRLINE_FLIGHTINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_FLIGHTINFO = "FlightNumber";
  public static final String DEPARTUREAIRPORT_FLIGHTINFO = "DepartureAirport";
  public static final String DEPARTUREDATE_FLIGHTINFO = "DepartureDate";
  public static final String TTL_AIRFLIGHTRS_SY_ELEMENT = "TTL_AirFlightRS";
  public static final String FLIGHTDETAILINFO_SY_ELEMENT = "FlightDetailInfo";
  public static final String CHECKINSTATUS_FLIGHTDETAILINFO_SY = "CheckInStatus";
  public static final String FLIGHTSTATUSHO_FLIGHTDETAILINFO_SY = "FlightStatusHO";
  public static final String CWT_FLIGHTDETAILINFO_SY = "CWT";
  public static final String UWT_FLIGHTDETAILINFO_SY = "UWT";
  public static final String CAW_FLIGHTDETAILINFO_SY = "CAW";
  public static final String UAW_FLIGHTDETAILINFO_SY = "UAW";
  public static final String WTN_FLIGHTDETAILINFO_SY = "WTN";
  public static final String GSLISTINFO_FLIGHTDETAILINFO_SY = "GSListInfo";
  public static final String GSINFO_FLIGHTDETAILINFO_SY = "GSInfo";
  public static final String GSINFO_NAME_FLIGHTDETAILINFO_SY = "Name";
  public static final String GSINFO_LIMITDESC_FLIGHTDETAILINFO_SY = "LimitDesc";
  public static final String AIRCRAFT_SY_ELEMENT = "Aircraft";
  public static final String WTNUNIT_FLIGHTDETAILINFO_SY = "WTNUNIT";
  public static final String STATUSECHO_FLIGHTDETAILINFO_SY = "STATUSECHO";
  public static final String HOLDTYPE_FLIGHTDETAILINFO_SY = "HoldType";
  public static final String SEGHOTYPES_AIRCRAFT_SY = "SegHoldTypes";
  public static final String SEGHOTYPE_AIRCRAFT_SY = "SegHoldType";
  public static final String DEPATURESTATION_SEGHOTYPES_SY = "DepatureStation";
  public static final String ARRIVALSTATION_SEGHOTYPES_SY = "ArrivalStation";
  public static final String HOLDTYPE_SEGHOTYPES_SY = "HoType";
  public static final String OPERATIONTYPE_SEGHOTYPES_SY = "OperationType";
  public static final String FLIGHTHOLDTYPES_AIRCRAFT_SY = "FlightHoldTypes";
  public static final String FLIGHTHOLDTYPE_AIRCRAFT_SY = "FlightHoldType";
  public static final String LEGHOTYPES_AIRCRAFT_SY = "LegHoldTypes";
  public static final String LEGHOTYPE_AIRCRAFT_SY = "LegHoldType";
  public static final String STATION_LEGHOTYPES_SY = "Station";
  public static final String HOLDTYPES_LEGHOTYPES_SY = "HoldTypes";
  public static final String HOLDTYPE_LEGHOTYPE_SY = "HoldType";
  public static final String HOTYPE_HOLDTYPES__SY = "HoType";
  public static final String HOSCOPE_HOLDTYPES__SY = "HoScope";
  public static final String OPERATIONTYPE_HOLDTYPES_SY = "OperationType";
  public static final String AIRCRAFTTYPE_AIRCRAFT_SY = "AircraftType";
  public static final String VERSION_AIRCRAFT_SY = "version";
  public static final String GTD_AIRCRAFT_SY = "GTD";
  public static final String POS_AIRCRAFT_SY = "POS";
  public static final String BN_AIRCRAFT_SY = "BN";
  public static final String AK_AIRCRAFT_SY = "AK";
  public static final String CD_AIRCRAFT_SY = "CD";
  public static final String SD_AIRCRAFT_SY = "SD";
  public static final String BDT_AIRCRAFT_SY = "BDT";
  public static final String DEPTTIME_AIRCRAFT_SY = "DeptTime";
  public static final String CNF_AIRCRAFT_SY = "CNF";
  public static final String CAP_AIRCRAFT_SY = "CAP";
  public static final String AV_AIRCRAFT_SY = "AV";
  public static final String PAD_AIRCRAFT_SY = "PAD";
  public static final String REGISTNUM_AIRCRAFT_SY = "REGISTNUM";
  public static final String EXTRAGATES_AIRCRAFT_SY = "ExtraGates";
  public static final String HL_AIRCRAFT_SY = "HL";
  public static final String SL_AIRCRAFT_SY = "SL";
  public static final String GS_AIRCRAFT_SY = "GS";
  public static final String ID_AIRCRAFT_SY = "Id";
  public static final String SEG_SY_ELEMENT = "Seg";
  public static final String SEGMENT_SEG_SY = "Segment";
  public static final String SEGPADINFO_SEG_SY = "SegPADInfo";
  public static final String RESNUM_SEG_SY = "RESNUM";
  public static final String RESFNUM_SEG_SY = "RESFNUM";
  public static final String RESCNUM_SEG_SY = "RESCNUM";
  public static final String RESYNUM_SEG_SY = "RESYNUM";
  public static final String CKINUM_SEG_SY = "CKINUM";
  public static final String CKIFNUM_SEG_SY = "CKIFNUM";
  public static final String CKICNUM_SEG_SY = "CKICNUM";
  public static final String CKIYNUM_SEG_SY = "CKIYNUM";
  public static final String BAGNUMWEIGHT_SEG_SY = "BAGNUMWEIGHT";
  public static final String BAGNUM_SEG_SY = "BAGNUM";
  public static final String UMNUM_SEG_SY = "UMNUM";
  public static final String WCH_SEG_SY = "WCH";
  public static final String SANUM_SEG_SY = "SANUM";
  public static final String SAFNUM_SEG_SY = "SAFNUM";
  public static final String SACNUM_SEG_SY = "SACNUM";
  public static final String SAYNUM_SEG_SY = "SAYNUM";
  public static final String EXSTNUM_SEG_SY = "EXSTNUM";
  public static final String EXSTFNUM_SEG_SY = "EXSTFNUM";
  public static final String EXSTCNUM_SEG_SY = "EXSTCNUM";
  public static final String EXSTYNUM_SEG_SY = "EXSTYNUM";
  public static final String XCRNUM_SEG_SY = "XCRNUM";
  public static final String XCRFNUM_SEG_SY = "XCRFNUM";
  public static final String XCRCNUM_SEG_SY = "XCRCNUM";
  public static final String XCRYNUM_SEG_SY = "XCRYNUM";
  public static final String SBNUM_SEG_SY = "SBNUM";
  public static final String SBFNUM_SEG_SY = "SBFNUM";
  public static final String SBCNUM_SEG_SY = "SBCNUM";
  public static final String SBYNUM_SEG_SY = "SBYNUM";
  public static final String SBB_SEG_SY = "SBB";
  public static final String BAGWEIGHT_SEG_SY = "BAGWEIGHT";
  public static final String BTNUM_SEG_SY = "BTNUM";
  public static final String BTFNUM_SEG_SY = "BTFNUM";
  public static final String BTCNUM_SEG_SY = "BTCNUM";
  public static final String BTYNUM_SEG_SY = "BTYNUM";
  public static final String RETNUM_SEG_SY = "RETNUM";
  public static final String RETFNUM_SEG_SY = "RETFNUM";
  public static final String RETCNUM_SEG_SY = "RETCNUM";
  public static final String RETYNUM_SEG_SY = "RETYNUM";
  public static final String CETNUM_SEG_SY = "CETNUM";
  public static final String CETFNUM_SEG_SY = "CETFNUM";
  public static final String CETCNUM_SEG_SY = "CETCNUM";
  public static final String CETYNUM_SEG_SY = "CETYNUM";
  public static final String VIPNUM_SEG_SY = "VIPNUM";
  public static final String VIPFNUM_SEG_SY = "VIPFNUM";
  public static final String VIPCNUM_SEG_SY = "VIPCNUM";
  public static final String VIPYNUM_SEG_SY = "VIPYNUM";
  public static final String UBNUMWEIGHT_SEG_SY = "UBNUMWEIGHT";
  public static final String UBNUM_SEG_SY = "UBNUM";
  public static final String UBWEIGHT_SEG_SY = "UBWEIGHT";
  public static final String INFNUM_SEG_SY = "INFNUM";
  public static final String BINFNUM_SEG_SY = "BINFNUM";
  public static final String BPAXNUM_SEG_SY = "BPAXNUM";
  public static final String SYDB_ELEMENT = "SyDBInfo";
  public static final String FILINGAIRLINE_SYDBINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_SYDBINFO = "FlightNumber";
  public static final String FLIGHTDATE_SYDBINFO = "FlightDate";
  public static final String FLIGHTTM_SYDBINFO = "FlightTime";
  public static final String DEPARTUREAIRPORT_SYDBINFO = "DepartureAirport";
  public static final String ARRIVEAIRPORT_SYDBINFO = "ArrivalAirport";
  public static final String STATUS_SYDBINFO = "Status";
  public static final String FLIGHTCONTROL_ELEMENT = "FlightInfo";
  public static final String FLIGHTCONTROL_FILINGAIRLINE = "AirlineCode";
  public static final String FLIGHTCONTROL_FLIGHTNUMBER = "FlightNumber";
  public static final String FLIGHTCONTROL_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String FLIGHTCONTROL_DESTAIRPORT = "DestAirport";
  public static final String FLIGHTCONTROL_CHANNEL = "Channel";
  public static final String FLIGHTCONTROL_OPTSERVICE = "OptService";
  public static final String FLIGHTCONTROL_STATUS = "Status";
  public static final String FLIGHTCONTROL_MTBEGINDAY = "MT_BeginDay";
  public static final String FLIGHTCONTROL_MTBEGINTIME = "MT_BeginTime";
  public static final String FLIGHTCONTROL_MTENDTIME = "MT_EndTime";
  public static final String FLIGHTCONTROL_TIMEZONEID = "TimeZoneID";
  public static final String FLIGHTCONTROL_MTTIMETYPE = "MT_TimeType";
  public static final String FLIGHTCONTROL_MTBEFOREHOURS = "MT_BeforeHours";
  public static final String FI_FLTDATE = "FltDate";
  public static final String FI_FLTATTR = "FltAttribute";
  public static final String FI_CONOFFICE = "ControlOffice";
  public static final String FI_STATIONS = "Stations";
  public static final String FI_STATION = "Station";
  public static final String FI_STATIONINDEX = "StaionIndex";
  public static final String FI_STATIONNAME = "StaionName";
  public static final String FI_DEPTTIME = "DeptTime";
  public static final String FI_DCSFLTTYPE = "DcsFltType";
  public static final String FI_DCSFLTTYPESUFFIX = "DcsTypeSuffix";
  public static final String FI_CNDNO = "CndNo";
  public static final String FI_HASMULTIGATE = "hasMultiGate";
  public static final String FI_ARRTIMECOMMENT = "ArrivalTimeComment";
  public static final String FI_ARRTIME = "ArrivalTime";
  public static final String FI_DEPTTIMECOMMET = "DeptTimeComment";
  public static final String FI_EQUIPNO = "EquipNo";
  public static final String FI_BOARDGATE = "Boardgate";
  public static final String AIRLINECODE_SEATRULE = "AirlineCode";
  public static final String CHANNEL_SEATRULE = "Channel";
  public static final String PAXLEVEL_SEATRULE = "PaxLevel";
  public static final String SEATRULE_ELEMENT = "SeatRule";
  public static final String RULE_ELEMENT = "Rule";
  public static final String RULETYPE_SEATRULE = "RuleType";
  public static final String SEATTYPE_SEATRULE = "SeatType";
  public static final String SPECIFIEDSEAT_SEATRULE = "SpecifiedSeat";
  public static final String SPECIFIEDROW_SEATRULE = "SpecifiedRow";
  public static final String TKTNUM_SEATRULE = "tktNum";
  public static final String BPPINFO_ELEMENT = "BppInfo";
  public static final String CHECKINSRC_EBPINFO = "CheckInSrc";
  public static final String PRINTSRC_EBPINFO = "PrintSrc";
  public static final String BPPTYPE_EBPINFO = "BppType";
  public static final String EBPTYPE_EBPINFO = "EbpType";
  public static final String BPPSTREAM_EBPINFO = "BppStream";
  public static final String EBPINFO_ELEMENT = "EbpInfo";
  public static final String EBPREAULT_EBPSTR = "EbpStr";
  public static final String BPP_TYPE = "6";
  public static final String CHECKCODE_QUERY = "QueryCheckCodeInput";
  public static final String CHECKCODE_ETNUMBER = "EtNumber";
  public static final String CHECKCODE_TOURINDEX = "TourIndex";
  public static final String CHECKCODE_OUPUT = "QueryCheckCodeOutput";
  public static final String CHECKCODE_CHECKCODE = "CheckCode";
  public static final String CHECKCODE_TELEPHONE = "Telephone";
  public static final String EDISE_SEINPUT = "SeInput";
  public static final String EDISE_AIRLINECODE = "AirlineCode";
  public static final String EDISE_FLIGHTNUMBER = "FlightNumber";
  public static final String EDISE_FLIGHTDATE = "FlightDate";
  public static final String EDISE_CABINTYPE = "CabinType";
  public static final String EDISE_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String EDISE_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String EDISE_TICKETNUM = "TicketNum";
  public static final String EDISE_RESULT = "Result";
  public static final String EDISE_RESULTCODE = "ResultCode";
  public static final String EDISE_RESULTSTR = "ResultString";
  public static final String EDISE_SEATMAP = "SeatMap";
  public static final String EDISE_AIRCRATYPE = "AircraftType";
  public static final String EDISE_FLIGHTPLAN = "FlightPlan";
  public static final String EDISE_AIRCRAFTVERSION = "AircraftVersion";
  public static final String EDISE_EDISEINPUT = "EdiSeInput";
  public static final String EDIHBPUO_FLIGHTINFO = "FlightInfo";
  public static final String EDIHBPUO_RPH = "RPH";
  public static final String EDIHBPUO_AIRLINECODE = "AirlineCode";
  public static final String EDIHBPUO_FLIGHTNUM = "FlightNumber";
  public static final String EDIHBPUO_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String EDIHBPUO_FLIGHTDATE = "FlightDate";
  public static final String EDIHBPUO_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String EDIHBPUO_PASSENGERINFO = "PassengerInfo";
  public static final String EDIHBPUO_SURNAME = "Surname";
  public static final String EDIHBPUO_PASSENGERFLIGHTINFO = "PassengerFlightInfo";
  public static final String EDIHBPUO_FLIGHTRPH = "FlightRPH";
  public static final String EDIHBPUO_PASSENGERRPH = "PassengerRPH";
  public static final String EDIHBPUO_HOSTNUM = "HostNumber";
  public static final String EDIHBPUO_CABINTYPE = "CabinType";
  public static final String EDIHBPUO_TICKETID = "TicketID";
  public static final String EDIHBPUO_SEQUENCENUMBER = "SequenceNumber";
  public static final String EDIHBPUO_SEATNO = "SeatNumber";
  public static final String EDIHBPUO_DATASTREAM = "DataStream";
  public static final String EDIHBPUO_DATASTREAMTEXT = "DataStreamText";
  public static final String EDIHBPUO_FFPINF = "FfpInf";
  public static final String EDIHBPUO_PROGRAMID = "ProgramID";
  public static final String EDIHBPUO_MENBERSHIPID = "MembershipID";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.FltSeatConStr
 * JD-Core Version:    0.6.0
 */