/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Reservation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6451150517608615555L;
/*    */   private String marriedSetNumber;
/*    */   private String bookingStatus;
/*    */ 
/*    */   public String getMarriedSetNumber()
/*    */   {
/* 30 */     return this.marriedSetNumber;
/*    */   }
/*    */ 
/*    */   public void setMarriedSetNumber(String marriedSetNumber)
/*    */   {
/* 37 */     this.marriedSetNumber = marriedSetNumber;
/*    */   }
/*    */ 
/*    */   public String getBookingStatus()
/*    */   {
/* 44 */     return this.bookingStatus;
/*    */   }
/*    */ 
/*    */   public void setBookingStatus(String bookingStatus)
/*    */   {
/* 51 */     this.bookingStatus = bookingStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Reservation
 * JD-Core Version:    0.6.0
 */