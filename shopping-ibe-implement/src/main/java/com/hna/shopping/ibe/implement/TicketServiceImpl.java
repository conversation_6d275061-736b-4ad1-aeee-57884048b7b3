package com.hna.shopping.ibe.implement;

import com.alibaba.fastjson.JSONObject;
import com.hna.eking.util.redis.lock.RedisSessionUtil;
import com.hna.shopping.ibe.common.constant.RedisKeyConstants;
import com.hna.shopping.ibe.common.util.ConvertUtil;
import com.hna.shopping.ibe.interfaces.TicketService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.TicketManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * pnr操作业务
 */
@Service
@Slf4j
public class TicketServiceImpl implements TicketService {

    @Autowired
    private TicketManager ticketManager;

    @Override
    @Retryable(maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public BaseResponse etrf(RefundTicketRequest request) {
        BaseResponse baseRes = new BaseResponse();
        Boolean exception = false;
        Boolean lock = false;
        try {
            // pnrNo为空则detr设值
            executeDetrSetPnrNo(request);
            // pnrNo加锁处理
            lock = executePnrNoLock(request.getPnrNo(), 10);
            // 操作etrf
            baseRes = ticketManager.etrf(request);
            // 反馈结果
            return baseRes;
        } catch (Exception e) {
            exception = true;
            log.error("service etrf error,params:{},error!", JSONObject.toJSONString(request), e);
            baseRes = new BaseResponse();
            baseRes.setSuccess(false);
            baseRes.setErrorCode(e.getClass().getSimpleName());
            baseRes.setErrorInfo(e.getMessage());
            return baseRes;
        } finally {
            executePnrNoUnlock(request.getPnrNo(), lock);
            if (exception) {
                throw new RuntimeException(baseRes.getErrorInfo());
            }
        }
    }

    /**
     * 加锁处理
     *
     * @param pnrNo
     * @param time
     * @return
     */
    private Boolean executePnrNoLock(String pnrNo, int time) {
        if (StringUtils.isNotBlank(pnrNo)) {
            Boolean lock = RedisSessionUtil.tryLock(RedisKeyConstants.LOCK_PNR_PREFIX + pnrNo, time);
            log.info("Redis handle,lock pnrNo:{},lock result:{},lock time:{}", pnrNo, lock, System.currentTimeMillis());
            if (!lock) {
                throw new RuntimeException("Redis pnrNo lock error !");
            }
        }
        return true;
    }

    /**
     * 解锁处理
     *
     * @param pnrNo
     * @param lock
     */
    private void executePnrNoUnlock(String pnrNo, Boolean lock) {
        log.info("Redis handle,unlock pnrNo:{},lock result:{},unlock time:{}", pnrNo, lock, System.currentTimeMillis());
        if (lock && StringUtils.isNotBlank(pnrNo)) RedisSessionUtil.unlock(RedisKeyConstants.LOCK_PNR_PREFIX + pnrNo);
    }

    /**
     * pnrNo为空则detr设值
     *
     * @param request
     */
    private void executeDetrSetPnrNo(RefundTicketRequest request) {
        if (StringUtils.isBlank(request.getPnrNo())) {
            DETRResponse detrRes = ticketManager.detr(ConvertUtil.map(request, DETRRequest.class));
            log.info("manager detr , result:{}", JSONObject.toJSONString(detrRes));
            if (Objects.nonNull(detrRes) && CollectionUtils.isNotEmpty(detrRes.getTicketInfos())
                    && CollectionUtils.isNotEmpty(detrRes.getTicketInfos().get(0).getSegs())) {
                for (DETRTktResult detrTktRes : detrRes.getTicketInfos()) {
                    for (DETRSeg detrSeg : detrTktRes.getSegs()) {
                        if (detrSeg.getSegmentIndex() == request.getSegID()) {
                            request.setPnrNo(detrSeg.getPnrNo());
                        }
                    }
                }
            }
        }
    }
}
