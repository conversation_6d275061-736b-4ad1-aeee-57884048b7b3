package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 通过 IBE 获取的票面信息
 */
@Data
public class IssueTicketSegment implements Serializable {


    private static final long serialVersionUID = 7618422725678647838L;
    private String flightNo;
    private String depCode;
    private String arrCode;
    private String depDate;

    private char fltClass;
    private String eiInfo;

    /**
     * 实付价
     */
    private BigDecimal adultNetFare;

    /**
     * 票面价
     */
    private BigDecimal adultMarketFare;

    /**
     * 燃油费
     */
    private BigDecimal adultFuelTax;

    /**
     * 基建费
     */
    private BigDecimal adultAirportTax;


    /**
     * 实付价
     */
    private BigDecimal childNetFare;

    /**
     * 票面价
     */
    private BigDecimal childMarketFare;

    /**
     * 燃油费
     */
    private BigDecimal childFuelTax;

    /**
     * 基建费
     */
    private BigDecimal childAirportTax;


    /**
     * 舱位
     */
    private String cabin;

    /**
     * 产品信息
     */
    private String productCode;
}
