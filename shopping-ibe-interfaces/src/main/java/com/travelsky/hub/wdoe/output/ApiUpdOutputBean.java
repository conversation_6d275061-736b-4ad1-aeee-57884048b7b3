/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ApiUpdOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3846324983885769210L;
/*    */   private long errorCode;
/*    */   private String errorMsg;
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 29 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(long errorCode)
/*    */   {
/* 42 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public long getErrorCode()
/*    */   {
/* 49 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 57 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 63 */     this.errorCode = 0L;
/* 64 */     this.errorMsg = "";
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.ApiUpdOutputBean
 * JD-Core Version:    0.6.0
 */