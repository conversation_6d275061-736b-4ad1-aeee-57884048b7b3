/*     */ package com.travelsky.hub.model.peentity.reserve.cancel.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class CancelReserveUpgRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -9008107132410589015L;
/*     */   private String reverseNumber;
/*     */   private String ocFlightNumber;
/*     */   private String ocAirlineCode;
/*     */   private String flightDate;
/*     */   private String ocFlightSuffix;
/*     */   private String arrivalAirport;
/*     */   private String departureAirport;
/*     */   private String ticketNumber;
/*     */   private String psrName;
/*     */   private String certNo;
/*     */   private String certType;
/*     */ 
/*     */   public void setReverseNumber(String reverseNumber)
/*     */   {
/*  69 */     this.reverseNumber = reverseNumber;
/*     */   }
/*     */ 
/*     */   public String getReverseNumber()
/*     */   {
/*  76 */     return this.reverseNumber;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineCode(String ocAirlineCode)
/*     */   {
/*  83 */     this.ocAirlineCode = ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineCode()
/*     */   {
/*  90 */     return this.ocAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/*  97 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/* 104 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/* 111 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/* 118 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 125 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 132 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 139 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 146 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 153 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 160 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 167 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 174 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 181 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 188 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setCertType(String certType)
/*     */   {
/* 195 */     this.certType = certType;
/*     */   }
/*     */ 
/*     */   public String getCertType()
/*     */   {
/* 202 */     return this.certType;
/*     */   }
/*     */ 
/*     */   public void setCertNo(String certNo)
/*     */   {
/* 209 */     this.certNo = certNo;
/*     */   }
/*     */ 
/*     */   public String getCertNo()
/*     */   {
/* 216 */     return this.certNo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.cancel.input.CancelReserveUpgRQ
 * JD-Core Version:    0.6.0
 */