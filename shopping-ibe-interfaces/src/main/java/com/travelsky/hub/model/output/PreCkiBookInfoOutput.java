/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PreCkiBookInfoOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String flightClass;
/*     */   private String passengerName;
/*     */   private String ticketID;
/*     */   private String sequenceNumber;
/*     */   private String pnr;
/*     */   private String seatNumber;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  34 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  42 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  51 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  59 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  71 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  79 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  88 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/*  96 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 105 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 113 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 120 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 127 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 135 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 143 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 151 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 159 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getPnr()
/*     */   {
/* 167 */     return this.pnr;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 174 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 182 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setPnr(String pnr)
/*     */   {
/* 190 */     this.pnr = pnr;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 198 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 206 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PreCkiBookInfoOutput
 * JD-Core Version:    0.6.0
 */