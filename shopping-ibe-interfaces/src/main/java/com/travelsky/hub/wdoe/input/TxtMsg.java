/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TxtMsg
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 17 */   private String msgType = "";
/*    */ 
/* 21 */   private String msgText = "";
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 27 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 34 */     this.msgType = msgType;
/*    */   }
/*    */ 
/*    */   public String getMsgText()
/*    */   {
/* 41 */     return this.msgText;
/*    */   }
/*    */ 
/*    */   public void setMsgText(String msgText)
/*    */   {
/* 48 */     this.msgText = msgText;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.TxtMsg
 * JD-Core Version:    0.6.0
 */