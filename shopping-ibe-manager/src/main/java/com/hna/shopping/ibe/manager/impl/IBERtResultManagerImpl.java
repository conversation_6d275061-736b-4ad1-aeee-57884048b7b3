package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.common.util.PassengerType;
import com.hna.shopping.ibe.interfaces.dto.AllChanTicketInfo;
import com.hna.shopping.ibe.manager.RtResultManager;
import com.travelsky.ibe.client.pnr.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Vector;

@Component("ibeRtResultManager")
@Slf4j
public class IBERtResultManagerImpl implements RtResultManager {
    @Override
    public Date getBirthday(RTResult rtResult, AllChanTicketInfo seg) {
        //先从ssr里拿
        Date date = getBirthdayFromSsr(rtResult, seg);
        //如果是婴儿票来提取客票信息，那么从rt结果中获取生日返回
        if(date == null && PassengerType.PASSENGER_3.getAlias().equals(seg.getPassengerType())){
            String psgId = getPsgrID(rtResult, seg);
            Vector<PNRInfant> pnrInfants = rtResult.getInfants();
            date = pnrInfants.stream().filter(pnrInfant -> psgId.equals(getPsgridFromInfant(pnrInfant))).map(PNRInfant::getBirth).findFirst().orElse(null);
        }
        return date;
    }


    @Override
    public Date getBirthdayFromSsr(RTResult rtResult, AllChanTicketInfo seg){
        //成人不能通过该方法获取生日
        if(PassengerType.PASSENGER_0.getAlias().equals(seg.getPassengerType())){
            return null;
        }
        if(rtResult == null){
            return null;
        }
        String psgId = getPsgrID(rtResult, seg);
        Vector<PNRSSR> ssrs = rtResult.getSsrs();
        try {
            return ssrs.stream().filter(pnrssr->{
                return ("CHLD".equals(pnrssr.getSSRType()) || "INFT".equals(pnrssr.getSSRType())) && (psgId).equals(getPsgrid(pnrssr));
            }).map(pnrssr->{
                String serverInfo = pnrssr.getServeInfo();
                //CHLD 8L HK1 21JUL13/P1
                //INFT Y8 HK1 SZXKWE 7523 N20APR YANG/XIAOXIAOCESHI 09DEC19/P1
                //现在的逻辑是取最后一组再按照/切割
                String[] tmps = serverInfo.split(" ");
                String birthStr = tmps[tmps.length-1];
                return DateUtil.stringToUsDateDDMMMyy(birthStr.split("/")[0]);
            }).findFirst().orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    //获取lineIndex，这里获取没有P

    @Override
    public Integer getPaxIndex(RTResult rtResult, AllChanTicketInfo seg){
        Vector<PNRPassenger> paggenserVector = rtResult.getPassengers();
        return paggenserVector.stream().filter(p->seg.getPassengerName().equals(p.getName())).map(PNRPassenger::getIndex).findFirst().orElse(0);
    }

    @Override
    public String getPsgrID(RTResult rtResult, AllChanTicketInfo seg){
        Vector<PNRTktNo> vector = rtResult.getTktnos();
        return vector.stream().filter(o->{
            String[] tickNos = o.getTktNo().split("-");
            String tickNo = tickNos.length > 1 ? tickNos[1] : tickNos[0];
            //处理票号可能有-也可能没有-
            return seg.getTicketNo().equals(tickNo) || seg.getTicketNo().equals(o.getTktNo());
        }).map(PNRTktNo::getPsgrID).findFirst().orElse("");
    }

    private String getPsgridFromInfant(PNRInfant pnrInfant){
        try {
            return pnrInfant.getPsgrid();
        }catch (Exception e){
            return null;
        }
    }

    //注意这里返回了null
    private String getPsgrid(PNRSSR pnrssr){
        try {
            return pnrssr.getPsgrid();
        }catch (Exception e){
            return null;
        }
    }
}
