/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Tax
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 580605993765298448L;
/*    */   private String amount;
/*    */   private String taxType;
/*    */   private String currencyCode;
/*    */   private String qualifier;
/*    */ 
/*    */   public String getAmount()
/*    */   {
/* 50 */     return this.amount;
/*    */   }
/*    */ 
/*    */   public void setAmount(String amount)
/*    */   {
/* 57 */     this.amount = amount;
/*    */   }
/*    */ 
/*    */   public String getTaxType()
/*    */   {
/* 64 */     return this.taxType;
/*    */   }
/*    */ 
/*    */   public void setTaxType(String taxType)
/*    */   {
/* 71 */     this.taxType = taxType;
/*    */   }
/*    */ 
/*    */   public String getCurrencyCode()
/*    */   {
/* 78 */     return this.currencyCode;
/*    */   }
/*    */ 
/*    */   public void setCurrencyCode(String currencyCode)
/*    */   {
/* 85 */     this.currencyCode = currencyCode;
/*    */   }
/*    */ 
/*    */   public String getQualifier()
/*    */   {
/* 92 */     return this.qualifier;
/*    */   }
/*    */ 
/*    */   public void setQualifier(String qualifier)
/*    */   {
/* 99 */     this.qualifier = qualifier;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Tax
 * JD-Core Version:    0.6.0
 */