/*     */ package com.travelsky.hub.model.peentity.trr.query.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class QueryRebookFlightRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2007736982694334142L;
/*     */   private String pnrNo;
/*     */   private String ticketNo;
/*     */   private String involuntaryIdentifier;
/*     */   private String airport;
/*     */   private String channel;
/*     */   private String splitPnr;
/*     */   private List<Segment> segments;
/*     */ 
/*     */   public String getPnrNo()
/*     */   {
/*  58 */     return this.pnrNo;
/*     */   }
/*     */ 
/*     */   public void setPnrNo(String pnrNo)
/*     */   {
/*  65 */     this.pnrNo = pnrNo;
/*     */   }
/*     */ 
/*     */   public String getTicketNo()
/*     */   {
/*  72 */     return this.ticketNo;
/*     */   }
/*     */ 
/*     */   public void setTicketNo(String ticketNo)
/*     */   {
/*  79 */     this.ticketNo = ticketNo;
/*     */   }
/*     */ 
/*     */   public String getInvoluntaryIdentifier()
/*     */   {
/*  86 */     return this.involuntaryIdentifier;
/*     */   }
/*     */ 
/*     */   public void setInvoluntaryIdentifier(String involuntaryIdentifier)
/*     */   {
/*  93 */     this.involuntaryIdentifier = involuntaryIdentifier;
/*     */   }
/*     */ 
/*     */   public String getAirport()
/*     */   {
/* 100 */     return this.airport;
/*     */   }
/*     */ 
/*     */   public void setAirport(String airport)
/*     */   {
/* 107 */     this.airport = airport;
/*     */   }
/*     */ 
/*     */   public String getChannel()
/*     */   {
/* 114 */     return this.channel;
/*     */   }
/*     */ 
/*     */   public void setChannel(String channel)
/*     */   {
/* 121 */     this.channel = channel;
/*     */   }
/*     */ 
/*     */   public String getSplitPnr()
/*     */   {
/* 128 */     return this.splitPnr;
/*     */   }
/*     */ 
/*     */   public void setSplitPnr(String splitPnr)
/*     */   {
/* 135 */     this.splitPnr = splitPnr;
/*     */   }
/*     */ 
/*     */   public List<Segment> getSegments()
/*     */   {
/* 142 */     return this.segments;
/*     */   }
/*     */ 
/*     */   public void setSegments(List<Segment> segments)
/*     */   {
/* 149 */     this.segments = segments;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.input.QueryRebookFlightRQ
 * JD-Core Version:    0.6.0
 */