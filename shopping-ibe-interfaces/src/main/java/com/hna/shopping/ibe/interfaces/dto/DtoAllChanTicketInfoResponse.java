package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel
public class DtoAllChanTicketInfoResponse extends BaseResponse  implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<AllChanTicketInfo> ticketInfos=new ArrayList<AllChanTicketInfo>();

}
