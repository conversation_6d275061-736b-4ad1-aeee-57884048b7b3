/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import com.travelsky.hub.wdoe.input.TxtMsg;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class UpdateInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  27 */   private String flightDate = "";
/*     */ 
/*  32 */   private String toCity = "";
/*     */ 
/*  37 */   private String psrClass = "";
/*     */ 
/*  42 */   private String passengerName = "";
/*     */ 
/*  47 */   private String tktNumber = "";
/*     */ 
/*  52 */   private String mseatNumber = "";
/*     */ 
/*  57 */   private String ffpAirlineCode = "";
/*     */ 
/*  61 */   private String tourIndex = "";
/*     */ 
/*  66 */   private String ffpCardNumber = "";
/*     */ 
/*  71 */   private String mFFPAirlineCode = "";
/*     */ 
/*  75 */   private String ffpCardPrior = "";
/*     */ 
/*  80 */   private String mFFPCardNumber = "";
/*     */ 
/*  85 */   private String certificateType = "";
/*     */ 
/*  89 */   private String mFFPCardPrior = "";
/*     */ 
/*  94 */   private String certificateNumber = "";
/*     */ 
/*  99 */   private String email = "";
/*     */ 
/* 103 */   private String telephoneNumber = "";
/*     */   private List<TxtMsg> msgList;
/* 113 */   private String isBppPrinted = "";
/*     */ 
/* 119 */   private String airlineCode = "";
/*     */ 
/* 253 */   private String fromCity = "";
/*     */ 
/* 271 */   private String flightNumber = "";
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 125 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 132 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String validate()
/*     */   {
/* 139 */     StringBuilder sb = new StringBuilder();
/* 140 */     if ((this.airlineCode == null) || (this.airlineCode.trim().length() == 0)) {
/* 141 */       sb.append("航空公司代码不能为空!");
/*     */     }
/* 143 */     else if ((this.flightNumber == null) || (this.flightNumber.trim().length() == 0)) {
/* 144 */       sb.append("航班号不能为空!");
/*     */     }
/* 146 */     else if ((this.flightDate == null) || (this.flightDate.trim().length() == 0)) {
/* 147 */       sb.append("航班日期不能为空!");
/*     */     }
/* 149 */     else if ((this.psrClass == null) || (this.psrClass.trim().length() == 0)) {
/* 150 */       sb.append("仓位不能为空!");
/*     */     }
/* 152 */     else if ((this.fromCity == null) || (this.fromCity.trim().length() == 0)) {
/* 153 */       sb.append("旅客出发城市不能为空!");
/*     */     }
/* 155 */     else if ((this.toCity == null) || (this.toCity.trim().length() == 0)) {
/* 156 */       sb.append("旅客到达城市不能为空!");
/*     */     }
/* 158 */     else if ((this.tourIndex == null) || (this.tourIndex.trim().length() == 0)) {
/* 159 */       sb.append("旅客ET行程序号不能为空!");
/*     */     }
/* 161 */     else if ((this.tktNumber == null) || (this.tktNumber.trim().length() == 0)) {
/* 162 */       sb.append("电子客票号不能为空!");
/*     */     }
/* 164 */     else if ((this.certificateType == null) || (this.certificateType.trim().length() == 0)) {
/* 165 */       sb.append("旅客证件类型不能为空!");
/*     */     }
/* 167 */     else if ((this.certificateNumber == null) || (this.certificateNumber.trim().length() == 0)) {
/* 168 */       sb.append("旅客证件号不能为空!");
/*     */     }
/* 170 */     else if (((this.mFFPCardNumber == null) || (this.mFFPCardNumber.trim().length() == 0)) && ((this.mseatNumber == null) || 
/* 171 */       (this.mseatNumber
/* 171 */       .trim().length() == 0)))
/*     */     {
/* 173 */       sb.append("请输入修改项！（常客卡号/座位号/CKIN项）!");
/*     */     }
/*     */ 
/* 176 */     return sb.toString();
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 183 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 190 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getEmail()
/*     */   {
/* 198 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/* 205 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 212 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 219 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 227 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 234 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 241 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 248 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 259 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 266 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 277 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 284 */     this.ffpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 291 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 298 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getIsBppPrinted()
/*     */   {
/* 305 */     return this.isBppPrinted;
/*     */   }
/*     */ 
/*     */   public void setIsBppPrinted(String isBppPrinted)
/*     */   {
/* 312 */     this.isBppPrinted = isBppPrinted;
/*     */   }
/*     */ 
/*     */   public String getMFFPCardNumber()
/*     */   {
/* 319 */     return this.mFFPCardNumber;
/*     */   }
/*     */ 
/*     */   public void setMFFPCardNumber(String cardNumber)
/*     */   {
/* 326 */     this.mFFPCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public String getMseatNumber()
/*     */   {
/* 334 */     return this.mseatNumber;
/*     */   }
/*     */ 
/*     */   public void setMFFPCardPrior(String cardPrior)
/*     */   {
/* 341 */     this.mFFPCardPrior = cardPrior;
/*     */   }
/*     */ 
/*     */   public String getMFFPAirlineCode()
/*     */   {
/* 348 */     return this.mFFPAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setMFFPAirlineCode(String airlineCode)
/*     */   {
/* 355 */     this.mFFPAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setMseatNumber(String mseatNumber)
/*     */   {
/* 362 */     this.mseatNumber = mseatNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 369 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 376 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getPsrClass()
/*     */   {
/* 383 */     return this.psrClass;
/*     */   }
/*     */ 
/*     */   public void setPsrClass(String psrClass)
/*     */   {
/* 390 */     this.psrClass = psrClass;
/*     */   }
/*     */ 
/*     */   public String getTelephoneNumber()
/*     */   {
/* 397 */     return this.telephoneNumber;
/*     */   }
/*     */ 
/*     */   public String getFFPCardPrior()
/*     */   {
/* 404 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFFPCardPrior(String cardPrior)
/*     */   {
/* 411 */     this.ffpCardPrior = cardPrior;
/*     */   }
/*     */ 
/*     */   public String getMFFPCardPrior()
/*     */   {
/* 418 */     return this.mFFPCardPrior;
/*     */   }
/*     */ 
/*     */   public void setTelephoneNumber(String telephoneNumber)
/*     */   {
/* 425 */     this.telephoneNumber = telephoneNumber;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 432 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 439 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 446 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 453 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 460 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 467 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public List<TxtMsg> getMsgList()
/*     */   {
/* 475 */     return this.msgList;
/*     */   }
/*     */ 
/*     */   public void setMsgList(List<TxtMsg> msgList)
/*     */   {
/* 483 */     this.msgList = msgList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.UpdateInputBean
 * JD-Core Version:    0.6.0
 */