package com.hna.shopping.ibe.interfaces.dto;

import java.util.ArrayList;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DETRResponse extends BaseResponse {

	
	
	
	 /**
	 * 
	 */
	private static final long serialVersionUID = -4788192616248243039L;
	private List<DETRTktResult> ticketInfos = new ArrayList<DETRTktResult>();                                                                                                                                                              

}
