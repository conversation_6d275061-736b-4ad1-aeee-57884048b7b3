/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class TdcResQueryOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8902329041305801630L;
/*    */   private List<TdcPassengerInfo> passengerInfos;
/*    */   private TdcError tdcError;
/*    */   private String transactionNumber;
/*    */ 
/*    */   public List<TdcPassengerInfo> getPassengerInfos()
/*    */   {
/* 37 */     return this.passengerInfos;
/*    */   }
/*    */ 
/*    */   public void setPassengerInfos(List<TdcPassengerInfo> passengerInfos)
/*    */   {
/* 45 */     this.passengerInfos = passengerInfos;
/*    */   }
/*    */ 
/*    */   public TdcError getTdcError()
/*    */   {
/* 53 */     return this.tdcError;
/*    */   }
/*    */ 
/*    */   public void setTdcError(TdcError tdcError)
/*    */   {
/* 61 */     this.tdcError = tdcError;
/*    */   }
/*    */ 
/*    */   public String getTransactionNumber()
/*    */   {
/* 69 */     return this.transactionNumber;
/*    */   }
/*    */ 
/*    */   public void setTransactionNumber(String transactionNumber)
/*    */   {
/* 77 */     this.transactionNumber = transactionNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TdcResQueryOutputBean
 * JD-Core Version:    0.6.0
 */