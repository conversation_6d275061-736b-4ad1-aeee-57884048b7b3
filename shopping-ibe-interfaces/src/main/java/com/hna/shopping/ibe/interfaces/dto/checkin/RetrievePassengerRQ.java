package com.hna.shopping.ibe.interfaces.dto.checkin; /**
 * <AUTHOR>
 * @date 2020/10/29
 */

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RetrievePassengerRQ extends BaseRequest implements Serializable {

    public List<PassengerInf> passengerInfs;

    @Data
    public static class PassengerInf{
        @ApiModelProperty("航班日期 yyyyMMDD或yyyy-MM-dd")
        private String flightDate;
        @ApiModelProperty("航班号 例如：CA1521（大小写均可）")
        private String flightNo;
        @ApiModelProperty("起飞城市三字码 大小写均可 ")
        private String fromCity;
        @ApiModelProperty("到达城市三字码 大小写均可 ")
        private String toCity;
        @ApiModelProperty("票号 不带“-” ")
        private String tkNo;
    }

}
