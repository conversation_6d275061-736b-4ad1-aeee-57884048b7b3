/*     */ package com.travelsky.hub.model.peentity.upgemds.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Service
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5683141600980751188L;
/*     */   private String serviceID;
/*     */   private String segmentID;
/*     */   private String subCode;
/*     */   private String inConnectionDocNbr;
/*     */   private String inConnectionNbr;
/*     */   private String hostNumber;
/*     */   private String svcAttendance;
/*     */   private String locationCode;
/*     */   private String locationDate;
/*     */   private String description;
/*     */ 
/*     */   public String getDescription()
/*     */   {
/*  71 */     return this.description;
/*     */   }
/*     */ 
/*     */   public void setDescription(String description)
/*     */   {
/*  78 */     this.description = description;
/*     */   }
/*     */ 
/*     */   public String getLocationDate()
/*     */   {
/*  85 */     return this.locationDate;
/*     */   }
/*     */ 
/*     */   public void setLocationDate(String locationDate)
/*     */   {
/*  92 */     this.locationDate = locationDate;
/*     */   }
/*     */ 
/*     */   public String getLocationCode()
/*     */   {
/*  99 */     return this.locationCode;
/*     */   }
/*     */ 
/*     */   public void setLocationCode(String locationCode)
/*     */   {
/* 106 */     this.locationCode = locationCode;
/*     */   }
/*     */ 
/*     */   public String getServiceID()
/*     */   {
/* 113 */     return this.serviceID;
/*     */   }
/*     */ 
/*     */   public void setServiceID(String serviceID)
/*     */   {
/* 120 */     this.serviceID = serviceID;
/*     */   }
/*     */ 
/*     */   public String getSegmentID()
/*     */   {
/* 127 */     return this.segmentID;
/*     */   }
/*     */ 
/*     */   public void setSegmentID(String segmentID)
/*     */   {
/* 134 */     this.segmentID = segmentID;
/*     */   }
/*     */ 
/*     */   public String getSubCode()
/*     */   {
/* 141 */     return this.subCode;
/*     */   }
/*     */ 
/*     */   public void setSubCode(String subCode)
/*     */   {
/* 148 */     this.subCode = subCode;
/*     */   }
/*     */ 
/*     */   public String getInConnectionDocNbr()
/*     */   {
/* 155 */     return this.inConnectionDocNbr;
/*     */   }
/*     */ 
/*     */   public void setInConnectionDocNbr(String inConnectionDocNbr)
/*     */   {
/* 162 */     this.inConnectionDocNbr = inConnectionDocNbr;
/*     */   }
/*     */ 
/*     */   public String getInConnectionNbr()
/*     */   {
/* 169 */     return this.inConnectionNbr;
/*     */   }
/*     */ 
/*     */   public void setInConnectionNbr(String inConnectionNbr)
/*     */   {
/* 176 */     this.inConnectionNbr = inConnectionNbr;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 183 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 190 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getSvcAttendance()
/*     */   {
/* 197 */     return this.svcAttendance;
/*     */   }
/*     */ 
/*     */   public void setSvcAttendance(String svcAttendance)
/*     */   {
/* 204 */     this.svcAttendance = svcAttendance;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Service
 * JD-Core Version:    0.6.0
 */