/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class HaqqInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 5001519842214896226L;
/*    */   private String destCity;
/*    */   private String hostID;
/*    */   private String msgType;
/*    */   private String ckiStatus;
/*    */ 
/*    */   public String getDestCity()
/*    */   {
/* 36 */     return this.destCity;
/*    */   }
/*    */ 
/*    */   public void setDestCity(String destCity)
/*    */   {
/* 43 */     this.destCity = destCity;
/*    */   }
/*    */ 
/*    */   public String getHostID()
/*    */   {
/* 50 */     return this.hostID;
/*    */   }
/*    */ 
/*    */   public void setHostID(String hostID)
/*    */   {
/* 57 */     this.hostID = hostID;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 65 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 72 */     this.msgType = msgType;
/*    */   }
/*    */   public String getCkiStatus() {
/* 75 */     return this.ckiStatus;
/*    */   }
/*    */   public void setCkiStatus(String ckiStatus) {
/* 78 */     this.ckiStatus = ckiStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.HaqqInputBean
 * JD-Core Version:    0.6.0
 */