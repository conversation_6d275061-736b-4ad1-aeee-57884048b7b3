package com.hna.shopping.ibe.interfaces.dto;

import java.util.List;

import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class AVResponse extends BaseResponse {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3045965896705111073L;
	private List<AVSegment> segments = Lists.newArrayList();

}
