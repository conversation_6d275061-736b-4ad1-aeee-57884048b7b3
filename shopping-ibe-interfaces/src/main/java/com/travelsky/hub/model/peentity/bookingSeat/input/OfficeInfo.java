/*    */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class OfficeInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1884209002502211310L;
/*    */   private String airline;
/*    */   private String station;
/*    */ 
/*    */   public String getAirline()
/*    */   {
/* 32 */     return this.airline;
/*    */   }
/*    */ 
/*    */   public String getStation()
/*    */   {
/* 44 */     return this.station;
/*    */   }
/*    */ 
/*    */   public void setAirline(String airline)
/*    */   {
/* 50 */     this.airline = airline;
/*    */   }
/*    */ 
/*    */   public void setStation(String station)
/*    */   {
/* 57 */     this.station = station;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.OfficeInfo
 * JD-Core Version:    0.6.0
 */