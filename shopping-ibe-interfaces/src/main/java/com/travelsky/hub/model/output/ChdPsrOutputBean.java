/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import com.travelsky.hub.wdoe.output.ChdCheckinPsgInfo;
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class ChdPsrOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 23 */   private String checkCode = "";
/*    */   private List<ChdCheckinPsgInfo> chdCheckinPsgList;
/*    */ 
/*    */   public String getCheckCode()
/*    */   {
/* 29 */     return this.checkCode;
/*    */   }
/*    */ 
/*    */   public void setChdCheckinPsgList(List<ChdCheckinPsgInfo> chdCheckinPsgList)
/*    */   {
/* 40 */     this.chdCheckinPsgList = chdCheckinPsgList;
/*    */   }
/*    */ 
/*    */   public List<ChdCheckinPsgInfo> getChdCheckinPsgList()
/*    */   {
/* 47 */     return this.chdCheckinPsgList;
/*    */   }
/*    */ 
/*    */   public void setCheckCode(String checkCode)
/*    */   {
/* 55 */     this.checkCode = checkCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ChdPsrOutputBean
 * JD-Core Version:    0.6.0
 */