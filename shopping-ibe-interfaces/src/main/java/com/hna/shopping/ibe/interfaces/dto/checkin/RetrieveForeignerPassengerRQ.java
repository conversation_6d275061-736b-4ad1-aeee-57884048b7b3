package com.hna.shopping.ibe.interfaces.dto.checkin; /**
 * <AUTHOR>
 * @date 2020/10/29
 */

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class RetrieveForeignerPassengerRQ implements Serializable {

    @ApiModelProperty("航空公司代码")
    private String airlineCode;
    @ApiModelProperty("航班号")
    private String flightNumber;
    @ApiModelProperty("航班日期")
    private String flightDate;
    @ApiModelProperty("到达机场")
    private String toCity;
    @ApiModelProperty("出发机场")
    private String fromCity;
    @ApiModelProperty("票号")
    private String tkNo;
    @ApiModelProperty("主机序号")
    private String hostNumber;

}
