/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class BagInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7090665578113719585L;
/*     */   private String thisDepartureAirport;
/*     */   private String departureAirportOrder;
/*     */   private String destinationAirportOrder;
/*     */   private String tagTotalNumber;
/*     */   private List<BagTagInfo> bagTagInfos;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private List<String> bagRemarks;
/*     */   private String bagDestination;
/*     */ 
/*     */   public String getThisDepartureAirport()
/*     */   {
/*  58 */     return this.thisDepartureAirport;
/*     */   }
/*     */ 
/*     */   public void setThisDepartureAirport(String thisDepartureAirport)
/*     */   {
/*  65 */     this.thisDepartureAirport = thisDepartureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirportOrder()
/*     */   {
/*  72 */     return this.departureAirportOrder;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirportOrder(String departureAirportOrder)
/*     */   {
/*  79 */     this.departureAirportOrder = departureAirportOrder;
/*     */   }
/*     */ 
/*     */   public String getDestinationAirportOrder()
/*     */   {
/*  86 */     return this.destinationAirportOrder;
/*     */   }
/*     */ 
/*     */   public void setDestinationAirportOrder(String destinationAirportOrder)
/*     */   {
/*  93 */     this.destinationAirportOrder = destinationAirportOrder;
/*     */   }
/*     */ 
/*     */   public String getTagTotalNumber()
/*     */   {
/* 100 */     return this.tagTotalNumber;
/*     */   }
/*     */ 
/*     */   public void setTagTotalNumber(String tagTotalNumber)
/*     */   {
/* 107 */     this.tagTotalNumber = tagTotalNumber;
/*     */   }
/*     */ 
/*     */   public List<BagTagInfo> getBagTagInfos()
/*     */   {
/* 114 */     return this.bagTagInfos;
/*     */   }
/*     */ 
/*     */   public void setBagTagInfos(List<BagTagInfo> bagTagInfos)
/*     */   {
/* 121 */     this.bagTagInfos = bagTagInfos;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 128 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 135 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 142 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 149 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public List<String> getBagRemarks()
/*     */   {
/* 156 */     return this.bagRemarks;
/*     */   }
/*     */ 
/*     */   public void setBagRemarks(List<String> bagRemarks)
/*     */   {
/* 163 */     this.bagRemarks = bagRemarks;
/*     */   }
/*     */ 
/*     */   public String getBagDestination()
/*     */   {
/* 170 */     return this.bagDestination;
/*     */   }
/*     */ 
/*     */   public void setBagDestination(String bagDestination)
/*     */   {
/* 177 */     this.bagDestination = bagDestination;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BagInfo
 * JD-Core Version:    0.6.0
 */