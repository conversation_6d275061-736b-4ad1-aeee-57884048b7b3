/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PsrCheckInResult
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String psrName;
/*     */   private List<String> deptAirport;
/*     */   private List<String> boardStream;
/*     */   private List<String> seatNo;
/*     */   private List<String> boardingNumber;
/*     */   private String cabin;
/*     */   private String checkCode;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private String boardingTime;
/*     */   private String boardingGateNumber;
/*     */   private List<StopOverInfo> stopOverInfos;
/*     */   private String standByNumber;
/*     */   private String pstCkiStatus;
/*     */ 
/*     */   public String getCheckCode()
/*     */   {
/*  56 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */   public void setCheckCode(String checkCode)
/*     */   {
/*  63 */     this.checkCode = checkCode;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  70 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  77 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  84 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  91 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  98 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 105 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 112 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 119 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public List<String> getDeptAirport()
/*     */   {
/* 126 */     return this.deptAirport;
/*     */   }
/*     */ 
/*     */   public void setDeptAirport(List<String> deptAirport)
/*     */   {
/* 133 */     this.deptAirport = deptAirport;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardStream()
/*     */   {
/* 140 */     return this.boardStream;
/*     */   }
/*     */ 
/*     */   public void setBoardStream(List<String> boardStream)
/*     */   {
/* 147 */     this.boardStream = boardStream;
/*     */   }
/*     */ 
/*     */   public List<String> getSeatNo()
/*     */   {
/* 154 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(List<String> seatNo)
/*     */   {
/* 161 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardingNumber()
/*     */   {
/* 168 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(List<String> boardingNumber)
/*     */   {
/* 175 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 182 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 190 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 197 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 205 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOverInfos()
/*     */   {
/* 213 */     return this.stopOverInfos;
/*     */   }
/*     */ 
/*     */   public void setStopOverInfos(List<StopOverInfo> stopOverInfos)
/*     */   {
/* 221 */     this.stopOverInfos = stopOverInfos;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 228 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 236 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 244 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 252 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getStandByNumber()
/*     */   {
/* 259 */     return this.standByNumber;
/*     */   }
/*     */ 
/*     */   public void setStandByNumber(String standByNumber)
/*     */   {
/* 267 */     this.standByNumber = standByNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 275 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 283 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public String getPstCkiStatus()
/*     */   {
/* 291 */     return this.pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPstCkiStatus(String pstCkiStatus)
/*     */   {
/* 299 */     this.pstCkiStatus = pstCkiStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PsrCheckInResult
 * JD-Core Version:    0.6.0
 */