/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class ConnCkiResult
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private List<String> seatNo;
/*     */   private String chnName;
/*     */   private List<String> boardingNumber;
/*     */   private String surname;
/*     */   private List<String> boardStream;
/*     */   private String tktNumber;
/*     */   private String docId;
/*     */ 
/*     */   public String getChnName()
/*     */   {
/*  49 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/*  56 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getSurname()
/*     */   {
/*  63 */     return this.surname;
/*     */   }
/*     */ 
/*     */   public void setSurname(String surname)
/*     */   {
/*  70 */     this.surname = surname;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardStream()
/*     */   {
/*  77 */     return this.boardStream;
/*     */   }
/*     */ 
/*     */   public void setBoardStream(List<String> boardStream)
/*     */   {
/*  84 */     this.boardStream = boardStream;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/*  91 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/*  98 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public List<String> getSeatNo()
/*     */   {
/* 105 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(List<String> seatNo)
/*     */   {
/* 112 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardingNumber()
/*     */   {
/* 119 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(List<String> boardingNumber)
/*     */   {
/* 126 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getDocId()
/*     */   {
/* 133 */     return this.docId;
/*     */   }
/*     */ 
/*     */   public void setDocId(String docId)
/*     */   {
/* 140 */     this.docId = docId;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ConnCkiResult
 * JD-Core Version:    0.6.0
 */