/*    */ package com.travelsky.hub.model.peentity.meal.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryMealInventoryRQ
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1974932268111568352L;
/*    */   private OfficeInfo officeInfo;
/*    */   private String ocAirlineID;
/*    */   private String ocFlightNumber;
/*    */   private String ocFlightSuffix;
/*    */   private String departureDate;
/*    */   private String departureAirport;
/*    */   private String arrivalAirport;
/*    */ 
/*    */   public OfficeInfo getOfficeInfo()
/*    */   {
/* 45 */     return this.officeInfo;
/*    */   }
/*    */   public String getDepartureDate() {
/* 48 */     return this.departureDate;
/*    */   }
/*    */ 
/*    */   public void setDepartureDate(String departureDate) {
/* 52 */     this.departureDate = departureDate;
/*    */   }
/*    */ 
/*    */   public void setOfficeInfo(OfficeInfo officeInfo) {
/* 56 */     this.officeInfo = officeInfo;
/*    */   }
/*    */ 
/*    */   public String getOcAirlineID() {
/* 60 */     return this.ocAirlineID;
/*    */   }
/*    */ 
/*    */   public void setOcAirlineID(String ocAirlineID) {
/* 64 */     this.ocAirlineID = ocAirlineID;
/*    */   }
/*    */   public String getArrivalAirport() {
/* 67 */     return this.arrivalAirport;
/*    */   }
/*    */ 
/*    */   public void setArrivalAirport(String arrivalAirport) {
/* 71 */     this.arrivalAirport = arrivalAirport;
/*    */   }
/*    */ 
/*    */   public String getOcFlightNumber() {
/* 75 */     return this.ocFlightNumber;
/*    */   }
/*    */ 
/*    */   public void setOcFlightNumber(String ocFlightNumber) {
/* 79 */     this.ocFlightNumber = ocFlightNumber;
/*    */   }
/*    */ 
/*    */   public String getOcFlightSuffix() {
/* 83 */     return this.ocFlightSuffix;
/*    */   }
/*    */ 
/*    */   public void setOcFlightSuffix(String ocFlightSuffix) {
/* 87 */     this.ocFlightSuffix = ocFlightSuffix;
/*    */   }
/*    */ 
/*    */   public String getDepartureAirport() {
/* 91 */     return this.departureAirport;
/*    */   }
/*    */ 
/*    */   public void setDepartureAirport(String departureAirport) {
/* 95 */     this.departureAirport = departureAirport;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.QueryMealInventoryRQ
 * JD-Core Version:    0.6.0
 */