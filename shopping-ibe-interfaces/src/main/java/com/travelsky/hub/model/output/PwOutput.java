/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PwOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9205215940657486699L;
/*    */   private String errorMsg;
/*    */   private long errorCode;
/*    */ 
/*    */   public PwOutput(long errorCode, String errorMsg)
/*    */   {
/* 31 */     this.errorCode = errorCode;
/* 32 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 38 */     this.errorCode = 0L;
/* 39 */     this.errorMsg = "";
/*    */   }
/*    */ 
/*    */   public void setErrorCode(long errorCode)
/*    */   {
/* 46 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public long getErrorCode()
/*    */   {
/* 53 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 60 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 67 */     return this.errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PwOutput
 * JD-Core Version:    0.6.0
 */