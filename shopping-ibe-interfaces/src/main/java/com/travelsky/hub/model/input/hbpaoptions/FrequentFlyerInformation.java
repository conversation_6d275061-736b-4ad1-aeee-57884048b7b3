/*    */ package com.travelsky.hub.model.input.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FrequentFlyerInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4859143287746884195L;
/*    */   private String frequentFlyerType;
/*    */   private String frequentFlyerAirline;
/*    */   private String frequentFlyerCard;
/*    */ 
/*    */   public String getFrequentFlyerType()
/*    */   {
/* 35 */     return this.frequentFlyerType;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerType(String frequentFlyerType)
/*    */   {
/* 42 */     this.frequentFlyerType = frequentFlyerType;
/*    */   }
/*    */ 
/*    */   public String getFrequentFlyerAirline()
/*    */   {
/* 49 */     return this.frequentFlyerAirline;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerAirline(String frequentFlyerAirline)
/*    */   {
/* 56 */     this.frequentFlyerAirline = frequentFlyerAirline;
/*    */   }
/*    */ 
/*    */   public String getFrequentFlyerCard()
/*    */   {
/* 63 */     return this.frequentFlyerCard;
/*    */   }
/*    */ 
/*    */   public void setFrequentFlyerCard(String frequentFlyerCard)
/*    */   {
/* 70 */     this.frequentFlyerCard = frequentFlyerCard;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.FrequentFlyerInformation
 * JD-Core Version:    0.6.0
 */