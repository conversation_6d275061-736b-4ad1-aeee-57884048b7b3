/*     */ package com.travelsky.hub.model.output.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerAttribute
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5745454663204220998L;
/*     */   private List<String> additionalServicesCode;
/*     */   private String freeBaggageAllowance;
/*     */   private List<String> upgradeOrDowngradeInformation;
/*     */   private List<String> specialServicesInformation;
/*     */   private String chineseName;
/*     */   private String checkinChannel;
/*     */   private List<String> transferOrRecheckFlag;
/*     */   private String staffFlag;
/*     */   private String specialPassenger;
/*     */   private String skyTeamPriority;
/*     */   private String hostNumber;
/*     */   private String passengerWeight;
/*     */   private String passengerType;
/*     */ 
/*     */   public List<String> getAdditionalServicesCode()
/*     */   {
/*  54 */     return this.additionalServicesCode;
/*     */   }
/*     */ 
/*     */   public void setAdditionalServicesCode(List<String> additionalServicesCode)
/*     */   {
/*  61 */     this.additionalServicesCode = additionalServicesCode;
/*     */   }
/*     */ 
/*     */   public String getFreeBaggageAllowance()
/*     */   {
/*  68 */     return this.freeBaggageAllowance;
/*     */   }
/*     */ 
/*     */   public void setFreeBaggageAllowance(String freeBaggageAllowance)
/*     */   {
/*  75 */     this.freeBaggageAllowance = freeBaggageAllowance;
/*     */   }
/*     */ 
/*     */   public List<String> getUpgradeOrDowngradeInformation()
/*     */   {
/*  82 */     return this.upgradeOrDowngradeInformation;
/*     */   }
/*     */ 
/*     */   public void setUpgradeOrDowngradeInformation(List<String> upgradeOrDowngradeInformation)
/*     */   {
/*  89 */     this.upgradeOrDowngradeInformation = upgradeOrDowngradeInformation;
/*     */   }
/*     */ 
/*     */   public List<String> getSpecialServicesInformation()
/*     */   {
/*  96 */     return this.specialServicesInformation;
/*     */   }
/*     */ 
/*     */   public void setSpecialServicesInformation(List<String> specialServicesInformation)
/*     */   {
/* 103 */     this.specialServicesInformation = specialServicesInformation;
/*     */   }
/*     */ 
/*     */   public String getChineseName()
/*     */   {
/* 110 */     return this.chineseName;
/*     */   }
/*     */ 
/*     */   public void setChineseName(String chineseName)
/*     */   {
/* 117 */     this.chineseName = chineseName;
/*     */   }
/*     */ 
/*     */   public String getCheckinChannel()
/*     */   {
/* 124 */     return this.checkinChannel;
/*     */   }
/*     */ 
/*     */   public void setCheckinChannel(String checkinChannel)
/*     */   {
/* 131 */     this.checkinChannel = checkinChannel;
/*     */   }
/*     */ 
/*     */   public List<String> getTransferOrRecheckFlag()
/*     */   {
/* 138 */     return this.transferOrRecheckFlag;
/*     */   }
/*     */ 
/*     */   public void setTransferOrRecheckFlag(List<String> transferOrRecheckFlag)
/*     */   {
/* 145 */     this.transferOrRecheckFlag = transferOrRecheckFlag;
/*     */   }
/*     */ 
/*     */   public String getStaffFlag()
/*     */   {
/* 152 */     return this.staffFlag;
/*     */   }
/*     */ 
/*     */   public void setStaffFlag(String staffFlag)
/*     */   {
/* 159 */     this.staffFlag = staffFlag;
/*     */   }
/*     */ 
/*     */   public String getSpecialPassenger()
/*     */   {
/* 166 */     return this.specialPassenger;
/*     */   }
/*     */ 
/*     */   public void setSpecialPassenger(String specialPassenger)
/*     */   {
/* 173 */     this.specialPassenger = specialPassenger;
/*     */   }
/*     */ 
/*     */   public String getSkyTeamPriority()
/*     */   {
/* 180 */     return this.skyTeamPriority;
/*     */   }
/*     */ 
/*     */   public void setSkyTeamPriority(String skyTeamPriority)
/*     */   {
/* 187 */     this.skyTeamPriority = skyTeamPriority;
/*     */   }
/*     */ 
/*     */   public String getPassengerType()
/*     */   {
/* 195 */     return this.passengerType;
/*     */   }
/*     */ 
/*     */   public void setPassengerType(String passengerType)
/*     */   {
/* 202 */     this.passengerType = passengerType;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 209 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 216 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerWeight()
/*     */   {
/* 223 */     return this.passengerWeight;
/*     */   }
/*     */ 
/*     */   public void setPassengerWeight(String passengerWeight)
/*     */   {
/* 230 */     this.passengerWeight = passengerWeight;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.PassengerAttribute
 * JD-Core Version:    0.6.0
 */