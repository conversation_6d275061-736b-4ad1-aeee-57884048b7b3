/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class QueryPassengerInfoInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1055277786477781302L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String dptAptCode;
/*     */   private String flightDate;
/*     */   private String etCode;
/*     */   private String boardingNumber;
/*     */   private String seatNumber;
/*     */   private String bagTagNumber;
/*     */   private String operateFlag;
/*     */ 
/*     */   public String getBagTagNumber()
/*     */   {
/*  65 */     return this.bagTagNumber;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumber(String bagTagNumber)
/*     */   {
/*  72 */     this.bagTagNumber = bagTagNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  79 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  86 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  93 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 100 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDptAptCode()
/*     */   {
/* 107 */     return this.dptAptCode;
/*     */   }
/*     */ 
/*     */   public void setDptAptCode(String dptAptCode)
/*     */   {
/* 114 */     this.dptAptCode = dptAptCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 121 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 128 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/* 135 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/* 142 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 149 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 156 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 163 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 170 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getOperateFlag()
/*     */   {
/* 177 */     return this.operateFlag;
/*     */   }
/*     */ 
/*     */   public void setOperateFlag(String operateFlag)
/*     */   {
/* 184 */     this.operateFlag = operateFlag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.QueryPassengerInfoInputBean
 * JD-Core Version:    0.6.0
 */