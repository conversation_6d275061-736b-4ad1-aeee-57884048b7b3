/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SeatOrderRecord
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7156712582643923633L;
/*    */   private List<Operation> operations;
/*    */   private String errorCode;
/*    */   private String errorMsg;
/*    */ 
/*    */   public List<Operation> getOperations()
/*    */   {
/* 28 */     return this.operations;
/*    */   }
/*    */ 
/*    */   public void setOperations(List<Operation> operations)
/*    */   {
/* 35 */     this.operations = operations;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 42 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 49 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 56 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 63 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatOrderRecord
 * JD-Core Version:    0.6.0
 */