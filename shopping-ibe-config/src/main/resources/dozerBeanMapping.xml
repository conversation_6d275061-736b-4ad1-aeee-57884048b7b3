<?xml version="1.0" encoding="UTF-8"?>
<mappings xmlns="http://dozer.sourceforge.net" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		  xsi:schemaLocation="http://dozer.sourceforge.net
          http://dozer.sourceforge.net/schema/beanmapping.xsd">

	<!--使用说明：http://dozer.sourceforge.net/documentation/mappingclasses.html -->

	<!-- 	<mapping> -->
	<!-- 		<class-a>com.travelsky.ibe.client.pnr.PNRObject</class-a> -->
	<!-- 		<class-b>com.hna.shopping.ibe.interfaces.dto.PNRSegment</class-b> -->
	<!-- 		<field-exclude > -->
	<!-- 			<a>psgrid</a> -->
	<!-- 			<b>psgrid</b> -->
	<!-- 		</field-exclude> -->
	<!-- 	</mapping> -->

	<mapping>

		<class-a>com.travelsky.ibe.client.pnr.RTResult</class-a>

		<class-b>com.hna.shopping.ibe.interfaces.dto.RTResult</class-b>

		<field>

			<a>ssrs</a>

			<b>ssrs</b>

			<a-hint>com.travelsky.ibe.client.pnr.PNRSSR_FOID,com.travelsky.ibe.client.pnr.PNRSSR_DOCS,com.travelsky.ibe.client.pnr.PNRSSR_FQTV,com.travelsky.ibe.client.pnr.PNRSSR_PSPT,com.travelsky.ibe.client.pnr.PNRSSR_RQST,com.travelsky.ibe.client.pnr.PNRSSR_SEAT,com.travelsky.ibe.client.pnr.PNRSSR_TKNE,com.travelsky.ibe.client.pnr.PNRSSR</a-hint>

			<b-hint>com.hna.shopping.ibe.interfaces.dto.PNRSSR_FOID,com.hna.shopping.ibe.interfaces.dto.PNRSSR_DOCS,com.hna.shopping.ibe.interfaces.dto.PNRSSR_FQTV,com.hna.shopping.ibe.interfaces.dto.PNRSSR_PSPT,com.hna.shopping.ibe.interfaces.dto.PNRSSR_RQST,com.hna.shopping.ibe.interfaces.dto.PNRSSR_SEAT,com.hna.shopping.ibe.interfaces.dto.PNRSSR_TKNE,com.hna.shopping.ibe.interfaces.dto.PNRSSR</b-hint>

		</field>

	</mapping>

</mappings>