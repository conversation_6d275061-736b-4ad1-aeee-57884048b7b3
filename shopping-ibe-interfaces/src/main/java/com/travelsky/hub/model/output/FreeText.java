/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FreeText
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 5412167690234731259L;
/*    */   private String textCode;
/*    */   private String textContent;
/*    */ 
/*    */   public String getTextCode()
/*    */   {
/* 33 */     return this.textCode;
/*    */   }
/*    */ 
/*    */   public String getTextContent()
/*    */   {
/* 41 */     return this.textContent;
/*    */   }
/*    */ 
/*    */   public void setTextCode(String textCode)
/*    */   {
/* 49 */     this.textCode = textCode;
/*    */   }
/*    */ 
/*    */   public void setTextContent(String textContent)
/*    */   {
/* 57 */     this.textContent = textContent;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.FreeText
 * JD-Core Version:    0.6.0
 */