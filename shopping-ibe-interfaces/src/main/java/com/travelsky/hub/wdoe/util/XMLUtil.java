/*    */ package com.travelsky.hub.wdoe.util;
/*    */ 
/*    */ public class XMLUtil
/*    */ {
/*    */   public static String buildChildren(String localName, String value)
/*    */   {
/* 20 */     value = (value == null) || ("".equals(value)) ? "" : value;
/* 21 */     StringBuilder childNode = new StringBuilder("\t\t<");
/* 22 */     childNode.append(localName).append(">");
/* 23 */     childNode.append(value);
/* 24 */     childNode.append("</").append(localName).append(">\r\n");
/* 25 */     return childNode.toString();
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.XMLUtil
 * JD-Core Version:    0.6.0
 */