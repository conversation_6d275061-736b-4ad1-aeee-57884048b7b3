/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CompletePaySeatOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4000013583956997541L;
/*    */   private String orderNum;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 22 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 30 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 39 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 46 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 55 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 62 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 69 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 80 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CompletePaySeatOutPutBean
 * JD-Core Version:    0.6.0
 */