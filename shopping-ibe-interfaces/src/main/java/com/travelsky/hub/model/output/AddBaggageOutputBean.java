/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class AddBaggageOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3077816836790619736L;
/*    */   private List<FlightInfoBag> flightInfoList;
/*    */   private List<PassengerInfoBag> psrInfoList;
/*    */   private List<PassengerFlightInfoBag> psrFlightInfoList;
/*    */   private List<String> btpStreamList;
/*    */ 
/*    */   public List<FlightInfoBag> getFlightInfoList()
/*    */   {
/* 40 */     return this.flightInfoList;
/*    */   }
/*    */ 
/*    */   public void setFlightInfoList(List<FlightInfoBag> flightInfoList)
/*    */   {
/* 47 */     this.flightInfoList = flightInfoList;
/*    */   }
/*    */ 
/*    */   public List<PassengerInfoBag> getPsrInfoList()
/*    */   {
/* 54 */     return this.psrInfoList;
/*    */   }
/*    */ 
/*    */   public void setPsrInfoList(List<PassengerInfoBag> psrInfoList)
/*    */   {
/* 61 */     this.psrInfoList = psrInfoList;
/*    */   }
/*    */ 
/*    */   public List<PassengerFlightInfoBag> getPsrFlightInfoList()
/*    */   {
/* 68 */     return this.psrFlightInfoList;
/*    */   }
/*    */ 
/*    */   public void setPsrFlightInfoList(List<PassengerFlightInfoBag> psrFlightInfoList)
/*    */   {
/* 75 */     this.psrFlightInfoList = psrFlightInfoList;
/*    */   }
/*    */ 
/*    */   public List<String> getBtpStreamList()
/*    */   {
/* 83 */     return this.btpStreamList;
/*    */   }
/*    */ 
/*    */   public void setBtpStreamList(List<String> btpStreamList)
/*    */   {
/* 90 */     this.btpStreamList = btpStreamList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AddBaggageOutputBean
 * JD-Core Version:    0.6.0
 */