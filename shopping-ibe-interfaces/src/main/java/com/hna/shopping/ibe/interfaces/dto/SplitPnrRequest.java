package com.hna.shopping.ibe.interfaces.dto;

import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel

public class SplitPnrRequest extends BaseRequest {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4636523424837888971L;
	private List<PNRPassenger> passengers;
	private String envelopType;
//	private String carrier;
	private String pnrNo;
	private int splitNum;//分离人数
	
	
}
