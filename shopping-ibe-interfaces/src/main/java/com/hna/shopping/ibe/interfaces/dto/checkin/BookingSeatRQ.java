package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import lombok.Data;

/**
 * Zero
 * 2022/3/10 17:25
 **/
@Data
public class BookingSeatRQ extends BaseRequest{
    private OfficeInfo officeInfo;
    private String pnrCode;
    private PassengerInfo passenger;
    private FlightSegment flightSegment;
    private ServiceItem serviceItem;

    @Data
    public static class OfficeInfo{
        private String airline;
        private String station;
    }
    @Data
    public static class PassengerInfo{
        private String passengerID;
        private String passengerName;
        private String passengerType;
    }
    @Data
    public static class FlightSegment{
        private String segmentID;
        private String departureDate;
        private String departureTime;
        private String departureAirport;
        private String arrivalAirport;
        private String mcRBD;
        private String mcFlightSuffix;
        private String mcFlightNumber;
        private String mcAirlineID;
        private String ocFlightNumber;
        private String ocAirlineID;
        private String ocFlightSuffix;
    }
    @Data
    public static class ServiceItem{
        private String segmentID;
        private String serviceID;
        private SeatInfo seatInfo;
    }
    @Data
    public static class SeatInfo{
        private String seatNumber;
        private String deckCode;
    }
}
