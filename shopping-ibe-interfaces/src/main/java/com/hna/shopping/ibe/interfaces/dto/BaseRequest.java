package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
@Data
@ApiModel
public class BaseRequest implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -6196878287303038144L;
	@ApiModelProperty(value = "签名，预留")
	private String sign;
	@ApiModelProperty(value = "时间戳，预留")
	private long timestamp;
	@ApiModelProperty(value = "调用渠道，访问权限控制用")
	private String channel;
	@ApiModelProperty(value = "指定操作office，非必填")
	private String officeNo;
	@ApiModelProperty(value = "指定航空公司配置，非必填")
	private String airlineCode;
	@ApiModelProperty(value = "指定目标接口，IBE/ABE/ETERM，非必填")
	private String target;
	
}
