/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import com.travelsky.hub.model.input.TxnInfo;
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryOrderRequest
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7771339655282350279L;
/*    */   private String bookingId;
/*    */   private TxnInfo txnInfo;
/*    */ 
/*    */   public String getBookingId()
/*    */   {
/* 30 */     return this.bookingId;
/*    */   }
/*    */ 
/*    */   public void setBookingId(String bookingId)
/*    */   {
/* 37 */     this.bookingId = bookingId;
/*    */   }
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 45 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 53 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.QueryOrderRequest
 * JD-Core Version:    0.6.0
 */