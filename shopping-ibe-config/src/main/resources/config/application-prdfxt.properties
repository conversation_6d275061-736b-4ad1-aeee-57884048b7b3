server.port=8086

# 引入公共配置
spring.profiles.include=common

# spring-boot-starter-actuator 生产环境访问需要鉴权
management.security.enabled=true

# 每个系统都应该不一样
memcached.prefix=fxt-ibe
memcached.serverHost[0]=************:11211
#memcached.userName[0]=memcached
#memcached.password[0]=ENC(yawm/celfy5VE3Qdx+dDaroo5NzNptdG)


# logback 配置
logging.path=/data/logs/fxt/jd-ibe
logging.level.com.hna.eking.basedemo=INFO
logging.level.root=INFO
logging.level.com.google.code.ssm=WARN

# eureka 注册中心地址
eureka.client.serviceUrl.defaultZone=http://************:8761/eureka/
#eureka.instance.ip-address=************
#eureka.client.serviceUrl.defaultZone=http://************:9013/eureka/

# redis配置
redisson.address=redis://***********:6379
redisson.password=ENC(ICntTYFx0k74ik7qSliuXuEu9AyaN7Vy)
redisson.config.open=true



# jd 配置
ibe.jd.server.ip=***********
ibe.jd.server.backupIp=***********
ibe.jd.server.port=6891
ibe.jd.client.app=jdhaiwai
ibe.jd.client.office=xiy255
ibe.jd.client.customNo=0
ibe.jd.client.validationNo=64
ibe.jd.client.printNos=1,2,3,4,6,7,8


# jd 强k配置
ibe.jd.k.server.ip=***********
ibe.jd.k.server.backupIp=***********
ibe.jd.k.server.port=6891
ibe.jd.k.client.app=jdhaiwai
ibe.jd.k.client.office=xiy255
ibe.jd.k.client.customNo=0
ibe.jd.k.client.validationNo=64
ibe.jd.k.client.printNos=1,2,3,4,6,7,8

#travel sky 配置
travelsky.service.url=