/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class DelBagOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8537915078969493938L;
/*    */   private String resultCode;
/*    */   private String resultList;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 32 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultList()
/*    */   {
/* 43 */     return this.resultList;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 49 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultList(String resultList)
/*    */   {
/* 56 */     this.resultList = resultList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DelBagOutputBean
 * JD-Core Version:    0.6.0
 */