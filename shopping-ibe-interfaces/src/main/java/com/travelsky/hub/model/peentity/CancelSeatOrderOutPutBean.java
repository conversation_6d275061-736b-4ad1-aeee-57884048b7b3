/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CancelSeatOrderOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1042363296424087693L;
/*    */   private String orderNum;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 22 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 31 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 38 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 47 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 54 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 63 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 70 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 79 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CancelSeatOrderOutPutBean
 * JD-Core Version:    0.6.0
 */