package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
//@EqualsAndHashCode(callSuper=true)
@ApiModel
public class FlightInfo  implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5483118677142721748L;
	private String origin; 
	private String destination; 
	private String flightNo;
	private Date departDate;
	private Date arriveDate;
	private String meal;
	private int stop;
	private boolean share;
	private String shareFlightNo;
	private String planeStyle;
	private String terminal1;
	private String terminal2;
	private String duration;
	private String arriTimeModify;
	
}
