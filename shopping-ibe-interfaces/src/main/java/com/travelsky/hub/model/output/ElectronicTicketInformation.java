/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ElectronicTicketInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6149628087312220240L;
/*    */   private String electronicTicketNumber;
/*    */   private String electronicTicketCoupon;
/*    */ 
/*    */   public String getElectronicTicketNumber()
/*    */   {
/* 33 */     return this.electronicTicketNumber;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketNumber(String electronicTicketNumber)
/*    */   {
/* 40 */     this.electronicTicketNumber = electronicTicketNumber;
/*    */   }
/*    */ 
/*    */   public String getElectronicTicketCoupon()
/*    */   {
/* 47 */     return this.electronicTicketCoupon;
/*    */   }
/*    */ 
/*    */   public void setElectronicTicketCoupon(String electronicTicketCoupon)
/*    */   {
/* 54 */     this.electronicTicketCoupon = electronicTicketCoupon;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ElectronicTicketInformation
 * JD-Core Version:    0.6.0
 */