/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class AdcResQueryOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<PassengerInfo> passengerInfos;
/*    */   private AdcError adcError;
/*    */ 
/*    */   public List<PassengerInfo> getPassengerInfos()
/*    */   {
/* 28 */     return this.passengerInfos;
/*    */   }
/*    */ 
/*    */   public void setPassengerInfos(List<PassengerInfo> passengerInfos)
/*    */   {
/* 37 */     this.passengerInfos = passengerInfos;
/*    */   }
/*    */ 
/*    */   public AdcError getAdcError()
/*    */   {
/* 46 */     return this.adcError;
/*    */   }
/*    */ 
/*    */   public void setAdcError(AdcError adcError)
/*    */   {
/* 55 */     this.adcError = adcError;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AdcResQueryOutputBean
 * JD-Core Version:    0.6.0
 */