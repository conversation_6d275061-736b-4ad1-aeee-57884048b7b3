/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class RefundOrderOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String isSuccess;
/*    */   private String OrderStatus;
/*    */   private String errorCode;
/*    */   private String errorMsg;
/*    */   private String orderNum;
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 25 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 37 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 44 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 51 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 58 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 65 */     return this.OrderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 72 */     this.OrderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 79 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 87 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 94 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.RefundOrderOutPutBean
 * JD-Core Version:    0.6.0
 */