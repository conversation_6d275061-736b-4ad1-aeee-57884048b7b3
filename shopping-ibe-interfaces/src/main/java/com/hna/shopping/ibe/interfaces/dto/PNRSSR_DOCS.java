package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRSSR_DOCS extends PNRSSR {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7358461640120404387L;
	String birth;                                                                    
	String doc_No;                                                                   
	String doc_type;                                                                 
	String expiry_Date;                                                              
	String firstname;                                                                
	String gender;                                                                   
	String holder;                                                                   
	String infant;                                                                   
	String issueCountry;                                                             
	String midname;                                                                  
	String nationality;                                                              
	int person_num;                                                                  
	String surname;                                                                  
	                                                                                 
	public static void main(String[] args)                                           
	{                                                                                
	}                                                                                
	                                                                                 
	public String getBirth()                                                         
	{                                                                                
	  return this.birth;                                                             
	}                                                                                
	                                                                                 
	public String getDoc_No()                                                        
	{                                                                                
	  return this.doc_No;                                                            
	}                                                                                
	                                                                                 
	public String getDoc_type()                                                      
	{                                                                                
	  return this.doc_type;                                                          
	}                                                                                
	                                                                                 
	public String getExpiry_Date()                                                   
	{                                                                                
	  return this.expiry_Date;                                                       
	}                                                                                
	                                                                                 
	public String getFirstname()                                                     
	{                                                                                
	  return this.firstname;                                                         
	}                                                                                
	                                                                                 
	public String getGender()                                                        
	{                                                                                
	  return this.gender;                                                            
	}                                                                                
	                                                                                 
	public String getHolder()                                                        
	{                                                                                
	  return this.holder;                                                            
	}                                                                                
	                                                                                 
	public String getInfant()                                                        
	{                                                                                
	  return this.infant;                                                            
	}                                                                                
	                                                                                 
	public String getIssueCountry()                                                  
	{                                                                                
	  return this.issueCountry;                                                      
	}                                                                                
	                                                                                 
	public String getMidname()                                                       
	{                                                                                
	  return this.midname;                                                           
	}                                                                                
	                                                                                 
	public String getNationality()                                                   
	{                                                                                
	  return this.nationality;                                                       
	}                                                                                
	                                                                                 
	public int getPersonNum()                                                        
	{                                                                                
	  return this.person_num;                                                        
	}                                                                                
	                                                                                 
	public String getSurname()                                                       
	{                                                                                
	  return this.surname;                                                           
	}                                                                                
	                                                                                 
	public void setBirth(String birth)                                               
	{                                                                                
	  this.birth = birth;                                                            
	}                                                                                
	                                                                                 
	public void setDoc_No(String doc_No)                                             
	{                                                                                
	  this.doc_No = doc_No;                                                          
	}                                                                                
	                                                                                 
	public void setDoc_type(String doc_type)                                         
	{                                                                                
	  this.doc_type = doc_type;                                                      
	}                                                                                
	                                                                                 
	public void setExpiry_Date(String expiry_Date)                                   
	{                                                                                
	  this.expiry_Date = expiry_Date;                                                
	}                                                                                
	                                                                                 
	public void setFirstname(String firstname)                                       
	{                                                                                
	  this.firstname = firstname;                                                    
	}                                                                                
	                                                                                 
	public void setGender(String gender)                                             
	{                                                                                
	  this.gender = gender;                                                          
	}                                                                                
	                                                                                 
	public void setHolder(String holder)                                             
	{                                                                                
	  this.holder = holder;                                                          
	}                                                                                
	                                                                                 
	public void setInfant(String infant)                                             
	{                                                                                
	  this.infant = infant;                                                          
	}                                                                                
	                                                                                 
	public void setIssueCountry(String issueCountry)                                 
	{                                                                                
	  this.issueCountry = issueCountry;                                              
	}                                                                                
	                                                                                 
	public void setMidname(String Midname)                                           
	{                                                                                
	  this.midname = Midname;                                                        
	}                                                                                
	                                                                                 
	public void setNationality(String nationality)                                   
	{                                                                                
	  this.nationality = nationality;                                                
	}                                                                                
	                                                                                 
	public void setPersonNum(int person_num)                                         
	{                                                                                
	  this.person_num = person_num;                                                  
	}                                                                                
	                                                                                 
	public void setSurname(String surname)                                           
	{                                                                                
	  this.surname = surname;                                                        
	}                                                                                
	                                                                                 
	public String toString()                                                         
	{                                                                                
	  StringBuffer sb = new StringBuffer();                                          
	  try {                                                                          
	    sb.append(this.index);                                                   
	    sb.append(".");                                                              
	    sb.append(getSSRType());                                                     
	    sb.append(" ");                                                              
	    sb.append(getActionCode());                                                  
	    sb.append((getPersonNum() == 0) ? "/" : String.valueOf(getPersonNum()));     
	    sb.append(" 类型：" + getDoc_type());                                        
	    sb.append(" 签发国：" + getIssueCountry());                                  
	    sb.append(" 编号：" + getDoc_No());                                          
	    sb.append(" 国籍：" + getNationality());                                     
	    sb.append(" 出生日期：" + getBirth());                                       
	    sb.append(" 性别：" + getGender());                                          
	    sb.append(" 证件有效日期：" + getExpiry_Date());                             
	    sb.append(" 姓:" + getFirstname());                                          
	    sb.append(" 名：" + getSurname());                                           
	    if ((getMidname() != null) && (getMidname().length() > 0))                   
	      sb.append(" midname：" + getMidname());                                    
	    sb.append(" 持有人标识：" + getHolder());                                    
	    sb.append(" PSGRID:" + getPsgrID());                                         
	    return sb.toString();                                                        
	  }                                                                              
	  catch (Exception localException) {                                             
	  }                                                                              
	  return super.toString();                                                       
	}                                                                                

}
