/*    */ package com.travelsky.hub.model.peentity.bookingSeat.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BookingSeatRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2972789043004288796L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 31 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 41 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 48 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 55 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.output.BookingSeatRS
 * JD-Core Version:    0.6.0
 */