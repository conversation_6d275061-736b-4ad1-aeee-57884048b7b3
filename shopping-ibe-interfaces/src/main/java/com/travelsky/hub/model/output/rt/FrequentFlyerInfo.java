/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FrequentFlyerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -3222327012016093660L;
/*     */   private String ffCompanyCode;
/*     */   private String ffNumber;
/*     */   private String ffLoyaltyLevel;
/*     */   private String ffLoyaltyTierCode;
/*     */   private String allianceCode;
/*     */   private String allianceLevel;
/*     */ 
/*     */   public String getFfCompanyCode()
/*     */   {
/*  46 */     return this.ffCompanyCode;
/*     */   }
/*     */ 
/*     */   public void setFfCompanyCode(String ffCompanyCode)
/*     */   {
/*  53 */     this.ffCompanyCode = ffCompanyCode;
/*     */   }
/*     */ 
/*     */   public String getFfNumber()
/*     */   {
/*  60 */     return this.ffNumber;
/*     */   }
/*     */ 
/*     */   public void setFfNumber(String ffNumber)
/*     */   {
/*  67 */     this.ffNumber = ffNumber;
/*     */   }
/*     */ 
/*     */   public String getFfLoyaltyLevel()
/*     */   {
/*  74 */     return this.ffLoyaltyLevel;
/*     */   }
/*     */ 
/*     */   public void setFfLoyaltyLevel(String ffLoyaltyLevel)
/*     */   {
/*  81 */     this.ffLoyaltyLevel = ffLoyaltyLevel;
/*     */   }
/*     */ 
/*     */   public String getFfLoyaltyTierCode()
/*     */   {
/*  88 */     return this.ffLoyaltyTierCode;
/*     */   }
/*     */ 
/*     */   public void setFfLoyaltyTierCode(String ffLoyaltyTierCode)
/*     */   {
/*  95 */     this.ffLoyaltyTierCode = ffLoyaltyTierCode;
/*     */   }
/*     */ 
/*     */   public String getAllianceCode()
/*     */   {
/* 102 */     return this.allianceCode;
/*     */   }
/*     */ 
/*     */   public void setAllianceCode(String allianceCode)
/*     */   {
/* 109 */     this.allianceCode = allianceCode;
/*     */   }
/*     */ 
/*     */   public String getAllianceLevel()
/*     */   {
/* 116 */     return this.allianceLevel;
/*     */   }
/*     */ 
/*     */   public void setAllianceLevel(String allianceLevel)
/*     */   {
/* 123 */     this.allianceLevel = allianceLevel;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FrequentFlyerInfo
 * JD-Core Version:    0.6.0
 */