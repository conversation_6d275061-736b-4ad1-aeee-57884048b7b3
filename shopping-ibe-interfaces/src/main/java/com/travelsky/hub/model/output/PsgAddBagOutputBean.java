/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PsgAddBagOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String carrAirlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String depCity;
/*     */   private String arrCity;
/*     */   private String cabin;
/*     */   private String psgName;
/*     */   private String psgStatus;
/*     */   private String seatNum;
/*     */   private String boardNum;
/*     */   private String hostNumber;
/*     */   private List<String> bTPStreams;
/*     */   private String addBagQuantity;
/*     */   private String addBagWeight;
/*     */   private String addBagWeightUnit;
/*     */   private List<BagTag> addBagTagList;
/*     */ 
/*     */   public String getDepCity()
/*     */   {
/*  56 */     return this.depCity;
/*     */   }
/*     */ 
/*     */   public void setDepCity(String depCity)
/*     */   {
/*  63 */     this.depCity = depCity;
/*     */   }
/*     */ 
/*     */   public String getArrCity()
/*     */   {
/*  70 */     return this.arrCity;
/*     */   }
/*     */ 
/*     */   public void setArrCity(String arrCity)
/*     */   {
/*  77 */     this.arrCity = arrCity;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  84 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  91 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getPsgName()
/*     */   {
/*  98 */     return this.psgName;
/*     */   }
/*     */ 
/*     */   public void setPsgName(String psgName)
/*     */   {
/* 105 */     this.psgName = psgName;
/*     */   }
/*     */ 
/*     */   public String getPsgStatus()
/*     */   {
/* 112 */     return this.psgStatus;
/*     */   }
/*     */ 
/*     */   public void setPsgStatus(String psgStatus)
/*     */   {
/* 119 */     this.psgStatus = psgStatus;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 127 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 134 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNum()
/*     */   {
/* 141 */     return this.seatNum;
/*     */   }
/*     */ 
/*     */   public void setSeatNum(String seatNum)
/*     */   {
/* 148 */     this.seatNum = seatNum;
/*     */   }
/*     */ 
/*     */   public String getBoardNum()
/*     */   {
/* 155 */     return this.boardNum;
/*     */   }
/*     */ 
/*     */   public void setBoardNum(String boardNum)
/*     */   {
/* 162 */     this.boardNum = boardNum;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 169 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 176 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public List<String> getbTPStreams()
/*     */   {
/* 183 */     return this.bTPStreams;
/*     */   }
/*     */ 
/*     */   public String getCarrAirlineCode()
/*     */   {
/* 190 */     return this.carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setCarrAirlineCode(String carrAirlineCode)
/*     */   {
/* 197 */     this.carrAirlineCode = carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setbTPStreams(List<String> bTPStreams)
/*     */   {
/* 204 */     this.bTPStreams = bTPStreams;
/*     */   }
/*     */ 
/*     */   public String getAddBagQuantity()
/*     */   {
/* 211 */     return this.addBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setAddBagQuantity(String addBagQuantity)
/*     */   {
/* 218 */     this.addBagQuantity = addBagQuantity;
/*     */   }
/*     */ 
/*     */   public String getAddBagWeight()
/*     */   {
/* 225 */     return this.addBagWeight;
/*     */   }
/*     */ 
/*     */   public void setAddBagWeight(String addBagWeight)
/*     */   {
/* 232 */     this.addBagWeight = addBagWeight;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 241 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 248 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getAddBagWeightUnit()
/*     */   {
/* 255 */     return this.addBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public void setAddBagWeightUnit(String addBagWeightUnit)
/*     */   {
/* 262 */     this.addBagWeightUnit = addBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public List<BagTag> getAddBagTagList()
/*     */   {
/* 269 */     return this.addBagTagList;
/*     */   }
/*     */ 
/*     */   public void setAddBagTagList(List<BagTag> addBagTagList)
/*     */   {
/* 276 */     this.addBagTagList = addBagTagList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PsgAddBagOutputBean
 * JD-Core Version:    0.6.0
 */