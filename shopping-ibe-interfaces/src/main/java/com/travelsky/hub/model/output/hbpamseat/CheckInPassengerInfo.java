/*     */ package com.travelsky.hub.model.output.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class CheckInPassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8167720365016240507L;
/*     */   private List<String> bppStream;
/*     */   private ErrorInfo errorInfo;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String ticketId;
/*     */   private String sequenceNumber;
/*     */   private String surName;
/*     */   private String chnName;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String seatNumber;
/*     */   private String boardingNumber;
/*     */   private String boardingTime;
/*     */   private String boardingGateNumber;
/*     */   private String cabin;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private List<StopOverInfo> stopOverInfoList;
/*     */   private String ffpAirlineCode;
/*     */   private String certificateType;
/*     */   private String certificateNumber;
/*     */   private String standByIndicator;
/*     */   private String standByNumber;
/*     */   private String passengerStatus;
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 123 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 130 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 137 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 144 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 151 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 158 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 165 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 172 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 179 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 186 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public List<String> getBppStream()
/*     */   {
/* 193 */     return this.bppStream;
/*     */   }
/*     */ 
/*     */   public void setBppStream(List<String> bppStream)
/*     */   {
/* 200 */     this.bppStream = bppStream;
/*     */   }
/*     */ 
/*     */   public ErrorInfo getErrorInfo()
/*     */   {
/* 207 */     return this.errorInfo;
/*     */   }
/*     */ 
/*     */   public void setErrorInfo(ErrorInfo errorInfo)
/*     */   {
/* 214 */     this.errorInfo = errorInfo;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 221 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 228 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 235 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 242 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 251 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 258 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getChnName()
/*     */   {
/* 264 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public String getTicketId()
/*     */   {
/* 270 */     return this.ticketId;
/*     */   }
/*     */ 
/*     */   public void setTicketId(String ticketId)
/*     */   {
/* 277 */     this.ticketId = ticketId;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/* 284 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 291 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 298 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 305 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 312 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 319 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 325 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 332 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 338 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 344 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 351 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 357 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 364 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 371 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 377 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 384 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 391 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 398 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 405 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOverInfoList()
/*     */   {
/* 412 */     return this.stopOverInfoList;
/*     */   }
/*     */ 
/*     */   public void setStopOverInfoList(List<StopOverInfo> stopOverInfoList)
/*     */   {
/* 419 */     this.stopOverInfoList = stopOverInfoList;
/*     */   }
/*     */ 
/*     */   public String getStandByIndicator()
/*     */   {
/* 426 */     return this.standByIndicator;
/*     */   }
/*     */ 
/*     */   public void setStandByIndicator(String standByIndicator)
/*     */   {
/* 433 */     this.standByIndicator = standByIndicator;
/*     */   }
/*     */ 
/*     */   public String getStandByNumber()
/*     */   {
/* 440 */     return this.standByNumber;
/*     */   }
/*     */ 
/*     */   public void setStandByNumber(String standByNumber)
/*     */   {
/* 447 */     this.standByNumber = standByNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 454 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 461 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpamseat.CheckInPassengerInfo
 * JD-Core Version:    0.6.0
 */