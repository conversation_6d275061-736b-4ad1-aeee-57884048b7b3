package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.text.DecimalFormat;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
//@Data
//@EqualsAndHashCode(callSuper=true)
//@ApiMode
public class FareBox implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3670807218270796152L;
	 fnitem f = new fnitem(); fnitem r = new fnitem(); fnitem e = new fnitem(); fnitem x = new fnitem(); fnitem[] t = new fnitem[3]; fnitem a = new fnitem(); fnitem[] o = new fnitem[3]; fnitem s = new fnitem();

	 fnitem b = new fnitem();
	 fnitem g = new fnitem();
	 fnitem r1 = new fnitem();
	
	 String type = "";
	
	 double c = -1.0D;
	
	 int taxCnt = 0;
	
	 int oTaxCnt = 0;
	
	 boolean infant = false;
	 String fn;
	 static final DecimalFormat format = new DecimalFormat("0.00");
	 public static final int F = 1;
	 public static final int E = 2;
	 public static final int R = 4;
	 public static final int T = 65536;
	 public static final int O = 32768;
	 public static final int A = 8;
	 public static final int X = 16;
	 public static final int S = 32;
	 public static final int B = 64;
	 public static final int G = 128;
	 public static final int R1 = 256;
	 public static final int UNDEFINED = -2;
	 public static final double EXEMPTTAX = -1.0D;
	

	

	
	 public boolean isInfant() {
	   return this.infant;
	 }
	
	 public void setInfant(boolean infant) {
	   this.infant = infant;
	 }
	
	 public int getOTaxCnt() {
	   return this.oTaxCnt;
	 }
	
	 void setOTaxCnt(int taxCnt) {
	   if ((taxCnt > 2) || (taxCnt < 0))
	     throw new UnsupportedOperationException();
	   this.oTaxCnt = taxCnt;
	 }
	
	 public int getTaxCnt() {
	   return this.taxCnt;
	 }
	
	 void setTaxCnt(int taxCnt) {
	   if ((taxCnt > 2) || (taxCnt < 0))
	     throw new UnsupportedOperationException();
	   this.taxCnt = taxCnt;
	 }
	
	 public double getAmount(int para)
	 {
	   if (para > 16384)
	     throw new UnsupportedOperationException();
	   switch (para)
	   {
	   case 1:
	     return this.f.amount;
	   case 2:
	     return this.e.amount;
	   case 4:
	     return this.r.amount;
	   case 8:
	     return this.a.amount;
	   case 16:
	     return this.x.amount;
	   case 32:
	     return this.s.amount;
	   case 64:
	     return this.b.amount;
	   case 128:
	     return this.g.amount;
	   case 256:
	     return this.r1.amount;
	   }
	
	   throw new UnsupportedOperationException();
	 }
	
	 public void setAmount(int para, String currency, double amount)
	 {
	   if (para > 16384)
	     throw new UnsupportedOperationException();
	   switch (para)
	   {
	   case 1:
	     this.f.amount = amount;
	     this.f.currency = currency;
	     break;
	   case 2:
	     this.e.amount = amount;
	     this.e.currency = currency;
	     break;
	   case 4:
	     this.r.amount = amount;
	     this.r.currency = currency;
	     break;
	   case 8:
	     this.a.amount = amount;
	     this.a.currency = currency;
	     break;
	   case 16:
	     this.x.amount = amount;
	     this.x.currency = currency;
	     break;
	   case 32:
	     this.s.amount = amount;
	     this.s.currency = currency;
	     break;
	   case 64:
	     this.b.amount = amount;
	     this.b.currency = currency;
	     break;
	   case 128:
	     this.g.amount = amount;
	     this.g.currency = currency;
	     break;
	   case 256:
	     this.r1.amount = amount;
	     this.r1.currency = currency;
	     break;
	   default:
	     throw new UnsupportedOperationException();
	   }
	 }
	
	 public String getTaxCode(int para, int index)
	 {
	   if (para == 65536) {
	     if ((index < 0) || (index >= this.taxCnt))
	       throw new UnsupportedOperationException();
	     return this.t[index].code; }
	   if (para == 32768) {
	     if ((index < 0) || (index >= this.oTaxCnt))
	       throw new UnsupportedOperationException();
	     return this.o[index].code;
	   }
	   throw new UnsupportedOperationException();
	 }
	
	 public String getTaxCurrency(int para, int index)
	 {
	   if (para == 65536) {
	     if ((index < 0) || (index >= this.taxCnt))
	       throw new UnsupportedOperationException();
	     return this.t[index].currency; }
	   if (para == 32768) {
	     if ((index < 0) || (index >= this.oTaxCnt))
	       throw new UnsupportedOperationException();
	     return this.o[index].currency;
	   }
	   throw new UnsupportedOperationException();
	 }
	
	 public double getTaxAmount(int para, int index) {
	   if (para == 65536) {
	     if ((index < 0) || (index >= this.taxCnt))
	       throw new UnsupportedOperationException();
	     return this.t[index].amount; }
	   if (para == 32768) {
	     if ((index < 0) || (index >= this.oTaxCnt))
	       throw new UnsupportedOperationException();
	     return this.o[index].amount;
	   }
	   throw new UnsupportedOperationException();
	 }
	
	 public String getCurrency(int para) {
	   if (para > 16384)
	     throw new UnsupportedOperationException();
	   switch (para)
	   {
	   case 1:
	     return this.f.currency;
	   case 2:
	     return this.e.currency;
	   case 4:
	     return this.r.currency;
	   case 8:
	     return this.a.currency;
	   case 16:
	     return this.x.currency;
	   case 32:
	     return this.s.currency;
	   case 64:
	     return this.b.currency;
	   case 128:
	     return this.g.currency;
	   case 256:
	     return this.r1.currency;
	   }
	
	   throw new UnsupportedOperationException();
	 }
	
	 public int getTaxCnt(int para)
	 {
	   if (para == 65536)
	     return getTaxCnt();
	   if (para == 32768) {
	     return getOTaxCnt();
	   }
	   throw new UnsupportedOperationException();
	 }
	
	 public fnitem removeTax(int para, String code)
	 {
	   fnitem[] items;
	   if (para == 65536)
	     items = this.t;
	   else if (para == 32768)
	     items = this.o;
	   else
	     throw new UnsupportedOperationException();
	   for (int i = 0; i < items.length; ++i) {
	     if ((items[i] != null) && (String.valueOf(items[i].code).equalsIgnoreCase(code)))
	       return removeTax(para, i);
	   }
	   return null;
	 }
	
	 public fnitem removeTax(int para, int i)
	 {
	   fnitem[] items;
	   if ((i >= getTaxCnt(para)) || (i < 0))
	     return null;
	   if (i == getTaxCnt(para))
	   {
	     if (para == 65536)
	       items = this.t;
	     else if (para == 32768)
	       items = this.o;
	     else {
	       throw new UnsupportedOperationException();
	     }
	     fnitem item = items[i];
	     items[i] = null;
	     this.taxCnt -= 1;
	     return item;
	   }
	
	   if (para == 65536)
	     items = this.t;
	   else if (para == 32768)
	     items = this.o;
	   else
	     throw new UnsupportedOperationException();
	   fnitem item = items[i];
	
	   for (int j = i; j < this.taxCnt - 1; ++j) {
	     items[j] = items[(j + 1)];
	   }
	   items[(this.taxCnt - 1)] = null;
	   this.taxCnt -= 1;
	   return item;
	 }
	
	 public void addTax(int para, String currency, double amount, String code)
	 {
	   if ((para != 32768) && (para != 65536))
	     throw new UnsupportedOperationException();
	   if (((para == 32768) && (this.oTaxCnt == 3)) || ((para == 65536) && (this.taxCnt == 3)))
	     throw new UnsupportedOperationException();
	   fnitem tax = new fnitem();
	   tax.currency = currency;
	   tax.amount = amount;
	   tax.code = code;
	   if (para == 32768) {
	     this.o[(this.oTaxCnt++)] = tax;
	   }
	   else
	     this.t[(this.taxCnt++)] = tax;
	 }
	
	 public double getC() {
	   return this.c;
	 }
	
	 public void setC(double c) {
	   this.c = c; }
	
	 public String makeString() {
	   FareBox fn = this;
	   try
	   {
	     int i;
	     StringBuffer sb = new StringBuffer();
	     sb.append("原文：");
	     sb.append((fn.getFn() == null) ? "" : fn.getFn());
	
	     if ("IT".equalsIgnoreCase(fn.getType()))
	       sb.append("\r\nIT运价");
	     else if ("NR".equalsIgnoreCase(fn.getType()))
	       sb.append("\r\nNR方式结算");
	     if (fn.getAmount(1) > -0.5D) {
	       sb.append("\r\n票面价：");
	       sb.append(fn.getCurrency(1));
	       sb.append(format.format(fn.getAmount(1)));
	     }
	     if (fn.getAmount(4) > -0.5D) {
	       sb.append("\r\n原票面价/信用卡支付金额：");
	       sb.append(fn.getCurrency(4));
	       sb.append(format.format(fn.getAmount(4)));
	     }
	     if (fn.getAmount(2) > -0.5D) {
	       sb.append("\r\n等值货币：");
	       sb.append(fn.getCurrency(2));
	       sb.append(format.format(fn.getAmount(2)));
	     }
	     if (fn.getAmount(32) > -0.5D) {
	       sb.append("\r\n实收：");
	       sb.append(fn.getCurrency(32));
	       sb.append(format.format(fn.getAmount(32)));
	     }
	     if (fn.getAmount(256) > -0.5D) {
	       sb.append("\r\n信用卡支付金额：");
	       sb.append(fn.getCurrency(256));
	       sb.append(format.format(fn.getAmount(256)));
	     }
	     if (fn.getAmount(64) > -0.5D) {
	       sb.append("\r\n信用卡支付税款：");
	       sb.append(fn.getCurrency(64));
	       sb.append(format.format(fn.getAmount(64)));
	     }
	     if (fn.getAmount(128) > -0.5D) {
	       sb.append("\r\n与航空公司结算的净价金额：");
	       sb.append(fn.getCurrency(128));
	       sb.append(format.format(fn.getAmount(128)));
	     }
	     if (fn.getC() > -0.5D) {
	       sb.append("\r\n代理费率：");
	       sb.append(format.format(fn.getC()));
	     }
	     if (fn.getAmount(16) > -0.5D) {
	       sb.append("\r\n附加费用之和：");
	       sb.append(fn.getCurrency(16));
	       sb.append(format.format(fn.getAmount(16)));
	     }
	     if (fn.getTaxCnt() > 0) {
	       sb.append("\r\n税项：");
	       for (i = 0; i < fn.getTaxCnt(); ++i) {
	         sb.append("      ");
	         sb.append(fn.getTaxCode(65536, i));
	         sb.append(":");
	         sb.append(fn.getTaxCurrency(65536, i));
	         if (Math.abs(-1.0D - fn.getTaxAmount(65536, i)) < 0.0001D)
	           sb.append("EXEMPT");
	         else
	           sb.append(format.format(fn.getTaxAmount(65536, i)));
	       }
	     }
	     if (fn.getOTaxCnt() > 0) {
	       sb.append("\r\n换开原票税项：");
	       for (i = 0; i < fn.getOTaxCnt(); ++i) {
	         sb.append("      ");
	         sb.append(fn.getTaxCode(32768, i));
	         sb.append(":");
	         sb.append(fn.getTaxCurrency(32768, i));
	         if (Math.abs(-1.0D - fn.getTaxAmount(32768, i)) < 0.0001D)
	           sb.append("EXEMPT");
	         else
	           sb.append(format.format(fn.getTaxAmount(32768, i)));
	       }
	     }
	     if (fn.getCurrency(8).length() > 0) {
	       sb.append("\r\n总价：");
	       sb.append(fn.getCurrency(8));
	       sb.append(format.format(fn.getAmount(8)));
	     }
	     return sb.toString();
	   } catch (Exception e) {
	     e.printStackTrace();
	   }
	   return null;
	 }
	
	 public String getFn() {
	   return this.fn;
	 }
	
	 public void setFn(String fn) {
	   this.fn = fn;
	 }
	
	 public String getType()
	 {
	   return this.type;
	 }
	
	 public void setType(String type)
	 {
	   this.type = type;
	 }
	
	 public static class fnitem
	   implements Serializable, Cloneable
	 {
	   private static final long serialVersionUID = 734860998766845722L;
	   String currency;
	   String code;
	   double amount;
	
	   fnitem()
	   {
	     this.currency = "";
	
	     this.code = "";
	
	     this.amount = -2.0D;
	   }
	
	   protected Object clone()
	     throws CloneNotSupportedException
	   {
	     return super.clone();
	   }
	
	   public boolean equals(Object obj)
	   {
	     if (obj == null)
	       return false;
	     if (!(obj instanceof fnitem))
	     {
	       return super.equals(obj); }
	     fnitem oth = (fnitem)obj;
	
	     return ((((this.currency != null) || (oth.currency != null))) && (((!(String.valueOf(this.currency).equalsIgnoreCase(oth.currency))) || (
	       (((this.code != oth.code) || (this.code != null))) && (((!(String.valueOf(this.code).equalsIgnoreCase(oth.code))) ||
	       (Math.abs(this.amount - oth.amount) >= 1.E-005D)))))));
	   }

	public String getCurrency() {
		return currency;
	}

	public String getCode() {
		return code;
	}

	public double getAmount() {
		return amount;
	}
	 }

	public fnitem[] getT() {
		return t;
	}

	public fnitem[] getO() {
		return o;
	}

	public fnitem getF() {
		return f;
	}

	public fnitem getR() {
		return r;
	}

	public fnitem getE() {
		return e;
	}

	public fnitem getX() {
		return x;
	}

	public fnitem getA() {
		return a;
	}

	public fnitem getS() {
		return s;
	}

}
