/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Line
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String LineNumber;
/*     */   private String LineAttributes;
/*     */   private String FrontEmptyLines;
/*     */   private String RearEmptyLines;
/*     */   private List<Seatinfo> Seatinfos;
/*     */ 
/*     */   public String getLineNumber()
/*     */   {
/*  37 */     return this.LineNumber;
/*     */   }
/*     */ 
/*     */   public void setLineNumber(String lineNumber)
/*     */   {
/*  44 */     this.LineNumber = lineNumber;
/*     */   }
/*     */ 
/*     */   public String getLineAttributes()
/*     */   {
/*  51 */     return this.LineAttributes;
/*     */   }
/*     */ 
/*     */   public void setLineAttributes(String lineAttributes)
/*     */   {
/*  58 */     this.LineAttributes = lineAttributes;
/*     */   }
/*     */ 
/*     */   public String getFrontEmptyLines()
/*     */   {
/*  65 */     return this.FrontEmptyLines;
/*     */   }
/*     */ 
/*     */   public void setFrontEmptyLines(String frontEmptyLines)
/*     */   {
/*  72 */     this.FrontEmptyLines = frontEmptyLines;
/*     */   }
/*     */ 
/*     */   public String getRearEmptyLines()
/*     */   {
/*  79 */     return this.RearEmptyLines;
/*     */   }
/*     */ 
/*     */   public void setRearEmptyLines(String rearEmptyLines)
/*     */   {
/*  86 */     this.RearEmptyLines = rearEmptyLines;
/*     */   }
/*     */ 
/*     */   public List<Seatinfo> getSeatinfos()
/*     */   {
/*  93 */     return this.Seatinfos;
/*     */   }
/*     */ 
/*     */   public void setSeatinfos(List<Seatinfo> seatinfos)
/*     */   {
/* 100 */     this.Seatinfos = seatinfos;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Line
 * JD-Core Version:    0.6.0
 */