/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class HbpuoInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2714529169457361296L;
/*  18 */   private String airlineCode = "";
/*     */ 
/*  23 */   private String flightNumber = "";
/*     */ 
/*  28 */   private String flightDate = "";
/*     */ 
/*  33 */   private String fromCity = "";
/*     */ 
/*  38 */   private String toCity = "";
/*     */ 
/*  43 */   private String psrClass = "";
/*     */ 
/*  48 */   private String passengerName = "";
/*     */ 
/*  53 */   private String tktNumber = "";
/*     */ 
/*  58 */   private String tourIndex = "";
/*     */ 
/*  63 */   private String seatNumber = "";
/*     */ 
/*  68 */   private String hostNumber = "";
/*     */   private boolean isSnr;
/*     */   private HbpuoInputBean throughPassenger;
/*  83 */   private String phoneNumber = "";
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  90 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  97 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 104 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 112 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 120 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public String getPsrClass()
/*     */   {
/* 128 */     return this.psrClass;
/*     */   }
/*     */ 
/*     */   public void setPsrClass(String psrClass)
/*     */   {
/* 135 */     this.psrClass = psrClass;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 142 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 149 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 157 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 164 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 171 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 178 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 185 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 192 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 199 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 206 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 213 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public boolean isSnr()
/*     */   {
/* 220 */     return this.isSnr;
/*     */   }
/*     */ 
/*     */   public void setSnr(boolean isSnr)
/*     */   {
/* 227 */     this.isSnr = isSnr;
/*     */   }
/*     */ 
/*     */   public HbpuoInputBean getThroughPassenger()
/*     */   {
/* 234 */     return this.throughPassenger;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 241 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 248 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setThroughPassenger(HbpuoInputBean throughPassenger)
/*     */   {
/* 255 */     this.throughPassenger = throughPassenger;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 262 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 269 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public boolean validPara()
/*     */   {
/* 277 */     return (validateValues(this)) && (validateValues(getThroughPassenger()));
/*     */   }
/*     */ 
/*     */   private boolean validateValues(HbpuoInputBean inputBean)
/*     */   {
/* 311 */     return (inputBean != null) && (inputBean.getAirlineCode() != null) && 
/* 291 */       (inputBean
/* 291 */       .getAirlineCode().trim().length() != 0) && 
/* 292 */       (inputBean
/* 292 */       .getFlightDate() != null) && 
/* 293 */       (inputBean
/* 293 */       .getFlightDate().trim().length() != 0) && 
/* 294 */       (inputBean
/* 294 */       .getFlightNumber() != null) && 
/* 295 */       (inputBean
/* 295 */       .getFlightNumber().trim().length() != 0) && 
/* 296 */       (inputBean
/* 296 */       .getPsrClass() != null) && 
/* 297 */       (inputBean
/* 297 */       .getPsrClass().trim().length() != 0) && 
/* 298 */       (inputBean
/* 298 */       .getToCity() != null) && 
/* 299 */       (inputBean
/* 299 */       .getToCity().trim().length() != 0) && 
/* 300 */       (inputBean
/* 300 */       .getPassengerName() != null) && 
/* 301 */       (inputBean
/* 301 */       .getPassengerName().trim().length() != 0) && 
/* 302 */       (inputBean
/* 302 */       .getFromCity() != null) && 
/* 303 */       (inputBean
/* 303 */       .getFromCity().trim().length() != 0) && 
/* 304 */       (inputBean
/* 304 */       .getTourIndex() != null) && 
/* 305 */       (inputBean
/* 305 */       .getTourIndex().trim().length() != 0) && 
/* 306 */       (inputBean
/* 306 */       .getTktNumber() != null) && 
/* 307 */       (inputBean
/* 307 */       .getTktNumber().trim().length() != 0) && 
/* 308 */       (inputBean
/* 308 */       .getHostNumber() != null) && 
/* 309 */       (inputBean
/* 309 */       .getHostNumber().trim().length() != 0);
/*     */   }
/*     */ 
/*     */   public void setPhoneNumber(String phoneNumber)
/*     */   {
/* 322 */     this.phoneNumber = phoneNumber;
/*     */   }
/*     */ 
/*     */   public String getPhoneNumber()
/*     */   {
/* 329 */     return this.phoneNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.HbpuoInputBean
 * JD-Core Version:    0.6.0
 */