/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.ibe;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * IBE 基本配置
 *
 * <AUTHOR>
 */
@Data
public abstract class IBEConfig {
    /**
     * Server 配置
     */
    private Server server;
    /**
     * Client 配置
     */
    private Client client;
    /**
     * Config 配置
     */
    private Config config;

    private String agencies;

    private String system;
    @Data
    public static class Server {
        /**
         * Server IP 地址
         */
        private String ip;
        /**
         * Server 备份 IP 地址
         */
        private String backupIp;
        /**
         * Server 端口
         */
        private int port;
    }

    @Data
    public static class Client {
        /**
         * client 应用程序
         */
        private String app;
        /**
         * client Office 号
         */
        private String office;
        /**
         * client 号
         */
        private String customNo;
        /**
         * 验证码
         */
        private String validationNo;
        /**
         * 打票机号
         */
        private String printNos;
    }

    public static class Config {
        /**
         * 负载均衡
         */
        private Boolean openBalance;
        /**
         *
         */
        private Boolean replaceContactNo;
        /**
         *
         */
        private Boolean openEI;
    }

}
