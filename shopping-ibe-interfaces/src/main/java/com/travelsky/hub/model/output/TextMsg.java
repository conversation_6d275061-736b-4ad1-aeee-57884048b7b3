/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TextMsg
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2507963507194963325L;
/*    */   private String msgText;
/*    */   private String msgType;
/*    */ 
/*    */   public String getMsgText()
/*    */   {
/* 36 */     return this.msgText;
/*    */   }
/*    */ 
/*    */   public void setMsgText(String msgText)
/*    */   {
/* 43 */     this.msgText = msgText;
/*    */   }
/*    */ 
/*    */   public String getMsgType()
/*    */   {
/* 50 */     return this.msgType;
/*    */   }
/*    */ 
/*    */   public void setMsgType(String msgType)
/*    */   {
/* 57 */     this.msgType = msgType;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TextMsg
 * JD-Core Version:    0.6.0
 */