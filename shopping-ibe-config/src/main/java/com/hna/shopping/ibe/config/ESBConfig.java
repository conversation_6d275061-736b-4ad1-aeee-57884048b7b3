package com.hna.shopping.ibe.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

//@Configuration
public class ESBConfig {

//    @Value("${esb.dubbo.registry.address}")
//    private String registryAddress;
//
//    @Value("${esb.dubbo.registry.protocol}")
//    private String registryProtocol;
//
//    @Value("${esb.dubbo.application.name}")
//    private String applicationName;
//
//    @Value("${esb.dubbo.application.owner}")
//    private String applicationOwner;
//
//    @Value("${esb.dubbo.protocol.port}")
//    private String protocolPort;
//
//    public String getRegistryAddress() {
//        return registryAddress;
//    }
//
//    public void setRegistryAddress(String registryAddress) {
//        this.registryAddress = registryAddress;
//    }
//
//    public String getRegistryProtocol() {
//        return registryProtocol;
//    }
//
//    public void setRegistryProtocol(String registryProtocol) {
//        this.registryProtocol = registryProtocol;
//    }
//
//    public String getApplicationName() {
//        return applicationName;
//    }
//
//    public void setApplicationName(String applicationName) {
//        this.applicationName = applicationName;
//    }
//
//    public String getApplicationOwner() {
//        return applicationOwner;
//    }
//
//    public void setApplicationOwner(String applicationOwner) {
//        this.applicationOwner = applicationOwner;
//    }
//
//    public String getProtocolPort() {
//        return protocolPort;
//    }
//
//    public void setProtocolPort(String protocolPort) {
//        this.protocolPort = protocolPort;
//    }
}
