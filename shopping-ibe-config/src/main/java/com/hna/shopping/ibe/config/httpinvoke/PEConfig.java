package com.hna.shopping.ibe.config.httpinvoke;

import com.hna.shopping.ibe.interfaces.checkin.pe.ICheckInService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean;
import org.springframework.remoting.httpinvoker.HttpInvokerRequestExecutor;
import org.springframework.remoting.httpinvoker.SimpleHttpInvokerRequestExecutor;

/**
 * <AUTHOR>
 * @date 2019/11/19
 */
@Configuration
public class PEConfig {
    @Value("${travelsky.service.url}")
    private String traveskyUrl;

    @SuppressWarnings("deprecation")
    @Bean
    public SimpleHttpInvokerRequestExecutor httpInvokerRequestExecutor(){
        //SimpleHttpInvokerRequestExecutor session = new SimpleHttpInvokerRequestExecutor();
        SimpleHttpInvokerRequestExecutor session = new CheckInHttpInvokerRequestExecutor();
        session.setAcceptGzipEncoding(true);
        return session;
    }


    @Bean
    public HttpInvokerProxyFactoryBean checkInService() {
        HttpInvokerProxyFactoryBean httpInvokerProxyFactoryBean = new HttpInvokerProxyFactoryBean();
        httpInvokerProxyFactoryBean.setServiceUrl(traveskyUrl + "checkIn.service");
        httpInvokerProxyFactoryBean.setServiceInterface(ICheckInService.class);
        httpInvokerProxyFactoryBean.setHttpInvokerRequestExecutor(httpInvokerRequestExecutor());
        return httpInvokerProxyFactoryBean;
    }

}
