/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class JcsOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6928206745983399906L;
/*    */   private String resultMsg;
/*    */   private String resultCode;
/*    */ 
/*    */   public String getResultMsg()
/*    */   {
/* 29 */     return this.resultMsg;
/*    */   }
/*    */ 
/*    */   public void setResultMsg(String resultMsg)
/*    */   {
/* 39 */     this.resultMsg = resultMsg;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 45 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 56 */     this.resultCode = resultCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.JcsOutputBean
 * JD-Core Version:    0.6.0
 */