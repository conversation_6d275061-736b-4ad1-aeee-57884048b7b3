package com.travelsky.hub.wdoe.util;

public final class CheckinConStr
{
  public static final String PWINPUT_ELEMENT = "PwInput";
  public static final String EXT_PW = "EXT";
  public static final String FORCEDCANCELLATION_EXT = "ForcedCancellation";
  public static final String WDOE_ERROR = "Error";
  public static final String WDOE_ERROCODE = "Code";
  public static final String WDOE_MSG = "Msg";
  public static final String PWOUTPUT_ELEMENT = "PwOutput";
  public static final String RESULTCODE_PWOUTPUT = "ResultCode";
  public static final String RESULTSTRING_PWOUTPUT = "ResultString";
  public static final String E_ = "";
  public static final String MESSAGEFUNCTION_ELEMENT = "MessageFunction";
  public static final String FUNCTION_MESSAGEFUNCTION = "Function";
  public static final String WDOE_ELEMENT = "WDoe";
  public static final String FLIGHTINFO_ELEMENT = "FlightInfo";
  public static final String RPH_FLIGHTINFO = "Index";
  public static final String PR_RPH_FLIGHTINFO = "RPH";
  public static final String FILINGAIRLINE_FLIGHTINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_FLIGHTINFO = "FlightNumber";
  public static final String DEPARTUREAIRPORT_FLIGHTINFO = "DepartureAirport";
  public static final String DEPARTUREDATE_FLIGHTINFO = "DepartureDate";
  public static final String ARRIVALAIRPORT_FLIGHTINFO = "ArrivalAirport";
  public static final String INDEX_FLIGHTINFO = "Index";
  public static final String PSG_OUTBOUNDTICKET = "OutBoundTicket";
  public static final String FLIGHTSEATINFO_ELEMENT = "FlightSeatInfo";
  public static final String AVAILCLASS_FLIGHTSEATINFO = "AvailClass";
  public static final String PADS_FLIGHTSEATINFO = "PADS";
  public static final String AIRCRAFT_ELEMENT = "Aircraft";
  public static final String AIRCRAFTTYPE_AIRCRAFT = "AircraftType";
  public static final String AIRCRAFTVERSION_AIRCRAFT = "AircraftVersion";
  public static final String GTD_AIRCRAFT = "GTD";
  public static final String POS_AIRCRAFT = "POS";
  public static final String BDT_AIRCRAFT = "BDT";
  public static final String SD_AIRCRAFT = "SD";
  public static final String DEPTTIMETYPE_AIRCRAFT = "DeptTimeType";
  public static final String DEPTTIME_AIRCRAFT = "DeptTime";
  public static final String ARRTIMETYPE_AIRCRAFT = "ArrTimeType";
  public static final String ARRTIME_AIRCRAFT = "ArrTime";
  public static final String FT_AIRCRAFT = "FT";
  public static final String PASSENGERS_ELEMENT = "Passengers";
  public static final String PASSENGER_ELEMENT = "Passenger";
  public static final String SURNAME_PASSENGER = "Surname";
  public static final String BAGTAGS_ELEMENT = "BagTags";
  public static final String INDEX_BAGTAG = "Index";
  public static final String BAGARRIVALAIRPORT_BAGTAG = "BagArrivalAirport";
  public static final String PASSENGERINFO_ELEMENT = "PassengerInfo";
  public static final String RPH_PASSENGERINFO = "RPH";
  public static final String SURNAME_PASSENGERINFO = "Surname";
  public static final String CHNNAME_PASSENGERINFO = "ChnName";
  public static final String BAGWEIGHT_PASSENGERINFO = "BagWeight";
  public static final String BAGQUANTITY_PASSENGERINFO = "BagQuantity";
  public static final String BAGARRIVALAIRPORT_PASSENGERINFO = "BagArrivalAirport";
  public static final String DOCTYPE_PASSENGERINFO = "DocType";
  public static final String DOCID_PASSENGERINFO = "DocID";
  public static final String CHECKCODEINFO_ELEMENT = "CheckCodeInfo";
  public static final String CHECKCODE_CHECKCODEINFO = "CheckCode";
  public static final String CONTACTINFO_ELEMENT = "ContactInfo";
  public static final String INFO_CONTACTINFO = "ContactText";
  public static final String BAGINFO_ELEMENT = "BagInfo";
  public static final String BAGWEIGHT_BAGINFO = "BagWeight";
  public static final String BAGQUANTITY_BAGINFO = "BagQuantity";
  public static final String BAGARRIVALAIRPORT_BAGINFO = "BagArrivalAirport";
  public static final String PASSENGERFLIGHTINFO_ELEMENT = "PassengerFlightInfo";
  public static final String FLIGHTRPH_PASSENGERFLIGHTINFO = "FlightRPH";
  public static final String PASSENGERRPH_PASSENGERFLIGHTINFO = "PassengerRPH";
  public static final String DENIEDBOARDINGVOLUNTEERIND_PASSENGERFLIGHTINFO = "DeniedBoardingVolunteerInd";
  public static final String CABINTYPE_PASSENGERFLIGHTINFO = "CabinType";
  public static final String PARENTCABINTYPE_PASSENGERFLIGHTINFO = "ParentCabinType";
  public static final String SEATNUMBER_PASSENGERFLIGHTINFO = "SeatNumber";
  public static final String PROGRAMID_PASSENGERFLIGHTINFO = "ProgramID";
  public static final String MEMBERSHIPID_PASSENGERFLIGHTINFO = "MembershipID";
  public static final String LAYOLLEVEL_PASSENGERFLIGHTINFO = "LayolLevel";
  public static final String CHD_PASSENGERFLIGHTINFO = "CHD";
  public static final String GENDER_PASSENGERFLIGHTINFO = "Gender";
  public static final String XBP_PASSENGERFLIGHTINFO = "XBP";
  public static final String TICKETID_PASSENGERFLIGHTINFO = "TicketID";
  public static final String SEQUENCENUMBER_PASSENGERFLIGHTINFO = "SequenceNumber";
  public static final String SNRFLAG_PASSENGERFLIGHTINFO = "SnrFlag";
  public static final String PSM_PASSENGERFLIGHTINFO = "PSM";
  public static final String GROUPCODE_PASSENGERFLIGHTINFO = "GroupCode";
  public static final String GROUPNUMBER_PASSENGERFLIGHTINFO = "GroupNumber";
  public static final String BOARDINGNUMBER_PASSENGERFLIGHTINFO = "BoardingNumber";
  public static final String STANDBYINDICATOR_PASSENGERFLIGHTINFO = "StandByIndicator";
  public static final String STANDBYNUMBER_PASSENGERFLIGHTINFO = "StandByNumber";
  public static final String MARKETINGAIRLINE_PASSENGERFLIGHTINFO = "MarketingAirline";
  public static final String MARKETINGFLIGHTNUMBER_PASSENGERFLIGHTINFO = "MarketingFlightNumber";
  public static final String FARECLASS_PASSENGERFLIGHTINFO = "FareClass";
  public static final String SPECAILSVCINFO_PASSENGERFLIGHTINFO = "SpecialSvcInfo";
  public static final String PSRSTATUS_PASSENGERFLIGHTINFO = "PassengerStatus";
  public static final String FAMILYTAG_PASSENGERFLIGHTINFO = "FamilyTag";
  public static final String HOSTNUMBER_PASSENGERFLIGHTINFO = "HostNumber";
  public static final String TXTMSG_ELEMENT = "TxtMsg";
  public static final String FFPINF_ELEMENT = "FfpInf";
  public static final String TXTMSG_PASSENGERFLIGHTINFO = "TxtInfo";
  public static final String SSQUENCE_PASSENGERFLIGHTINFO = "SequenceNumber";
  public static final String BOARDINGATENUMBER_HBPUOPTIONS = "BoardingGateNumber";
  public static final String BORDINGTIME_HBPUOPTIONS = "BordingTime";
  public static final String DEPARTURETIME_HBPUOPTIONS = "DepartureTime";
  public static final String ARRIVALTIME_HBPUOPTIONS = "ArrivalTime";
  public static final String SEATNUMBER_HBPUOPTIONS = "SeatNumber";
  public static final String BORDINGNUMBER_HBPUOPTIONS = "BordingNumber";
  public static final String MARKETINGAIRLINE_HBPUOPTIONS = "MarketingAirline";
  public static final String MARKETINGFLIGHTNUMBER_HBPUOPTIONS = "MarketingFlightNumber";
  public static final String FARECLASS_HBPUOPTIONS = "FareClass";
  public static final String SURNAME_HBPUOPTIONS = "SurName";
  public static final String CHNNAME_HBPUOPTIONS = "ChnName";
  public static final String DOCTYPE_HBPUOPTIONS = "DocType";
  public static final String DOCID_HBPUOPTIONS = "DocID";
  public static final String PASSENGERSTATUS_HBPUOPTIONS = "PassengerStatus";
  public static final String HOSTNUMBER_HBPUOPTIONS = "HostNumber";
  public static final String CABINTYPE_HBPUOPTIONS = "CabinType";
  public static final String TICKETID_HBPUOPTIONS = "TicketID";
  public static final String GROUPCODE_HBPUOPTIONS = "GroupCode";
  public static final String GROUPNUMBER_HBPUOPTIONS = "GroupNumber";
  public static final String SEQUENCENUMBER_HBPUOPTIONS = "SequenceNumber";
  public static final String FFPAIRLINECODE_HBPUOPTIONS = "FfpAirlineCode";
  public static final String FFPCARDNUMBER_HBPUOPTIONS = "FfpCardNumber";
  public static final String FFPCARDPRIOR_HBPUOPTIONS = "FfpCardPrior";
  public static final String CONTACTTEXT_HBPUOPTIONS = "ContactText";
  public static final String EXTRASEAT_ELEMENT = "ExtraSeat";
  public static final String SEATNUMBER_EXTRASEAT = "SeatNumber";
  public static final String SEATTYPE_EXTRASEAT = "ExtraType";
  public static final String ITEMWEIGHT_EXTRASEAT = "ExtraWeight";
  public static final String SEATCHANGE_ELEMENT = "SeatChange";
  public static final String SEATNUMBER_SEATCHANGE = "SeatNumber";
  public static final String CABINTYPE_SEATCHANGE = "CabinType";
  public static final String ARRIVALAIRPORT_SEATCHANGE = "ArrivalAirport";
  public static final String INFANT_ELEMENT = "Infant";
  public static final String INFINFO_INFANT = "INFInfo";
  public static final String INFETNO_INFANT = "INFEtNo";
  public static final String INFETSEQ_INFANT = "INFEtSequence";
  public static final String TEXTMSG_ELEMENT = "TextMSG";
  public static final String MSGTYPE_TEXTMSG = "MSGType";
  public static final String MSGTEXT_TEXTMSG = "MSGText";
  public static final String CKIN_TEXTMSG = "CKIN";
  public static final String PSPTINFO_ELEMENT = "PsptInfo";
  public static final String PSPTNUMBER_PSPTINFO = "PSPTNumber";
  public static final String COUNTRYNAME_PSPTINFO = "CountryName";
  public static final String BIRTHDATE_PSPTINFO = "BirthDate";
  public static final String GENDER_PSPTINFO = "Gender";
  public static final String MULTIPASSENGER_PSPTINFO = "Multipassenger";
  public static final String RECHECKINFO_ELEMENT = "RecheckInfo";
  public static final String FILINGAIRLINE_RECHECKINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_RECHECKINFO = "FlightNumber";
  public static final String FLIGHTDATE_RECHECKINFO = "FlightDate";
  public static final String CABINTYPE_RECHECKINFO = "CabinType";
  public static final String ARRIVALAIRPORT_RECHECKINFO = "ArrivalAirport";
  public static final String BOUNDINFO_ELEMENT = "BoundInfo";
  public static final String INDICATOR_BOUNDINFO = "BoundType";
  public static final String AIRLINE_BOUNDINFO = "AirlineCode";
  public static final String FLIGHTNUMBER_BOUNDINFO = "FlightNumber";
  public static final String FLIGHTSUFFIX_BOUNDINFO = "FlightSuffix";
  public static final String FLIGHTDATE_BOUNDINFO = "FlightDate";
  public static final String BOUNDSTATUS_BOUNDINFO = "BoundStatus";
  public static final String BRDINFO_BOUNDINFO = "BrdInfo";
  public static final String CABINTYPE_BOUNDINFO = "CabinType";
  public static final String PARENTCABINTYPE_BOUNDINFO = "ParentCabinType";
  public static final String SEATINFO_BOUNDINFO = "SeatInfo";
  public static final String CITY_BOUNDINFO = "ArrivalAirport";
  public static final String EDI_BOUNDINFO = "EDI";
  public static final String REMARK_ELEMENT = "Remark";
  public static final String TEXTINFO_REMARK = "TextInfo";
  public static final String APISHOWREQ_ELEMENT = "ApiShowReq";
  public static final String APISHOW_AIRLINECODE = "AirlineCode";
  public static final String APISHOW_FLIGHTNUMBER = "FlightNumber";
  public static final String APISHOW_DEPARTUREDATE = "DepartureDate";
  public static final String APISHOW_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String APISHOW_HOSTNUMBER = "HostNumber";
  public static final String APISHOW_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String APIINFO_ELEMENT = "ApiInfo";
  public static final String SURNAME_APIINFO = "SurName";
  public static final String GIVENNAME_APIINFO = "GivenName";
  public static final String MIDDLENAME_APIINFO = "MiddleName";
  public static final String GENDER_APIINFO = "Gender";
  public static final String DOCHOLDERNATIONALITY_APIINFO = "DocHolderNationality";
  public static final String BIRTHDATE_APIINFO = "BirthDate";
  public static final String DOCTYPE_APIINFO = "DocType";
  public static final String DOCID_APIINFO = "DocID";
  public static final String DOCISSUECOUNTRY_APIINFO = "DocIssueCountry";
  public static final String BIRTHLOCATION_APIINFO = "BirthLocation";
  public static final String INFANTIND_APIINFO = "InfantInd";
  public static final String PRIMARYHOLDERIND_APIINFO = "PrimaryHolderInd";
  public static final String TRANSFERIND_APIINFO = "TransferInd";
  public static final String DOCISSUESTATEPROV_APIINFO = "DocIssueStateProv";
  public static final String EXPIREDATE_APIINFO = "ExpireDate";
  public static final String EFFECTIVEDATE_APIINFO = "EffectiveDate";
  public static final String APIINFOSOURCE_APIINFO = "ApiInfoSource";
  public static final String BIRTHCOUNTRY_APIINFO = "BirthCountry";
  public static final String RESIDENCECOUNTRY_APIINFO = "ResidenceCountry";
  public static final String REDRESSNUMBER_APIINFO = "RedressNumber";
  public static final String KNOWNTRAVELNUMBER_APIINFO = "KnownTravelerNumber";
  public static final String VISAINFO_ELEMENT = "VisaInfo";
  public static final String VISAISSUEPLACE_VISAINFO = "VisaIssuePlace";
  public static final String EXPIREDATE_VISAINFO = "ExpireDate";
  public static final String EFFECTIVEDATE_VISAINFO = "EffectiveDate";
  public static final String VISATYPE_VISAINFO = "VisaType";
  public static final String VISAID_VISAINFO = "VisaID";
  public static final String OTHERDOC_ELEMENT = "OtherDocInfo";
  public static final String DOCISSUEPLACE_OTHERDOC = "OtherDocIssuePlace";
  public static final String EXPIREDATE_OTHERDOC = "ExpireDate";
  public static final String EFFECTIVEDATE_OTHERDOC = "EffectiveDate";
  public static final String DOCTYPE_OTHERDOC = "OtherDocType";
  public static final String DOCID_OTHERDOC = "OtherDocID";
  public static final String HOMEADDRESS_ELEMENT = "HomeAddress";
  public static final String DESTADDRESS_ELEMENT = "DestAddress";
  public static final String STREETNMBR_ADDRESS = "StreetNmbr";
  public static final String CITYNAME_ADDRESS = "CityName";
  public static final String STATEPROV_ADDRESS = "StateProv";
  public static final String STATECODE_ADDRESS = "StateCode";
  public static final String COUNTRYNAME_ADDRESS = "CountryName";
  public static final String POSTALCODE_ADDRESS = "PostalCode";
  public static final String TELEPHONE_ADDRESS = "Telephone";
  public static final String PHONE_ELEMENT = "PhoneInfo";
  public static final String PHONE_PHONEINFO = "PhoneNO";
  public static final String EMAILINFO_ELEMENT = "EmailInfo";
  public static final String EMAILADDRESS_EMAILINFO = "EmailAddress";
  public static final String DATASTREAM_ELEMENT = "DataStream";
  public static final String STREAMINDEX_DATASTREAM = "StreamIndex";
  public static final String STREAMTYPE_DATASTREAM = "StreamType";
  public static final String CONTENT_DATASTREAM = "DataStreamText";
  public static final String BOARDING_DATASTREAM = "2";
  public static final String BAG_DATASTREAM = "1";
  public static final String BPP_TYPE = "BPP_TYPE";
  public static final String BTP_TYPE = "BTP_TYPE";
  public static final String HBPUO_BORDINGTIME = "BordingTime";
  public static final String HBPUO_BOARDINGGATENUMBER = "BoardingGateNumber";
  public static final String HBPUO_DEPARTURETIME = "DepartureTime";
  public static final String HBPUO_ARRIVALTIME = "ArrivalTime";
  public static final String HBPUO_SURNAME = "SurName";
  public static final String HBPUO_FLIGHTNUMBER = "FlightNumber";
  public static final String HBPUO_DEPARTUREDATE = "DepartureDate";
  public static final String HBPUO_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String HBPUO_SEATNUMBER = "SeatNumber";
  public static final String HBPUO_BOARDINGNUMBER = "BoardingNumber";
  public static final String HBPUO_MARKETINGAIRLINE = "MarketingAirline";
  public static final String HBPUO_MARKETINGFLIGHTNUMBER = "MarketingFlightNumber";
  public static final String HBPUO_FARECLASS = "FareClass";
  public static final String HBPUO_CHNNAME = "ChnName";
  public static final String HBPUO_DOCTYPE = "DocType";
  public static final String HBPUO_DOCID = "DocID";
  public static final String HBPUO_PASSENGERSTATUS = "PassengerStatus";
  public static final String HBPUO_HOSTNUMBER = "HostNumber";
  public static final String HBPUO_CABINTYPE = "CabinType";
  public static final String HBPUO_TICKETID = "TicketID";
  public static final String HBPUO_GROUPCODE = "GroupCode";
  public static final String HBPUO_GROUPNUMBER = "GroupNumber";
  public static final String HBPUO_FFPAIRLINECODE = "FfpAirlineCode";
  public static final String HBPUO_FFPCARDNUMBER = "FfpCardNumber";
  public static final String HBPUO_FFPCARDPRIOR = "FfpCardPrior";
  public static final String HBPUO_CONTACTTEXT = "ContactText";
  public static final String INTERNATIONALPROCESSINGINFO_ELEMENT = "InternationalProcessingInfo";
  public static final String DOCUMENTVERIFIEDIND_INTERNATIONALPROCESSINGINFO = "DocumentVerifiedInd";
  public static final String APPINFO_ELEMENT = "AppInfo";
  public static final String SURNAME_APPINFO = "SurName";
  public static final String GIVENNAME_APPINFO = "GivenName";
  public static final String MIDDLENAME_APPINFO = "MiddleName";
  public static final String GENDER_APPINFO = "Gender";
  public static final String DOCHOLDERNATIONALITY_APPINFO = "DocHolderNationality";
  public static final String BIRTHDATE_APPINFO = "BirthDate";
  public static final String DOCTYPE_APPINFO = "DocType";
  public static final String DOCID_APPINFO = "DocID";
  public static final String APPSTATUS_APPINFO = "AppStatus";
  public static final String RESULT_ELEMENT = "Result";
  public static final String RESULTCODE_RESULT = "ResultCode";
  public static final String RESULTSTRING_RESULT = "ResultString";
  public static final String WARNING_ELEMENT = "Warnings";
  public static final String WARNINGOWNER_WARNING = "WarningOwner";
  public static final String FLTRPH_WARNING = "FLTRPH";
  public static final String PSRRPH_WARNING = "PSRRPH";
  public static final String WARNINGCODE_WARNING = "WarningCode";
  public static final String WARNINGMSG_WARNING = "WarningMsg";
  public static final String USERINFO_ELEMENT = "UserInfo";
  public static final String SESSIONCODE_USERINFO = "SessionCode";
  public static final String NAME_USERINFO = "Name";
  public static final String AIRLINE_USERINFO = "Airline";
  public static final String AIRPORT_USERINFO = "Airport";
  public static final String APPFLAG_USERINFO = "AppFlag";
  public static final String IPADDRESS_USERINFO = "IpAddress";
  public static final String TRANSACTIONNO_USERINFO = "TransactionNo";
  public static final String FULLSCREENFLAG_USERINFO = "FullScreenFlag";
  public static final String BPP_USERINFO = "Bpp";
  public static final String BTP_USERINFO = "Btp";
  public static final String HOSTCONF_ELEMENT = "HostConf";
  public static final String SYSTEM_HOSTCONF = "System";
  public static final String PID_HOSTCONF = "Pid";
  public static final String AGENT_HOSTCONF = "Agent";
  public static final String OFFICE_HOSTCONF = "Office";
  public static final String USERGROUP_HOSTCONF = "UserGroup";
  public static final String SUCCESS_ELEMENT = "SUCCESS";
  public static final String COUNTRY_REQUEST = "Country";
  public static final String DEPARTUREAREA_REQUEST = "departure";
  public static final String ARRIVALAREA_REQUEST = "arrival";
  public static final String AIRLINECODE_REQUEST = "AirlineCode";
  public static final String COUNTRY_RESPONSE = "Country";
  public static final String DEPARTUREAREA_RESPONSE = "departure";
  public static final String ARRIVALAREA_RESPONSE = "arrival";
  public static final String AIRLINECODELIST_RESPONSE = "airlinecodelist";
  public static final String PASSPORTTYPE_RESPONSE = "passportType";
  public static final String PASSPORTISSUECOUNTRY_RESPONSE = "passportIssueCountry";
  public static final String DOCHOLDERNATIONALITY_RESPONSE = "DocHolderNationality";
  public static final String DOCISSUECOUNTRY_RESPONSE = "DocIssueCountry";
  public static final String DOCTYPE_RESPONSE = "DocType";
  public static final String VALUE_RESPONSE = "value";
  public static final String TSETINFO_ELEMENT = "TSETINFO";
  public static final String TSETINFO_MSGFUNC = "MSGFUNC";
  public static final String TSETINFO_TICKETINFO = "TICKETINFO";
  public static final String TICKETINFO_TICKETID = "TicketID";
  public static final String TICKETINFO_SEQUENCENUM = "SequenceNumber";
  public static final String TICKETINFO_FILINGAIRLINE = "FilingAirline";
  public static final String TICKETINFO_FLTNUM = "FlightNumber";
  public static final String TICKETINFO_DEPTAIRPORT = "DepartureAirport";
  public static final String TICKETINFO_DEPTDATE = "DepartureDate";
  public static final String TICKETINFO_ARRIVEAIRPORT = "ArrivalAirport";
  public static final String TICKETINFO_CABIN = "Cabin";
  public static final String TICKETINFO_OVERSEQ = "OvrSeqs";
  public static final String TICKETINFO_ICS = "IcsPnr";
  public static final String TICKETINFO_CRS = "CrsPnr";
  public static final String TICKETINFO_NAME = "Name";
  public static final String TICKETINFO_ERRCODE = "ErrCode";
  public static final String TSETINFO_TICKETDETAIL = "TICKETDETAIL";
  public static final String FREECMD = "FreeCmd";
  public static final String FREE_OUTPUT = "Output";
  public static final String PU_BOARDINGGATENUMBER = "BoardingGateNumber";
  public static final String PU_DEPARTURETIME = "DepartureTime";
  public static final String PU_ARRIVALTIME = "ArrivalTime";
  public static final String BCINPUT_ELEMENT = "BcInput";
  public static final String BCOUTPUT_ELEMENT = "BcOutput";
  public static final String INDEX_BC = "1";
  public static final String BOARDINGGATENUMBER_BC = "BoardingGateNumber";
  public static final String BOARDINGTIME_BC = "BoardingTime";
  public static final String DEPARTURETIME_BC = "DepartureTime";
  public static final String ARRIVALTIME_BC = "ArrivalTime";
  public static final String PRINFOREQ_ELEMENT = "PrInfoReq";
  public static final String BAGTAG_ELEMENT = "BagTag";
  public static final String BAGINDEX_BAGTAG = "BagIndex";
  public static final String BAGTAGNO_BAGTAG = "BagTagNo";
  public static final String BAGTAGDESTCITY_BAGTAG = "BagArrivalAirport";
  public static final String BAGSTATUS_BAGTAG = "BagStatus";
  public static final String INPUT_ELEMENT_HOSTPR = "HostPrInput";
  public static final String INPUT_INDICATOR_HOSTPR = "Indicator";
  public static final String INPUT_AIRLINECODE_HOSTPR = "AirlineCode";
  public static final String INPUT_FLIGHTNUM_HOSTPR = "FlightNumber";
  public static final String INPUT_DEPTAIRPORT_HOSTPR = "DepartureAirport";
  public static final String INPUT_DEPTDATE_HOSTPR = "DepartureDate";
  public static final String INPUT_TKTID_HOSTPR = "TicketID";
  public static final String INPUT_BOARDINGNUM_HOSTPR = "BoardingNumber";
  public static final String INPUT_SEATNUM_HOSTPR = "SeatNumber";
  public static final String INPUT_HOSTNUM_HOSTPR = "HostNumber";
  public static final String INPUT_BAGTAG_HOSTPR = "BagTagNo";
  public static final String INPUT_ELEMENT_INV = "Inv";
  public static final String INPUT_ELEMENT_TKTNO = "TicketID";
  public static final String INPUT_ELEMENT_ETERMNAME = "EtermName";
  public static final String INPUT_ELEMENT_TPNO = "TPNo";
  public static final String PARTNER_INFO = "PartnerInfo";
  public static final String PARTNER_INDEX = "Index";
  public static final String PARTNER_HOSTNUMBER = "HostNumber";
  public static final String PARTNER_TICKETID = "TicketID";
  public static final String PARTNER_SEQUENCENUMBER = "SequenceNumber";
  public static final String AIRLINE_CODE = "AirlineCode";
  public static final String FLIGHT_NUMBER = "FlightNumber";
  public static final String DEPARTURE_DATE = "DepartureDate";
  public static final String DEPARTURE_AIRPORT = "DepartureAirport";
  public static final String ARRIVAL_AIRPORT = "ArrivalAirport";
  public static final String SUR_NAME = "Surname";
  public static final String CHN_NAME = "ChnName";
  public static final String DOC_TYPE = "DocType";
  public static final String DOC_ID = "DocID";
  public static final String CABIN_TYPE = "CabinType";
  public static final String TICKET_ID = "TicketID";
  public static final String BOARDING_NUMBER = "BoardingNumber";
  public static final String SEAT_NUMBER = "SeatNumber";
  public static final String FFP_AIRLINE = "ProgramID";
  public static final String FFP_CARDNUMBER = "MembershipID";
  public static final String FFP_CARDPRIOR = "LayolLevel";
  public static final String BOARDING_GATENUMBER = "BoardingGateNumber";
  public static final String BORDING_TIME = "BordingTime";
  public static final String BOARDING_TIME = "BoardingTime";
  public static final String STOPOVERINFOS = "StopOverInfos";
  public static final String STOPOVERINFO = "StopOverInfo";
  public static final String PRNAME_PASSENGERINFO = "PassengerInfo";
  public static final String PRNAME_FLIGHTINFO = "FlightInfo";
  public static final String PRNAME_PASSENGERFLIGHTINFO = "PassengerFlightInfo";
  public static final String PRNAME_AIRLINECODE = "AirlineCode";
  public static final String PRNAME_FLIGHTNUMBER = "FlightNumber";
  public static final String PRNAME_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRNAME_DEPARTUREDATE = "DepartureDate";
  public static final String PRNAME_SURNAME = "Surname";
  public static final String PRNAME_TICKETID = "TicketID";
  public static final String PRNAME_SEQUENCENUMBER = "SequenceNumber";
  public static final String STOPOVERINFO_ELEMENT = "StopOverInfo";
  public static final String STOPOVERINFO_SEATNUMBER = "SeatNumber";
  public static final String STOPOVERINFO_DEPARTURELAIRPORT = "DepartureAirport";
  public static final String STOPOVERINFO_CABINTYPE = "CabinType";
  public static final String STOPOVERINFO_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String STOPOVERINFO_BOARDINGTIME = "BoardingTime";
  public static final String STOPOVERINFO_BOARDINGGATENUMBER = "BoardingGateNumber";
  public static final String PRNAME_ELEMENT_UPGFLTINFO = "UpgFltInfo";
  public static final String PRNAME_AIRLINE = "AirlineCode";
  public static final String PRNAME_AIRLINE_ = "Airline";
  public static final String PRNAME_UPGFLT_ELEMENT = "UpgFlt";
  public static final String PRNAME_FLIGHTNUM = "FlightNumber";
  public static final String PRNAME_FLIGHTDATE = "FlightDate";
  public static final String PRNAME_ESTTIME = "EstTime";
  public static final String PRNAME_DEPARTUREPORT = "DepartureAirport";
  public static final String PRNAME_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRNAME_BEGINTIME = "BeginTime";
  public static final String PRNAME_ENDTIME = "EndTime";
  public static final String PRNAME_ELEMENT_UPGPSRINF = "UpgPsrInfo";
  public static final String PRNAME_ATTR_FlIGHTNUMBER = "FlightNumber";
  public static final String PRNAME_ATTR_FLIGHTDATE = "FlightDate";
  public static final String PRNAME_ATTR_DEPARTAIRPORT = "DepartureAirport";
  public static final String PRNAME_ATTR_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRNAME_CHILELEMENT_UPGPSR = "UpgFltInfo";
  public static final String PRNAME_SUBELEMENT_PSR = "PsrInfo";
  public static final String PRNAME_ATTR_PSRNAME = "PsrName";
  public static final String PRNAME_ATTR_PNR = "Pnr";
  public static final String PRNAME_ATTR_TICKETNUMBER = "TicketNumber";
  public static final String PRNAME_ATTR_TOURINDEX = "TourIndex";
  public static final String PRNAME_ATTR_CERTTYPE = "CertType";
  public static final String PRNAME_ATTR_CERTNO = "CertNo";
  public static final String PRNAME_ATTR_CONTACTINFO = "ContactInfo";
  public static final String PRNAME_ATTR_PSRLEVEL = "PsrLevel";
  public static final String PRNAME_ATTR_CABIN = "Cabin";
  public static final String PRNAME_ATTR_SEAT = "Seat";
  public static final String PRNAME_ATTR_CARNUMBER = "CardNumber";
  public static final String PRNAME_ATTR_BOARDINGNUM = "BoardingNum";
  public static final String PRNAME_ATTR_CURRENCY = "Currency";
  public static final String PRNAME_ATTR_NATIONALITY = "Nationality";
  public static final String PRNAME_ELEMENT_PSRFLTINFO = "PsrFltInfo";
  public static final String PRNAME_CHILELEMENT_UPGFLTINFO = "UpgFltInfo";
  public static final String PRNAME_SUBELEMENT_AVAILABLECABIN = "AvailableCabin";
  public static final String PRNAME_ATTR_AIRLINECODE = "AirlineCode";
  public static final String PRNAME_ATTR_FLIGHTSTUFFIX = "FlightSuffix";
  public static final String PRNAME_ATTR_UPCABIN = "UpCabin";
  public static final String PRNAME_ATTR_TOCABIN = "ToCabin";
  public static final String PRNAME_ATTR_UPPRICE = "UpPrice";
  public static final String PRNAME_ATTR_UPLEVEL = "UpLevel";
  public static final String PRNAME_ATTR_RESEATNUM = "ReseatNum";
  public static final String PRNAME_ATTR_ISSUCCESS = "IsSuccess";
  public static final String PRNAME_ATTR_ERRORMGR = "ErrorMsg";
  public static final String PRNAME_ATTR_ERRORCODE = "ErrorCode";
  public static final String PRNAME_UPGFLTINFO_PSRNAME = "PsrName";
  public static final String PRNAME_UPGFLTINFO_PNR = "Pnr";
  public static final String PRNAME_UPGFLTINFO_TICKETNUMBER = "TicketNumber";
  public static final String PRNAME_UPGFLTINFO_TOURINDEX = "TourIndex";
  public static final String PRNAMEUPGFLTINFO_CERTTYPE = "CertType";
  public static final String PRNAME_UPGFLTINFO_CENTNO = "CertNo";
  public static final String PRNAME_UPGFLTINFO_CONTACTINFO = "ContactInfo";
  public static final String PRNAME_UPGFLTINFO_PSRLEVEL = "PsrLevel";
  public static final String PRNAME_UPGFLTINFO_CABIN = "Cabin";
  public static final String PRNAME_UPGFLTINFO_SEAT = "Seat";
  public static final String PRNAME_UPGFLTINFO_UPGSTARTTIME = "UpgStartTime";
  public static final String PRNAME_UPGFLTINFO_UPGSTOPTIME = "UpgStopTime";
  public static final String PRNAME_UPGFLTINFO_RULESTARTDATE = "RuleStartDate";
  public static final String PRNAME_UPGFLTINFO_RULEENDDATE = "RuleEndDate";
  public static final String PRNAME_ELEMENT_SEATPRICEREQUEST = "SeatPriceRequest";
  public static final String PRNAME_SEAT_AIRLINECODE = "AirlineCode";
  public static final String PRNAME_SEAT_FLIGHTNUMBER = "FlightNumber";
  public static final String PRNAME_SEAT_FLIGHTSUFFIX = "FlightSuffix";
  public static final String PRNAME_SEAT_FLIGHTDATE = "FlightDate";
  public static final String PRNAME_SEAT_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRNAME_SEAT_ARRIVALAIRPOTR = "ArrivalAirport";
  public static final String PRNAME_SEAT_PLANETYPE = "PlaneType";
  public static final String PRNAME_SEAT_VERSION = "Version";
  public static final String PRNAME_SEAT_PSRLEVEL = "PsrLevel";
  public static final String PRAME_ELEMENT_SEATPRICERESULT = "SeatPriceResult";
  public static final String PRAME_ATTR_PSRLEVEL = "PsrLevel";
  public static final String PRAME_ELEMENT_SEATPRICE = "SeatPrice";
  public static final String PRAME_ELEMENT_PRICELEVEL = "PriceLevel";
  public static final String PRAME_ELEMENT_ROWNO = "RowNo";
  public static final String PRAME_ELEMENT_COLUMNNO = "ColumnNo";
  public static final String PRAME_ELEMENT_PRICE = "Price";
  public static final String PRAME_ELEMENT_SEATS = "Seats";
  public static final String PRAME_ELEMENT_CURRENCY = "Currency";
  public static final String PRAME_ELEMENT_SEATLEVEL = "SeatLevel";
  public static final String PRAME_ELEMENT_MILEAGE = "Mileage";
  public static final String PRNAME_ELEMENT_RULES = "Rules";
  public static final String PRNAME_ELEMENT_SEATFLT = "SeatFlightRequest";
  public static final String PRNAME_SEATFLT_AIRLINECODE = "AirlineCode";
  public static final String PRNAME_SEATFLT_FLIGHTNUMBER = "FlightNumber";
  public static final String PRNAME_SEATFLT_FLAIGHTDATE = "FlightDate";
  public static final String PRNAME_SEATFLT_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRNAME_SEATFLT_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRNAME_ELEMENT_SEATFLTRESULT = "SeatFlightResult";
  public static final String PRNAME_SEATFLTRESULT_AIRLINECODE = "AirlineCode";
  public static final String PRNAME_SEATFLTRESULT_FLIGHTNUMBER = "FlightNumber";
  public static final String PRNAME_SEATFLTRESULT_FLIGHTDATE = "FlightDate";
  public static final String PRNAME_SEATFLTRESULT_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRNAME_SEATFLTRESULT_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRNAME_SEATFLTRESULT_PLAYTYPE = "PlaneType";
  public static final String PRNAME_SEATFLTRESULT_VERSION = "Version";
  public static final String PRNAME_SEATFLTRESULT_SALEFLAG = "SaleFlag";
  public static final String PRNAME_SEATFLTRESULT_CANCLEFLAG = "CancelFlag";
  public static final String PRNAME_UPGINFOER_CERTORET = "CertNoEt";
  public static final String PARAM_HBPUOCHD_FLIGHTINFO = "FlightInfo";
  public static final String PARAM_HBPUOCHD_THROUGHCKIFLIGHTINFO = "ThroughCkiFlightInfo";
  public static final String PARAM_HBPUOCHD_AIRLINECODE = "AirlineCode";
  public static final String PARAM_HBPUOCHD_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_HBPUOCHD_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PARAM_HBPUOCHD_FLIGHTDATE = "FlightDate";
  public static final String PARAM_HBPUOCHD_ARRIVEAIRPORT = "ArrivalAirport";
  public static final String PARAM_HBPUOCHD_PASSENGERINFO = "PassengerInfo";
  public static final String PARAM_HBPUOCHD_SUBPASSENGERINFO = "SubPassengerInfo";
  public static final String PARAM_HBPUOCHD_PRECABINTYPE = "PreCabinType";
  public static final String PARAM_HBPUOCHD_SNR = "SnrFlag";
  public static final String PARAM_HBPUOCHD_SURNAME = "Surname";
  public static final String PARAM_HBPUOCHD_CABINTYPE = "CabinType";
  public static final String PARAM_HBPUOCHD_GENDER = "Gender";
  public static final String PARAM_HBPUOCHD_TICKETID = "TicketID";
  public static final String PARAM_HBPUOCHD_DOCID = "DocID";
  public static final String PARAM_HBPUOCHD_SEQUENCENUMBER = "SequenceNumber";
  public static final String PARAM_HBPUOCHD_HOSTNUMBER = "HostNumber";
  public static final String PARAM_HBPUOCHD_CHD = "CHD";
  public static final String PARAM_HBPUOCHD_DATASTREAM = "DataStream";
  public static final String PARAM_HBPUOCHD_DATASTREAMTEXT = "DataStreamText";
  public static final String PARAM_HBPUOCHD_SEATNUMBER = "SeatNumber";
  public static final String PARAM_HBPUOCHD_CHNNAME = "ChnName";
  public static final String PARAM_HBPUOCHD_BORDINGNUMBER = "BoardingNumber";
  public static final String HBPACHD_ELEMENT_SUBPASSENGERINFO = "SubPassengerInfo";
  public static final String HBPACHD_ELEMENT_CHECKCODE = "CheckCodeInfo";
  public static final String HBPACHD_ELEMENT_PHONEINFO = "PhoneInfo";
  public static final String HBPACHD_ELEMENT_EMAILINFO = "EmailInfo";
  public static final String HBPACHD_ATTRIBUTE_USERNAME = "Username";
  public static final String HBPACHD_ATTRIBUTE_FLIGHTDATE = "FlightDate";
  public static final String HBPACHD_ATTRIBUTE_INDEX = "Index";
  public static final String HBPACHD_ATTRIBUTE_CHECKCODE = "CheckCode";
  public static final String HBPACHD_ATTRIBUTE_TICKETPRICE = "TicketPrice";
  public static final String HBPACHD_ATTRIBUTE_PHONENO = "PhoneNO";
  public static final String HBPACHD_ATTRIBUTE_EMAILADDRESS = "EmailAddress";
  public static final String HBPACHD_ATTRIBUTE_PROGRAMID = "ProgramID";
  public static final String HBPACHD_ATTRIBUTE_MEMBERSHIPID = "MembershipID";
  public static final String PSGBAG_ELEMENT_ADDBAGRQ = "ProcessPassengerAddBagRQ";
  public static final String PARAM_ADDBAG_AILINECODE = "AirlineCode";
  public static final String PARAM_ADDBAG_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_ADDBAG_FLIGHTDATE = "FlightDate";
  public static final String PARAM_ADDBAG_DEPCITY = "DeptAirportCode";
  public static final String PARAM_ADDBAG_ARRCITY = "DestAirportCode";
  public static final String PARAM_ADDBAG_HOSTNUM = "HostNumber";
  public static final String PARAM_ADDBAG_CABIN = "FlightSubClass";
  public static final String PARAM_ADDBAG_ADDBAGQUANTITY_RQ = "AddBagQuantity";
  public static final String PARAM_ADDBAG_ADDBAGQUANTITY_RS = "addBagQuantity";
  public static final String PARAM_ADDBAG_ADDBAGWEIGHT_RQ = "AddBagWeight";
  public static final String PARAM_ADDBAG_ADDBAGWEIGHT_RS = "addBagWeight";
  public static final String PARAM_ADDBAG_ADDBAGWEIGHTUNIT_RQ = "AddBagWeightUnit";
  public static final String PARAM_ADDBAG_ADDBAGWEIGHTUNIT_RS = "addBagWeightUnit";
  public static final String PARAM_ADDBAG_BAGTAGDEST = "BagTagDest";
  public static final String PARAM_ADDBAG_ADDBAGTAG = "AddBagTag";
  public static final String PSGBAG_ELEMENT_ADDBAGRS = "ProcessPassengerAddBagRS";
  public static final String PARAM_ADDBAG_ETNO = "EtNumber";
  public static final String PARAM_ADDBAG_PSGNAME = "PassengerName";
  public static final String PARAM_ADDBAG_PSGSTATUS = "PassengerStatus";
  public static final String PARAM_ADDBAG_PSGSEAT = "SeatNumber";
  public static final String PARAM_ADDBAG_BTNO = "BoardNumber";
  public static final String PARAM_ADDBAG_BTPSTREAMS = "BTPStreams";
  public static final String PARAM_ADDBAG_BTPSTREAM = "BTPStream";
  public static final String PARAM_ADDBAG_BTPSTREAM_VALUE = "value";
  public static final String PARAM_ADDBAG_ADDBAGTAGNUMLIST_RS = "addBagTagNumberList";
  public static final String PSGBAG_ELEMENT_BAGTAG = "BagTag";
  public static final String PARAM_ADDBAG_BAGTAGNUM = "BagTagNumber";
  public static final String PARAM_ADDBAG_BAGARRCITY = "BagTagDestCode";
  public static final String PARAM_ADDBAG_BAGSTATUS = "BagTagStatus";
  public static final String PARAM_ELEMENT_QUERYPASSENGERINFORQ = "QueryPassengerInfoRQ";
  public static final String PARAM_QUERY_AIRLINECODE = "AirlineCode";
  public static final String PARAM_QUERY_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_QUERY_FLIGHTDATE = "FlightDate";
  public static final String PARAM_QUERY_DEPTAIRPORTCODE = "DeptAirportCode";
  public static final String PARAM_QUERY_ETNUMBER = "EtNumber";
  public static final String PARAM_ELEMENT_QUERYPASSENGERINFORS = "QueryPassengerInfoRS";
  public static final String PARAM_QUERY_DESTAIRPORTCODE = "DestAirportCode";
  public static final String PARAM_QUERY_FLIGHTSUBCLASS = "FlightSubClass";
  public static final String PARAM_QUERY_PASSENGERNAME = "PassengerName";
  public static final String PARAM_QUERY_PASSENGERSTATUS = "PassengerStatus";
  public static final String PARAM_QUERY_SEATNUMBER = "SeatNumber";
  public static final String PARAM_QUERY_BOARDNUMBER = "BoardNumber";
  public static final String PARAM_QUERY_HOSTNUMBER = "HostNumber";
  public static final String PARAM_QUERY_BAGINFO = "BagInfo";
  public static final String PARAM_QUERY_CHECKEDBAGQUANTITY = "CheckedBagQuantity";
  public static final String PARAM_QUERY_CHECKEDBAGWEIGHT = "CheckedBagWeight";
  public static final String PARAM_QUERY_CHECKEDWEIGHTUNIT = "CheckedBagWeightUnit";
  public static final String PARAM_QUERY_BAGTAGNUMBERLIST = "BagTagNumberList";
  public static final String PARAM_QUERY_BAGTAG = "BagTag";
  public static final String PARAM_QUERY_BAGTAGNUMBER = "BagTagNumber";
  public static final String PARAM_QUERY_BAGTAGDESTCODE = "BagTagDestCode";
  public static final String PARAM_QUERY_BAGTAGSTATUS = "BagTagStatus";
  public static final String PARAM_ACTIVEBAG_AIRLINECODE = "AirlineCode";
  public static final String PARAM_ACTIVEBAG_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_ACTIVEBAG_FLIGHTDATE = "FlightDate";
  public static final String PARAM_ACTIVEBAG_DEPTAIRPORTCODE = "DeptAirportCode";
  public static final String PARAM_ACTIVEBAG_DESTAIRPORTCODE = "DestAirportCode";
  public static final String PARAM_ACTIVEBAG_HOSTNUMBER = "HostNumber";
  public static final String PARAM_ACTIVEBAG_ETNUMBER = "EtNumber";
  public static final String PARAM_ACTIVEBAG_FLIGHTSUBCLASS = "FlightSubClass";
  public static final String PARAM_ACTIVEBAG_ACTIVEBAGQUANTITY = "ActiveBagQuantity";
  public static final String PARAM_ACTIVEBAG_ACTIVEBAGWEIGHT = "ActiveBagWeight";
  public static final String PARAM_ACTIVEBAG_ACTIVEBAGWEIGHTUNIT = "ActiveBagWeightUnit";
  public static final String PARAM_ACTIVEBAG_PASSENGERNAME = "PassengerName";
  public static final String PARAM_ACTIVEBAG_PASSENGERSTATUS = "PassengerStatus";
  public static final String PARAM_ACTIVEBAG_SEATNUMBER = "SeatNumber";
  public static final String PARAM_ACTIVEBAG_BOARDNUMBER = "BoardNumber";
  public static final String PARAM_ACTIVEBAG_ACTIVEBAGTAG = "ActiveBagTag";
  public static final String PARAM_ACTIVEBAG_ACTIVEBAGTAGNUMBERLIST = "ActiveBagTagNumberList";
  public static final String PARAM_ELEMENT_PROCESSPASSENGEPRINTBAGTAGRQ = "ProcessPassengerPrintBagtagRQ";
  public static final String PARAM_PSGPRINTBAG_AIRLINECODE = "AirlineCode";
  public static final String PARAM_PSGPRINTBAG_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_PSGPRINTBAG_FLIGHTDATE = "FlightDate";
  public static final String PARAM_PSGPRINTBAG_DEPTAIRPORTCODE = "DeptAirportCode";
  public static final String PARAM_PSGPRINTBAG_DESTAIRPORTCODE = "DestAirportCode";
  public static final String PARAM_PSGPRINTBAG_HOSTNUMBER = "HostNumber";
  public static final String PARAM_PSGPRINTBAG_ETNUMBER = "EtNumber";
  public static final String PARAM_PSGPRINTBAG_CabinType = "CabinType";
  public static final String PARAM_PSGPRINTBAG_PSRNAME = "PsrName";
  public static final String PARAM_PSGPRINTBAG_BAGTAG = "BagTag";
  public static final String PARAM_PSGPRINTBAG_BTPSTREAM = "Btpstream";
  public static final String PSGBAG_ELEMENT_REMOVEBAG_RQ = "ProcessPassengerRemoveBagRQ";
  public static final String PSGBAG_ELEMENT_REMOVEBAG_RS = "ProcessPassengerRemoveBagRS";
  public static final String PARAM_REMOVEBAG_AIRLINECODE = "AirlineCode";
  public static final String PARAM_REMOVEBAG_FLIGHTNUMBER = "FlightNumber";
  public static final String PARAM_REMOVEBAG_FLIGHTDATE = "FlightDate";
  public static final String PARAM_REMOVEBAG_DEPTAIRPORTCODE = "DeptAirportCode";
  public static final String PARAM_REMOVEBAG_DESTAIRPORTCODE = "DestAirportCode";
  public static final String PARAM_REMOVEBAG_HOSTNUMBER = "HostNumber";
  public static final String PARAM_REMOVEBAG_ETNUMBER = "EtNumber";
  public static final String PARAM_REMOVEBAG_FLIGHTSUBCLASS = "FlightSubClass";
  public static final String PARAM_REMOVEBAG_REMOVEBAGQUANTITY = "RemoveBagQuantity";
  public static final String PARAM_REMOVEBAG_REMOVEBAGWEIGHT = "RemoveBagWeight";
  public static final String PARAM_REMOVEBAG_REMOVEBAGWEIGHTUNIT = "RemoveBagWeightUnit";
  public static final String PARAM_REMOVEBAG_REMOVEBAGTAGS = "RemoveBagTags";
  public static final String PARAM_REMOVEBAG_REMOVEBAGTAG = "RemoveBagTag";
  public static final String PARAM_REMOVEBAG_PASSENGERNAME = "PassengerName";
  public static final String PARAM_REMOVEBAG_PASSENGERSTATUS = "PassengerStatus";
  public static final String PARAM_REMOVEBAG_SEATNUMBER = "SeatNumber";
  public static final String PARAM_REMOVEBAG_BOARDNUMBER = "BoardNumber";
  public static final String PARAM_REMOVEBAG_BAGINFO = "BagInfo";
  public static final String PARAM_REMOVEBAG_FBA = "FBA";
  public static final String PARAM_REMOVEBAG_FBAUNIT = "FBAUnit";
  public static final String PARAM_REMOVEBAG_CHECKEDBAGQUANTITY = "CheckedBagQuantity";
  public static final String PARAM_REMOVEBAG_CHECKEDBAGWEIGHT = "CheckedBagWeight";
  public static final String PARAM_REMOVEBAG_CHECKEDBAGWEIGHTUNIT = "CheckedBagWeightUnit";
  public static final String PARAM_REMOVEBAG_BAGTAGNUMBERLIST = "BagTagNumberList";
  public static final String PARAM_REMOVEBAG_BAGTAG = "BagTag";
  public static final String PARAM_REMOVEBAG_BAGTAGNUMBER = "BagTagNumber";
  public static final String PARAM_REMOVEBAG_BAGTAGDESTCODE = "BagTagDestCode";
  public static final String PARAM_REMOVEBAG_BAGTAGSTATUS = "BagTagStatus";
  public static final String PARAM_ICS_SEATMAP_ICSSEATMAPREQUEST = "IcsSeatMapRequest";
  public static final String PARAM_ICS_SEATMAP_ICSSEATMAPRESULT = "IcsSeatMapResult";
  public static final String PARAM_ICS_SEATMAP_PNR = "PNR";
  public static final String PARAM_ICS_SEATMAP_PLANETYPE = "PlaneType";
  public static final String PARAM_ICS_SEATMAP_REMARK = "Remark";
  public static final String PARAM_ICS_SEATMAP_SEATMAP = "SeatMap";
  public static final String PARAM_ICS_SEATMAP_SEATMAPOLD = "SeatMapOld";
  public static final String PARAM_PRECKIBOOK_PRECKIBOOKREQUEST = "PreCkiBookRequest";
  public static final String PARAM_PRECKIBOOK_PRECKIBOOKRESULT = "PreCkiBookResult";
  public static final String PARAM_PRECKIBOOK_DEPARTURETIME = "DepartureTime";
  public static final String PARAM_PRECKIBOOK_FLIGHTCLASS = "FlightClass";
  public static final String PARAM_PRECKIBOOK_PASSENGERNAME = "PassengerName";
  public static final String PARAM_PRECKIBOOK_CERTIFICATETYPE = "CertificateType";
  public static final String PARAM_PRECKIBOOK_CERTIFICATENUMBER = "CertificateNumber";
  public static final String PARAM_PRECKIBOOK_MOBILE = "Mobile";
  public static final String PARAM_PRECKIBOOK_RESULTCODE = "ResultCode";
  public static final String PARAM_PRECKIBOOK_RESULTMSG = "ResultMsg";
  public static final String PRECKIBOOK_FLIGHTDATE = "FlightDate";
  public static final String PRECKIDELBOOK_PRECKIXEBOOKREQUEST = "PreCkiXeBookRequest";
  public static final String PRECKIDELBOOK_AIRLINECODE = "AirlineCode";
  public static final String PRECKIDELBOOK_FLIGHTNUMBER = "FlightNumber";
  public static final String PRECKIDELBOOK_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRECKIDELBOOK_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRECKIDELBOOK_FLIGHTDATE = "FlightDate";
  public static final String PRECKIDELBOOK_TICKETID = "TicketID";
  public static final String PRECKIDELBOOK_SEQUENCENUMBER = "SequenceNumber";
  public static final String RESULTCODE_PRECKIXEBOOKRESULT = "ResultCode";
  public static final String RESULTMSG_PRECKIXEBOOKRESULT = "ResultMsg";
  public static final String PRECKIXEBOOKRESULT_ELEMENT = "PreCkiXeBookResult";
  public static final String PRECKIBOOKINFO_PRECKIBOOKINFOREQUEST = "PreCkiBookInfoRequest";
  public static final String PRECKIBOOKINFO_AIRLINECODE = "AirlineCode";
  public static final String PRECKIBOOKINFO_FLIGHTNUMBER = "FlightNumber";
  public static final String PRECKIBOOKINFO_DEPARTUREAIRPORT = "DepartureAirport";
  public static final String PRECKIBOOKINFO_ARRIVALAIRPORT = "ArrivalAirport";
  public static final String PRECKIBOOKINFO_FLIGHTDATE = "FlightDate";
  public static final String PRECKIBOOKINFO_TICKETID = "TicketID";
  public static final String PRECKIBOOKINFO_SEQUENCENUMBER = "SequenceNumber";
  public static final String PRECKIBOOKINFO_FLIGHTCLASS = "FlightClass";
  public static final String PRECKIBOOKINFO_PASSENGERNAME = "PassengerName";
  public static final String PRECKIBOOKINFO_SEATNUMBER = "SeatNumber";
  public static final String PRECKIBOOKINFO_PNR = "PNR";
  public static final String PRECKIBOOKINFORESULT_ELEMENT = "PreCkiBookInfoResult";
  public static final String BRD_REQUEST = "BrdRequest";
  public static final String SEUT_REQUEST = "SeutRequest";
  public static final String PAX_AIRLINECODE = "AirlineCode";
  public static final String PAX_FLIGHTNUMBER = "FlightNumber";
  public static final String PAX_FLIGHTSUFFIX = "FlightSuffix";
  public static final String PAX_FLIGHTDATE = "FlightDate";
  public static final String PAX_DEPTCITY = "DeptCity";
  public static final String PAX_CERTTYPE = "CertType";
  public static final String PAX_CERTNO = "CertNo";
  public static final String PAX_TICKETID = "TicketID";
  public static final String PAX_BOARDINGNUMBER = "BoardingNumber";
  public static final String PAX_BRDINFO = "PaxBrdInfo";
  public static final String PAX_SEUTINFO = "PaxSeutInfo";
  public static final String PAX_BRDSTATUS = "BrdStatus";
  public static final String PAX_BRDTIME = "BrdTime";
  public static final String PAX_HOSTNUMBER = "HostNumber";
  public static final String PAX_BRDGATE = "BrdGate";
  public static final String PAX_SEATNBR = "SeatNbr";
  public static final String PAX_PASSFLAG = "PassFlag";
  public static final String PAX_SEUTTIME = "SeutTime";
  public static final String PAX_SEUTSTATUS = "SeutStatus";
  public static final String PAX_SEUTCHANNEL = "SeutChannel";
  public static final String PAX_SEUTTXT = "SeutTxt";
  public static final String PAX_SEUTIOC = "SeutIoc";
  public static final String PAX_HANDFLAG = "HandFlag";
  public static final String PAX_PSGSCID = "PsgScid";
  public static final String PAX_BRDPCODE = "BrdpCode";
  public static final String PAX_BRDPTYPE = "BrdpType";
  public static final String PAX_SEUTSIGN = "SeutSign";
  public static final String PAX_BRDPPSGCODE = "BrdpPsgCode";
  public static final String PAX_EBPFLAG = "EbpFlag";
  public static final String PAX_SEUTDATAFLAG = "SeutDataFlag";
  public static final String PAX_RDID = "RdId";
  public static final String PAX_COMN = "Comn";
  public static final String PAX_PSGDOCVALIDITYDATE = "PsgDocValidityDate";
  public static final String PAX_PSGDOCISSUEDPT = "PsgDocIssueDpt";
  public static final String PAX_PSGADDR = "PsgAddr";
  public static final String PAX_PSGBIRTHDAY = "PsgBirthday";
  public static final String PAX_PSGNAT = "PsgNat";
  public static final String PAX_PSGNAMEZH = "PsgNameZh";
  public static final String PAX_PSGNAMEEN = "PsgNameEn";
  public static final String PAX_PSGGENDER = "PsgGender";
  public static final String PAX_PSGPHOTO = "PsgPhoto";
  public static final String PAX_PSGDOCNBR = "PsgDocNbr";
  public static final String PAX_PSGDOCTYPE = "PsgDocType";
  public static final String PRF_VALUE = "Value";
  public static final String PRF_ELEMENT = "PrfRequest";
  public static final String PRF_DOCID = "DocID";
  public static final String PRF_BKDAYPREF = "BkDayPref";
  public static final String PRF_CKINTIMEPREF = "CkinTimePref";
  public static final String PRF_DEPTMRANGPREF = "DepTmRangPref";
  public static final String PRF_SEATPREFS = "SeatPrefs";
  public static final String PRF_SEATPREF = "SeatPref";
  public static final String PRF_CKINTYPEPREFS = "CkinTypePrefs";
  public static final String PRF_CKINTYPEPREF = "CkinTypePref";
  public static final String PRF_TYPE = "Type";
  public static final String PRF_PRATE = "Prate";
  public static final String EBAG_REQUEST = "DBtpRequest";
  public static final String EBAG_AIRLINECODE = "AirlineCode";
  public static final String EBAG_FLIGHTDATE = "FlightDate";
  public static final String EBAG_FLIGHTNUMBER = "FlightNumber";
  public static final String EBAG_TOCITY = "ArrivalAirport";
  public static final String EBAG_TICKETID = "EtNumber";
  public static final String EBAG_CABINTYPE = "CabinType";
  public static final String EBAG_PASSENGERNAME = "PassengerName";
  public static final String EBAG_HOSTNUMBER = "HostNumber";
  public static final String EBAG_RESPONSE = "DBtpResponse";
  public static final String EBAG_BTPSTREAM = "Btpstream";
  public static final String EBAG_DBTPSTR = "DBtpStr";
  public static final String EBAG_FROMCITY = "DepartureAirport";
  public static final String EBAG_BOARDINGNUMBER = "BoardingNumber";
  public static final String FLIGHTSTATUS = "FlightStatus";
  public static final String FFBASICINFO = "FfBasicInfo";
  public static final String FFAIRLINCODE = "FfAirlineCode";
  public static final String FFCARDNO = "FfCardNo";
  public static final String FFLEVEL = "FfLevel";
  public static final String ASEASVClIST = "AseASVCList";
  public static final String ASVC = "ASVC";
  public static final String EMDTYPECODE = "EmdTypeCode";
  public static final String SSCODE = "SsrCode";
  public static final String SUBCODE = "SubCode";
  public static final String DOCUMENTNUM = "DocumentNum";
  public static final String COUPONNUM = "CouponNum";
  public static final String EMDSTATUS = "EmdStatus";
  public static final String BAGWEIGHT = "BagWeight";
  public static final String EXPENSE = "Expense";
  public static final String CONNECTIONINFO = "ConnectionInfo";
  public static final String AUTOINDICATOR = "AutoIndicator";
  public static final String FBA = "FBA";
  public static final String FBAUNIT = "FBAUnit";
  public static final String FBAUNIT_KG = "KG";
  public static final String FBAUNIT_PC = "PC";
  public static final String JCS_REQUEST = "JcsRequest";
  public static final String JCS_AIRLINE_CODE = "AirlineCode";
  public static final String JCS_FLIGHT_NUMBER = "FlightNumber";
  public static final String JCS_FLIGHT_DATE = "FlightDate";
  public static final String JCS_DEPARTURE_AIRPORT = "DepartureAirport";
  public static final String JCS_ARRIVAL_AIRPORT = "ArrivalAirport";
  public static final String JCS_CABINTYPE = "CabinType";
  public static final String JCS_HOSTNUMBER = "HostNumber";
  public static final String JCS_SEATNUMBER = "SeatNumber";
  public static final String JCS_TYPE = "JcsType";
  public static final String JCS_RESULT = "JcsResult";
  public static final String JCS_RESULTCODE = "ResultCode";
  public static final String JCS_RESULTMSG = "ResultMsg";
  public static final String HBPW = "HBPW";
  public static final String FLIGHTBANNEDCONTROL_SWATCH = "FLIGHTBANNEDCONTROL_SWATCH";
  public static final String PP = "PP";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.CheckinConStr
 * JD-Core Version:    0.6.0
 */