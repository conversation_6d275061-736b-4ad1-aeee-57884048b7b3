
package com.hna.shopping.ibe.interfaces.dto.searchone.request;
import java.util.List;

import lombok.Data;
@Data
public class Flights {

    private int id;
    private String carrier;
    private String flightNumber;
    private String operatingCarrier;
    private String operatingFlightNumber;
    private String departureAirport;
    private String departureTerminal;
    private String arrivalAirport;
    private String arrivalTerminal;
    private DepartureDate departureDate;
    private DepartureTime departureTime;
    private ArrivalDate arrivalDate;
    private ArrivalTime arrivalTime;
    private boolean aircraftChange;
    private List<AircraftTypes> aircraftTypes;
    private String serviceType;
    private int distance;
    private int duration;
    private String displayCarrier;
    private String mealService;
    private boolean et;
    private boolean ASR = false;
    private int stopQuantity;
    private String avl;

}