package com.travelsky.hub.wdoe.util;

public final class PmcSeatchartConstr
{
  public static final String PMCSEATCHART_WDOE_ELEMENT = "WDoe";
  public static final String PMCSEATCHART_FROMCITY = "DepartureAirport";
  public static final String PMCSEATCHART_TOCITY = "ArrivalAirport";
  public static final String PMCSEATCHART_REQUEST = "PmcSeInput";
  public static final String PMCSEATCHART_RESPONSE = "SeatMap";
  public static final String PMCSEATCHART_AIRLINECODE = "AirlineCode";
  public static final String PMCSEATCHART_FLIGHTNUMBER = "FlightNumber";
  public static final String PMCSEATCHART_FLIGHTDATE = "DepartureDate";
  public static final String PMCSEATCHART_CABINTYPE = "CabinType";
  public static final String PMCSEATCHART_TICKETID = "TicketID";
  public static final String PMCSEATCHART_FLIGHTLAYOUT = "FlightLayout";
  public static final String PMCSEATCHART_PLANETYPE = "AircraftType";
  public static final String PMCSEATCHART_PLANECLASS = "AircraftVersion";
  public static final String PMCSEATCHART_SEATMAP = "FlightPlan";
  public static final String PMCSEATCHART_SEATATTR = "SeatAttr";
  public static final String PMCSEATCHART_SEATATTR_KEY = "Key";
  public static final String PMCSEATCHART_SEATATTR_VALUE = "Value";
  public static final String PMCSEATCHART_SEATATTR_SEATNUMBER = "SeatNumber";
  public static final String PMCSEATCHART_SEATATTR_GENDER = "Gender";
  public static final String PMCSEATCHART_SEATATTR_PASSENGERTYPE = "PassengerType";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.PmcSeatchartConstr
 * JD-Core Version:    0.6.0
 */