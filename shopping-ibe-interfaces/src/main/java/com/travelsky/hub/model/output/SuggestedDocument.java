/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SuggestedDocument
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3133304731681439891L;
/*    */   private String prompt;
/*    */   private List<DocumentType> documentTypes;
/*    */ 
/*    */   public String getPrompt()
/*    */   {
/* 26 */     return this.prompt;
/*    */   }
/*    */ 
/*    */   public void setPrompt(String prompt)
/*    */   {
/* 33 */     this.prompt = prompt;
/*    */   }
/*    */ 
/*    */   public List<DocumentType> getDocumentTypes()
/*    */   {
/* 40 */     return this.documentTypes;
/*    */   }
/*    */ 
/*    */   public void setDocumentTypes(List<DocumentType> documentTypes)
/*    */   {
/* 47 */     this.documentTypes = documentTypes;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SuggestedDocument
 * JD-Core Version:    0.6.0
 */