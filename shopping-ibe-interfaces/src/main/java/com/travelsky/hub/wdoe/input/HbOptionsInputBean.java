/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class HbOptionsInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2602343638611889323L;
/*  18 */   private String airlineCode = "";
/*     */ 
/*  20 */   private String flightNumber = "";
/*     */ 
/*  22 */   private String flightDate = "";
/*     */ 
/*  24 */   private String cabin = "";
/*     */ 
/*  26 */   private String destCity = "";
/*     */ 
/*  28 */   private String deptCity = "";
/*     */ 
/*  30 */   private String hostNumber = "";
/*     */ 
/*  32 */   private String seatNo = "";
/*     */   private boolean isSNR;
/*     */   private String tktNumber;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ckinMessage;
/*     */   private String ctcMessage;
/*     */   private String msgMessage;
/*     */   private String psmMessage;
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  39 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  59 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/*  79 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  88 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/*  95 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 103 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 112 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/* 120 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 128 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 137 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 145 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 153 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/* 161 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 169 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 177 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/* 185 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public boolean isSNR()
/*     */   {
/* 193 */     return this.isSNR;
/*     */   }
/*     */ 
/*     */   public void setSNR(boolean isSNR)
/*     */   {
/* 201 */     this.isSNR = isSNR;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 209 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 217 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 225 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 233 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public String getCkinMessage()
/*     */   {
/* 240 */     return this.ckinMessage;
/*     */   }
/*     */ 
/*     */   public void setCkinMessage(String ckinMessage)
/*     */   {
/* 247 */     this.ckinMessage = ckinMessage;
/*     */   }
/*     */ 
/*     */   public String getCtcMessage()
/*     */   {
/* 254 */     return this.ctcMessage;
/*     */   }
/*     */ 
/*     */   public void setCtcMessage(String ctcMessage)
/*     */   {
/* 261 */     this.ctcMessage = ctcMessage;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 269 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 277 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getMsgMessage()
/*     */   {
/* 284 */     return this.msgMessage;
/*     */   }
/*     */ 
/*     */   public void setMsgMessage(String msgMessage)
/*     */   {
/* 291 */     this.msgMessage = msgMessage;
/*     */   }
/*     */ 
/*     */   public String getPsmMessage()
/*     */   {
/* 298 */     return this.psmMessage;
/*     */   }
/*     */ 
/*     */   public void setPsmMessage(String psmMessage)
/*     */   {
/* 305 */     this.psmMessage = psmMessage;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.HbOptionsInputBean
 * JD-Core Version:    0.6.0
 */