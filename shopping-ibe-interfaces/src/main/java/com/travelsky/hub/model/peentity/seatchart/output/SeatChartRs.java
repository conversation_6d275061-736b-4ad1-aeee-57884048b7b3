/*    */ package com.travelsky.hub.model.peentity.seatchart.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SeatChartRs
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6786577902053495719L;
/*    */   private String resultCode;
/*    */   private String resultMessage;
/*    */   private List<LineInfo> lineInfos;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 13 */     this.resultCode = resultCode; } 
/* 13 */   public void setResultMessage(String resultMessage) { this.resultMessage = resultMessage; } 
/* 13 */   public void setLineInfos(List<LineInfo> lineInfos) { this.lineInfos = lineInfos;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 25 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 30 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public List<LineInfo> getLineInfos()
/*    */   {
/* 35 */     return this.lineInfos;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.output.SeatChartRs
 * JD-Core Version:    0.6.0
 */