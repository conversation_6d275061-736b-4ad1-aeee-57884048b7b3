/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfoBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String deptDate;
/*     */   private String flightNo;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String deptTime;
/*  26 */   private String optService = "";
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  32 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  39 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setDeptDate(String deptDate)
/*     */   {
/*  47 */     this.deptDate = deptDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  54 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public String getDeptDate()
/*     */   {
/*  61 */     return this.deptDate;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  68 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  76 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  83 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/*  90 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/*  97 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getOptService()
/*     */   {
/* 104 */     return this.optService;
/*     */   }
/*     */ 
/*     */   public void setOptService(String optService)
/*     */   {
/* 111 */     this.optService = optService;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FlightInfoBean
 * JD-Core Version:    0.6.0
 */