/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class BoundInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3581076985562758673L;
/*     */   private String boundType;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String checkInStatus;
/*     */   private String cabinType;
/*     */   private String airport;
/*     */   private String edi;
/*     */ 
/*     */   public String getBoundType()
/*     */   {
/*  68 */     return this.boundType;
/*     */   }
/*     */ 
/*     */   public void setBoundType(String boundType)
/*     */   {
/*  75 */     this.boundType = boundType;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  82 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  89 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getCheckInStatus()
/*     */   {
/*  96 */     return this.checkInStatus;
/*     */   }
/*     */ 
/*     */   public void setCheckInStatus(String checkInStatus)
/*     */   {
/* 103 */     this.checkInStatus = checkInStatus;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 110 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 117 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getAirport()
/*     */   {
/* 124 */     return this.airport;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 130 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 137 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setAirport(String airport)
/*     */   {
/* 144 */     this.airport = airport;
/*     */   }
/*     */ 
/*     */   public String getEdi()
/*     */   {
/* 151 */     return this.edi;
/*     */   }
/*     */ 
/*     */   public void setEdi(String edi)
/*     */   {
/* 158 */     this.edi = edi;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 164 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 171 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BoundInfo
 * JD-Core Version:    0.6.0
 */