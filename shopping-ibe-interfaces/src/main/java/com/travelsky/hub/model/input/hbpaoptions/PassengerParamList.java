/*     */ package com.travelsky.hub.model.input.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerParamList
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2332610434463401381L;
/*     */   private PassengerQryInfo passengerQryInfo;
/*     */   private FrequentFlyerInformation frequentFlyerInformation;
/*     */   private List<IdentityInformation> identityInformation;
/*     */   private List<PassengerAttribute> passengerAttribute;
/*     */   private List<ElectronicTicketInformation> electronicTicketInformation;
/*     */   private List<SeatInformation> seatInformation;
/*     */ 
/*     */   public PassengerQryInfo getPassengerQryInfo()
/*     */   {
/*  42 */     return this.passengerQryInfo;
/*     */   }
/*     */ 
/*     */   public void setPassengerQryInfo(PassengerQryInfo passengerQryInfo)
/*     */   {
/*  49 */     this.passengerQryInfo = passengerQryInfo;
/*     */   }
/*     */ 
/*     */   public FrequentFlyerInformation getFrequentFlyerInformation()
/*     */   {
/*  56 */     return this.frequentFlyerInformation;
/*     */   }
/*     */ 
/*     */   public void setFrequentFlyerInformation(FrequentFlyerInformation frequentFlyerInformation)
/*     */   {
/*  63 */     this.frequentFlyerInformation = frequentFlyerInformation;
/*     */   }
/*     */ 
/*     */   public List<IdentityInformation> getIdentityInformation()
/*     */   {
/*  69 */     return this.identityInformation;
/*     */   }
/*     */ 
/*     */   public void setIdentityInformation(List<IdentityInformation> identityInformation)
/*     */   {
/*  76 */     this.identityInformation = identityInformation;
/*     */   }
/*     */ 
/*     */   public List<PassengerAttribute> getPassengerAttribute()
/*     */   {
/*  83 */     return this.passengerAttribute;
/*     */   }
/*     */ 
/*     */   public void setPassengerAttribute(List<PassengerAttribute> passengerAttribute)
/*     */   {
/*  90 */     this.passengerAttribute = passengerAttribute;
/*     */   }
/*     */ 
/*     */   public List<ElectronicTicketInformation> getElectronicTicketInformation()
/*     */   {
/*  97 */     return this.electronicTicketInformation;
/*     */   }
/*     */ 
/*     */   public void setElectronicTicketInformation(List<ElectronicTicketInformation> electronicTicketInformation)
/*     */   {
/* 104 */     this.electronicTicketInformation = electronicTicketInformation;
/*     */   }
/*     */ 
/*     */   public List<SeatInformation> getSeatInformation()
/*     */   {
/* 111 */     return this.seatInformation;
/*     */   }
/*     */ 
/*     */   public void setSeatInformation(List<SeatInformation> seatInformation)
/*     */   {
/* 118 */     this.seatInformation = seatInformation;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.PassengerParamList
 * JD-Core Version:    0.6.0
 */