/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class RemoveBagInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String deptAirportCode;
/*     */   private String destAirportCode;
/*     */   private String etNumber;
/*     */   private String flightSubClass;
/*     */   private String removeBagQuantity;
/*     */   private String removeBagWeight;
/*     */   private String removeBagWeightUnit;
/*     */   private List<String> removeBagTags;
/*     */ 
/*     */   public String getDeptAirportCode()
/*     */   {
/*  53 */     return this.deptAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDeptAirportCode(String deptAirportCode)
/*     */   {
/*  60 */     this.deptAirportCode = deptAirportCode;
/*     */   }
/*     */ 
/*     */   public String getDestAirportCode()
/*     */   {
/*  67 */     return this.destAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDestAirportCode(String destAirportCode)
/*     */   {
/*  74 */     this.destAirportCode = destAirportCode;
/*     */   }
/*     */ 
/*     */   public String getEtNumber()
/*     */   {
/*  81 */     return this.etNumber;
/*     */   }
/*     */ 
/*     */   public void setEtNumber(String etNumber)
/*     */   {
/*  88 */     this.etNumber = etNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSubClass()
/*     */   {
/*  95 */     return this.flightSubClass;
/*     */   }
/*     */ 
/*     */   public void setFlightSubClass(String flightSubClass)
/*     */   {
/* 102 */     this.flightSubClass = flightSubClass;
/*     */   }
/*     */ 
/*     */   public String getRemoveBagQuantity()
/*     */   {
/* 109 */     return this.removeBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setRemoveBagQuantity(String removeBagQuantity)
/*     */   {
/* 116 */     this.removeBagQuantity = removeBagQuantity;
/*     */   }
/*     */ 
/*     */   public String getRemoveBagWeight()
/*     */   {
/* 123 */     return this.removeBagWeight;
/*     */   }
/*     */ 
/*     */   public void setRemoveBagWeight(String removeBagWeight)
/*     */   {
/* 130 */     this.removeBagWeight = removeBagWeight;
/*     */   }
/*     */ 
/*     */   public String getRemoveBagWeightUnit()
/*     */   {
/* 137 */     return this.removeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public void setRemoveBagWeightUnit(String removeBagWeightUnit)
/*     */   {
/* 144 */     this.removeBagWeightUnit = removeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public List<String> getRemoveBagTags()
/*     */   {
/* 151 */     return this.removeBagTags;
/*     */   }
/*     */ 
/*     */   public void setRemoveBagTags(List<String> removeBagTags)
/*     */   {
/* 158 */     this.removeBagTags = removeBagTags;
/*     */   }
/*     */ 
/*     */   public static long getSerialversionuid()
/*     */   {
/* 165 */     return 1L;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.RemoveBagInputBean
 * JD-Core Version:    0.6.0
 */