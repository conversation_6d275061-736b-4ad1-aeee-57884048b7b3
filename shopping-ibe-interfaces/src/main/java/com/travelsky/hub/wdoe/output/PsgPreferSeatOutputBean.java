/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PsgPreferSeatOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String assignNo;
/*    */ 
/*    */   public String getAssignNo()
/*    */   {
/* 23 */     return this.assignNo;
/*    */   }
/*    */ 
/*    */   public void setAssignNo(String assignNo)
/*    */   {
/* 30 */     this.assignNo = assignNo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PsgPreferSeatOutputBean
 * JD-Core Version:    0.6.0
 */