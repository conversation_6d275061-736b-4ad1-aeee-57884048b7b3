/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class OutBoundInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String filingAirline;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String cabinType;
/*     */   private String arrivalAirport;
/*     */   private String ediFlag;
/*     */   private String psrCkiStatus;
/*     */   private String seatInfo;
/*     */   private String parentCabinType;
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  50 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  57 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getEdiFlag()
/*     */   {
/*  68 */     return this.ediFlag;
/*     */   }
/*     */ 
/*     */   public void setEdiFlag(String ediFlag)
/*     */   {
/*  75 */     this.ediFlag = ediFlag;
/*     */   }
/*     */ 
/*     */   public OutBoundInfo()
/*     */   {
/*  81 */     this.filingAirline = "";
/*  82 */     this.flightNumber = "";
/*  83 */     this.flightDate = "";
/*  84 */     this.cabinType = "";
/*  85 */     this.arrivalAirport = "";
/*  86 */     this.ediFlag = "";
/*  87 */     this.parentCabinType = "";
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  94 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 101 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 110 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 117 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getPsrCkiStatus()
/*     */   {
/* 124 */     return this.psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getFilingAirline()
/*     */   {
/* 131 */     return this.filingAirline;
/*     */   }
/*     */ 
/*     */   public void setFilingAirline(String filingAirline)
/*     */   {
/* 138 */     this.filingAirline = filingAirline;
/*     */   }
/*     */ 
/*     */   public void setPsrCkiStatus(String psrCkiStatus)
/*     */   {
/* 145 */     this.psrCkiStatus = psrCkiStatus;
/*     */   }
/*     */ 
/*     */   public String getParentCabinType()
/*     */   {
/* 153 */     return this.parentCabinType;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 160 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getSeatInfo()
/*     */   {
/* 167 */     return this.seatInfo;
/*     */   }
/*     */ 
/*     */   public void setSeatInfo(String seatInfo)
/*     */   {
/* 174 */     this.seatInfo = seatInfo;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 181 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setParentCabinType(String parentCabinType)
/*     */   {
/* 188 */     this.parentCabinType = parentCabinType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.OutBoundInfo
 * JD-Core Version:    0.6.0
 */