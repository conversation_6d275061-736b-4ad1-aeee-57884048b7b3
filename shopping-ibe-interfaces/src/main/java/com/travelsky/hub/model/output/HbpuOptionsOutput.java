/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class HbpuOptionsOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1882120281649260090L;
/*     */   private String resultCode;
/*     */   private String resultMsg;
/*     */   private String boardingGateNumber;
/*     */   private String bordingTime;
/*     */   private String departureTime;
/*     */   private String arrivalTime;
/*     */   private String seatNumber;
/*     */   private String bordingNumber;
/*     */   private List<BoundInfo> boundInfos;
/*     */   private String marketingAirline;
/*     */   private String marketingFlightNumber;
/*     */   private String fareClass;
/*     */   private String surName;
/*     */   private String docType;
/*     */   private String docID;
/*     */   private String passengerStatus;
/*     */   private String hostNumber;
/*     */   private String cabinType;
/*     */   private String ticketID;
/*     */   private String groupCode;
/*     */   private String groupNumber;
/*     */   private String sequenceNumber;
/*     */   private String ffpAirlineCode;
/*     */   private String ffpCardNumber;
/*     */   private String ffpCardPrior;
/*     */   private String contactText;
/*     */   private List<TextMsg> textMsgList;
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 159 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 166 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getBordingTime()
/*     */   {
/* 173 */     return this.bordingTime;
/*     */   }
/*     */ 
/*     */   public String getMarketingFlightNumber()
/*     */   {
/* 179 */     return this.marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setMarketingFlightNumber(String marketingFlightNumber)
/*     */   {
/* 186 */     this.marketingFlightNumber = marketingFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setBordingTime(String bordingTime)
/*     */   {
/* 193 */     this.bordingTime = bordingTime;
/*     */   }
/*     */ 
/*     */   public String getDepartureTime()
/*     */   {
/* 200 */     return this.departureTime;
/*     */   }
/*     */ 
/*     */   public void setDepartureTime(String departureTime)
/*     */   {
/* 207 */     this.departureTime = departureTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 214 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String arrivalTime)
/*     */   {
/* 221 */     this.arrivalTime = arrivalTime;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 228 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 235 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBordingNumber()
/*     */   {
/* 242 */     return this.bordingNumber;
/*     */   }
/*     */ 
/*     */   public String getMarketingAirline()
/*     */   {
/* 249 */     return this.marketingAirline;
/*     */   }
/*     */ 
/*     */   public String getFareClass()
/*     */   {
/* 255 */     return this.fareClass;
/*     */   }
/*     */ 
/*     */   public void setFareClass(String fareClass)
/*     */   {
/* 262 */     this.fareClass = fareClass;
/*     */   }
/*     */ 
/*     */   public void setMarketingAirline(String marketingAirline)
/*     */   {
/* 268 */     this.marketingAirline = marketingAirline;
/*     */   }
/*     */ 
/*     */   public void setBordingNumber(String bordingNumber)
/*     */   {
/* 274 */     this.bordingNumber = bordingNumber;
/*     */   }
/*     */ 
/*     */   public List<BoundInfo> getBoundInfos()
/*     */   {
/* 281 */     return this.boundInfos;
/*     */   }
/*     */ 
/*     */   public void setBoundInfos(List<BoundInfo> boundInfos)
/*     */   {
/* 288 */     this.boundInfos = boundInfos;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 297 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 304 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 310 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 317 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 323 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 329 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 335 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 341 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 348 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setGroupCode(String groupCode)
/*     */   {
/* 354 */     this.groupCode = groupCode;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 360 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getGroupCode()
/*     */   {
/* 367 */     return this.groupCode;
/*     */   }
/*     */ 
/*     */   public String getGroupNumber()
/*     */   {
/* 373 */     return this.groupNumber;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 379 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 385 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 392 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 398 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 405 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 412 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setGroupNumber(String groupNumber)
/*     */   {
/* 418 */     this.groupNumber = groupNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 425 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 432 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 439 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 446 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 453 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public List<TextMsg> getTextMsgList()
/*     */   {
/* 462 */     return this.textMsgList;
/*     */   }
/*     */ 
/*     */   public void setTextMsgList(List<TextMsg> textMsgList)
/*     */   {
/* 469 */     this.textMsgList = textMsgList;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 475 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setResultCode(String resultCode)
/*     */   {
/* 481 */     this.resultCode = resultCode;
/*     */   }
/*     */ 
/*     */   public String getResultMsg()
/*     */   {
/* 488 */     return this.resultMsg;
/*     */   }
/*     */ 
/*     */   public String getContactText()
/*     */   {
/* 495 */     return this.contactText;
/*     */   }
/*     */ 
/*     */   public void setContactText(String contactText)
/*     */   {
/* 502 */     this.contactText = contactText;
/*     */   }
/*     */ 
/*     */   public String getResultCode()
/*     */   {
/* 508 */     return this.resultCode;
/*     */   }
/*     */ 
/*     */   public void setResultMsg(String resultMsg)
/*     */   {
/* 516 */     this.resultMsg = resultMsg;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.HbpuOptionsOutput
 * JD-Core Version:    0.6.0
 */