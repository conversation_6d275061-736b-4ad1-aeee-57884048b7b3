/*     */ package com.travelsky.hub.model.input.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PsrCheckInInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7938700176381983913L;
/*     */   private String airline;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String departureDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String primaryClass;
/*     */   private String opType;
/*     */   private List<PassengerParamList> passengerParamList;
/*     */ 
/*     */   public String getAirline()
/*     */   {
/*  47 */     return this.airline;
/*     */   }
/*     */ 
/*     */   public void setAirline(String airline)
/*     */   {
/*  54 */     this.airline = airline;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  60 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  67 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  74 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  81 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getPrimaryClass()
/*     */   {
/*  87 */     return this.primaryClass;
/*     */   }
/*     */ 
/*     */   public void setPrimaryClass(String primaryClass)
/*     */   {
/*  94 */     this.primaryClass = primaryClass;
/*     */   }
/*     */ 
/*     */   public String getOpType()
/*     */   {
/* 101 */     return this.opType;
/*     */   }
/*     */ 
/*     */   public void setOpType(String opType)
/*     */   {
/* 108 */     this.opType = opType;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 114 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 121 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public List<PassengerParamList> getPassengerParamList()
/*     */   {
/* 128 */     return this.passengerParamList;
/*     */   }
/*     */ 
/*     */   public void setPassengerParamList(List<PassengerParamList> passengerParamList)
/*     */   {
/* 135 */     this.passengerParamList = passengerParamList;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 141 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 148 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 155 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 162 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.PsrCheckInInput
 * JD-Core Version:    0.6.0
 */