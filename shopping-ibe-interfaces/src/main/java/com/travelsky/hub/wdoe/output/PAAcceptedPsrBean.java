/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import com.travelsky.hub.model.output.StopOverInfo;
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PAAcceptedPsrBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7632492184137143529L;
/*  21 */   private String seatNumber = "";
/*     */   private List bordingStreams;
/*  31 */   private String boardingNumber = "";
/*     */ 
/*  36 */   private String flightNo = "";
/*     */ 
/*  41 */   private String flightDate = "";
/*     */ 
/*  46 */   private String deptAirport = "";
/*     */ 
/*  51 */   private String psrName = "";
/*     */ 
/*  55 */   private String ffpAirlineCode = "";
/*     */ 
/*  59 */   private String ffpCardNumber = "";
/*     */ 
/*  63 */   private String ffpCardPrior = "";
/*     */   private List<StopOverInfo> stopOverInfos;
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  74 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String i)
/*     */   {
/*  81 */     this.seatNumber = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/*  88 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String i)
/*     */   {
/*  95 */     this.boardingNumber = i;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 101 */     this.seatNumber = "";
/* 102 */     this.boardingNumber = "";
/* 103 */     this.bordingStreams = null;
/*     */   }
/*     */ 
/*     */   public List getBordingStreams()
/*     */   {
/* 111 */     return this.bordingStreams;
/*     */   }
/*     */ 
/*     */   public void setBordingStreams(List bordingStreams)
/*     */   {
/* 118 */     this.bordingStreams = bordingStreams;
/*     */   }
/*     */ 
/*     */   public String getFfpCardPrior()
/*     */   {
/* 125 */     return this.ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public void setFfpCardPrior(String ffpCardPrior)
/*     */   {
/* 133 */     this.ffpCardPrior = ffpCardPrior;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 140 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public String getFfpAirlineCode()
/*     */   {
/* 147 */     return this.ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFfpAirlineCode(String ffpAirlineCode)
/*     */   {
/* 155 */     this.ffpAirlineCode = ffpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 162 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 169 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 176 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptAirport()
/*     */   {
/* 183 */     return this.deptAirport;
/*     */   }
/*     */ 
/*     */   public void setDeptAirport(String deptAirport)
/*     */   {
/* 190 */     this.deptAirport = deptAirport;
/*     */   }
/*     */ 
/*     */   public String getPsrName()
/*     */   {
/* 197 */     return this.psrName;
/*     */   }
/*     */ 
/*     */   public void setPsrName(String psrName)
/*     */   {
/* 204 */     this.psrName = psrName;
/*     */   }
/*     */ 
/*     */   public String getFfpCardNumber()
/*     */   {
/* 211 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFfpCardNumber(String ffpCardNumber)
/*     */   {
/* 219 */     this.ffpCardNumber = ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public List<StopOverInfo> getStopOverInfos()
/*     */   {
/* 226 */     return this.stopOverInfos;
/*     */   }
/*     */ 
/*     */   public void setStopOverInfos(List<StopOverInfo> stopOverInfos)
/*     */   {
/* 234 */     this.stopOverInfos = stopOverInfos;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PAAcceptedPsrBean
 * JD-Core Version:    0.6.0
 */