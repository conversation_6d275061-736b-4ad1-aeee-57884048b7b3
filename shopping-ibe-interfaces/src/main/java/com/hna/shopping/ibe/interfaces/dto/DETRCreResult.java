package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;

@Data
//@EqualsAndHashCode(callSuper = true)
@ApiModel
public class DETRCreResult implements Serializable {
	/**
	 * 机票证件号信息
	 */
	private static final long serialVersionUID = -6614767851927425881L;
	String idNo = null;
	String idtype = null;
	String tktn = null;
	String name = null;
	ArrayList idnos = new ArrayList();

	public String getIdNo() {
		return this.idNo;
	}

	public String getIdtype() {
		return this.idtype;
	}

	public String getName() {
		return this.name;
	}

	public String getTktn() {
		return this.tktn;
	}

	public void setIdNo(String newIdNo) {
		this.idNo = newIdNo;
	}

	public void setIdtype(String newIdtype) {
		this.idtype = newIdtype;
	}

	public void setName(String newName) {
		this.name = newName;
	}

	public void setTktn(String newTktn) {
		this.tktn = newTktn;
	}

	public void addID(String id) {
		if (this.idnos == null) {
			this.idnos = new ArrayList();
		}

		this.idnos.add(id);
	}

	public int getIdnoCount() {
		if (this.idnos == null) {
			return this.idNo == null ? 0 : 1;
		} else {
			return this.idnos.size();
		}
	}

	public String getIdTypeAt(int i) {
		if (this.idnos == null) {
			return "";
		} else if (this.idnos.size() <= i) {
			return "";
		} else {
			String s = (String)this.idnos.get(i);
			return s.length() >= 2 ? s.substring(0, 2) : "";
		}
	}

	public String getIdNoAt(int i) {
		if (this.idnos == null) {
			return "";
		} else if (this.idnos.size() <= i) {
			return "";
		} else {
			String s = (String)this.idnos.get(i);
			return s.length() >= 2 ? s.substring(2) : "";
		}
	}

	public String getIdAt(int i) {
		if (this.idnos == null) {
			return "";
		} else {
			return this.idnos.size() <= i ? "" : (String)this.idnos.get(i);
		}
	}

	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append("票号：");
		sb.append(this.tktn);
		sb.append("\r\n乘机人姓名：");
		sb.append(this.name);

		for(int i = 0; i < this.idnos.size(); ++i) {
			sb.append("\r\n");
			sb.append(i + 1);
			sb.append(".");
			sb.append(this.idnos.get(i));
		}

		sb.append("\r\n");
		return sb.toString();
	}
}
