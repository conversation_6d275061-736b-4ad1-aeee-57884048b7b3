package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.client.pnr.BookSegment;

import java.util.Date;

public class SellSeat extends BaseRequest {
	private static final long serialVersionUID = -3699010408219656532L;
	private int millisecondsBeforeEnvelop = -1;
	private com.travelsky.ibe.client.pnr.BookInformation bif;
	
	public SellSeat()
	{
	this.bif = new BookInformation();
	}

	public SellSeat(BookInformation bookinfo)
	{
	this.bif = bookinfo;
	}

	public int getMillisecondsBeforeEnvelop()
	{
	return this.millisecondsBeforeEnvelop;
	}

	public void setMillisecondsBeforeEnvelop(int millisecondsBeforeEnvelop) {
		this.millisecondsBeforeEnvelop = millisecondsBeforeEnvelop;
	}

	public void setBif(BookInformation bif)
	{
	this.bif = bif;
	}

	public void addAdult(String name) throws Exception {
		this.bif.addAdult(name);
	}

	public void addAirSeg(BookSegment airseg) throws Exception {
		this.bif.addAirSeg(airseg);
	}

	public void addAirSeg(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum, Date departureTime) throws Exception {
		BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
		this.bif.addAirSeg(airseg);
	}

	public void addAirSeg(String airNo, char fltClass, String orgCity, String desCity, String actionCode, int tktNum, String departureTime) throws Exception {
		BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime, 0);
		this.bif.addAirSeg(airseg);
	}

	public void addChild(String name) throws Exception {
		BookPassengerEx passenger = new BookPassengerEx(name, 1, 0);
		this.bif.addPassenger(passenger);
	}

	public void addContact(BookContactEx contact) throws Exception {
		this.bif.addContact(contact);
	}

	public void addContact(String contactinfo) throws Exception {
		BookContactEx contact = new BookContactEx(contactinfo);
		this.bif.addContact(contact);
	}

	public void addEI(String ei)
	{
	 this.bif.addEI(ei);
	}
	public void addEI(BookEIEx ei) {
		if (ei != null){this.bif.addEI(ei);}
	}

	public void addEI(BookEIEx ei, String[] name) {
		if (ei != null){this.bif.addEI(ei, name);}
	}

	public void addFC(BookFCEx fc)
	{
	this.bif.addFC(fc);
	}

	public void addFC(String fcStr)
	{
	this.bif.addFC(new BookFCEx(fcStr));
	}

	public void addFN(BookFNEx fn)
	{
	this.bif.addFN(fn);
	}

	public void addFN(String fnStr)
	{
	this.bif.addFN(new BookFNEx(fnStr));
	}

	public void addFP(BookFP fp)
	{
	this.bif.addFP(fp);
	}

	public void addFP(String fpStr)
	{
	this.bif.addFP(new BookFP(fpStr));
	}

	public void addInfant(BookInfantEx infant) throws Exception {
		this.bif.addInfant(infant);
	}

	public void addInfant(Date birth, String infantCarrierName, String name) throws Exception {
	    BookInfantEx infant = new BookInfantEx(birth, infantCarrierName, name);
	    this.bif.addInfant(infant);
	}

	public void addInfant(String birth, String carrierName, String name) throws Exception {
	    BookInfantEx infant = new BookInfantEx(birth, carrierName, name);
	    this.bif.addInfant(infant);
	}

	public void addInfoAirSeg(String orgCity, String desCity) throws Exception {
	    BookSegment airseg = new BookSegment(orgCity, desCity);
	    this.bif.addAirSeg(airseg);
	}

	public void addInfoAirSeg(String orgCity, String desCity, Date departureTime) throws Exception {
	    BookSegment airseg = new BookSegment(orgCity, desCity, departureTime);
	    this.bif.addAirSeg(airseg);
	}

	public void addInfoAirSeg(String orgCity, String desCity, String departureTime) throws Exception {
	    BookSegment airseg = new BookSegment(orgCity, desCity, departureTime);
	    this.bif.addAirSeg(airseg);
	}

	public void addOI(BookOIEx oi) throws Exception {
	    this.bif.addOI(oi);
	}

	public void addOpenAirSeg(String airNo, char fltClass, String orgCity, String desCity) throws Exception {
	    BookSegment airseg = new BookSegment(airNo, fltClass, orgCity, desCity);
	    this.bif.addAirSeg(airseg);
	}

	public void addOSI(BookOSIEx osi) throws Exception {
	    this.bif.addOSI(osi);
	}

	public void addOSI(String airCode, String osiinfo) throws Exception {
	    BookOSIEx osi = new BookOSIEx(airCode, osiinfo);
	    this.bif.addOSI(osi);
	}

	public void addPassenger(BookPassengerEx passenger) throws Exception {
	    this.bif.addPassenger(passenger);
	}

	public void addRMK(BookRMKEx rmk) throws Exception {
	    this.bif.addRMK(rmk);
	}

	public void addRMK(String rmktype, String rmkinfo) throws Exception {
	    BookRMKEx rmk = new BookRMKEx(rmktype, rmkinfo);
	    this.bif.addRMK(rmk);
	}

	public void addSSR(BookSSREx ssr) throws Exception {
	    this.bif.addSSR(ssr);
	}

	public void addSSR_DOCA(String airline, String doca_type, String country, String address, String city, String state, String zip, boolean infant, String name) throws Exception {
	    addSSR(new BookSSREx("doca", airline, "hk", 1, "", "", ' ', "", doca_type + "/" + country + "/" + address + "/" + city + "/" + state + "/" + zip + ((infant) ? "/I" : ""), name));
	}

	public void addSSR_DOCO_V(String airline, String birthplace, String visaNum, String issueplace, Date issuedate, String issueCountry, boolean infant, String name) throws Exception {
	    addSSR(new BookSSREx("Doco", airline, "Hk", 1, "", "", ' ', "", birthplace + "/V/" + visaNum + "/" + issueplace + "/" + QDateTime.dateToString(issuedate, "ddmmmyy") + "/" + issueCountry + ((infant) ? "/i" : ""), name));
	}

	public void addSSR_DOCS(String airline, String doc_type, String issueCountry, String doc_No, String nationality, Date birth, String gender, boolean infant, Date expiry_Date, String surname, String firstname, String midname, boolean holder, String name) throws Exception {
        String expiry;
        if (expiry_Date != null)
         expiry = QDateTime.dateToString(expiry_Date, "ddmmmyy");
        else
         expiry = "";
        addSSR(
         new BookSSREx("DOCS", airline, "hK", 1, "", "", ' ', "", doc_type + "/" + issueCountry + "/" + doc_No + "/" + nationality + "/" + QDateTime.dateToString(birth, "ddmmmyy") + "/" + gender + ((infant) ? "I/" : "/") + expiry + "/" + surname + "/" + firstname +
         (((midname == null) || (midname.length() == 0)) ? "" : new StringBuilder("/").append(midname).toString()) + ((holder) ? "/H" : ""), name));
	}

	public void addSSR_FOID(String airline, String idtype, String id, String name) throws Exception {
	    addSSR(new BookSSREx("FOID", airline, "hk", 0, "", "", ' ', "", idtype + id, name));
	}

	public void addSSR_FQTV(String cardno, String name) throws Exception {
	    addSSR(new BookSSREx("FQTV", cardno.substring(0, 2), "hk", 0, "", "", ' ', "", cardno, name));
	}

	public void addSSR_FQTV(String airline, String cardno, String name) throws Exception {
	    addSSR(new BookSSREx("FQTV", airline, "hk", 0, "", "", ' ', "", cardno, name));
	}

	public void addSSR_INFT(String airline, String segment, String fltNo, String fltClass, String date, String infantName, String infantBirth, String freeText, String carrierName) throws Exception {
        if ((airline == null) || (airline.length() == 0))
         throw new Exception("addSSR_INFT(),parameter airline is null or \"\"");
        if ((segment == null) || (segment.length() == 0))
         throw new Exception("addSSR_INFT(),parameter segment is null or \"\"");
        if ((fltNo == null) || (segment.length() == 0)) {
         throw new Exception("addSSR_INFT(),parameter fltNo is null or \"\"");
        }
        if ((fltClass == null) || (fltClass.length() == 0))
         throw new Exception("addSSR_INFT(),parameter fltClass is null or \"\"");
        if ((infantName == null) || (infantName.length() == 0))
         throw new Exception("addSSR_INFT(),parameter infantName is null or \"\"");
        try {
         QDateTime.stringToCalendar(infantBirth, (infantBirth.length() == 5) ? "ddmmm" : "ddmmmyy");
        } catch (Exception e) {
         throw new Exception("addSSR_INFT(),parameter infantBirth is " + ((infantBirth.length() == 0) ? "\"\"" : infantBirth));
        }
        try {
         QDateTime.stringToCalendar(date, (date.length() == 5) ? "ddmmm" : "ddmmmyy");
        } catch (Exception e) {
         throw new Exception("addSSR_INFT(),parameter date is " + ((infantBirth.length() == 0) ? "\"\"" : infantBirth));
        }
        if ((carrierName == null) || (carrierName.length() == 0)) {
         throw new Exception("addSSR_INFT(),parameter carrierName is null or \"\"");
        }
        addSSR(new BookSSREx("INFt", airline, "nn", 1, "", "", ' ', "", segment + " " + fltNo + " " + fltClass + " " + date + " " + infantName + " " + infantBirth + (((freeText == null) || (freeText.length() == 0)) ? "" : new StringBuilder("/").append(freeText).toString()), carrierName));
	}

	public void addSSR_PSPT(String airline, String psptinfo, String name) throws Exception {
		addSSR(new BookSSREx("PSPT", airline, "hk", 1, "", "", ' ', "", psptinfo, name));
	}

	public void addSSR_PSPT(String airline, String psptNo, String NationCode, Date birth, String sex, String paxname, boolean infant, boolean holder, String name) throws Exception {
		addSSR(new BookSSREx("PSPT", airline, "hk", 1, "", "", ' ', "", psptNo + "/" + NationCode + "/" + QDateTime.dateToString(birth, "ddmmmyy") + "/" + paxname + "/" + sex + ((infant) ? "i" : "") + ((holder) ? "/h" : ""), name));
	}

	public void addTC(String tc) {
		if (tc != null) {
			this.bif.addTC(new BookTC(tc));
	 	}
	}

	public void addTC(BookTC tc) {
		if (tc != null){this.bif.addTC(tc);}
	}

	public void addTC(BookTC tc, String[] passengername)
	{
	this.bif.addTC(tc, passengername);
	}

	public void addTktstatus(BookTktStatus bt) {
		if (bt != null){this.bif.addTktstatus(bt);}
	}

	public void addUnCompanionChild(String name, int age) throws Exception {
		BookPassengerEx passenger = new BookPassengerEx(name, 2, age);
		this.bif.addPassenger(passenger);
	}

	public boolean isGroupTicket()
	{
	return this.bif.isGroupticket();
	}

	public void setEnvelopType(String newEnvelopType)
	{
	this.bif.setEnvelopType(newEnvelopType);
	}

	public void setGroupName(String groupName) throws Exception {
		this.bif.setGroupName(groupName);
	}

	public void setGroupTicket(boolean isGroup) throws Exception {
		this.bif.setGroupticket(isGroup);
	}

	public void setOffice(String office) throws Exception {
		this.bif.setOfficeCode(office);
	}

	public void setPassengerNumber(int passengerNumber) throws Exception {
		this.bif.setPassengernumber(passengerNumber);
	}

	public void setTimelimit(Date dateLimit) throws Exception {
		this.bif.setTimelimit(QDateTime.dateToString(dateLimit, "YYYY-MM-DD HH:MI:SS"));
	}

	public void setTimelimit(String dateLimit) throws Exception {
		this.bif.setTimelimit(dateLimit);
	}

	public void addSSR_FQTV(String airline, String cardno, String name, String segidx) throws Exception {
		BookSSREx ssr = new BookSSREx("FQTV", airline, "hk", 0, "", "", ' ', "", cardno, name);
		ssr.setSegidx(segidx);
		addSSR(ssr);
	}

	public void addAuxiliary(BookAuxiliaryService ba) {
		this.bif.addAuxiliaryService(ba);
	}

	public BookInformation getBif() {
		return bif;
	}
}