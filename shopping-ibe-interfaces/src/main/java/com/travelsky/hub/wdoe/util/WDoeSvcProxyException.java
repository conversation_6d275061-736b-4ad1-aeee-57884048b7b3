/*    */ package com.travelsky.hub.wdoe.util;
/*    */ 
/*    */ public class WDoeSvcProxyException extends RuntimeException
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 15 */   private String message = "";
/*    */   private String errorCode;
/*    */   private Throwable rootCause;
/*    */ 
/*    */   public WDoeSvcProxyException(Throwable rootCause)
/*    */   {
/* 24 */     super(rootCause);
/* 25 */     this.rootCause = rootCause;
/*    */   }
/*    */ 
/*    */   public WDoeSvcProxyException(String message, String errorCode, Throwable rootCause)
/*    */   {
/* 35 */     super(message, rootCause);
/* 36 */     this.errorCode = errorCode;
/* 37 */     this.message = message;
/* 38 */     this.rootCause = rootCause;
/*    */   }
/*    */ 
/*    */   public String getMessage()
/*    */   {
/* 46 */     return this.message;
/*    */   }
/*    */ 
/*    */   public void setMessage(String message)
/*    */   {
/* 54 */     this.message = message;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 62 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 70 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public Throwable getRootCause()
/*    */   {
/* 78 */     return this.rootCause;
/*    */   }
/*    */ 
/*    */   public void setRootCause(Throwable rootCause)
/*    */   {
/* 86 */     this.rootCause = rootCause;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.WDoeSvcProxyException
 * JD-Core Version:    0.6.0
 */