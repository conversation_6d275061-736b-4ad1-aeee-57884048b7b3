/*     */ package com.travelsky.hub.model.peentity.upgemds.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Segment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5764649854852867292L;
/*     */   private String segmentID;
/*     */   private Carrier operatingCarrier;
/*     */   private Carrier marketingCarrier;
/*     */   private String flightDate;
/*     */   private String departureAirportCode;
/*     */   private String bookingCabin;
/*     */   private String arrivalAirportCode;
/*     */ 
/*     */   public String getSegmentID()
/*     */   {
/*  63 */     return this.segmentID;
/*     */   }
/*     */ 
/*     */   public void setSegmentID(String segmentID)
/*     */   {
/*  70 */     this.segmentID = segmentID;
/*     */   }
/*     */ 
/*     */   public Carrier getOperatingCarrier()
/*     */   {
/*  77 */     return this.operatingCarrier;
/*     */   }
/*     */ 
/*     */   public void setOperatingCarrier(Carrier operatingCarrier)
/*     */   {
/*  84 */     this.operatingCarrier = operatingCarrier;
/*     */   }
/*     */ 
/*     */   public Carrier getMarketingCarrier()
/*     */   {
/*  91 */     return this.marketingCarrier;
/*     */   }
/*     */ 
/*     */   public void setMarketingCarrier(Carrier marketingCarrier)
/*     */   {
/*  98 */     this.marketingCarrier = marketingCarrier;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 105 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 112 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirportCode()
/*     */   {
/* 119 */     return this.departureAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirportCode(String departureAirportCode)
/*     */   {
/* 126 */     this.departureAirportCode = departureAirportCode;
/*     */   }
/*     */ 
/*     */   public String getBookingCabin()
/*     */   {
/* 133 */     return this.bookingCabin;
/*     */   }
/*     */ 
/*     */   public void setBookingCabin(String bookingCabin)
/*     */   {
/* 140 */     this.bookingCabin = bookingCabin;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirportCode()
/*     */   {
/* 147 */     return this.arrivalAirportCode;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirportCode(String arrivalAirportCode)
/*     */   {
/* 154 */     this.arrivalAirportCode = arrivalAirportCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.Segment
 * JD-Core Version:    0.6.0
 */