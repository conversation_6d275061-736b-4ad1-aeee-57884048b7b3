/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class CheckedMsgBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String index;
/*     */   private String serviceType;
/*     */   private String messageCode;
/*     */   private String messageStatus;
/*     */   private String message;
/*     */   private String prompt;
/*     */   private List<CheckedDocType> documentTypes;
/*     */ 
/*     */   public String getServiceType()
/*     */   {
/*  49 */     return this.serviceType;
/*     */   }
/*     */ 
/*     */   public void setServiceType(String serviceType)
/*     */   {
/*  56 */     this.serviceType = serviceType;
/*     */   }
/*     */ 
/*     */   public String getMessageCode()
/*     */   {
/*  63 */     return this.messageCode;
/*     */   }
/*     */ 
/*     */   public void setMessageCode(String messageCode)
/*     */   {
/*  70 */     this.messageCode = messageCode;
/*     */   }
/*     */ 
/*     */   public String getMessageStatus()
/*     */   {
/*  77 */     return this.messageStatus;
/*     */   }
/*     */ 
/*     */   public void setMessageStatus(String messageStatus)
/*     */   {
/*  84 */     this.messageStatus = messageStatus;
/*     */   }
/*     */ 
/*     */   public String getMessage()
/*     */   {
/*  91 */     return this.message;
/*     */   }
/*     */ 
/*     */   public void setMessage(String message)
/*     */   {
/*  98 */     this.message = message;
/*     */   }
/*     */ 
/*     */   public String getPrompt()
/*     */   {
/* 105 */     return this.prompt;
/*     */   }
/*     */ 
/*     */   public void setPrompt(String prompt)
/*     */   {
/* 112 */     this.prompt = prompt;
/*     */   }
/*     */ 
/*     */   public List<CheckedDocType> getDocumentTypes()
/*     */   {
/* 119 */     return this.documentTypes;
/*     */   }
/*     */ 
/*     */   public void setDocumentTypes(List<CheckedDocType> documentTypes)
/*     */   {
/* 126 */     this.documentTypes = documentTypes;
/*     */   }
/*     */ 
/*     */   public String getIndex()
/*     */   {
/* 133 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/* 140 */     this.index = index;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckedMsgBean
 * JD-Core Version:    0.6.0
 */