/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.ibe;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * IBE JD XIY258 配置（票务处理）
 *
 * <AUTHOR>
@Configuration
@ConfigurationProperties(prefix = "ibe.jd.pw")
@Slf4j
@Data
public class IBEConfigJDPW extends IBEConfig {
}
