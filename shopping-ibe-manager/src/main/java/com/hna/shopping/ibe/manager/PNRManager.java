package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.*;
import com.travelsky.ibe.client.pnr.BookAirSeg;
import com.travelsky.ibe.exceptions.IBEException;

import java.util.List;
import java.util.Vector;

public interface PNRManager {
	
	/**
	 * @param sellSeat
	 * @return
	 */
	SSResult ss(SellSeat sellSeat);
	
	/**
	 * @param request
	 * @return
	 */
	BaseResponse addPNRInfo(AddPnrInfoRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	RTResult rt(RTRequest request);

	/**
	 * @param request
	 * @return
	 */
	SplitPNRResponse splitPnr(SplitPnrRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	BaseResponse cancelPnr(CancelPnrRequest request);

	BaseResponse cancelPnrK(CancelPnrRequest request);

	/**
	 * @param request
	 * @return
	 */
	BaseResponse delPnrItem(DelPnrItemRequest request);

	/**
	 *
	 * @param request
	 * @return
	 */
	RTResult rtHistory(RTRequest request);

	/**
	 *
	 * @param request
	 * @return
	 */
	BaseResponse changePaxInfo(ChangePaxInfoReq request);

	BaseResponse updatePnr(UpdatePnrReq updatePnrReq);

	/**
	 * 确认航段
	 * @param reconfirmAirSegReq
	 * @return
     */
	BaseResponse reconfirmAirSeg(ReconfirmAirSegReq reconfirmAirSegReq);

	/**
	 * 增加SSR项TKNE项
	 * @param request 添加PNR信息请求参数
	 * @return BaseResponse
	 */
	BaseResponse addPnrTKNE(AddPnrInfoRequest request) throws Exception;

	/**
	 * 强k
	 * @param request
	 * @return
	 */
	BaseResponse confirmAirSeg(ReconfirmAirSegRQ request);

	/**
	 * 检查所有航段为HK状态
	 * @param request
	 * @return
	 */
	boolean checkAllSegmentHK(RTRequest request) throws Exception;

	MLResponse ml(MLRequest request);

    List<FDBasePriceRS> fdBasePrice(String airline, String depCode, String arrCode, String flightDate) ;
	List<FDBasePriceRS> fd(String airline, String depCode, String arrCode, String flightDate);
	List<FDBasePriceRS> fdBySeller(String airline,String seller ,String depCode, String arrCode, String flightDate);

	BaseResponse reConfirmAirSeg(ReconfirmAirSegReq request) throws IBEException;

	BaseResponse changeAirSeg(String airlineCode, String pnr, BookAirSeg org, BookAirSeg dest) throws IBEException;

	FDBasePriceRS fdTax(String airline, String depCode, String arrCode, String flightDate, String passType);

	FDBasePriceAndCabinPriceRS fdPriceAndTax(String airline, String depCode, String arrCode, String flightDate, String passType);

	String removeName(RemoveNameRequest request) throws Exception;

}