package com.hna.shopping.ibe;

import com.alibaba.dubbo.config.spring.context.annotation.DubboComponentScan;
import com.hnair.opcnet.rpc.annotation.EnableOdsApi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = { "com.hna.shopping.ibe", "com.hnair.opcnet"})
@EnableOdsApi                       // 启用 ODS API，必须在 SpringBootApplication 之后
@EnableScheduling					// 开启定时任务
@EnableDiscoveryClient              // 启动 eureka
@EnableFeignClients                 // 启用 feign 进行远程调用
@EnableAsync                        // 开启异步调用
@EnableRetry
// 如果 BaseDemoApplication 没有在默认的 com.eking.sample.base 下，则需要通过下面的三个 Scan 来设置扫描路径
// @ComponentScan(value = {"com.eking.sample.base"})
// @EntityScan(value = {"com.eking.sample.base"})
// @EnableJpaRepositories(value = {"com.eking.sample.base"})
@DubboComponentScan
public class WebApplication {

	public static void main(String[] args) {
		SpringApplication.run(WebApplication.class, args);
	}
}
