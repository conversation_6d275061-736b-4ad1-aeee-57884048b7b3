/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TxnInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1741125261289967117L;
/*    */   private String userName;
/*    */   private String txnCode;
/*    */   private String apiVersion;
/*    */ 
/*    */   public String getUserName()
/*    */   {
/* 38 */     return this.userName;
/*    */   }
/*    */ 
/*    */   public void setUserName(String userName)
/*    */   {
/* 45 */     this.userName = userName;
/*    */   }
/*    */ 
/*    */   public String getTxnCode()
/*    */   {
/* 52 */     return this.txnCode;
/*    */   }
/*    */ 
/*    */   public void setTxnCode(String txnCode)
/*    */   {
/* 59 */     this.txnCode = txnCode;
/*    */   }
/*    */ 
/*    */   public String getApiVersion()
/*    */   {
/* 66 */     return this.apiVersion;
/*    */   }
/*    */ 
/*    */   public void setApiVersion(String apiVersion)
/*    */   {
/* 73 */     this.apiVersion = apiVersion;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.TxnInfo
 * JD-Core Version:    0.6.0
 */