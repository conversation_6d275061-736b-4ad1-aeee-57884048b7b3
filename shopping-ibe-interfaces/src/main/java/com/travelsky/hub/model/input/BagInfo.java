/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class BagInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8526722415937064679L;
/*     */   private String hostNumber;
/*     */   private String arrivalAirport;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private List<String> bagTags;
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/*  49 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/*  56 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  63 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  70 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/*  77 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/*  84 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/*  91 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/*  98 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTags()
/*     */   {
/* 105 */     return this.bagTags;
/*     */   }
/*     */ 
/*     */   public void setBagTags(List<String> bagTags)
/*     */   {
/* 112 */     this.bagTags = bagTags;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.BagInfo
 * JD-Core Version:    0.6.0
 */