/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class OtherDocInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1464543477519859587L;
/*     */   private String otherDocIssuePlace;
/*     */   private String expireDate;
/*     */   private String effectiveDate;
/*     */   private String otherDocType;
/*     */   private String otherDocID;
/*     */ 
/*     */   public String getEffectiveDate()
/*     */   {
/*  39 */     return this.effectiveDate;
/*     */   }
/*     */ 
/*     */   public void setEffectiveDate(String effectiveDate)
/*     */   {
/*  46 */     this.effectiveDate = effectiveDate;
/*     */   }
/*     */ 
/*     */   public String getExpireDate()
/*     */   {
/*  53 */     return this.expireDate;
/*     */   }
/*     */ 
/*     */   public void setExpireDate(String expireDate)
/*     */   {
/*  60 */     this.expireDate = expireDate;
/*     */   }
/*     */ 
/*     */   public String getOtherDocID()
/*     */   {
/*  67 */     return this.otherDocID;
/*     */   }
/*     */ 
/*     */   public void setOtherDocID(String otherDocID)
/*     */   {
/*  74 */     this.otherDocID = otherDocID;
/*     */   }
/*     */ 
/*     */   public String getOtherDocIssuePlace()
/*     */   {
/*  81 */     return this.otherDocIssuePlace;
/*     */   }
/*     */ 
/*     */   public void setOtherDocIssuePlace(String otherDocIssuePlace)
/*     */   {
/*  88 */     this.otherDocIssuePlace = otherDocIssuePlace;
/*     */   }
/*     */ 
/*     */   public String getOtherDocType()
/*     */   {
/*  95 */     return this.otherDocType;
/*     */   }
/*     */ 
/*     */   public void setOtherDocType(String otherDocType)
/*     */   {
/* 102 */     this.otherDocType = otherDocType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.OtherDocInfo
 * JD-Core Version:    0.6.0
 */