/*    */ package com.travelsky.hub.model.peentity.reserve.create.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class LoyaltyRedemption
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1955649506830514957L;
/*    */   private String ffpCardNumber;
/*    */   private String serialNumber;
/*    */   private String ffpLevel;
/*    */   private String airlineID;
/*    */ 
/*    */   public void setSerialNumber(String serialNumber)
/*    */   {
/* 49 */     this.serialNumber = serialNumber;
/*    */   }
/*    */ 
/*    */   public String getSerialNumber()
/*    */   {
/* 56 */     return this.serialNumber;
/*    */   }
/*    */ 
/*    */   public void setFfpCardNumber(String ffpCardNumber)
/*    */   {
/* 63 */     this.ffpCardNumber = ffpCardNumber;
/*    */   }
/*    */ 
/*    */   public String getFfpCardNumber()
/*    */   {
/* 70 */     return this.ffpCardNumber;
/*    */   }
/*    */ 
/*    */   public void setAirlineID(String airlineID)
/*    */   {
/* 77 */     this.airlineID = airlineID;
/*    */   }
/*    */ 
/*    */   public String getAirlineID()
/*    */   {
/* 84 */     return this.airlineID;
/*    */   }
/*    */ 
/*    */   public void setFfpLevel(String ffpLevel)
/*    */   {
/* 91 */     this.ffpLevel = ffpLevel;
/*    */   }
/*    */ 
/*    */   public String getFfpLevel()
/*    */   {
/* 98 */     return this.ffpLevel;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.input.LoyaltyRedemption
 * JD-Core Version:    0.6.0
 */