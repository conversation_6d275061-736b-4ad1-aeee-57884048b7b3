/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class RaOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8665839714245333919L;
/*    */   private String resultMsg;
/*    */   private String resultCode;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 27 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 34 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultMsg(String resultMsg)
/*    */   {
/* 44 */     this.resultMsg = resultMsg;
/*    */   }
/*    */ 
/*    */   public String getResultMsg()
/*    */   {
/* 51 */     return this.resultMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.RaOutputBean
 * JD-Core Version:    0.6.0
 */