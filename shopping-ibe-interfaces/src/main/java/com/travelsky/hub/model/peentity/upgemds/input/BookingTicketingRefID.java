/*    */ package com.travelsky.hub.model.peentity.upgemds.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BookingTicketingRefID
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8998964054300316050L;
/*    */   private String pnr;
/*    */   private String type;
/*    */ 
/*    */   public String getPnr()
/*    */   {
/* 38 */     return this.pnr;
/*    */   }
/*    */ 
/*    */   public void setPnr(String pnr)
/*    */   {
/* 45 */     this.pnr = pnr;
/*    */   }
/*    */ 
/*    */   public String getType()
/*    */   {
/* 52 */     return this.type;
/*    */   }
/*    */ 
/*    */   public void setType(String type)
/*    */   {
/* 59 */     this.type = type;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.BookingTicketingRefID
 * JD-Core Version:    0.6.0
 */