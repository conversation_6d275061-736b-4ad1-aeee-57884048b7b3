/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PwInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1298991879166316297L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String departureDate;
/*     */   private String arrivalAirport;
/*     */   private String surName;
/*     */   private String cabinType;
/*     */   private String seatNumber;
/*     */   private String boardingNumber;
/*     */   private String ticketNumber;
/*     */   private String sequenceNumber;
/*     */   private String outboundEtNumber;
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  85 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  93 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 101 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 109 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 117 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 125 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 133 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 141 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 149 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 157 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 165 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 173 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 181 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 189 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 197 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 205 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 213 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 221 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 229 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 237 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 245 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 253 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getOutboundEtNumber()
/*     */   {
/* 261 */     return this.outboundEtNumber;
/*     */   }
/*     */ 
/*     */   public void setOutboundEtNumber(String outboundEtNumber)
/*     */   {
/* 269 */     this.outboundEtNumber = outboundEtNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PwInput
 * JD-Core Version:    0.6.0
 */