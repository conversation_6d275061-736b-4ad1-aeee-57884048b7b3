/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class EDISeInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String firstDeptcity;
/*     */   private String etCode;
/*     */   private String flightNum;
/*     */   private String deptCity;
/*     */   private String arvCity;
/*     */   private String airlineCode;
/*     */   private String cabin;
/*     */   private String flightDate;
/*     */ 
/*     */   public String getFlightNum()
/*     */   {
/*  36 */     return this.flightNum;
/*     */   }
/*     */ 
/*     */   public void setFlightNum(String flightNum)
/*     */   {
/*  43 */     this.flightNum = flightNum;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  50 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  57 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/*  64 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/*  71 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/*  78 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/*  85 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getArvCity()
/*     */   {
/*  92 */     return this.arvCity;
/*     */   }
/*     */ 
/*     */   public void setArvCity(String arvCity)
/*     */   {
/*  99 */     this.arvCity = arvCity;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 106 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 113 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 120 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 127 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFirstDeptcity()
/*     */   {
/* 134 */     return this.firstDeptcity;
/*     */   }
/*     */ 
/*     */   public void setFirstDeptcity(String firstDeptcity)
/*     */   {
/* 141 */     this.firstDeptcity = firstDeptcity;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.EDISeInput
 * JD-Core Version:    0.6.0
 */