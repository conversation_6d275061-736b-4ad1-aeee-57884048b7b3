/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AccExpressBagsOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8833968925561018193L;
/*     */   private String arrivalAirport;
/*     */   private String isXBT;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String flightDate;
/*     */   private String airlineCode;
/*     */   private String flightClass;
/*     */   private List<String> bagTagNumbers;
/*     */   private String bagDestination;
/*     */   private String scheduledDepartureTime;
/*     */   private String departureAirport;
/*     */   private String bagRemark;
/*     */   private List<String> bagTagTreams;
/*     */ 
/*     */   public String getIsXBT()
/*     */   {
/*  81 */     return this.isXBT;
/*     */   }
/*     */ 
/*     */   public void setIsXBT(String isXBT)
/*     */   {
/*  89 */     this.isXBT = isXBT;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/*  97 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 105 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 113 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 121 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 129 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 137 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 144 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 152 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 160 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 168 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 178 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 186 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTagNumbers()
/*     */   {
/* 195 */     return this.bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumbers(List<String> bagTagNumbers)
/*     */   {
/* 203 */     this.bagTagNumbers = bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public String getBagDestination()
/*     */   {
/* 211 */     return this.bagDestination;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 218 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 226 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setBagDestination(String bagDestination)
/*     */   {
/* 233 */     this.bagDestination = bagDestination;
/*     */   }
/*     */ 
/*     */   public String getScheduledDepartureTime()
/*     */   {
/* 241 */     return this.scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public void setScheduledDepartureTime(String scheduledDepartureTime)
/*     */   {
/* 249 */     this.scheduledDepartureTime = scheduledDepartureTime;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 257 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 265 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getBagRemark()
/*     */   {
/* 273 */     return this.bagRemark;
/*     */   }
/*     */ 
/*     */   public void setBagRemark(String bagRemark)
/*     */   {
/* 281 */     this.bagRemark = bagRemark;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTagTreams()
/*     */   {
/* 289 */     return this.bagTagTreams;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 297 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 305 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public void setBagTagTreams(List<String> bagTagTreams)
/*     */   {
/* 312 */     this.bagTagTreams = bagTagTreams;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.AccExpressBagsOutputBean
 * JD-Core Version:    0.6.0
 */