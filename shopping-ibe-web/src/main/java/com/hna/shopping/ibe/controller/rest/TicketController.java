/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.TicketService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.TicketManager;
import com.hna.shopping.ibe.manager.esb.ESBService;
import com.hna.shopping.ibe.manager.interceptor.logger.Logger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Api("客票操作接口")
@RestController
@RequestMapping("/api/ticket")
@Slf4j
public class TicketController {

    @Autowired
    private TicketManager ticketManager;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private ESBService esbService;


    @ApiOperation(value = "detr 查询")
    @RequestMapping(value = "detr", method = RequestMethod.POST)
    RestResponse<DETRResponse> detr(@ApiParam(value = "detr参数对象", required = true)
    @RequestBody DETRRequest request) {
    	String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , detr: {}", uuid, JSON.toJSONString(request));
        DETRResponse avResponse = ticketManager.detr(request);
//        DETRResponse avResponse = new DETRResponse();
//        List<DETRTktResult> ticketInfos = Lists.newArrayList();
//        DETRTktResult detrTktResult = new DETRTktResult();
//        detrTktResult.setTicketNo("111-1111111111");
//        detrTktResult.setPassengerName("测试张三");
//        detrTktResult.setPassengerType(0);
//        List<DETRSeg> segs = Lists.newArrayList();
//        DETRSeg detrSeg = new DETRSeg();
//        detrSeg.setFlightNo("JD5596");
//        detrSeg.setAirline("JD");
//        detrSeg.setDepAirportCode("HAK");
//        detrSeg.setArrAirportCode("PEK");
//        detrSeg.setSegmentIndex(1);
//        detrSeg.setDepTime(DateUtil.stringToDate("2018-07-10"));
//        segs.add(detrSeg);
//        detrTktResult.setSegs(segs);
//        ticketInfos.add(detrTktResult);
//        try {
//            avResponse.setSuccess(true);
//            avResponse.setTicketInfos(ticketInfos);
//            Thread.sleep(1000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
        log.info(" End uuid: {} , detr: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "getTicketHistory 查询")
    @RequestMapping(value = "getTicketHistory", method = RequestMethod.POST)
    RestResponse<DETRHistoryResponse> getTicketHistory(@ApiParam(value = "getTicketHistory参数对象", required = true)
    @RequestBody DETRHistoryRequest request) {
    	String uuid = UUID.randomUUID().toString();
    	log.info("Start uuid: {} , getTicketHistory: {}", uuid, JSON.toJSONString(request));
    	DETRHistoryResponse avResponse = ticketManager.getTicketHistory(request);
    	log.info("End uuid: {} , getTicketHistory: {}", uuid, JSON.toJSONString(avResponse));
    	return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "etrf")
    @RequestMapping(value = "etrf", method = RequestMethod.POST)
    @Logger
    RestResponse<BaseResponse> etrf(@ApiParam(value = "etrf参数对象", required = true)
                                    @RequestBody RefundTicketRequest request) {
        return RestResponse.ok(ticketService.etrf(request));
    }

    @ApiOperation(value = "etdz")
    @RequestMapping(value = "etdz", method = RequestMethod.POST)
    RestResponse<BaseResponse> etdz(@ApiParam(value = "etdz参数对象", required = true)
    @RequestBody IssueTicketRequest request) {
    	String uuid = UUID.randomUUID().toString();
    	log.info("Start uuid: {} , etdz: {}", uuid, JSON.toJSONString(request));
    	BaseResponse avResponse = ticketManager.etdz(request);
//        BaseResponse avResponse = new BaseResponse();
//        try {
//            avResponse.setSuccess(true);
//            Thread.sleep(5000);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
    	log.info("End uuid: {} , etdz: {}", uuid, JSON.toJSONString(avResponse));
    	return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "issueTicketWithTN")
    @RequestMapping(value = "issueTicketWithTN", method = RequestMethod.POST)
    RestResponse<IssueTicketResponse> issueTicketWithTN(@ApiParam(value = "issueTicketWithTN参数对象", required = true)
    @RequestBody IssueTicketRequest request) {
    	String uuid = UUID.randomUUID().toString();
    	log.info("Start uuid: {} , issueTicketWithTN: {}", uuid, JSON.toJSONString(request));
    	IssueTicketResponse avResponse = ticketManager.issueTicketWithTN(request);
    	log.info("End uuid: {} , issueTicketWithTN: {}", uuid, JSON.toJSONString(avResponse));
    	return RestResponse.ok(avResponse);
    }


    @ApiOperation(value = "ticketInfo查询")
    @RequestMapping(value = "ticketInfo", method = RequestMethod.POST)
    RestResponse<DtoTicketInfoResponse> getTicketInfo(@ApiParam(value = "ticketInfo参数对象", required = true)
                                    @RequestBody DtoTicketInfoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , ticketInfo: {}", uuid, JSON.toJSONString(request));
        DtoTicketInfoResponse avResponse = ticketManager.getTicketInfo(request);
        log.info(" End uuid: {} , ticketInfo: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }


    /**
     * V1版本，不校验过滤证件号、航班日期
     * @param request
     * @return
     */
    @ApiOperation(value = "ticketInfo查询")
    @RequestMapping(value = "ticketInfoV1", method = RequestMethod.POST)
    RestResponse<DtoTicketInfoResponse> getTicketInfoV1(@ApiParam(value = "ticketInfo参数对象", required = true)
                                                      @RequestBody DtoTicketInfoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , ticketInfoV1: {}", uuid, JSON.toJSONString(request));
        DtoTicketInfoResponse avResponse = ticketManager.getTicketInfoV1(request);
        log.info(" End uuid: {} , ticketInfoV1: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }


    @ApiOperation(value = "ticketInfoByHSD查询")
    @RequestMapping(value = "ticketInfoByHSD", method = RequestMethod.POST)
    RestResponse<DtoTicketInfoResponse> ticketInfoByHSD(@ApiParam(value = "ticketInfo参数对象", required = true)
                                    @RequestBody DtoTicketInfoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , ticketInfoByHSD: {}", uuid, JSON.toJSONString(request));
        DtoTicketInfoResponse avResponse = ticketManager.getTicketInfoByHSD(request);
        log.info(" End uuid: {} , ticketInfoByHSD: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    @ApiOperation(value = "detrCredential 根据票号查询证件号，必须传票号")
    @RequestMapping(value = "detrCredential", method = RequestMethod.POST)
    RestResponse<DETRCreResponse> detrCredential(@ApiParam(value = "detr参数对象", required = true)
                                              @RequestBody DETRRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , detrCredential: {}", uuid, JSON.toJSONString(request));
        DETRCreResponse creResponse = ticketManager.detrCredential(request);
        log.info(" End uuid: {} , detrCredential: {}", uuid, JSON.toJSONString(creResponse));
        return RestResponse.ok(creResponse);
    }

    /**
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "ticketInfoByFlightInfoAndName", method = RequestMethod.POST)
    RestResponse<DtoTicketInfoResponse> ticketInfoByHSD(@RequestBody DtoTicketInfoByFlightInfoAndNameRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , ticketInfoByFlightInfoAndName: {}", uuid, JSON.toJSONString(request));
        DtoTicketInfoResponse avResponse = ticketManager.getTicketInfoByFlightInfoAndName(request);
        log.info(" End uuid: {} , ticketInfoByFlightInfoAndName: {}", uuid, JSON.toJSONString(avResponse));
        return RestResponse.ok(avResponse);
    }

    /**
     * 旅服系统回迁。通过高频接口获取航班旅客信息
     * @param request
     * @return
     */
    @RequestMapping(value = "getFlightPassengerInfoByHSD", method = RequestMethod.POST)
    RestResponse<FlightPassengerInfoResponse> getFlightPassengerInfoByHSD(@RequestBody FlightPassengerInfoRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , getFlightPassengerInfoByHSD: {}", uuid, JSON.toJSONString(request));
        FlightPassengerInfoResponse response = ticketManager.getFlightPassengerInfoByHSD(request);
        log.info(" End uuid: {} , getFlightPassengerInfoByHSD result size: {}", uuid, response.getFlightPassengerInfoList().size());
        return RestResponse.ok(response);
    }


    @RequestMapping(value = "getFixDepartureDate", method = RequestMethod.POST)
    RestResponse<List<Date>> getFixDepartureDate(@RequestBody DETRRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , getFixDepartureDate: {}", uuid, JSON.toJSONString(request));

        List<Date> response = ticketManager.getFixDepartureDate(request);

        log.info(" End uuid: {} , getFixDepartureDate: {}", uuid, JSON.toJSONString(response));
        return RestResponse.ok(response);
    }

    @RequestMapping(value = "getTicketListByCert", method = RequestMethod.POST)
    RestResponse<Vector<String>> getTicketListByCert(@RequestBody Map<String, String> request) {
        String uuid = UUID.randomUUID().toString();
        log.info(" Start uuid: {} , getTicketListByCert: {}", uuid, JSON.toJSONString(request));

        Vector<String> response = ticketManager.getTicketListByCert(request);

        log.info(" End uuid: {} , getTicketListByCert: {}", uuid, JSON.toJSONString(response));
        return RestResponse.ok(response);
    }

}