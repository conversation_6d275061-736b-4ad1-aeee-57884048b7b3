/*     */ package com.travelsky.hub.model.peentity.meal.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class QueryMealOrderRS
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1122589814557632811L;
/*     */   private String resultCode;
/*     */   private String resultMessage;
/*     */   private String orderItemID;
/*     */   private String mealType;
/*     */   private String ssrCode;
/*     */   private String tabooMealDescription;
/*     */ 
/*     */   public String getResultCode()
/*     */   {
/*  52 */     return this.resultCode;
/*     */   }
/*     */ 
/*     */   public void setResultCode(String resultCode)
/*     */   {
/*  60 */     this.resultCode = resultCode;
/*     */   }
/*     */ 
/*     */   public String getResultMessage()
/*     */   {
/*  68 */     return this.resultMessage;
/*     */   }
/*     */ 
/*     */   public void setResultMessage(String resultMessage)
/*     */   {
/*  76 */     this.resultMessage = resultMessage;
/*     */   }
/*     */ 
/*     */   public String getOrderItemID()
/*     */   {
/*  84 */     return this.orderItemID;
/*     */   }
/*     */ 
/*     */   public void setOrderItemID(String orderItemID)
/*     */   {
/*  92 */     this.orderItemID = orderItemID;
/*     */   }
/*     */ 
/*     */   public String getMealType()
/*     */   {
/* 100 */     return this.mealType;
/*     */   }
/*     */ 
/*     */   public void setMealType(String mealType)
/*     */   {
/* 108 */     this.mealType = mealType;
/*     */   }
/*     */ 
/*     */   public String getSsrCode()
/*     */   {
/* 116 */     return this.ssrCode;
/*     */   }
/*     */ 
/*     */   public void setSsrCode(String ssrCode)
/*     */   {
/* 124 */     this.ssrCode = ssrCode;
/*     */   }
/*     */ 
/*     */   public String getTabooMealDescription()
/*     */   {
/* 132 */     return this.tabooMealDescription;
/*     */   }
/*     */ 
/*     */   public void setTabooMealDescription(String tabooMealDescription)
/*     */   {
/* 140 */     this.tabooMealDescription = tabooMealDescription;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.QueryMealOrderRS
 * JD-Core Version:    0.6.0
 */