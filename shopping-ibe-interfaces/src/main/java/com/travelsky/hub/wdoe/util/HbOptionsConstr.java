package com.travelsky.hub.wdoe.util;

public final class HbOptionsConstr
{
  public static final String HBPUOPTIONSINFO_ELEMENT = "HbPuInfo";
  public static final String HBPWOPTIONSINFO_ELEMENT = "HbPwInfo";
  public static final String HBOPTIONSINFO_AIRLINECODE = "AirlineCode";
  public static final String HBOPTIONSINFO_FLIGHTNUMBER = "FlightNumber";
  public static final String HBOPTIONSINFO_FLIGHTDATE = "FlightDate";
  public static final String HBOPTIONSINFO_CABIN = "Cabin";
  public static final String HBOPTIONSINFO_DESTCITY = "DestCity";
  public static final String HBOPTIONSINFO_DEPTCITY = "DeptCity";
  public static final String HBOPTIONSINFO_HOSTNUMBER = "HostNumber";
  public static final String HBOPTIONSINFO_TKTNUMBER = "tktNumber";
  public static final String HBOPTIONSINFO_OUTPUT = "Result";
  public static final String HBOPTION_ELEMENT = "Option";
  public static final String HBOPTION_TYPE = "type";
  public static final String HBOPTION_VALUE = "Values";
  public static final String HBPU_OPTION_EMD = "HbpuEmdOption";
  public static final String HBPW_OPTION_EMD = "HbpwEmdOption";
  public static final String OPTION_EMDCOUPON = "emdCoupon";
  public static final String OPTION_EMDTICKETNO = "emdTicketNo";
  public static final String OPTION_EMDSTATUS = "emdStatus";
  public static final String OPTION_EMDSVCNAME = "emdServiceName";
  public static final String HBPU_OPTION_SEAT = "HbpuSeatOption";
  public static final String OPTION_SEATNO = "seatNo";
  public static final String OPTION_ISSNR = "snr";
  public static final String OPTION_ISSNR_PRO = "isSnr";
  public static final String HBPU_OPTION_CKIN = "HbpuCkinOption";
  public static final String OPTION_CKIN_MESSAGE = "ckinMessage";
  public static final String HBPU_OPTION_CTC = "HbpuCtcOption";
  public static final String OPTION_CTC_MESSAGE = "ctcMessage";
  public static final String HBPU_OPTION_MSG = "HbpuMsgOption";
  public static final String OPTION_MSG_MESSAGE = "msgMessage";
  public static final String HBPU_OPTION_PSM = "HbpuPsmOption";
  public static final String OPTION_PSM_MESSAGE = "psmMessage";
  public static final String HBPU_OPTION_FFP = "HbpuFFOption";
  public static final String OPTION_FFPCARDNUMBER = "ffpCardNumber";
  public static final String OPTION_FFPCARDAIRLINECODE = "ffpCardAirlineCode";
  public static final String HBPW_OPTION_THROUGH = "HbpwThroughOption";
  public static final String OPTION_THROUGH_AIRLINECODE = "AirlineCode";
  public static final String OPTION_THROUGH_FLIGHTNUMBER = "FlightNumber";
  public static final String OPTION_THROUGH_FLIGHTDATE = "FlightDate";
  public static final String OPTION_THROUGH_CABIN = "Cabin";
  public static final String OPTION_THROUGH_DESTCITY = "DestCity";
  public static final String OPTION_THROUGH_ETNUMBER = "EtNumber";
  public static final String OPTION_THROUGH_HOSTNUMBER = "HostNumber";
  public static final String OPTION_THROUGH_TOURINDEX = "TourIndex";
  public static final String FLIGHT_ELEMENT = "FlightInfo";
  public static final String THROUGH_FLIGHT_ELEMENT = "ThroughFlightInfo";
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.util.HbOptionsConstr
 * JD-Core Version:    0.6.0
 */