/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SpecialMealsInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6581811368586802922L;
/*    */   private String specialMealsType;
/*    */   private String specialMealsRemark;
/*    */ 
/*    */   public String getSpecialMealsType()
/*    */   {
/* 32 */     return this.specialMealsType;
/*    */   }
/*    */ 
/*    */   public void setSpecialMealsType(String specialMealsType)
/*    */   {
/* 39 */     this.specialMealsType = specialMealsType;
/*    */   }
/*    */ 
/*    */   public String getSpecialMealsRemark()
/*    */   {
/* 46 */     return this.specialMealsRemark;
/*    */   }
/*    */ 
/*    */   public void setSpecialMealsRemark(String specialMealsRemark)
/*    */   {
/* 53 */     this.specialMealsRemark = specialMealsRemark;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.SpecialMealsInformation
 * JD-Core Version:    0.6.0
 */