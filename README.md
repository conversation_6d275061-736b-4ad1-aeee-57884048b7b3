- install: 直接 mvn clean install 即可; (shopping-ibe-web 是最终执行的 module)
- 发布(sit环境): java -jar -Dspring.profiles.active=sit ./shopping-ibe-web-0.0.2-SNAPSHOT.jar 
- 访问 web 接口 [http://127.0.0.1:1125/web/welcome?name=33](http://127.0.0.1:1125/web/welcome?name=33)
- 访问 restful 接口 [http://127.0.0.1:1125/api/welcome?name=33](http://127.0.0.1:1125/api/welcome?name=33)

- 访问 h2 控制台(需要手动放开 pom 里的 h2) [http://127.0.0.1:1125/h2-console/](http://127.0.0.1:1125/h2-console/)
- 添加用户 [http://127.0.0.1:1125/api/person/add?name=55](http://127.0.0.1:1125/api/person/add?name=55)
- 所有用户 [http://127.0.0.1:1125/api/person/all](http://127.0.0.1:1125/api/person/all)
- 查找用户(JPA) [http://127.0.0.1:1125/api/person/find?id=0](http://127.0.0.1:1125/api/person/find?id=0)
- 查找用户(原生sql) [http://127.0.0.1:1125/api/person/find?name=54](http://127.0.0.1:1125/api/person/find?name=54)

- druid 数据库控制台: [http://127.0.0.1:1125/druid](http://127.0.0.1:1125/druid)
- druid 数据库用户名/密码: admin/123456

- module 说明 (By 阿里巴巴 Java 开发手册)
  - config 配置信息, 从配置文件读取进来的, 都在这里转化成 Java Object
  - common 公共定义信息, 包括 exception, responsecode, util 等
  - dao 数据库访问层
  - manager 通用业务处理层    
    - 对第三方平台封装的层，预处理返回结果及转化异常信息
    - 对 service 层通用能力的下沉，如缓存方案、中间件通用处理
    - 与 DAO 层交互，对多个 DAO 的组合复用
  - service 层，相对具体的业务逻辑服务层，由 interface+implement 组成
    - interface 接口层，所有需要对外提供的接口，都定义在 interface 里（web 层直接访问 interface）
    - implement 接口实现层，一个完整的事务由这里进入，即 @Transactional 都在这里定义 
      - 一般来说，如果可以一个函数写完的，就在 implement 里写完；如果需要拆分成 2 个以上的函数，建议需要写到 manager 层里
      - 对于一些简单 sql 查询，implement 层可以直接访问 DAO 层
  - web 页面访问入口, Restful url 声明都在这里
  - task 定时任务, 相关启动 cron 配置, 都应该写在 application-task.properties 里
  
- 启动函数
  - com.eking.basedemo.BaseDemoApplication 在 web module里, 感觉应该放在 config 里比较合适; 但是 run 一个 config 也怪怪的
  
- 服务说明
  - 如果以后需要往外提供接口服务, 需要注意只能导出 manager\common 这两层; 所以这两层, 应该要能完备描述这个系统的功能

- swagger [http://127.0.0.1:1125/swagger-ui.html](http://127.0.0.1:1125/swagger-ui.html)

- spring-boot-starter-actuator [http://127.0.0.1:1125/mappings](http://127.0.0.1:1125/mappings)
 - 包括 /autoconfig /configprops /beans /dump /env /health /mappings 等信息


- 分层领域模型规约(By 阿里巴巴 Java 开发手册)
 - DO（Data Object）：与数据库表结构一一对应，通过 DAO 层向上传输数据源对象。
 - DTO（Data Transfer Object）：数据传输对象，Service 和 Manager 向外传输的对象。
 - BO（Business Object）：业务对象。可以由 Service 层输出的封装业务逻辑的对象。
 - QUERY：数据查询对象，各层接收上层的查询请求。注：超过 2 个参数的查询封装，禁止使用 Map 类来传输。
 - VO（View Object）：显示层对象，通常是 Web 向模板渲染引擎层传输的对象。
  
- GAV 规约（与阿里巴巴 Java 开发手册不同，考虑到我们是多 module 以及 .m2 下面 repository 目录层次结构）
 - GroupID 按 4 层，如 com.eking.sample.base (sample 为具体的业务，base 为子业务)，com.eking.newgt.baseinfo
 - ArtifactID 按 子业务-module名，如 base-common、base-config；baseinfo-common、baseinfo-task
