/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import com.travelsky.hub.model.output.BagTag;
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class BagInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String checkedBagQuantity;
/*    */   private String checkedBagWeight;
/*    */   private String checkedBagWeightUnit;
/*    */   private List<BagTag> bagTagNumberList;
/*    */ 
/*    */   public String getCheckedBagQuantity()
/*    */   {
/* 40 */     return this.checkedBagQuantity;
/*    */   }
/*    */ 
/*    */   public void setCheckedBagQuantity(String checkedBagQuantity)
/*    */   {
/* 47 */     this.checkedBagQuantity = checkedBagQuantity;
/*    */   }
/*    */ 
/*    */   public String getCheckedBagWeight()
/*    */   {
/* 54 */     return this.checkedBagWeight;
/*    */   }
/*    */ 
/*    */   public void setCheckedBagWeight(String checkedBagWeight)
/*    */   {
/* 61 */     this.checkedBagWeight = checkedBagWeight;
/*    */   }
/*    */ 
/*    */   public String getCheckedBagWeightUnit()
/*    */   {
/* 68 */     return this.checkedBagWeightUnit;
/*    */   }
/*    */ 
/*    */   public void setCheckedBagWeightUnit(String checkedBagWeightUnit)
/*    */   {
/* 75 */     this.checkedBagWeightUnit = checkedBagWeightUnit;
/*    */   }
/*    */ 
/*    */   public List<BagTag> getBagTagNumberList()
/*    */   {
/* 82 */     return this.bagTagNumberList;
/*    */   }
/*    */ 
/*    */   public void setBagTagNumberList(List<BagTag> bagTagNumberList)
/*    */   {
/* 89 */     this.bagTagNumberList = bagTagNumberList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.BagInfo
 * JD-Core Version:    0.6.0
 */