/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ContactInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -974425153341869643L;
/*    */   private String contactPurposeText;
/*    */ 
/*    */   public String getContactPurposeText()
/*    */   {
/* 26 */     return this.contactPurposeText;
/*    */   }
/*    */ 
/*    */   public void setContactPurposeText(String contactPurposeText)
/*    */   {
/* 33 */     this.contactPurposeText = contactPurposeText;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.ContactInfo
 * JD-Core Version:    0.6.0
 */