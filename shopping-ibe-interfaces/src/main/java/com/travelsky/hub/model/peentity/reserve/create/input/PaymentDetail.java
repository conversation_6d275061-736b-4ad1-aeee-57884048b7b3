/*     */ package com.travelsky.hub.model.peentity.reserve.create.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PaymentDetail
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4254149272908955720L;
/*     */   private String autoIndicator;
/*     */   private String upgPrice;
/*     */   private String payType;
/*     */   private String currencyCode;
/*     */   private PaymentCard paymentCard;
/*     */   private String payText;
/*     */   private LoyaltyRedemption loyaltyRedemption;
/*     */ 
/*     */   public void setUpgPrice(String upgPrice)
/*     */   {
/*  64 */     this.upgPrice = upgPrice;
/*     */   }
/*     */ 
/*     */   public String getUpgPrice()
/*     */   {
/*  71 */     return this.upgPrice;
/*     */   }
/*     */ 
/*     */   public void setAutoIndicator(String autoIndicator)
/*     */   {
/*  78 */     this.autoIndicator = autoIndicator;
/*     */   }
/*     */ 
/*     */   public String getAutoIndicator()
/*     */   {
/*  85 */     return this.autoIndicator;
/*     */   }
/*     */ 
/*     */   public void setCurrencyCode(String currencyCode)
/*     */   {
/*  92 */     this.currencyCode = currencyCode;
/*     */   }
/*     */ 
/*     */   public String getCurrencyCode()
/*     */   {
/*  99 */     return this.currencyCode;
/*     */   }
/*     */ 
/*     */   public void setPayType(String payType)
/*     */   {
/* 106 */     this.payType = payType;
/*     */   }
/*     */ 
/*     */   public String getPayType()
/*     */   {
/* 113 */     return this.payType;
/*     */   }
/*     */ 
/*     */   public void setPayText(String payText)
/*     */   {
/* 120 */     this.payText = payText;
/*     */   }
/*     */ 
/*     */   public String getPayText()
/*     */   {
/* 127 */     return this.payText;
/*     */   }
/*     */ 
/*     */   public void setPaymentCard(PaymentCard paymentCard)
/*     */   {
/* 134 */     this.paymentCard = paymentCard;
/*     */   }
/*     */ 
/*     */   public PaymentCard getPaymentCard()
/*     */   {
/* 141 */     return this.paymentCard;
/*     */   }
/*     */ 
/*     */   public void setLoyaltyRedemption(LoyaltyRedemption loyaltyRedemption)
/*     */   {
/* 148 */     this.loyaltyRedemption = loyaltyRedemption;
/*     */   }
/*     */ 
/*     */   public LoyaltyRedemption getLoyaltyRedemption()
/*     */   {
/* 155 */     return this.loyaltyRedemption;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.input.PaymentDetail
 * JD-Core Version:    0.6.0
 */