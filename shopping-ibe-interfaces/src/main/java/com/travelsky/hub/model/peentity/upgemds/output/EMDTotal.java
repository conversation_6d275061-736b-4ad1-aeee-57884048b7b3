/*    */ package com.travelsky.hub.model.peentity.upgemds.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class EMDTotal
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 2628362011353783161L;
/*    */   private String currencyCode;
/*    */   private String baseTotalAmount;
/*    */   private String currencyCodeNumber;
/*    */ 
/*    */   public String getCurrencyCodeNumber()
/*    */   {
/* 41 */     return this.currencyCodeNumber;
/*    */   }
/*    */ 
/*    */   public void setCurrencyCodeNumber(String currencyCodeNumber)
/*    */   {
/* 48 */     this.currencyCodeNumber = currencyCodeNumber;
/*    */   }
/*    */ 
/*    */   public String getCurrencyCode()
/*    */   {
/* 55 */     return this.currencyCode;
/*    */   }
/*    */ 
/*    */   public void setCurrencyCode(String currencyCode)
/*    */   {
/* 62 */     this.currencyCode = currencyCode;
/*    */   }
/*    */ 
/*    */   public String getBaseTotalAmount()
/*    */   {
/* 69 */     return this.baseTotalAmount;
/*    */   }
/*    */ 
/*    */   public void setBaseTotalAmount(String baseTotalAmount)
/*    */   {
/* 76 */     this.baseTotalAmount = baseTotalAmount;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.output.EMDTotal
 * JD-Core Version:    0.6.0
 */