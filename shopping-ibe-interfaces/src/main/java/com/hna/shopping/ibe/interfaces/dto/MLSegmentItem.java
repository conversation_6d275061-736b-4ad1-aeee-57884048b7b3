package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel

public class MLSegmentItem implements Serializable {


	private static final long serialVersionUID = -945126268879729118L;

	private String pnrNo;
	//编码状态
	private String actionCode;
	private String passengerName;
	//office号
	private String officeCode;
	//编码占座时间
	private String createTime;

	private String depCode;
	private String arrCode;

}
