/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class UpgPsrOutPutBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String airlineCode;
/*     */   private String flightSuffix;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private List<Psr> psrs;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  38 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  45 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  52 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  59 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  66 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  73 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public List<Psr> getPsrs()
/*     */   {
/*  80 */     return this.psrs;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  87 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  94 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setPsrs(List<Psr> psrs)
/*     */   {
/* 101 */     this.psrs = psrs;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 108 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 115 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 122 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 129 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpgPsrOutPutBean
 * JD-Core Version:    0.6.0
 */