/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class EBagOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List<String> dBtpStr;
/*    */ 
/*    */   public List<String> getdBtpStr()
/*    */   {
/* 28 */     return this.dBtpStr;
/*    */   }
/*    */ 
/*    */   public void setdBtpStr(List<String> dBtpStr)
/*    */   {
/* 36 */     this.dBtpStr = dBtpStr;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.EBagOutputBean
 * JD-Core Version:    0.6.0
 */