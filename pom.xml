<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.hna.shopping.ibe</groupId>
    <artifactId>shopping-ibe-parent</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <modules>
        <module>shopping-ibe-config</module>
        <module>shopping-ibe-common</module>
        <module>shopping-ibe-dao</module>
        <module>shopping-ibe-manager</module>
        <module>shopping-ibe-interfaces</module>
        <module>shopping-ibe-implement</module>
        <module>shopping-ibe-web</module>
    </modules>
    <packaging>pom</packaging>

    <name>shopping-ibe-parent</name>
    <description>Demo project for Spring Boot</description>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.4.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>Dalston.SR3</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!--<dependency>-->
            <!--&lt;!&ndash;è¿™ä¸ªå’?dubbo ä¸€èµ·ç”¨æ—¶ï¼Œä¼šæŠ¥&ndash;&gt;-->
            <!--&lt;!&ndash;java.lang.ClassCastException: DubboProviderServiceImpl cannot be cast to DubboProviderServiceImpl&ndash;&gt;-->
            <!--<groupId>org.springframework.boot</groupId>-->
            <!--<artifactId>spring-boot-devtools</artifactId>-->
            <!--<scope>runtime</scope>-->
        <!--</dependency>-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--<groupId>com.h2database</groupId>-->
        <!--<artifactId>h2</artifactId>-->
        <!--<scope>runtime</scope>-->
        <!--</dependency>-->

        <!-- spring cloud >>>-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-eureka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-feign</artifactId>
        </dependency>
        <!--<<< spring cloud-->

        <dependency>
            <groupId>org.springframework.retry</groupId>
            <artifactId>spring-retry</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- é˜¿é‡Œæ•°æ®åº“è¿žæŽ¥æ±  >>>-->
        <!--1.0.28 ç‰ˆæœ¬è¿?oracle çš„æ—¶å€™æœ‰é—®é¢˜ï¼Œåœ¨æŠ¥é”™ä¹‹åŽä¸èƒ½æ­£ç¡®é‡Šæ”¾è¿žæŽ¥ï¼Œå¯¼è‡´æŠ¥é”?
        java.sql.SQLException: internal error
        SQL Error: 17089, SQLState: null
        HHH000010: On release of batch it still contained JDBC statements-->
        <!--å‡çº§åˆ?1.0.29 ä¹‹åŽå°±å¯ä»¥è§£å†³æ­¤é—®é¢˜-->
        <!--è¯¦è§ release 1.0.29 https://github.com/alibaba/druid/releases-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.1.1</version>
        </dependency>
        <!--<<< é˜¿é‡Œæ•°æ®åº“è¿žæŽ¥æ±  -->

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>19.0</version>
        </dependency>
        <!-- apache åŸºç¡€åº?>>>-->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!--<<< apache åŸºç¡€åº?-->

        <!-- memcached ssm åº?>>>-->
        <dependency>
            <groupId>com.google.code.simple-spring-memcached</groupId>
            <artifactId>spring-cache</artifactId>
            <version>3.6.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.simple-spring-memcached</groupId>
            <artifactId>xmemcached-provider</artifactId>
            <version>3.6.1</version>
        </dependency>
        <!--<<< memcached ssm åº?-->

        <!-- swagger2 >>>-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.6.1</version>
        </dependency>
        <!--<<< swagger2 -->

        <dependency>
            <!--Dozer is a Java Bean to Java Bean mapper that recursively copies data from one object to another.-->
            <!-- https://github.com/DozerMapper/dozer -->
            <groupId>com.github.dozermapper</groupId>
            <artifactId>dozer-core</artifactId>
            <version>6.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.8.1</version>
        </dependency>
        <dependency>
            <!--è‡³å°‘è¦ç”¨ 1.2.28ï¼Œå¦åˆ™æ²¡æœ?SerializerFeature.MapSortFieldï¼Œæ— æ³•å¯¹ Map é‡Œçš„ Key è¿›è¡Œå‡åºæŽ’åº-->
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.37</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>2.0.31</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
        </dependency>
        <dependency>
            <groupId>com.hna.eking</groupId>
            <artifactId>utils-date</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.eking</groupId>
            <artifactId>utils-reflect</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.eking</groupId>
            <artifactId>utils-redis</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.eking</groupId>
            <artifactId>utils-http</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.belerweb</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.1</version>
        </dependency>


        <!-- esb 接口  -->
        <dependency>
            <groupId>com.hnair</groupId>
            <artifactId>opcnet-api</artifactId>
            <version>2.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../libs/opcnet-api-2.0-SNAPSHOT.jar</systemPath>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.hnair.opcnet</groupId>-->
<!--            <artifactId>opcnet-esb-client</artifactId>-->
<!--            <version>2.0-RELEASE</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/../libs/opcnet-esb-client-2.0-RELEASE.jar</systemPath>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.hnair.opcnet</groupId>
            <artifactId>opcnet-esb-client</artifactId>
            <version>4.4.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.hnair.opcnet</groupId>
            <artifactId>opcnet-esb-dubbo-wrapper</artifactId>
            <version>4.4.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.hnair.opcnet</groupId>
            <artifactId>opcnet-esb-common</artifactId>
            <version>4.4.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>5.2.1</version>
        </dependency>

        <dependency>
            <groupId>net.sourceforge</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/../libs/pinyin4j-2.5.0.jar</systemPath>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>dubbo</artifactId>-->
<!--            <version>2.5.8</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.zookeeper</groupId>-->
<!--            <artifactId>zookeeper</artifactId>-->
<!--            <version>3.4.6</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.101tec</groupId>-->
<!--            <artifactId>zkclient</artifactId>-->
<!--            <version>0.8</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>4.0.7</version>
        </dependency>
        <!-- esb 接口  -->

        <!-- 集成 redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- 集成 redis -->

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.4</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
            <version>3.3.1.tmp</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.3.1.tmp</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>3.3.1</version>
        </dependency>

        <dependency>
            <artifactId>joda-time</artifactId>
            <groupId>joda-time</groupId>
            <version>2.9.9</version>
        </dependency>

    </dependencies>
 
</project>
