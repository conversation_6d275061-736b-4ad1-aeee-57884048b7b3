package com.hna.shopping.ibe.interfaces.dto;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel
public class RefundTicketRequest extends BaseRequest {/**
	 * 
	 */
	private static final long serialVersionUID = 2981020202366881780L;
	private String ticketNo;
	 private int segID;
	 private String currType;
	 private double amount;
	 private String prntNo;
	 private String remark;
	private String pnrNo;
}
