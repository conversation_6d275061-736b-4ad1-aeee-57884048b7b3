/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Operation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7819295940564588247L;
/*    */   private String operation;
/*    */   private String opTime;
/*    */   private String isOpSuccess;
/*    */   private String errCode;
/*    */   private String errMsg;
/*    */ 
/*    */   public String getOperation()
/*    */   {
/* 31 */     return this.operation;
/*    */   }
/*    */ 
/*    */   public void setOperation(String operation)
/*    */   {
/* 38 */     this.operation = operation;
/*    */   }
/*    */ 
/*    */   public String getOpTime()
/*    */   {
/* 45 */     return this.opTime;
/*    */   }
/*    */ 
/*    */   public void setOpTime(String opTime)
/*    */   {
/* 52 */     this.opTime = opTime;
/*    */   }
/*    */ 
/*    */   public String getIsOpSuccess()
/*    */   {
/* 59 */     return this.isOpSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsOpSuccess(String isOpSuccess)
/*    */   {
/* 66 */     this.isOpSuccess = isOpSuccess;
/*    */   }
/*    */ 
/*    */   public String getErrCode()
/*    */   {
/* 73 */     return this.errCode;
/*    */   }
/*    */ 
/*    */   public void setErrCode(String errCode)
/*    */   {
/* 80 */     this.errCode = errCode;
/*    */   }
/*    */ 
/*    */   public String getErrMsg()
/*    */   {
/* 87 */     return this.errMsg;
/*    */   }
/*    */ 
/*    */   public void setErrMsg(String errMsg)
/*    */   {
/* 94 */     this.errMsg = errMsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.Operation
 * JD-Core Version:    0.6.0
 */