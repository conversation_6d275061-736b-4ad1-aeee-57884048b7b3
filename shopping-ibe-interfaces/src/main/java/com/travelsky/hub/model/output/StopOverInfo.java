/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class StopOverInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String departurelAirport;
/*     */   private String arrivalAirport;
/*     */   private String cabinType;
/*     */   private String seatNumber;
/*     */   private String boardingGateNumber;
/*     */   private String boardingTime;
/*     */ 
/*     */   public String getDeparturelAirport()
/*     */   {
/*  44 */     return this.departurelAirport;
/*     */   }
/*     */ 
/*     */   public void setDeparturelAirport(String departurelAirport)
/*     */   {
/*  53 */     this.departurelAirport = departurelAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  60 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  68 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  76 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  84 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  92 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 100 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingGateNumber()
/*     */   {
/* 107 */     return this.boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingGateNumber(String boardingGateNumber)
/*     */   {
/* 114 */     this.boardingGateNumber = boardingGateNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardingTime()
/*     */   {
/* 121 */     return this.boardingTime;
/*     */   }
/*     */ 
/*     */   public void setBoardingTime(String boardingTime)
/*     */   {
/* 128 */     this.boardingTime = boardingTime;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.StopOverInfo
 * JD-Core Version:    0.6.0
 */