/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsrInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String tktNumber;
/*     */   private String certificateNumber;
/*     */   private String isGroup;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String certificateType;
/*     */   private String flightClass;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String ffpCardNumber;
/*     */   private String passengerName;
/*     */   private String tourIndex;
/*     */   private String bagWeight;
/*     */   private String bagQuantity;
/*     */   private String seatNumber;
/*     */   private String ftpAirlineCode;
/*     */   private String bagArrivalAirport;
/*     */   private String email;
/*     */   private InfInfoBean infInfo;
/*     */   private String hostNumber;
/*     */   private String phoneNumber;
/*     */   private String sNoption;
/*     */   private String xbp;
/*     */   private String chd;
/*     */   private String deptTime;
/*     */   private String psm;
/*     */   private String gender;
/*     */ 
/*     */   public String getEmail()
/*     */   {
/*  80 */     return this.email;
/*     */   }
/*     */ 
/*     */   public void setEmail(String email)
/*     */   {
/*  87 */     this.email = email;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  95 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 103 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 112 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 119 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 127 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 134 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getCertificateNumber()
/*     */   {
/* 141 */     return this.certificateNumber;
/*     */   }
/*     */ 
/*     */   public void setCertificateNumber(String certificateNumber)
/*     */   {
/* 149 */     this.certificateNumber = certificateNumber;
/*     */   }
/*     */ 
/*     */   public String getFFPAirlineCode()
/*     */   {
/* 157 */     return this.ftpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFFPAirlineCode(String airlineCode)
/*     */   {
/* 165 */     this.ftpAirlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getCertificateType()
/*     */   {
/* 173 */     return this.certificateType;
/*     */   }
/*     */ 
/*     */   public void setCertificateType(String certificateType)
/*     */   {
/* 180 */     this.certificateType = certificateType;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 188 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 195 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 203 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getFFPCardNumber()
/*     */   {
/* 210 */     return this.ffpCardNumber;
/*     */   }
/*     */ 
/*     */   public void setFFPCardNumber(String cardNumber)
/*     */   {
/* 218 */     this.ffpCardNumber = cardNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 225 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 235 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getIsGroup()
/*     */   {
/* 243 */     return this.isGroup;
/*     */   }
/*     */ 
/*     */   public void setIsGroup(String isGroup)
/*     */   {
/* 250 */     this.isGroup = isGroup;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 257 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 264 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 272 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 279 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 287 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 297 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 304 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getPsm()
/*     */   {
/* 311 */     return this.psm;
/*     */   }
/*     */ 
/*     */   public void setPsm(String psm)
/*     */   {
/* 319 */     this.psm = psm;
/*     */   }
/*     */ 
/*     */   public String getSNoption()
/*     */   {
/* 327 */     return this.sNoption;
/*     */   }
/*     */ 
/*     */   public void setSNoption(String noption)
/*     */   {
/* 335 */     this.sNoption = noption;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 344 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 352 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getTKTNumber()
/*     */   {
/* 359 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTKTNumber(String number)
/*     */   {
/* 367 */     this.tktNumber = number;
/*     */   }
/*     */ 
/*     */   public String getTourIndex()
/*     */   {
/* 374 */     return this.tourIndex;
/*     */   }
/*     */ 
/*     */   public void setTourIndex(String tourIndex)
/*     */   {
/* 381 */     this.tourIndex = tourIndex;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 389 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 396 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getXbp()
/*     */   {
/* 403 */     return this.xbp;
/*     */   }
/*     */ 
/*     */   public void setXbp(String xbp)
/*     */   {
/* 411 */     this.xbp = xbp;
/*     */   }
/*     */ 
/*     */   public String getBagArrivalAirport()
/*     */   {
/* 418 */     return this.bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagArrivalAirport(String bagArrivalAirport)
/*     */   {
/* 425 */     this.bagArrivalAirport = bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 436 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 443 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/* 450 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/* 457 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 464 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 471 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getFtpAirlineCode()
/*     */   {
/* 479 */     return this.ftpAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setFtpAirlineCode(String ftpAirlineCode)
/*     */   {
/* 486 */     this.ftpAirlineCode = ftpAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getPhoneNumber()
/*     */   {
/* 493 */     return this.phoneNumber;
/*     */   }
/*     */ 
/*     */   public void setPhoneNumber(String phoneNumber)
/*     */   {
/* 500 */     this.phoneNumber = phoneNumber;
/*     */   }
/*     */ 
/*     */   public InfInfoBean getInfInfo()
/*     */   {
/* 507 */     return this.infInfo;
/*     */   }
/*     */ 
/*     */   public void setInfInfo(InfInfoBean infInfo)
/*     */   {
/* 515 */     this.infInfo = infInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PsrInputBean
 * JD-Core Version:    0.6.0
 */