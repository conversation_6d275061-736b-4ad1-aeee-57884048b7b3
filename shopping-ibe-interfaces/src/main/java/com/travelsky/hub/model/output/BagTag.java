/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class BagTag
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String bagTagNum;
/*    */   private String bagArrCity;
/*    */   private String bagStatus;
/*    */ 
/*    */   public String getBagTagNum()
/*    */   {
/* 33 */     return this.bagTagNum;
/*    */   }
/*    */ 
/*    */   public void setBagTagNum(String bagTagNum)
/*    */   {
/* 40 */     this.bagTagNum = bagTagNum;
/*    */   }
/*    */ 
/*    */   public String getBagArrCity()
/*    */   {
/* 47 */     return this.bagArrCity;
/*    */   }
/*    */ 
/*    */   public void setBagArrCity(String bagArrCity)
/*    */   {
/* 54 */     this.bagArrCity = bagArrCity;
/*    */   }
/*    */ 
/*    */   public String getBagStatus()
/*    */   {
/* 61 */     return this.bagStatus;
/*    */   }
/*    */ 
/*    */   public void setBagStatus(String bagStatus)
/*    */   {
/* 68 */     this.bagStatus = bagStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BagTag
 * JD-Core Version:    0.6.0
 */