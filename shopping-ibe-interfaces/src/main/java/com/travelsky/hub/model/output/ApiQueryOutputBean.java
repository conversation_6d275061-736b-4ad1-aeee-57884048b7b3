/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ApiQueryOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String departureDate;
/*     */   private String arrivalAirport;
/*     */   private String hostNumber;
/*     */   private String deniedBoardingVolunteerInd;
/*     */   private ApiInfo apiInfo;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  53 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  60 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  67 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  74 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  81 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  88 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  95 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 102 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 109 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 116 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 123 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 130 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public ApiInfo getApiInfo()
/*     */   {
/* 137 */     return this.apiInfo;
/*     */   }
/*     */ 
/*     */   public void setApiInfo(ApiInfo apiInfo)
/*     */   {
/* 144 */     this.apiInfo = apiInfo;
/*     */   }
/*     */ 
/*     */   public String getDeniedBoardingVolunteerInd()
/*     */   {
/* 151 */     return this.deniedBoardingVolunteerInd;
/*     */   }
/*     */ 
/*     */   public void setDeniedBoardingVolunteerInd(String deniedBoardingVolunteerInd)
/*     */   {
/* 158 */     this.deniedBoardingVolunteerInd = deniedBoardingVolunteerInd;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ApiQueryOutputBean
 * JD-Core Version:    0.6.0
 */