package com.hna.shopping.ibe.manager.impl;

import com.hna.shopping.ibe.common.util.ConvertUtil;
import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.dao.entity.PnrAndTicketRecord;
import com.hna.shopping.ibe.dao.entity.PnrClearRecord;
import com.hna.shopping.ibe.dao.mapper.PnrAndTicketRecordMapper;
import com.hna.shopping.ibe.dao.mapper.PnrClearRecordMapper;
import com.hna.shopping.ibe.interfaces.PNRService;
import com.hna.shopping.ibe.interfaces.TicketService;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.manager.PNRManager;
import com.hna.shopping.ibe.manager.TicketAndPnrClearManager;
import com.hna.shopping.ibe.manager.TicketManager;
import com.hna.shopping.ibe.manager.util.IbeCmd;
import com.travelsky.ibe.client.pnr.BookPassenger;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.PnrManage;
import com.travelsky.ibe.client.pnr.RP;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;


@Component("ticketAndPnrClearManagerImpl")
@Slf4j
public class TicketAndPnrClearManagerImpl implements TicketAndPnrClearManager {

    @Autowired
    private TicketManager ticketManager;

    @Autowired
    private PNRManager pnrManager;

    @Autowired
    private PnrAndTicketRecordMapper pnrAndTicketRecordMapper;

    @Autowired
    private PnrClearRecordMapper pnrClearRecordMapper;

    @Autowired
    private TicketService ticketService;

    @Autowired
    private PNRService pnrService;


    /**
     * 票务处理。退票+清位
     * @param request
     * @return
     */
    @Override
    @Async
    public Boolean ticketAndPnrClear(TicketAndPnrClearRequest request) {
        String pnrNo;
        for (TicketAndPnrClearRequest.TicketInfo reqTicketInfo : request.getTicketInfoList()) {
            for (TicketAndPnrClearRequest.Seg reqSeg : reqTicketInfo.getSegList()) {
                try {
                    DETRRequest detrRequest = new DETRRequest();
                    detrRequest.setAirlineCode("JDPW");
                    detrRequest.setTicketNo(reqTicketInfo.getIssCode() + "-" + reqTicketInfo.getTicketNo());
                    detrRequest.setPassengerName(reqTicketInfo.getPassengerName());
                    detrRequest.setPassengerType(reqTicketInfo.getCertType());
                    detrRequest.setPassengerNo(reqTicketInfo.getCertNo());
                    detrRequest.setPnrNo(reqTicketInfo.getPnrNo());
                    DETRResponse detrResponse = ticketManager.detr(detrRequest);
                    if (!detrResponse.isSuccess()) {
                        log.error("detr error: {}", detrResponse);
                        return false;
                    }
                    pnrNo = getPnrNo(detrResponse, reqSeg);
                    this.handleRefundAndClear(detrResponse, reqSeg, pnrNo, detrRequest.getTicketNo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return true;
    }

    /**
     * 票务处理。退票+清位(中文国际票)
     * @param request
     * @return
     */
    @Override
    @Async
    public Boolean ticketAndPnrClearInter(TicketAndPnrClearRequest request) {
        String pnrNo;
        for (TicketAndPnrClearRequest.TicketInfo reqTicketInfo : request.getTicketInfoList()) {
            for (TicketAndPnrClearRequest.Seg reqSeg : reqTicketInfo.getSegList()) {
                try {
                    DETRRequest detrRequest = new DETRRequest();
                    detrRequest.setAirlineCode("JDPW");
                    detrRequest.setTicketNo(reqTicketInfo.getIssCode() + "-" + reqTicketInfo.getTicketNo());
                    detrRequest.setPassengerName(reqTicketInfo.getPassengerName());
                    detrRequest.setPassengerType(reqTicketInfo.getCertType());
                    detrRequest.setPassengerNo(reqTicketInfo.getCertNo());
                    detrRequest.setPnrNo(reqTicketInfo.getPnrNo());
                    DETRResponse detrResponse = ticketManager.detr(detrRequest);
                    if (!detrResponse.isSuccess()) {
                        log.error("detr error: {}", detrResponse);
                        return false;
                    }
                    pnrNo = getPnrNo(detrResponse, reqSeg);
                    this.handleRefundAndClearInter(detrResponse, reqSeg, pnrNo, detrRequest.getTicketNo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return true;
    }

    /**
     * 票务处理。退票+清位(海外站)
     * @param request
     * @return
     */
    @Override
    @Async
    public Boolean ticketAndPnrClearHWZ(TicketAndPnrClearRequest request) {
        String pnrNo;
        for (TicketAndPnrClearRequest.TicketInfo reqTicketInfo : request.getTicketInfoList()) {
            for (TicketAndPnrClearRequest.Seg reqSeg : reqTicketInfo.getSegList()) {
                try {
                    DETRRequest detrRequest = new DETRRequest();
                    detrRequest.setAirlineCode("JDPW");
                    detrRequest.setTicketNo(reqTicketInfo.getIssCode() + "-" + reqTicketInfo.getTicketNo());
                    detrRequest.setPassengerName(reqTicketInfo.getPassengerName());
                    detrRequest.setPassengerType(reqTicketInfo.getCertType());
                    detrRequest.setPassengerNo(reqTicketInfo.getCertNo());
                    detrRequest.setPnrNo(reqTicketInfo.getPnrNo());
                    DETRResponse detrResponse = ticketManager.detr(detrRequest);
                    if (!detrResponse.isSuccess()) {
                        log.error("detr inter error: {}", detrResponse);
                        return false;
                    }
                    pnrNo = getPnrNo(detrResponse, reqSeg);
                    this.handleRefundAndClearHWZ(detrResponse, reqSeg, pnrNo, detrRequest.getTicketNo());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return true;
    }


    /**
     * 匹配航段，获取编码
     * @param detrResponse
     * @param reqSeg
     * @return
     */
    private String getPnrNo(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg) {
        for (DETRTktResult ticketInfo : detrResponse.getTicketInfos()) {
            for (DETRSeg seg : ticketInfo.getSegs()) {
                if (reqSeg.getDepCode().equals(seg.getDepAirportCode())
                        && reqSeg.getArrCode().equals(seg.getArrAirportCode())
                        && StringUtils.isNotEmpty(seg.getPnrNo())
                ) {
                    return seg.getPnrNo();
                }
            }
        }
        return "";
    }

    /**
     * 获取航段ID
     * @param detrResponse
     * @param reqSeg
     * @return
     */
    private int getSegIdForRefund(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg) {
        for (DETRTktResult ticketInfo : detrResponse.getTicketInfos()) {
            for (DETRSeg seg : ticketInfo.getSegs()) {
                if (reqSeg.getDepCode().equals(seg.getDepAirportCode()) && reqSeg.getArrCode().equals(seg.getArrAirportCode())) {
                    return "OPEN FOR USE".equals(seg.getTicketStatus()) ? seg.getSegmentIndex() : -1;
                }
            }
        }
        return -1;
    }

    /**
     * 是否集团内航司
     * @param rtResult
     * @param reqSeg
     * @return
     */
    private boolean isHNA(RTResult rtResult, TicketAndPnrClearRequest.Seg reqSeg) {
        for (PNRSegment airSeg : rtResult.getAirSegs()) {
            if (airSeg.getOrgCity().equals(reqSeg.getDepCode()) && airSeg.getDesCity().equals(reqSeg.getArrCode())) {
                if (!airSeg.getAirNo().startsWith("HU")
                        && !airSeg.getAirNo().startsWith("JD")
                        && !airSeg.getAirNo().startsWith("GS")
                        && !airSeg.getAirNo().startsWith("PN")
                        && !airSeg.getAirNo().startsWith("8L")
                        && !airSeg.getAirNo().startsWith("FU")
                        && !airSeg.getAirNo().startsWith("Y8")
                        && !airSeg.getAirNo().startsWith("GX")
                        && !airSeg.getAirNo().startsWith("UQ")
                ) {
                    return false;
                }
            }
        }
        return true;
    }


    private void handleRefundAndClear(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo, String ticketNo) {

        if (StringUtils.isNotEmpty(pnrNo)) {
            try {
                RTRequest rtRequest = new RTRequest();
                rtRequest.setAirlineCode("JDPW");
                rtRequest.setPnrNo(pnrNo);
                RTResult rtRes = pnrManager.rt(rtRequest);

                DETRTktResult detrTktResult = detrResponse.getTicketInfos().get(0);

                if (detrTktResult.getETicketType() == 12
                        || detrTktResult.getETicketType() == 13
                        || !isHNA(rtRes, reqSeg)
                ) {  // BSP 或者非集团内航司
                    // 获取RP权限
                    RP rp = IbeCmd.getIbeClient("JDPW", RP.class);
                    rp.ModifyResp(pnrNo, "XIY258", null);
                }

                // 婴儿单独处理
                if (detrTktResult.getPassengerType() == 3) {
                    this.clearInfant(detrResponse, rtRes, reqSeg, pnrNo);
                } else {
                    // 将要退的人拆到新编码
                    if (rtRes.getPassengers().size() > 1) {
                        SplitPnrRequest splitPnrRequest = new SplitPnrRequest();
                        splitPnrRequest.setAirlineCode("JDPW");
                        splitPnrRequest.setPnrNo(pnrNo);
                        splitPnrRequest.setSplitNum(1);
                        PNRPassenger pnrPassenger = new PNRPassenger();
                        pnrPassenger.setType(detrTktResult.getPassengerType());
                        pnrPassenger.setName(detrTktResult.getPassengerName());
                        splitPnrRequest.setPassengers(Collections.singletonList(pnrPassenger));
                        SplitPNRResponse splitPNRResponse = pnrManager.splitPnr(splitPnrRequest);
                        if (splitPNRResponse.isSuccess()) {
                            pnrNo = splitPNRResponse.getPnrNo();
                        } else {
                            throw new RuntimeException(splitPNRResponse.getErrorInfo());
                        }
                    }

                    CancelPnrRequest cancelPnrRequest = new CancelPnrRequest();
                    cancelPnrRequest.setAirlineCode("JDPW");
                    cancelPnrRequest.setPnrNo(pnrNo);
                    for (PNRSegment airSeg : rtRes.getAirSegs()) {
                        if (airSeg.getOrgCity().equals(reqSeg.getDepCode()) && airSeg.getDesCity().equals(reqSeg.getArrCode())) {
                            CancelPnrRequest.Segment segment = new CancelPnrRequest.Segment();
                            ConvertUtil.map(airSeg, segment);
                            cancelPnrRequest.setPnrSegment(segment);
                        }
                    }
                    BaseResponse response = pnrService.cancelPnr(cancelPnrRequest);
                    // 清位记录
                    if (response.isSuccess()) {
                        this.savePnrClearRecord("DONE", detrResponse, reqSeg, pnrNo);
                    } else {
                        this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                    }
                }
            } catch (Exception e) {
                this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                log.error("pnr clear error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
            }
        }

        try {
            // 插入退票记录
            this.savePnrRecord(detrResponse, reqSeg, pnrNo, ticketNo);
            RefundTicketRequest refundTicketRequest = new RefundTicketRequest();
            refundTicketRequest.setAirlineCode("JDPW");
            refundTicketRequest.setTicketNo(ticketNo.replaceAll("-", ""));
            refundTicketRequest.setPnrNo(pnrNo);
            refundTicketRequest.setSegID(getSegIdForRefund(detrResponse, reqSeg));
            refundTicketRequest.setAmount(0.0);
            refundTicketRequest.setCurrType("CNY");
            refundTicketRequest.setRemark("");
            refundTicketRequest.setPrntNo("1");
            BaseResponse refundRes = ticketService.etrf(refundTicketRequest);
        } catch (Exception e) {
            log.error("ticket refund error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
        }

    }


    private void handleRefundAndClearInter(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo, String ticketNo) {
        if (StringUtils.isNotEmpty(pnrNo)) {
            try {
                RTRequest rtRequest = new RTRequest();
                rtRequest.setAirlineCode("JDPW");
                rtRequest.setPnrNo(pnrNo);
                RTResult rtRes = pnrManager.rt(rtRequest);

                DETRTktResult detrTktResult = detrResponse.getTicketInfos().get(0);

                // 婴儿单独处理
                if (detrTktResult.getPassengerType() == 3) {
                    this.clearInfant(detrResponse, rtRes, reqSeg, pnrNo);
                } else {
                    // 将要退的人拆到新编码
                    if (rtRes.getPassengers().size() > 1) {
                        SplitPnrRequest splitPnrRequest = new SplitPnrRequest();
                        splitPnrRequest.setAirlineCode("JDPW");
                        splitPnrRequest.setPnrNo(pnrNo);
                        splitPnrRequest.setSplitNum(1);
                        PNRPassenger pnrPassenger = new PNRPassenger();
                        pnrPassenger.setType(detrTktResult.getPassengerType());
                        pnrPassenger.setName(detrTktResult.getPassengerName());
                        splitPnrRequest.setPassengers(Collections.singletonList(pnrPassenger));
                        SplitPNRResponse splitPNRResponse = pnrManager.splitPnr(splitPnrRequest);
                        if (splitPNRResponse.isSuccess()) {
                            pnrNo = splitPNRResponse.getPnrNo();
                        } else {
                            throw new RuntimeException(splitPNRResponse.getErrorInfo());
                        }
                    }

                    CancelPnrRequest cancelPnrRequest = new CancelPnrRequest();
                    cancelPnrRequest.setAirlineCode("JDPW");
                    cancelPnrRequest.setPnrNo(pnrNo);
                    cancelPnrRequest.setXepnr(true);
                    // XE
                    BaseResponse response = pnrService.cancelPnr(cancelPnrRequest);
                    // 清位记录
                    if (response.isSuccess()) {
                        this.savePnrClearRecord("DONE", detrResponse, reqSeg, pnrNo);
                    } else {
                        this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                    }
                }
            } catch (Exception e) {
                this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                log.error("pnr clear error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
            }
        }

        try {
            // 获取RP权限
            RP rp = IbeCmd.getIbeClient("JDPW", RP.class);
            rp.ModifyResp(pnrNo, "XIY258", null);
            // 插入退票记录
            this.savePnrRecord(detrResponse, reqSeg, pnrNo, ticketNo);
            RefundTicketRequest refundTicketRequest = new RefundTicketRequest();
            refundTicketRequest.setAirlineCode("JDPW");
            refundTicketRequest.setTicketNo(ticketNo.replaceAll("-", ""));
            refundTicketRequest.setPnrNo(pnrNo);
            refundTicketRequest.setSegID(getSegIdForRefund(detrResponse, reqSeg));
            refundTicketRequest.setAmount(0.0);
            refundTicketRequest.setCurrType("CNY");
            refundTicketRequest.setRemark("");
            refundTicketRequest.setPrntNo("1");
            for (DETRSeg seg : detrResponse.getTicketInfos().get(0).getSegs()) {
                if (seg.getDepAirportCode().equals(reqSeg.getDepCode()) && seg.getArrAirportCode().equals(reqSeg.getArrCode())) {
                    if (seg.getTicketStatus().startsWith("AIRP")) {
                        DETR detr = IbeCmd.getIbeClient("JDPW", DETR.class);
                        //收回控制权
                        int[] ds = {seg.getSegmentIndex()};//航段
                        String result = detr.getCouponControl(ticketNo, ds, "");
                        if (!"OK".equalsIgnoreCase(result)) {
                            log.error("get coupon control error: {}", result);
                        }
                    }
                }
            }
            BaseResponse refundRes = ticketService.etrf(refundTicketRequest);
        } catch (Exception e) {
            log.error("ticket refund error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
        }
    }

    /**
     * 国际票。
     * @param detrResponse
     * @param reqSeg
     * @param pnrNo
     * @param ticketNo
     */
    private void handleRefundAndClearHWZ(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo, String ticketNo) {

        if (StringUtils.isNotEmpty(pnrNo)) {
            try {
                RTRequest rtRequest = new RTRequest();
                rtRequest.setAirlineCode("JDPW");
                rtRequest.setPnrNo(pnrNo);
                RTResult rtRes = pnrManager.rt(rtRequest);

                DETRTktResult detrTktResult = detrResponse.getTicketInfos().get(0);

                // 获取RP权限
                RP rp = IbeCmd.getIbeClient("JDPW", RP.class);
                rp.ModifyResp(pnrNo, "XIY258", null);

                // 婴儿单独处理
                if (detrTktResult.getPassengerType() == 3) {
                    this.clearInfant(detrResponse, rtRes, reqSeg, pnrNo);
                } else {
                    BaseResponse response = new BaseResponse();
                    // 编码有多个人，removeName
                    if (rtRes.getPassengers().size() > 1) {
                        Vector vector = new Vector();
                        BookPassenger bookPassenger = new BookPassenger(detrTktResult.getPassengerName());
                        vector.add(bookPassenger);
                        RemoveNameRequest removeNameRequest = new RemoveNameRequest();
                        removeNameRequest.setPnrNo(pnrNo);
                        removeNameRequest.setPassengers(vector);
                        String result = pnrManager.removeName(removeNameRequest);
                        if (result.equalsIgnoreCase("OK")) {
                            response.setSuccess(true);
                        } else {
                            response.setErrorInfo(result);
                        }
                    } else {
                        CancelPnrRequest cancelPnrRequest = new CancelPnrRequest();
                        cancelPnrRequest.setAirlineCode("JDPW");
                        cancelPnrRequest.setPnrNo(pnrNo);
                        cancelPnrRequest.setXepnr(true);
                        response = pnrService.cancelPnr(cancelPnrRequest);
                    }

                    // 清位记录
                    if (response.isSuccess()) {
                        this.savePnrClearRecord("DONE", detrResponse, reqSeg, pnrNo);
                    } else {
                        this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                    }
                }
            } catch (Exception e) {
                this.savePnrClearRecord("UNHANDLE", detrResponse, reqSeg, pnrNo);
                log.error("pnr clear error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
            }
        }

        try {
            // 获取RP权限
            RP rp = IbeCmd.getIbeClient("JDPW", RP.class);
            rp.ModifyResp(pnrNo, "XIY258", null);
            // 插入退票记录
            this.savePnrRecord(detrResponse, reqSeg, pnrNo, ticketNo);
            RefundTicketRequest refundTicketRequest = new RefundTicketRequest();
            refundTicketRequest.setAirlineCode("JDPW");
            refundTicketRequest.setTicketNo(ticketNo.replaceAll("-", ""));
            refundTicketRequest.setPnrNo(pnrNo);
            refundTicketRequest.setSegID(getSegIdForRefund(detrResponse, reqSeg));
            refundTicketRequest.setAmount(0.0);
            refundTicketRequest.setCurrType("CNY");
            refundTicketRequest.setRemark("");
            refundTicketRequest.setPrntNo("1");
            for (DETRSeg seg : detrResponse.getTicketInfos().get(0).getSegs()) {
                if (seg.getDepAirportCode().equals(reqSeg.getDepCode()) && seg.getArrAirportCode().equals(reqSeg.getArrCode())) {
                    if (seg.getTicketStatus().startsWith("AIRP")) {
                        DETR detr = IbeCmd.getIbeClient("JDPW", DETR.class);
                        //收回控制权
                        int[] ds = {seg.getSegmentIndex()};//航段
                        String result = detr.getCouponControl(ticketNo, ds, "");
                        if (!"OK".equalsIgnoreCase(result)) {
                            log.error("get coupon control error: {}", result);
                        }
                    }
                }
            }
            BaseResponse refundRes = ticketService.etrf(refundTicketRequest);
        } catch (Exception e) {
            log.error("ticket refund error: {}, detrResponse: {}, reqSeg: {}, pnrNo: {}, ticketNo: {}", e.getMessage(), detrResponse, reqSeg, pnrNo, ticketNo);
        }

    }


    @Retryable
    private void clearInfant(DETRResponse detrResponse, RTResult rtResult, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo) throws Exception {
        PnrManage pnrManage = IbeCmd.getIbeClient("JDPW", PnrManage.class);
        int ssrInft = 0;
        List<Integer> itemIndexList = new ArrayList<>();
        for (PNRSSR ssr : rtResult.getSsrs()) {
            if (ssr.getSSRType().equals("INFT")) {
                ssrInft++;
                if (ssr.getCitypair().equals(reqSeg.getDepCode() + "" + reqSeg.getArrCode())) {
                    itemIndexList.add(ssr.getIndex());
                    ssrInft--;
                }
            }
        }
        if (ssrInft == 0) {
            itemIndexList.add(rtResult.getInfants().get(0).getIndex());
        }

        // 删除 SSR INFT 和 XN/IN
        log.info("start delete infant item, pnrNo: {}, seg: {}", pnrNo, reqSeg);
        pnrManage.deleteItem(pnrNo, itemIndexList.stream().mapToInt(i -> i).toArray());
        log.info("end delete infant item, pnrNo: {}, seg: {}", pnrNo, reqSeg);
    }


    private void savePnrRecord(DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo, String ticketNo) {
        try {
            PnrAndTicketRecord pnrAndTicketRecord = new PnrAndTicketRecord();

            DETRSeg detrSeg = new DETRSeg();
            for (DETRTktResult ticketInfo : detrResponse.getTicketInfos()) {
                for (DETRSeg seg : ticketInfo.getSegs()) {
                    if (reqSeg.getDepCode().equals(seg.getDepAirportCode())
                            && reqSeg.getArrCode().equals(seg.getArrAirportCode())
                    ) {
                        detrSeg = seg;
                    }
                }
            }
            pnrAndTicketRecord.setPassengerName(detrResponse.getTicketInfos().get(0).getPassengerName());
            pnrAndTicketRecord.setIdCard(detrResponse.getTicketInfos().get(0).getPassengerID());
            pnrAndTicketRecord.setDepCode(detrSeg.getDepAirportCode());
            pnrAndTicketRecord.setArrCode(detrSeg.getArrAirportCode());
            pnrAndTicketRecord.setFlightDate(detrSeg.getDepTime() != null ? DateUtil.toStringYMDHM(detrSeg.getDepTime()) : null);
            pnrAndTicketRecord.setCabin(String.valueOf(detrSeg.getCabin()));
            pnrAndTicketRecord.setFlightNo(detrSeg.getFlightNo());
            pnrAndTicketRecord.setPnrNo(pnrNo);
            pnrAndTicketRecord.setTicketNo(ticketNo);
            pnrAndTicketRecord.setIssueTicketTime(detrResponse.getTicketInfos().get(0).getIssueDate());

            int id = pnrAndTicketRecordMapper.insert(pnrAndTicketRecord);
        } catch (Exception e) {
            log.error("savePnrRecord error: pnrNo: {}, ticketNo: {}", pnrNo, ticketNo);
            e.printStackTrace();
        }
    }


        private void savePnrClearRecord(String status, DETRResponse detrResponse, TicketAndPnrClearRequest.Seg reqSeg, String pnrNo) {
        String fltDate = null, fltNo = null, depCode = null, arrCode = null, passengerName = null;
        try {
            PnrClearRecord pnrClearRecord = new PnrClearRecord();

            DETRSeg detrSeg = new DETRSeg();
            for (DETRTktResult ticketInfo : detrResponse.getTicketInfos()) {
                for (DETRSeg seg : ticketInfo.getSegs()) {
                    if (reqSeg.getDepCode().equals(seg.getDepAirportCode())
                            && reqSeg.getArrCode().equals(seg.getArrAirportCode())
                            && StringUtils.isNotEmpty(seg.getPnrNo())
                    ) {
                        detrSeg = seg;
                    }
                }
            }

            pnrClearRecord.setPnrNo(pnrNo);
            if ("DONE".equals(status)) {
                pnrClearRecord.setClearPnrTime(new Date());
            }
            fltDate = DateUtil.toStringYMDHM(detrSeg.getDepTime());
            fltNo = detrSeg.getFlightNo();
            depCode = detrSeg.getDepAirportCode();
            arrCode = detrSeg.getArrAirportCode();
            passengerName = detrResponse.getTicketInfos().get(0).getPassengerName();
            pnrClearRecord.setStatus("DONE".equals(status)?1:0);
            pnrClearRecord.setFlightDate(fltDate);
            pnrClearRecord.setFlightNo(fltNo);
            pnrClearRecord.setDepCode(depCode);
            pnrClearRecord.setArrCode(arrCode);
            pnrClearRecord.setPassengerName(passengerName);
            pnrClearRecord.setCreateTime(new Date());
            pnrClearRecordMapper.insert(pnrClearRecord);
        } catch (Exception e) {
            //防止pnr丢失
            log.error("savePnrClearRecord error:"+ e.getMessage()+ "/" + pnrNo + fltDate + fltNo
                    + depCode + arrCode + passengerName);
        }
    }

}
