/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgAddBagInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String addBagTag;
/*     */   private String carrAirlineCode;
/*     */   private String etNo;
/*     */   private String depCity;
/*     */   private String flightNumber;
/*  42 */   private String addBagWeight = "0";
/*     */   private String flightDate;
/*     */   private String arrCity;
/*     */   private String hostNumber;
/*     */   private String cabin;
/*     */   private String addBagQuantity;
/*     */   private String addBagWeightUnit;
/*     */   private String bagTagArrCity;
/*     */ 
/*     */   public String getDepCity()
/*     */   {
/*  30 */     return this.depCity;
/*     */   }
/*     */ 
/*     */   public void setDepCity(String depCity)
/*     */   {
/*  37 */     this.depCity = depCity;
/*     */   }
/*     */ 
/*     */   public void setAddBagWeight(String addBagWeight)
/*     */   {
/*  61 */     this.addBagWeight = addBagWeight;
/*     */   }
/*     */ 
/*     */   public String getAddBagWeightUnit()
/*     */   {
/*  68 */     return this.addBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public String getCarrAirlineCode()
/*     */   {
/*  77 */     return this.carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public void setCarrAirlineCode(String carrAirlineCode)
/*     */   {
/*  84 */     this.carrAirlineCode = carrAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  91 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  98 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 105 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 112 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getArrCity()
/*     */   {
/* 119 */     return this.arrCity;
/*     */   }
/*     */ 
/*     */   public void setArrCity(String arrCity)
/*     */   {
/* 126 */     this.arrCity = arrCity;
/*     */   }
/*     */ 
/*     */   public String getEtNo()
/*     */   {
/* 134 */     return this.etNo;
/*     */   }
/*     */ 
/*     */   public void setEtNo(String etNo)
/*     */   {
/* 141 */     this.etNo = etNo;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 149 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 156 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 163 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 170 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getAddBagQuantity()
/*     */   {
/* 177 */     return this.addBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setAddBagQuantity(String addBagQuantity)
/*     */   {
/* 184 */     this.addBagQuantity = addBagQuantity;
/*     */   }
/*     */ 
/*     */   public String getAddBagWeight()
/*     */   {
/* 191 */     return this.addBagWeight;
/*     */   }
/*     */ 
/*     */   public void setAddBagWeightUnit(String addBagWeightUnit)
/*     */   {
/* 198 */     this.addBagWeightUnit = addBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public String getBagTagArrCity()
/*     */   {
/* 205 */     return this.bagTagArrCity;
/*     */   }
/*     */ 
/*     */   public void setBagTagArrCity(String bagTagArrCity)
/*     */   {
/* 212 */     this.bagTagArrCity = bagTagArrCity;
/*     */   }
/*     */ 
/*     */   public String getAddBagTag()
/*     */   {
/* 219 */     return this.addBagTag;
/*     */   }
/*     */ 
/*     */   public void setAddBagTag(String addBagTag)
/*     */   {
/* 226 */     this.addBagTag = addBagTag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PsgAddBagInputBean
 * JD-Core Version:    0.6.0
 */