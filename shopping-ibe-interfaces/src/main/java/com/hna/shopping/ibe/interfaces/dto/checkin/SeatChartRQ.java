package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import com.travelsky.hub.model.peentity.seatchart.input.FrequentFlyerProgram;
import lombok.Data;

import java.util.List;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:06
 * @Version 1.0
 */
@Data
public class SeatChartRQ extends BaseRequest{
    private String ticketingDate;//服务销售日期2011-12-01 T09:00:00
    private String vcAirline;//出票航空公司
    private Passenger passenger;
    private FlightSegment flightSegment;
    List<FrequentFlyerProgram> frequentFlyerPrograms;

    @Data
    public static class Passenger{
        private String passengerType;//旅客类型ADT
        private Integer passengerAge;//旅客年龄
        private Integer passengerOccurrence;
        private String passengerAttribute;//ADULT
        private String ethnicity;//民族
        private String passengerFOIDType;//旅客证件类型PASSPORT
        private String gender;//F-女，M-男
        private Integer passengerValue;
        private String passengerNationality;
        private Integer passengerPsptAge;
        private Integer passengerPocsAge;
        private String passengerStatus;
        private List<String> purchasedPros;
    }
    @Data
    public static class FlightSegment{
        private String segmentID;//航段数序号
        private String departureAirport;
        private String arrivalAirport;
        private String departureDateTime;//2019-03-29 T08:00:00
        private String arrivalDateTime;
        private String mcAirlineCode;
        private String mcFlightNumber;
        private String mcRBD;//*市场方订座舱位（小舱）
        private String ocAirlineCode;
        private String ocFlightNumber;
        private String ocRBD;//承运方订座舱位（小舱）
        private String equipment;//机型
        private String mcCabin;//服务等级（市场方大舱位）
    }

    @Data
    public static class FrequentFlyerProgram{
        private String ffpAirline;//常客卡所属航司
        private String ffpLevel;//常客等级
        private Integer mileage;//传入该用户会员卡的剩余的里程积
    }
}
