/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.config.memcached;

import java.lang.reflect.Method;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.interceptor.SimpleKey;
import org.apache.commons.codec.digest.DigestUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义 key 生成策略, 每个 key 前面都加上前缀
 *
 * <AUTHOR>
 */
@Slf4j
public class CustomerKeyGenerator implements KeyGenerator {

    private static MemcachedConfig.MemcachedConfigInfo memcachedConfigInfo;

    @Autowired
    public CustomerKeyGenerator(MemcachedConfig.MemcachedConfigInfo memcachedConfigInfo) {
        CustomerKeyGenerator.memcachedConfigInfo = memcachedConfigInfo;
    }

    @Override
    public Object generate(Object target, Method method, Object... params) {
        return generateKey(params);
    }

    /**
     * Generate a key based on the specified parameters.
     */
    public static Object generateKey(Object... params) {
        // 每次在所有参数面前补一个 prefix, 这个 prefix 是系统相关的, 每个系统与其他系统的 prefix 必须不同
        Object[] extraParams = new Object[params.length + 1];
        extraParams[0] = memcachedConfigInfo.getPrefix();
        System.arraycopy(params, 0, extraParams, 1, params.length);

        if (extraParams.length == 1) {
            Object param = extraParams[0];
            if (param != null && !param.getClass().isArray()) {
                return param;
            }
        }

        SimpleKey simpleKey = new SimpleKey(extraParams);
        String md5 = DigestUtils.md2Hex(simpleKey.toString());
        //log.info("MD5: {}, Simple key: {}", md5, simpleKey.toString());
        return md5;
        //        return new SimpleKey(extraParams);
    }
}