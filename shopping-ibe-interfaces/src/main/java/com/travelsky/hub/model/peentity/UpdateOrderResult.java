/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class UpdateOrderResult
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -3795118140211328975L;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */   private String isSuccess;
/*    */   private String OrderStatus;
/*    */   private String orderNum;
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 32 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 39 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public String getIsSuccess()
/*    */   {
/* 46 */     return this.isSuccess;
/*    */   }
/*    */ 
/*    */   public void setIsSuccess(String isSuccess)
/*    */   {
/* 53 */     this.isSuccess = isSuccess;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 60 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 67 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 74 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 81 */     this.errorCode = errorCode;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 88 */     return this.OrderStatus;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 95 */     this.OrderStatus = orderStatus;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.UpdateOrderResult
 * JD-Core Version:    0.6.0
 */