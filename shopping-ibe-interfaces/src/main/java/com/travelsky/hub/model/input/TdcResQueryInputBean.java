/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class TdcResQueryInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -9169966807234380542L;
/*     */   private String etCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String dptAptCode;
/*     */   private String hostNum;
/*     */   private String airlineCode;
/*     */   private String flightClass;
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/*  23 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/*  30 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  51 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  57 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  66 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  73 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  81 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  88 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  95 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 102 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getDptAptCode()
/*     */   {
/* 109 */     return this.dptAptCode;
/*     */   }
/*     */ 
/*     */   public void setDptAptCode(String dptAptCode)
/*     */   {
/* 116 */     this.dptAptCode = dptAptCode;
/*     */   }
/*     */ 
/*     */   public String getHostNum()
/*     */   {
/* 123 */     return this.hostNum;
/*     */   }
/*     */ 
/*     */   public void setHostNum(String hostNum)
/*     */   {
/* 130 */     this.hostNum = hostNum;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.TdcResQueryInputBean
 * JD-Core Version:    0.6.0
 */