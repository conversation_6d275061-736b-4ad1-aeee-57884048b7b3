package com.hna.shopping.ibe.manager;

import com.hna.shopping.ibe.interfaces.dto.*;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Vector;

public interface TicketManager {

	/**
	 * @param request
	 * @return
	 */
	DETRResponse detr(DETRRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	BaseResponse etrf(RefundTicketRequest request);
	/**
	 * @param request
	 * @return
	 */
	BaseResponse etdz(IssueTicketRequest request);
	
	/**
	 * @param request
	 * @return
	 */
	IssueTicketResponse issueTicketWithTN(IssueTicketRequest request);

	/**
	 * @param request
	 * @return
	 */
	DETRHistoryResponse getTicketHistory(DETRHistoryRequest request);

	/**
	 * 根据客票号或者证件号、pnr提取客票信息
	 * @param request
	 * @return
     */
	DtoTicketInfoResponse getTicketInfo(DtoTicketInfoRequest request);

	/**
	 * 根据客票号或者证件号、pnr提取客票信息. V1版本，不校验过滤证件号、航班日期
	 * @param request
	 * @return
     */
	DtoTicketInfoResponse getTicketInfoV1(DtoTicketInfoRequest request);

	/**
	 * 根据客票号或者证件号、pnr提取客票信息(高频数据源)
	 * @param request
	 * @return
     */
	DtoTicketInfoResponse getTicketInfoByHSD(DtoTicketInfoRequest request);

	/**
	 * 根据航班号、航班日期、旅客姓名提取客票信息(高频数据源)
	 * @param request
	 * @return
     */
	DtoTicketInfoResponse getTicketInfoByFlightInfoAndName(DtoTicketInfoByFlightInfoAndNameRequest request);


	/**
	 * @param request
	 * @return
	 */
	DETRCreResponse detrCredential(DETRRequest request);

	/**
	 * 旅服系统回迁。通过高频接口获取航班旅客信息
	 * @param request
	 * @return
	 */
	FlightPassengerInfoResponse getFlightPassengerInfoByHSD(FlightPassengerInfoRequest request);
	List<Date> getFixDepartureDate(DETRRequest request);

	Vector<String> getTicketListByCert(Map<String, String> request);
}
