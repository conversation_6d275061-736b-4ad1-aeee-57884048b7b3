/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class HostPsgDetailOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6471527152691401111L;
/* 14 */   private String prmsg = "";
/*    */ 
/*    */   public String getPrmsg()
/*    */   {
/* 20 */     return this.prmsg;
/*    */   }
/*    */ 
/*    */   public void setPrmsg(String prmsg)
/*    */   {
/* 27 */     this.prmsg = prmsg;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.HostPsgDetailOutputBean
 * JD-Core Version:    0.6.0
 */