/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatChartInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String flightOriCity;
/*     */   private String flightDesCity;
/*     */   private String etCode;
/*     */   private String cabin;
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  44 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  51 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  58 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  65 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightOriCity()
/*     */   {
/*  72 */     return this.flightOriCity;
/*     */   }
/*     */ 
/*     */   public void setFlightOriCity(String flightOriCity)
/*     */   {
/*  79 */     this.flightOriCity = flightOriCity;
/*     */   }
/*     */ 
/*     */   public String getFlightDesCity()
/*     */   {
/*  86 */     return this.flightDesCity;
/*     */   }
/*     */ 
/*     */   public void setFlightDesCity(String flightDesCity)
/*     */   {
/*  93 */     this.flightDesCity = flightDesCity;
/*     */   }
/*     */ 
/*     */   public String getEtCode()
/*     */   {
/* 100 */     return this.etCode;
/*     */   }
/*     */ 
/*     */   public void setEtCode(String etCode)
/*     */   {
/* 107 */     this.etCode = etCode;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 114 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 121 */     this.cabin = cabin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.SeatChartInputBean
 * JD-Core Version:    0.6.0
 */