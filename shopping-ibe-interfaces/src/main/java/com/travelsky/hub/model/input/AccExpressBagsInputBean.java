/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AccExpressBagsInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7094199523332839528L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightSuffix;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String bagCount;
/*     */   private String bagWeight;
/*     */   private String isCrewBag;
/*     */   private String bagRemark;
/*     */   private String isXBT;
/*     */   private String isManualBag;
/*     */   private String bagDestination;
/*     */   private String bagAirlineCode;
/*     */   private List<String> bagTagNumbers;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  85 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  93 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 100 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 108 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 116 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 124 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 132 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 140 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getBagCount()
/*     */   {
/* 147 */     return this.bagCount;
/*     */   }
/*     */ 
/*     */   public void setBagCount(String bagCount)
/*     */   {
/* 155 */     this.bagCount = bagCount;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/* 163 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/* 171 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getIsCrewBag()
/*     */   {
/* 179 */     return this.isCrewBag;
/*     */   }
/*     */ 
/*     */   public void setIsCrewBag(String isCrewBag)
/*     */   {
/* 187 */     this.isCrewBag = isCrewBag;
/*     */   }
/*     */ 
/*     */   public String getBagRemark()
/*     */   {
/* 195 */     return this.bagRemark;
/*     */   }
/*     */ 
/*     */   public void setBagRemark(String bagRemark)
/*     */   {
/* 203 */     this.bagRemark = bagRemark;
/*     */   }
/*     */ 
/*     */   public String getIsXBT()
/*     */   {
/* 211 */     return this.isXBT;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 219 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 227 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setIsXBT(String isXBT)
/*     */   {
/* 235 */     this.isXBT = isXBT;
/*     */   }
/*     */ 
/*     */   public String getIsManualBag()
/*     */   {
/* 243 */     return this.isManualBag;
/*     */   }
/*     */ 
/*     */   public void setIsManualBag(String isManualBag)
/*     */   {
/* 251 */     this.isManualBag = isManualBag;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 258 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 266 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getBagDestination()
/*     */   {
/* 274 */     return this.bagDestination;
/*     */   }
/*     */ 
/*     */   public void setBagDestination(String bagDestination)
/*     */   {
/* 282 */     this.bagDestination = bagDestination;
/*     */   }
/*     */ 
/*     */   public String getBagAirlineCode()
/*     */   {
/* 290 */     return this.bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 298 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 306 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagAirlineCode(String bagAirlineCode)
/*     */   {
/* 314 */     this.bagAirlineCode = bagAirlineCode;
/*     */   }
/*     */ 
/*     */   public List<String> getBagTagNumbers()
/*     */   {
/* 322 */     return this.bagTagNumbers;
/*     */   }
/*     */ 
/*     */   public void setBagTagNumbers(List<String> bagTagNumbers)
/*     */   {
/* 330 */     this.bagTagNumbers = bagTagNumbers;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AccExpressBagsInputBean
 * JD-Core Version:    0.6.0
 */