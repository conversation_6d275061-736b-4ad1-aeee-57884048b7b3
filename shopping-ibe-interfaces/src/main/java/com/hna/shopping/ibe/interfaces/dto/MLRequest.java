package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper=false)
@ApiModel

public class MLRequest extends BaseRequest {


	private static final long serialVersionUID = 4505019349870320820L;
	//8L1234
	private String flightNo;
	//yyyy-MM-dd HH:mm
	private String flightDate;

	//状态，空即全部，HK，HX，RR
	private String actionCode;

	//起飞城市
	private String depCode;

	private String arrCode;

}
