package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
// @NoArgsConstructor
@ApiModel
public class PNRTktNo extends PNRObject{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1443572853587902147L;
	 private String psgrID;                                                                 
	 private String remark;                                                                 
	 private String tktNo;                                                                  
	 private String conjunction;                                                            
	private int index;                                                                         
	                                                                                        
	 public PNRTktNo()                                                                      
	   throws Exception                                                                     
	 {                                                                                      
	 }                                                                                      
	                                                                                        
	 public PNRTktNo(String psgrID, String remark, String tktNo)                            
	   throws Exception                                                                     
	 {                                                                                      
	   this.psgrID = psgrID;                                                                
	   this.remark = remark;                                                                
	   this.tktNo = tktNo;                                                                  
	 }                                                                                      
	                                                                                        
	 void setPsgrID(String psgrID)                                                          
	 {                                                                                      
	   this.psgrID = psgrID;                                                                
	 }                                                                                      
	                                                                                        
	 public String getPsgrID()                                                              
	 {                                                                                      
	   return this.psgrID;                                                                  
	 }                                                                                      
	                                                                                        
	 public String getPsgrid() throws Exception, UnsupportedOperationException              
	 {                                                                                      
	   return getPsgrID();                                                                  
	 }                                                                                      
	                                                                                        
	 void setRemark(String remark) {                                                        
	   this.remark = remark;                                                                
	 }                                                                                      
	                                                                                        
	 public String getRemark()                                                              
	 {                                                                                      
	   return this.remark;                                                                  
	 }                                                                                      
	                                                                                        
	 void setTktNo(String tktNo) {                                                          
	   this.tktNo = tktNo;                                                                  
	 }                                                                                      
	                                                                                        
	 public String getTktNo()                                                               
	 {                                                                                      
	   return this.tktNo;                                                                   
	 }                                                                                      
	                                                                                        
                                                                                
	                                                                                        
	 public int compareTo(Object o)                                                         
	   throws ClassCastException                                                            
	 {                                                                                      
	   if (!(o.getClass().equals(super.getClass()))) {                                      
	     throw new ClassCastException();                                                    
	   }                                                                                    
	   return getTktNo().compareTo(((PNRTktNo)o).getTktNo());                               
	 }                                                                                      
	                                                                                        
	 public String getConjunction()                                                         
	 {                                                                                      
	   return this.conjunction;                                                             
	 }                                                                                      
	                                                                                        
	 public void setConjunction(String conjunction)                                         
	 {                                                                                      
	   this.conjunction = conjunction;                                                      
	 }                                                                                      

}
