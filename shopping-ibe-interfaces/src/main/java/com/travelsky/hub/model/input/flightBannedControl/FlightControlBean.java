/*     */ package com.travelsky.hub.model.input.flightBannedControl;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightControlBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5732507149907053047L;
/*     */   private String airlineCode;
/*     */   private String departureAirport;
/*     */   private String destAirport;
/*     */   private String flightNumber;
/*     */   private String channel;
/*     */   private String optService;
/*     */   private String departureDate;
/*     */   private String deptTime;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  60 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  67 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  74 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  81 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDestAirport()
/*     */   {
/*  88 */     return this.destAirport;
/*     */   }
/*     */ 
/*     */   public void setDestAirport(String destAirport)
/*     */   {
/*  95 */     this.destAirport = destAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 102 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 109 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getChannel()
/*     */   {
/* 116 */     return this.channel;
/*     */   }
/*     */ 
/*     */   public void setChannel(String channel)
/*     */   {
/* 123 */     this.channel = channel;
/*     */   }
/*     */ 
/*     */   public String getOptService()
/*     */   {
/* 130 */     return this.optService;
/*     */   }
/*     */ 
/*     */   public void setOptService(String optService)
/*     */   {
/* 137 */     this.optService = optService;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 144 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 151 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 158 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 165 */     this.deptTime = deptTime;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.flightBannedControl.FlightControlBean
 * JD-Core Version:    0.6.0
 */