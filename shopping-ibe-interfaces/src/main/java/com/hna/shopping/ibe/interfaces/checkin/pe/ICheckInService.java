package com.hna.shopping.ibe.interfaces.checkin.pe;

import com.travelsky.hub.model.input.*;
import com.travelsky.hub.model.input.govsecuritycheck.GovSecurityCheckInput;
import com.travelsky.hub.model.input.hbpamseat.MultiPsrCheckInSeatInput;
import com.travelsky.hub.model.input.hbpaoptions.PsrCheckInInput;
import com.travelsky.hub.model.input.poolingbaggageallowance.PoolingBaggageAllowanceInput;
import com.travelsky.hub.model.input.rt.QueryOrderInput;
import com.travelsky.hub.model.output.*;
import com.travelsky.hub.model.output.govsecuritycheck.GovSecurityCheckOutput;
import com.travelsky.hub.model.output.hbpamseat.MultiPsrCheckInSeatOutput;
import com.travelsky.hub.model.output.hbpaoptions.PsrCheckInOutput;
import com.travelsky.hub.model.output.poolingbaggageallowance.PoolingBaggageAllowanceOutput;
import com.travelsky.hub.model.output.rt.QueryOrderOutput;
import com.travelsky.hub.model.peentity.*;
import com.travelsky.hub.model.peentity.bookingSeat.input.BookingSeatRQ;
import com.travelsky.hub.model.peentity.bookingSeat.output.BookingSeatRS;
import com.travelsky.hub.model.peentity.meal.input.BookingMealRQ;
import com.travelsky.hub.model.peentity.meal.input.CancelMealRQ;
import com.travelsky.hub.model.peentity.meal.input.QueryMealInventoryRQ;
import com.travelsky.hub.model.peentity.meal.input.QueryMealOrderRQ;
import com.travelsky.hub.model.peentity.meal.output.BookingMealRS;
import com.travelsky.hub.model.peentity.meal.output.CancelMealRS;
import com.travelsky.hub.model.peentity.meal.output.QueryMealInventoryRS;
import com.travelsky.hub.model.peentity.meal.output.QueryMealOrderRS;
import com.travelsky.hub.model.peentity.reserve.cancel.input.CancelReserveUpgRQ;
import com.travelsky.hub.model.peentity.reserve.cancel.output.CancelReserveUpgRS;
import com.travelsky.hub.model.peentity.reserve.create.input.ReserveUpgRQ;
import com.travelsky.hub.model.peentity.reserve.create.output.ReserveUpgRS;
import com.travelsky.hub.model.peentity.reserve.query.input.QueryOrderListRQ;
import com.travelsky.hub.model.peentity.reserve.query.output.QueryOrderListRS;
import com.travelsky.hub.model.peentity.seatchart.input.SeatChartRq;
import com.travelsky.hub.model.peentity.seatchart.output.SeatChartRs;
import com.travelsky.hub.model.peentity.trr.confirm.input.DoRebookRQ;
import com.travelsky.hub.model.peentity.trr.confirm.output.DoRebookRS;
import com.travelsky.hub.model.peentity.trr.query.input.QueryRebookFlightRQ;
import com.travelsky.hub.model.peentity.trr.query.output.QueryRebookFlightRS;
import com.travelsky.hub.model.peentity.upgemds.input.IssueEMDSServiceOrderRQ;
import com.travelsky.hub.model.peentity.upgemds.output.IssueEMDSServiceOrderRS;
import com.travelsky.hub.util.APISvcException;
import com.travelsky.hub.util.HubServiceException;
import com.travelsky.hub.util.MealServiceException;
import com.travelsky.hub.wdoe.input.HbOptionsInputBean;
import com.travelsky.hub.wdoe.input.HbpuoInputBean;
import com.travelsky.hub.wdoe.output.HbOptionsOutputBean;
import com.travelsky.hub.wdoe.output.PUOutputBean;

import java.util.Map;

public abstract interface ICheckInService
{
  public abstract SYPRResultBean doSYPR(SYPRQueryBean paramSYPRQueryBean)
    throws HubServiceException;

  public abstract DetrOutPutBean detrTicket(DetrInputBean paramDetrInputBean)
    throws HubServiceException;

  public abstract PsrCheckInResult doPsrCheckin(PsrCheckInBean paramPsrCheckInBean)
    throws HubServiceException;

  public abstract WeatherInfoResult queryCityWeather(WeatherQueryBean paramWeatherQueryBean)
    throws HubServiceException;

  public abstract BoardInfoResult queryFlightBoardInAirport(FlightBoardQueryBean paramFlightBoardQueryBean)
    throws HubServiceException;

  public abstract PWResult doDelPsr(PsgPwInfoBean paramPsgPwInfoBean)
    throws HubServiceException;

  public abstract SeatChartResultBean querySeatChart(SeatChartQueryBean paramSeatChartQueryBean)
    throws HubServiceException;

  public abstract RePrintOutputBean rePrint(RePrintInputBean paramRePrintInputBean)
    throws HubServiceException;

  public abstract MultiPsrOutputBean multiPsrCheckIn(MultiPsrInputBean paramMultiPsrInputBean)
    throws HubServiceException;

  public abstract HbOptionsOutputBean preHbpuOptions(HbOptionsInputBean paramHbOptionsInputBean)
    throws HubServiceException;

  public abstract CheckInStatusQueryOutputBean queryCheckInStatus(CheckInStatusQueryInputBean paramCheckInStatusQueryInputBean)
    throws HubServiceException;

  public abstract UpdateOutputBean passengerUpdate(UpdateInputBean paramUpdateInputBean)
    throws HubServiceException;

  public abstract PUOutputBean connectingFlightCheckIn(HbpuoInputBean paramHbpuoInputBean)
    throws HubServiceException;

  public abstract EbpOutputBean getEboardingPass(EbpInfo paramEbpInfo)
    throws HubServiceException;

  public abstract EbpOutputBean getPdf417Ebp(EbpInfo paramEbpInfo)
    throws HubServiceException;

  public abstract EbpOutputBean getBarCodeEbp(EbpInfo paramEbpInfo)
    throws HubServiceException;

  public abstract SeatChartResultBean newQuerySeatMap(SeatChartInputBean paramSeatChartInputBean)
    throws HubServiceException;

  public abstract CheckCodeOutputBean queryCheckCode(CheckCodeInputBean paramCheckCodeInputBean)
    throws HubServiceException;

  public abstract ApiQueryOutputBean apiInfoQuery(ApiQueryInputBean paramApiQueryInputBean)
    throws HubServiceException;

  public abstract AqqQueryOutputBean aqqConfQuery(AqqQueryInputBean paramAqqQueryInputBean)
    throws HubServiceException;

  public abstract AqqResQueryOutputBean aqqResQuery(AqqResQueryInputBean paramAqqResQueryInputBean)
    throws HubServiceException;

  public abstract ApiUpdateOutputBean apiUpdate(ApiUpdateInput paramApiUpdateInput)
    throws HubServiceException;

  public abstract AqqOutputBean aqqRequest(AqqRequestBean paramAqqRequestBean)
    throws HubServiceException;

  public abstract ChdPsrOutputBean chdPsrCheckIn(ChdPsrInputBean paramChdPsrInputBean)
    throws HubServiceException;

  public abstract ConnCkiWithCHDOuputBean connCheckInWithCHD(ConnCkiWithCHDInputBean paramConnCkiWithCHDInputBean)
    throws HubServiceException;

  public abstract UpgFltOutBean getUpgFlts(String paramString)
    throws HubServiceException;

  public abstract UpgPsrOutPutBean getUpgPsrByFltInfo(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract UpgFltInfoOutBean getPsrFltInfoByCert(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract UpgFltInfoOutBean getPsrFltInfoByCertOrEtOnly(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract UpgradeOrderOutPutBean createUpgradeOrder(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract CompletePayOutPutBean completePay(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract PsrOrdersOutPutBean getOrdersByPsr(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract UpgOrderDetail getOrderTetail(String paramString)
    throws HubServiceException;

  public abstract CancelOrderOutPutBean cancelOrder(String paramString)
    throws HubServiceException;

  public abstract UpdateOrderResult upgOrderUpdate(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract RefundOrderOutPutBean refundOrder(String paramString)
    throws HubServiceException;

  public abstract UpgBasicInfoResult getUpgBasicInfoByPsr(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract UpgFltInfoOutBean getPsrFltInfoByCertAll(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract SeatOrderOutPutBean createSeatOrder(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract CompletePaySeatOutPutBean completeSeatPay(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract SeatOrdersOutPutBean getSeatOrdersByPsr(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract SeatOrderCreated getSeatOrderCreateResult(String paramString)
    throws HubServiceException;

  public abstract SeatOrderIssued getSeatOrderIssueResult(String paramString)
    throws HubServiceException;

  public abstract SeatOrderCanceled getSeatOrderCancelResult(String paramString)
    throws HubServiceException;

  public abstract SeatOrderDetail getSeatOrderDetail(String paramString)
    throws HubServiceException;

  public abstract CancelSeatOrderOutPutBean cancelSeatOrder(String paramString)
    throws HubServiceException;

  public abstract SeatChartRs getSeatChart(SeatChartRq paramSeatChartRq)
    throws HubServiceException;

  public abstract SeatOrderRecord getSeatOrderRecord(String paramString)
    throws HubServiceException;

  public abstract CompleteSeatRefundOutPutBean completeSeatRefund(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract PsgAddBagOutputBean psgAddBag(PsgAddBagInputBean paramPsgAddBagInputBean)
    throws HubServiceException;

  public abstract QueryPsgInfoOutput queryPsgBag(QueryPsgInfoInput paramQueryPsgInfoInput)
    throws HubServiceException;

  public abstract IcsSeatMapOutput showIcsSeatMap(IcsSeatMapInput paramIcsSeatMapInput)
    throws HubServiceException;

  public abstract PreCkiBookOutput preCkiBook(PreCkiBookInput paramPreCkiBookInput)
    throws HubServiceException;

  public abstract PreCkiDelBookResult preCkiDelBook(PreCkiDelBookRequest paramPreCkiDelBookRequest)
    throws HubServiceException;

  public abstract PreCkiBookInfoOutput preCkiBookInfo(PreCkiBookInfoInput paramPreCkiBookInfoInput)
    throws HubServiceException;

  public abstract EDISeReqOutput querySeatEDIReq(EDISeInput paramEDISeInput)
    throws HubServiceException;

  public abstract EDISeResOutput querySeatEDIRes(EDISeInput paramEDISeInput)
    throws HubServiceException;

  public abstract EDIHbpuoReqOutput connEDICheckInReq(EDIHbpuoInfo paramEDIHbpuoInfo)
    throws HubServiceException;

  public abstract EDIHbpuoResOutput connEDICheckInRes(EDIHbpuoInfo paramEDIHbpuoInfo)
    throws HubServiceException;

  public abstract PaxBrdOutPutBean getPaxBrdInfo(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract PaxSeutOutPutBean getPaxSeutInfo(Map<String, String> paramMap)
    throws HubServiceException;

  public abstract AdcOutputBean adcRequest(AdcRequestBean paramAdcRequestBean)
    throws HubServiceException;

  public abstract AdcResQueryOutputBean adcResQuery(AdcResQueryInputBean paramAdcResQueryInputBean)
    throws HubServiceException;

  public abstract PrfOutputBean passengerPrf(QueryPrfBean paramQueryPrfBean)
    throws HubServiceException;

  public abstract EBagOutputBean getEBag(EBagInputBean paramEBagInputBean)
    throws HubServiceException;

  public abstract EboardingCardOutputBean getEBoardingCard(EboardingCardInputBean paramEboardingCardInputBean)
    throws HubServiceException;

  public abstract PmcSeOutput queryPmcSeatchart(PmcSeInput paramPmcSeInput)
    throws HubServiceException;

  public abstract TdcOutputBean tdcRequest(TdcRequestBean paramTdcRequestBean)
    throws HubServiceException;

  public abstract TdcResQueryOutputBean tdcResQuery(TdcResQueryInputBean paramTdcResQueryInputBean)
    throws HubServiceException;

  public abstract QueryMealInventoryRS queryMealInventory(QueryMealInventoryRQ paramQueryMealInventoryRQ)
    throws MealServiceException;

  public abstract BookingMealRS bookingMeal(BookingMealRQ paramBookingMealRQ)
    throws MealServiceException;

  public abstract QueryMealOrderRS queryMealOrder(QueryMealOrderRQ paramQueryMealOrderRQ)
    throws MealServiceException;

  public abstract CancelMealRS cancelMeal(CancelMealRQ paramCancelMealRQ)
    throws MealServiceException;

  public abstract CancelReserveUpgRS cancelReserveUpg(CancelReserveUpgRQ paramCancelReserveUpgRQ)
    throws APISvcException;

  public abstract AccExpressBagsOutputBean accExpressbags(AccExpressBagsInputBean paramAccExpressBagsInputBean)
    throws APISvcException;

  public abstract DelExpressBagsOutputBean delExpressBags(DelExpressBagsInputBean paramDelExpressBagsInputBean)
    throws APISvcException;

  public abstract QryExpressBagsOutputBean qryExpressBags(QryExpressBagsInputBean paramQryExpressBagsInputBean)
    throws APISvcException;

  public abstract DifferSeatchartOutput getDifferSeatchart(DifferSeatchartInput paramDifferSeatchartInput)
    throws APISvcException;

  public abstract QueryPassengerInfoOutputBean queryPassengerInfo(QueryPassengerInfoInputBean paramQueryPassengerInfoInputBean)
    throws APISvcException;

  public abstract BookingSeatRS bookingSeat(BookingSeatRQ paramBookingSeatRQ)
    throws APISvcException;

  public abstract ReserveUpgRS reserveUpg(ReserveUpgRQ paramReserveUpgRQ)
    throws APISvcException;

  public abstract JcsOutputBean seatReservation(JcsInputBean paramJcsInputBean)
    throws APISvcException;

  public abstract RaOutputBean seatRelease(RaInputBean paramRaInputBean)
    throws APISvcException;

  public abstract HbpuOptionsOutput hbpuOptions(HbpuOptionsInput paramHbpuOptionsInput)
    throws APISvcException;

  public abstract DelPsrInfoOutput delPsrInfo(DelPsrInfoInput paramDelPsrInfoInput)
    throws APISvcException;

  public abstract QueryOrderOutput queryOrderDetail(QueryOrderInput paramQueryOrderInput)
    throws APISvcException;

  public abstract AddBaggageOutputBean addBaggage(AddBaggageInputBean paramAddBaggageInputBean)
    throws APISvcException;

  public abstract DelBagOutputBean delBaggage(DelBagInputBean paramDelBagInputBean)
    throws APISvcException;

  public abstract MultiPsrCheckInSeatOutput multiPsrCheckInSeat(MultiPsrCheckInSeatInput paramMultiPsrCheckInSeatInput)
    throws APISvcException;

  public abstract GovSecurityCheckOutput govSecurityCheck(GovSecurityCheckInput paramGovSecurityCheckInput)
    throws APISvcException;

  public abstract PsrCheckInOutput psrCheckIn(PsrCheckInInput paramPsrCheckInInput)
    throws APISvcException;

  public abstract PoolingBaggageAllowanceOutput poolingBaggageAllowance(PoolingBaggageAllowanceInput paramPoolingBaggageAllowanceInput)
    throws APISvcException;

  public abstract PwOutput delPsr(PwInput paramPwInput)
    throws APISvcException;

  public abstract DoRebookRS doRebook(DoRebookRQ paramDoRebookRQ)
    throws APISvcException;

  public abstract QueryRebookFlightRS queryRebookFlight(QueryRebookFlightRQ paramQueryRebookFlightRQ)
    throws APISvcException;

  public abstract IssueEMDSServiceOrderRS issueEMDSServiceOrder(IssueEMDSServiceOrderRQ paramIssueEMDSServiceOrderRQ)
    throws APISvcException;

  public abstract QueryOrderListRS queryReserveOrderList(QueryOrderListRQ paramQueryOrderListRQ)
    throws APISvcException;
}

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.svc.ICheckInService
 * JD-Core Version:    0.6.0
 */