
package com.hna.shopping.ibe.interfaces.dto.searchone.request;
import java.util.List;

import lombok.Data;
@Data
public class JourneyPreferences {

    private boolean allowMultiCabinCombo = false ;
    private String availability = "HIGH";
    private String channelId  = "B2C";
    private boolean detailedPricing = true;
    private String[] fareTypes = new String[]{"PUBLIC", "PRIVATE"};
    private boolean includeBaggage = true;;
    private boolean includeCommission = true;;
    private boolean includePenalties = true;
    private boolean multiCabin = true;
    private boolean noInterline = false;
    private String platingCarrier;
    private boolean returnResultsInBatches = false;
    private boolean sameCarrierFare = false;
    private int searchSpeed = 1;
    private boolean splitTicket = false;


}