/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class AqqRequestBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6107224918330114683L;
/*     */   private String fromCity;
/*     */   private String flightClass;
/*     */   private String ticketNumber;
/*     */   private String msgType;
/*     */   private String destCity;
/*     */   private String hostNumber;
/*     */   private String ckiStatus;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String flightNumber;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  45 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  53 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  61 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  69 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getMsgType()
/*     */   {
/*  76 */     return this.msgType;
/*     */   }
/*     */ 
/*     */   public void setMsgType(String msgType)
/*     */   {
/*  84 */     this.msgType = msgType;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  91 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 100 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 107 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 115 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 123 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 131 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 139 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setCkiStatus(String ckiStatus)
/*     */   {
/* 147 */     this.ckiStatus = ckiStatus;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 155 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 163 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 170 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 177 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 185 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getCkiStatus()
/*     */   {
/* 192 */     return this.ckiStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AqqRequestBean
 * JD-Core Version:    0.6.0
 */