package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class DETRH<PERSON>oryRequest extends BaseRequest {

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8827010362173338382L;
	@ApiModelProperty(value = "客票号")
	private String ticketNo;

	@ApiModelProperty(value = "pnrNo")
	private String pnrNo;

	@ApiModelProperty(value = "旅客证件类型")
	private String passengerType;

	@ApiModelProperty(value = "旅客证件号")
	private String passengerNo;

	/**
	 * 旅客姓名。
	 */
	private String passengerName;

}
