/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgPreferSeatInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String flightSuffix;
/*     */   private String paxOriCity;
/*     */   private String paxDesCity;
/*     */   private String paxLevel;
/*     */   private String seatPrefer;
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/*  44 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/*  51 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public String getPaxLevel()
/*     */   {
/*  60 */     return this.paxLevel;
/*     */   }
/*     */ 
/*     */   public void setPaxLevel(String paxLevel)
/*     */   {
/*  67 */     this.paxLevel = paxLevel;
/*     */   }
/*     */ 
/*     */   public String getPaxOriCity()
/*     */   {
/*  75 */     return this.paxOriCity;
/*     */   }
/*     */ 
/*     */   public void setPaxOriCity(String paxOriCity)
/*     */   {
/*  82 */     this.paxOriCity = paxOriCity;
/*     */   }
/*     */ 
/*     */   public String getPaxDesCity()
/*     */   {
/*  89 */     return this.paxDesCity;
/*     */   }
/*     */ 
/*     */   public void setPaxDesCity(String paxDesCity)
/*     */   {
/*  96 */     this.paxDesCity = paxDesCity;
/*     */   }
/*     */ 
/*     */   public String getSeatPrefer()
/*     */   {
/* 103 */     return this.seatPrefer;
/*     */   }
/*     */ 
/*     */   public void setSeatPrefer(String seatPrefer)
/*     */   {
/* 110 */     this.seatPrefer = seatPrefer;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.PsgPreferSeatInputBean
 * JD-Core Version:    0.6.0
 */