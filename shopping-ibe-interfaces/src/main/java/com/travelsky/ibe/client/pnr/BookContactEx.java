package com.travelsky.ibe.client.pnr;

public class BookContactEx extends BookContact {

	/**
	 * 
	 */
	private static final long serialVersionUID = 7274680844432734616L;
	/*     */ public  String getCity() {
		/*  79 */     return super.getCity();//.city;
		/*     */   }
	/*     */ public  String getContact() {
		/*  92 */     return super.getContact();//.contact;
		/*     */   }
	/*     */ public  String getpsgrName() {
		/* 107 */     return super.getpsgrName();//.psgrName;
		/*     */   }
	public BookContactEx() {
		super();
		// TODO Auto-generated constructor stub
	}
	public BookContactEx(String city, String contact, String psgrName) throws Exception {
		super(city, contact, psgrName);
		// TODO Auto-generated constructor stub
	}
	public BookContactEx(String city, String contact) throws Exception {
		super(city, contact);
		// TODO Auto-generated constructor stub
	}
	public BookContactEx(String contact) throws Exception {
		super(contact);
		// TODO Auto-generated constructor stub
	}
}
