package com.hna.shopping.ibe.interfaces.dto.pricing.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PIFlightSegment implements Serializable {
    private static final long serialVersionUID = 1L;
    private String arrivalAirport;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date arrivalDateTime;
    private String departureAirport;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date departureDateTime;
    private String flightNumber;
    private String airline;
    private String cabin;


}