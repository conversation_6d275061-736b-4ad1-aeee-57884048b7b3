/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TourCode
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2171255174693869463L;
/*    */   private String isManInput;
/*    */   private String infantIndi;
/*    */   private String overflowInd;
/*    */   private String text;
/*    */ 
/*    */   public String getIsManInput()
/*    */   {
/* 38 */     return this.isManInput;
/*    */   }
/*    */ 
/*    */   public void setIsManInput(String isManInput)
/*    */   {
/* 45 */     this.isManInput = isManInput;
/*    */   }
/*    */ 
/*    */   public String getInfantIndi()
/*    */   {
/* 52 */     return this.infantIndi;
/*    */   }
/*    */ 
/*    */   public void setInfantIndi(String infantIndi)
/*    */   {
/* 59 */     this.infantIndi = infantIndi;
/*    */   }
/*    */ 
/*    */   public String getOverflowInd()
/*    */   {
/* 66 */     return this.overflowInd;
/*    */   }
/*    */ 
/*    */   public void setOverflowInd(String overflowInd)
/*    */   {
/* 73 */     this.overflowInd = overflowInd;
/*    */   }
/*    */ 
/*    */   public String getText()
/*    */   {
/* 80 */     return this.text;
/*    */   }
/*    */ 
/*    */   public void setText(String text)
/*    */   {
/* 87 */     this.text = text;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.TourCode
 * JD-Core Version:    0.6.0
 */