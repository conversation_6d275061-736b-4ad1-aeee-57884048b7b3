/*      */ package com.travelsky.hub.model.output;
/*      */ 
/*      */ import com.travelsky.hub.model.input.Extraseat;
/*      */ import com.travelsky.hub.model.input.InfInfoBean;
/*      */ import com.travelsky.hub.model.output.pr.Asvc;
/*      */ import java.io.Serializable;
/*      */ import java.util.List;
/*      */ 
/*      */ public class PrPassengerInfo
/*      */   implements Serializable
/*      */ {
/*      */   private static final long serialVersionUID = 1818763373018288789L;
/*      */   private String surName;
/*      */   private String chnName;
/*      */   private String bagWeight;
/*      */   private String bagQuantity;
/*      */   private String docType;
/*      */   private String docID;
/*      */   private String contactInfo;
/*      */   private List<BaggageTag> baggageTags;
/*      */   private List<OperationInfo> operationInfos;
/*      */   private String icsPassengerNameRecord;
/*      */   private String crsPassengerNameRecord;
/*      */   private String skyPriorityFlag;
/*      */   private String hostNumber;
/*      */   private String parentCabinType;
/*      */   private String cabinType;
/*      */   private String snrFlag;
/*      */   private String seatNumber;
/*      */   private List<SeatChange> seatChanges;
/*      */   private String ffpAirlineCode;
/*      */   private String ffpCardNumber;
/*      */   private String ffpCardPrior;
/*      */   private String chd;
/*      */   private String gender;
/*      */   private String groupCode;
/*      */   private String groupNumber;
/*      */   private String boardingNumber;
/*      */   private String standByIndicator;
/*      */   private String standByNumber;
/*      */   private String marketingAirline;
/*      */   private String marketingFlightNumber;
/*      */   private String fareClass;
/*      */   private String ticketNumber;
/*      */   private String sequenceNumber;
/*      */   private Extraseat extraSeat;
/*      */   private String specialSvcInfo;
/*      */   private InfInfoBean infant;
/*      */   private List<TextMsg> textMsgs;
/*      */   private PsptInfo psptInfo;
/*      */   private RecheckInfo recheckInfo;
/*      */   private List<Bound> boundInfos;
/*      */   private String passengerStatus;
/*      */   private String remark;
/*      */   private String aecSeatNumber;
/*      */   private String passengerType;
/*      */   private String uresFlag;
/*      */   private String aqqFlag;
/*      */   private String estaFlag;
/*      */   private String isAsrSeat;
/*      */   private String vipFlag;
/*      */   private PrApiInfo prApiInfo;
/*      */   private AppInfo appInfo;
/*      */   private String reservedSeatType;
/*      */   private String fba;
/*      */   private String gfba;
/*      */   private String familyCount;
/*      */   private String isFamily;
/*      */   private List<Asvc> asvcInfo;
/*      */   private List<String> ckinContent;
/*      */   private String checkinChannel;
/*      */ 
/*      */   public String getCheckinChannel()
/*      */   {
/*  275 */     return this.checkinChannel;
/*      */   }
/*      */ 
/*      */   public void setCheckinChannel(String checkinChannel)
/*      */   {
/*  282 */     this.checkinChannel = checkinChannel;
/*      */   }
/*      */ 
/*      */   public List<Asvc> getAsvcInfo()
/*      */   {
/*  289 */     return this.asvcInfo;
/*      */   }
/*      */ 
/*      */   public void setAsvcInfo(List<Asvc> asvcInfo)
/*      */   {
/*  296 */     this.asvcInfo = asvcInfo;
/*      */   }
/*      */ 
/*      */   public List<String> getCkinContent()
/*      */   {
/*  303 */     return this.ckinContent;
/*      */   }
/*      */ 
/*      */   public void setCkinContent(List<String> ckinContent)
/*      */   {
/*  310 */     this.ckinContent = ckinContent;
/*      */   }
/*      */ 
/*      */   public String getChnName()
/*      */   {
/*  319 */     return this.chnName;
/*      */   }
/*      */ 
/*      */   public void setChnName(String chnName)
/*      */   {
/*  327 */     this.chnName = chnName;
/*      */   }
/*      */ 
/*      */   public String getSurName()
/*      */   {
/*  335 */     return this.surName;
/*      */   }
/*      */ 
/*      */   public void setSurName(String surName)
/*      */   {
/*  343 */     this.surName = surName;
/*      */   }
/*      */ 
/*      */   public String getBagQuantity()
/*      */   {
/*  352 */     return this.bagQuantity;
/*      */   }
/*      */ 
/*      */   public void setBagQuantity(String bagQuantity)
/*      */   {
/*  360 */     this.bagQuantity = bagQuantity;
/*      */   }
/*      */ 
/*      */   public String getBagWeight()
/*      */   {
/*  367 */     return this.bagWeight;
/*      */   }
/*      */ 
/*      */   public void setBagWeight(String bagWeight)
/*      */   {
/*  375 */     this.bagWeight = bagWeight;
/*      */   }
/*      */ 
/*      */   public String getDocType()
/*      */   {
/*  382 */     return this.docType;
/*      */   }
/*      */ 
/*      */   public void setDocType(String docType)
/*      */   {
/*  390 */     this.docType = docType;
/*      */   }
/*      */ 
/*      */   public String getDocID()
/*      */   {
/*  398 */     return this.docID;
/*      */   }
/*      */ 
/*      */   public void setDocID(String docID)
/*      */   {
/*  406 */     this.docID = docID;
/*      */   }
/*      */ 
/*      */   public String getContactInfo()
/*      */   {
/*  414 */     return this.contactInfo;
/*      */   }
/*      */ 
/*      */   public void setContactInfo(String contactInfo)
/*      */   {
/*  422 */     this.contactInfo = contactInfo;
/*      */   }
/*      */ 
/*      */   public List<BaggageTag> getBaggageTags()
/*      */   {
/*  430 */     return this.baggageTags;
/*      */   }
/*      */ 
/*      */   public void setBaggageTags(List<BaggageTag> baggageTags)
/*      */   {
/*  438 */     this.baggageTags = baggageTags;
/*      */   }
/*      */ 
/*      */   public List<OperationInfo> getOperationInfos()
/*      */   {
/*  446 */     return this.operationInfos;
/*      */   }
/*      */ 
/*      */   public void setOperationInfos(List<OperationInfo> operationInfos)
/*      */   {
/*  454 */     this.operationInfos = operationInfos;
/*      */   }
/*      */ 
/*      */   public String getIcsPassengerNameRecord()
/*      */   {
/*  462 */     return this.icsPassengerNameRecord;
/*      */   }
/*      */ 
/*      */   public void setIcsPassengerNameRecord(String icsPassengerNameRecord)
/*      */   {
/*  470 */     this.icsPassengerNameRecord = icsPassengerNameRecord;
/*      */   }
/*      */ 
/*      */   public String getCrsPassengerNameRecord()
/*      */   {
/*  478 */     return this.crsPassengerNameRecord;
/*      */   }
/*      */ 
/*      */   public void setCrsPassengerNameRecord(String crsPassengerNameRecord)
/*      */   {
/*  486 */     this.crsPassengerNameRecord = crsPassengerNameRecord;
/*      */   }
/*      */ 
/*      */   public String getSkyPriorityFlag()
/*      */   {
/*  494 */     return this.skyPriorityFlag;
/*      */   }
/*      */ 
/*      */   public void setSkyPriorityFlag(String skyPriorityFlag)
/*      */   {
/*  502 */     this.skyPriorityFlag = skyPriorityFlag;
/*      */   }
/*      */ 
/*      */   public String getHostNumber()
/*      */   {
/*  510 */     return this.hostNumber;
/*      */   }
/*      */ 
/*      */   public void setHostNumber(String hostNumber)
/*      */   {
/*  518 */     this.hostNumber = hostNumber;
/*      */   }
/*      */ 
/*      */   public String getParentCabinType()
/*      */   {
/*  526 */     return this.parentCabinType;
/*      */   }
/*      */ 
/*      */   public void setParentCabinType(String parentCabinType)
/*      */   {
/*  534 */     this.parentCabinType = parentCabinType;
/*      */   }
/*      */ 
/*      */   public String getCabinType()
/*      */   {
/*  542 */     return this.cabinType;
/*      */   }
/*      */ 
/*      */   public void setCabinType(String cabinType)
/*      */   {
/*  550 */     this.cabinType = cabinType;
/*      */   }
/*      */ 
/*      */   public String getSnrFlag()
/*      */   {
/*  558 */     return this.snrFlag;
/*      */   }
/*      */ 
/*      */   public void setSnrFlag(String snrFlag)
/*      */   {
/*  566 */     this.snrFlag = snrFlag;
/*      */   }
/*      */ 
/*      */   public String getSeatNumber()
/*      */   {
/*  574 */     return this.seatNumber;
/*      */   }
/*      */ 
/*      */   public void setSeatNumber(String seatNumber)
/*      */   {
/*  582 */     this.seatNumber = seatNumber;
/*      */   }
/*      */ 
/*      */   public List<SeatChange> getSeatChanges()
/*      */   {
/*  590 */     return this.seatChanges;
/*      */   }
/*      */ 
/*      */   public void setSeatChanges(List<SeatChange> seatChanges)
/*      */   {
/*  598 */     this.seatChanges = seatChanges;
/*      */   }
/*      */ 
/*      */   public String getFfpAirlineCode()
/*      */   {
/*  606 */     return this.ffpAirlineCode;
/*      */   }
/*      */ 
/*      */   public void setFfpAirlineCode(String ffpAirlineCode)
/*      */   {
/*  614 */     this.ffpAirlineCode = ffpAirlineCode;
/*      */   }
/*      */ 
/*      */   public String getFfpCardNumber()
/*      */   {
/*  622 */     return this.ffpCardNumber;
/*      */   }
/*      */ 
/*      */   public void setFfpCardNumber(String ffpCardNumber)
/*      */   {
/*  630 */     this.ffpCardNumber = ffpCardNumber;
/*      */   }
/*      */ 
/*      */   public String getFfpCardPrior()
/*      */   {
/*  638 */     return this.ffpCardPrior;
/*      */   }
/*      */ 
/*      */   public void setFfpCardPrior(String ffpCardPrior)
/*      */   {
/*  646 */     this.ffpCardPrior = ffpCardPrior;
/*      */   }
/*      */ 
/*      */   public String getChd()
/*      */   {
/*  654 */     return this.chd;
/*      */   }
/*      */ 
/*      */   public void setChd(String chd)
/*      */   {
/*  662 */     this.chd = chd;
/*      */   }
/*      */ 
/*      */   public String getGender()
/*      */   {
/*  670 */     return this.gender;
/*      */   }
/*      */ 
/*      */   public void setGender(String gender)
/*      */   {
/*  678 */     this.gender = gender;
/*      */   }
/*      */ 
/*      */   public String getGroupCode()
/*      */   {
/*  686 */     return this.groupCode;
/*      */   }
/*      */ 
/*      */   public void setGroupCode(String groupCode)
/*      */   {
/*  694 */     this.groupCode = groupCode;
/*      */   }
/*      */ 
/*      */   public String getGroupNumber()
/*      */   {
/*  702 */     return this.groupNumber;
/*      */   }
/*      */ 
/*      */   public void setGroupNumber(String groupNumber)
/*      */   {
/*  710 */     this.groupNumber = groupNumber;
/*      */   }
/*      */ 
/*      */   public String getBoardingNumber()
/*      */   {
/*  718 */     return this.boardingNumber;
/*      */   }
/*      */ 
/*      */   public void setBoardingNumber(String boardingNumber)
/*      */   {
/*  726 */     this.boardingNumber = boardingNumber;
/*      */   }
/*      */ 
/*      */   public String getStandByIndicator()
/*      */   {
/*  735 */     return this.standByIndicator;
/*      */   }
/*      */ 
/*      */   public void setStandByIndicator(String standByIndicator)
/*      */   {
/*  743 */     this.standByIndicator = standByIndicator;
/*      */   }
/*      */ 
/*      */   public String getStandByNumber()
/*      */   {
/*  751 */     return this.standByNumber;
/*      */   }
/*      */ 
/*      */   public void setStandByNumber(String standByNumber)
/*      */   {
/*  759 */     this.standByNumber = standByNumber;
/*      */   }
/*      */ 
/*      */   public String getMarketingAirline()
/*      */   {
/*  767 */     return this.marketingAirline;
/*      */   }
/*      */ 
/*      */   public void setMarketingAirline(String marketingAirline)
/*      */   {
/*  775 */     this.marketingAirline = marketingAirline;
/*      */   }
/*      */ 
/*      */   public String getMarketingFlightNumber()
/*      */   {
/*  783 */     return this.marketingFlightNumber;
/*      */   }
/*      */ 
/*      */   public void setMarketingFlightNumber(String marketingFlightNumber)
/*      */   {
/*  791 */     this.marketingFlightNumber = marketingFlightNumber;
/*      */   }
/*      */ 
/*      */   public String getFareClass()
/*      */   {
/*  799 */     return this.fareClass;
/*      */   }
/*      */ 
/*      */   public void setFareClass(String fareClass)
/*      */   {
/*  807 */     this.fareClass = fareClass;
/*      */   }
/*      */ 
/*      */   public String getTicketNumber()
/*      */   {
/*  815 */     return this.ticketNumber;
/*      */   }
/*      */ 
/*      */   public void setTicketNumber(String ticketNumber)
/*      */   {
/*  823 */     this.ticketNumber = ticketNumber;
/*      */   }
/*      */ 
/*      */   public String getSequenceNumber()
/*      */   {
/*  831 */     return this.sequenceNumber;
/*      */   }
/*      */ 
/*      */   public void setSequenceNumber(String sequenceNumber)
/*      */   {
/*  839 */     this.sequenceNumber = sequenceNumber;
/*      */   }
/*      */ 
/*      */   public Extraseat getExtraSeat()
/*      */   {
/*  847 */     return this.extraSeat;
/*      */   }
/*      */ 
/*      */   public void setExtraSeat(Extraseat extraSeat)
/*      */   {
/*  855 */     this.extraSeat = extraSeat;
/*      */   }
/*      */ 
/*      */   public String getSpecialSvcInfo()
/*      */   {
/*  863 */     return this.specialSvcInfo;
/*      */   }
/*      */ 
/*      */   public void setSpecialSvcInfo(String specialSvcInfo)
/*      */   {
/*  871 */     this.specialSvcInfo = specialSvcInfo;
/*      */   }
/*      */ 
/*      */   public InfInfoBean getInfant()
/*      */   {
/*  879 */     return this.infant;
/*      */   }
/*      */ 
/*      */   public void setInfant(InfInfoBean infant)
/*      */   {
/*  887 */     this.infant = infant;
/*      */   }
/*      */ 
/*      */   public List<TextMsg> getTextMsgs()
/*      */   {
/*  895 */     return this.textMsgs;
/*      */   }
/*      */ 
/*      */   public void setTextMsgs(List<TextMsg> textMsgs)
/*      */   {
/*  903 */     this.textMsgs = textMsgs;
/*      */   }
/*      */ 
/*      */   public PsptInfo getPsptInfo()
/*      */   {
/*  911 */     return this.psptInfo;
/*      */   }
/*      */ 
/*      */   public void setPsptInfo(PsptInfo psptInfo)
/*      */   {
/*  919 */     this.psptInfo = psptInfo;
/*      */   }
/*      */ 
/*      */   public RecheckInfo getRecheckInfo()
/*      */   {
/*  927 */     return this.recheckInfo;
/*      */   }
/*      */ 
/*      */   public void setRecheckInfo(RecheckInfo recheckInfo)
/*      */   {
/*  935 */     this.recheckInfo = recheckInfo;
/*      */   }
/*      */ 
/*      */   public List<Bound> getBoundInfos()
/*      */   {
/*  943 */     return this.boundInfos;
/*      */   }
/*      */ 
/*      */   public void setBoundInfos(List<Bound> boundInfos)
/*      */   {
/*  951 */     this.boundInfos = boundInfos;
/*      */   }
/*      */ 
/*      */   public String getPassengerStatus()
/*      */   {
/*  959 */     return this.passengerStatus;
/*      */   }
/*      */ 
/*      */   public void setPassengerStatus(String passengerStatus)
/*      */   {
/*  967 */     this.passengerStatus = passengerStatus;
/*      */   }
/*      */ 
/*      */   public String getRemark()
/*      */   {
/*  975 */     return this.remark;
/*      */   }
/*      */ 
/*      */   public void setRemark(String remark)
/*      */   {
/*  983 */     this.remark = remark;
/*      */   }
/*      */ 
/*      */   public String getAecSeatNumber()
/*      */   {
/*  991 */     return this.aecSeatNumber;
/*      */   }
/*      */ 
/*      */   public void setAecSeatNumber(String aecSeatNumber)
/*      */   {
/*  999 */     this.aecSeatNumber = aecSeatNumber;
/*      */   }
/*      */ 
/*      */   public String getPassengerType()
/*      */   {
/* 1007 */     return this.passengerType;
/*      */   }
/*      */ 
/*      */   public void setPassengerType(String passengerType)
/*      */   {
/* 1015 */     this.passengerType = passengerType;
/*      */   }
/*      */ 
/*      */   public String getUresFlag()
/*      */   {
/* 1023 */     return this.uresFlag;
/*      */   }
/*      */ 
/*      */   public void setUresFlag(String uresFlag)
/*      */   {
/* 1031 */     this.uresFlag = uresFlag;
/*      */   }
/*      */ 
/*      */   public String getAqqFlag()
/*      */   {
/* 1039 */     return this.aqqFlag;
/*      */   }
/*      */ 
/*      */   public void setAqqFlag(String aqqFlag)
/*      */   {
/* 1047 */     this.aqqFlag = aqqFlag;
/*      */   }
/*      */ 
/*      */   public String getEstaFlag()
/*      */   {
/* 1055 */     return this.estaFlag;
/*      */   }
/*      */ 
/*      */   public void setEstaFlag(String estaFlag)
/*      */   {
/* 1063 */     this.estaFlag = estaFlag;
/*      */   }
/*      */ 
/*      */   public String getIsAsrSeat()
/*      */   {
/* 1071 */     return this.isAsrSeat;
/*      */   }
/*      */ 
/*      */   public void setIsAsrSeat(String isAsrSeat)
/*      */   {
/* 1079 */     this.isAsrSeat = isAsrSeat;
/*      */   }
/*      */ 
/*      */   public String getVipFlag()
/*      */   {
/* 1087 */     return this.vipFlag;
/*      */   }
/*      */ 
/*      */   public void setVipFlag(String vipFlag)
/*      */   {
/* 1095 */     this.vipFlag = vipFlag;
/*      */   }
/*      */ 
/*      */   public PrApiInfo getPrApiInfo()
/*      */   {
/* 1102 */     return this.prApiInfo;
/*      */   }
/*      */ 
/*      */   public void setPrApiInfo(PrApiInfo prApiInfo)
/*      */   {
/* 1109 */     this.prApiInfo = prApiInfo;
/*      */   }
/*      */ 
/*      */   public AppInfo getAppInfo()
/*      */   {
/* 1116 */     return this.appInfo;
/*      */   }
/*      */ 
/*      */   public void setAppInfo(AppInfo appInfo)
/*      */   {
/* 1123 */     this.appInfo = appInfo;
/*      */   }
/*      */ 
/*      */   public String getReservedSeatType()
/*      */   {
/* 1131 */     return this.reservedSeatType;
/*      */   }
/*      */ 
/*      */   public void setReservedSeatType(String reservedSeatType)
/*      */   {
/* 1139 */     this.reservedSeatType = reservedSeatType;
/*      */   }
/*      */ 
/*      */   public String getFba()
/*      */   {
/* 1147 */     return this.fba;
/*      */   }
/*      */ 
/*      */   public void setFba(String fba)
/*      */   {
/* 1155 */     this.fba = fba;
/*      */   }
/*      */ 
/*      */   public String getGfba()
/*      */   {
/* 1163 */     return this.gfba;
/*      */   }
/*      */ 
/*      */   public void setGfba(String gfba)
/*      */   {
/* 1170 */     this.gfba = gfba;
/*      */   }
/*      */ 
/*      */   public String getFamilyCount()
/*      */   {
/* 1178 */     return this.familyCount;
/*      */   }
/*      */ 
/*      */   public void setFamilyCount(String familyCount)
/*      */   {
/* 1185 */     this.familyCount = familyCount;
/*      */   }
/*      */ 
/*      */   public String getIsFamily()
/*      */   {
/* 1192 */     return this.isFamily;
/*      */   }
/*      */ 
/*      */   public void setIsFamily(String isFamily)
/*      */   {
/* 1199 */     this.isFamily = isFamily;
/*      */   }
/*      */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PrPassengerInfo
 * JD-Core Version:    0.6.0
 */