/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class HbPamOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6819501278491392746L;
/*    */   private List passengerInfoList;
/*    */ 
/*    */   public void setPassengerInfoList(List passengerInfoList)
/*    */   {
/* 22 */     this.passengerInfoList = passengerInfoList;
/*    */   }
/*    */ 
/*    */   public List getPassengerInfoList()
/*    */   {
/* 29 */     return this.passengerInfoList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.HbPamOutputBean
 * JD-Core Version:    0.6.0
 */