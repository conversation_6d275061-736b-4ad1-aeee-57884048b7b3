/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PsgActiveBagInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7538039461235378825L;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String depCity;
/*     */   private String arrCity;
/*     */   private String hostNumber;
/*     */   private String etNo;
/*     */   private String cabin;
/*     */   private String activeBagQuantity;
/*     */   private String activeBagWeight;
/*     */   private String activeBagWeightUnit;
/*     */   private String activeBagTag;
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  53 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  61 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/*  77 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  85 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public static long getSerialVersionUID()
/*     */   {
/*  97 */     return 7538039461235378825L;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 105 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 113 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getArrCity()
/*     */   {
/* 120 */     return this.arrCity;
/*     */   }
/*     */ 
/*     */   public void setArrCity(String arrCity)
/*     */   {
/* 128 */     this.arrCity = arrCity;
/*     */   }
/*     */ 
/*     */   public String getEtNo()
/*     */   {
/* 136 */     return this.etNo;
/*     */   }
/*     */ 
/*     */   public String getDepCity()
/*     */   {
/* 143 */     return this.depCity;
/*     */   }
/*     */ 
/*     */   public void setDepCity(String depCity)
/*     */   {
/* 151 */     this.depCity = depCity;
/*     */   }
/*     */ 
/*     */   public void setEtNo(String etNo)
/*     */   {
/* 159 */     this.etNo = etNo;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 167 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 174 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 182 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 190 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getActiveBagQuantity()
/*     */   {
/* 198 */     return this.activeBagQuantity;
/*     */   }
/*     */ 
/*     */   public void setActiveBagQuantity(String activeBagQuantity)
/*     */   {
/* 206 */     this.activeBagQuantity = activeBagQuantity;
/*     */   }
/*     */ 
/*     */   public String getActiveBagWeight()
/*     */   {
/* 214 */     return this.activeBagWeight;
/*     */   }
/*     */ 
/*     */   public void setActiveBagWeight(String activeBagWeight)
/*     */   {
/* 222 */     this.activeBagWeight = activeBagWeight;
/*     */   }
/*     */ 
/*     */   public String getActiveBagWeightUnit()
/*     */   {
/* 230 */     return this.activeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public void setActiveBagWeightUnit(String activeBagWeightUnit)
/*     */   {
/* 238 */     this.activeBagWeightUnit = activeBagWeightUnit;
/*     */   }
/*     */ 
/*     */   public String getActiveBagTag()
/*     */   {
/* 246 */     return this.activeBagTag;
/*     */   }
/*     */ 
/*     */   public void setActiveBagTag(String activeBagTag)
/*     */   {
/* 254 */     this.activeBagTag = activeBagTag;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PsgActiveBagInput
 * JD-Core Version:    0.6.0
 */