/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PreCkiBookOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 6180780093679287420L;
/*    */   private String resultCode;
/*    */   private String resultMsg;
/*    */ 
/*    */   public String getResultMsg()
/*    */   {
/* 19 */     return this.resultMsg;
/*    */   }
/*    */ 
/*    */   public void setResultMsg(String resultMsg)
/*    */   {
/* 26 */     this.resultMsg = resultMsg;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 33 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 40 */     this.resultCode = resultCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PreCkiBookOutput
 * JD-Core Version:    0.6.0
 */