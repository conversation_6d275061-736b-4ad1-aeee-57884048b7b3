/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Extraseat
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4274010224225594202L;
/*    */   private String seatNumber;
/*    */   private String extraType;
/*    */   private String extraWeight;
/*    */ 
/*    */   public String getSeatNumber()
/*    */   {
/* 34 */     return this.seatNumber;
/*    */   }
/*    */ 
/*    */   public void setSeatNumber(String seatNumber)
/*    */   {
/* 42 */     this.seatNumber = seatNumber;
/*    */   }
/*    */ 
/*    */   public String getExtraType()
/*    */   {
/* 50 */     return this.extraType;
/*    */   }
/*    */ 
/*    */   public void setExtraType(String extraType)
/*    */   {
/* 58 */     this.extraType = extraType;
/*    */   }
/*    */ 
/*    */   public String getExtraWeight()
/*    */   {
/* 66 */     return this.extraWeight;
/*    */   }
/*    */ 
/*    */   public void setExtraWeight(String extraWeight)
/*    */   {
/* 74 */     this.extraWeight = extraWeight;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.Extraseat
 * JD-Core Version:    0.6.0
 */