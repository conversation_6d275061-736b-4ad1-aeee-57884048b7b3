package com.hna.shopping.ibe.controller.rest;

import com.alibaba.fastjson.JSON;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import com.hna.shopping.ibe.interfaces.dto.TicketAndPnrClearRequest;
import com.hna.shopping.ibe.manager.TicketAndPnrClearManager;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@Api("票务编码清位操作接口")
@RestController
@RequestMapping("/api/ticketAndPnr")
@Slf4j
public class TicketAndPnrClearController {

    @Autowired
    private TicketAndPnrClearManager ticketAndPnrClearManager;

    /**
     * 票务处理。退票+清位
     * @param request
     * @return
     */
    @RequestMapping(value = "clear", method = RequestMethod.POST)
    RestResponse<Boolean> ticketAndPnrClear(@RequestBody TicketAndPnrClearRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , ticketAndPnrClear: {}", uuid, JSON.toJSONString(request));
        Boolean response = ticketAndPnrClearManager.ticketAndPnrClear(request);
        log.info("End uuid: {} , ticketAndPnrClear result : {}", uuid, response);
        return RestResponse.ok(response);
    }


    /**
     * 票务处理。退票+清位(中文国际)
     * @param request
     * @return
     */
    @RequestMapping(value = "clearInter", method = RequestMethod.POST)
    RestResponse<Boolean> ticketAndPnrClearInter(@RequestBody TicketAndPnrClearRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , ticketAndPnrClearInter: {}", uuid, JSON.toJSONString(request));
        Boolean response = ticketAndPnrClearManager.ticketAndPnrClearInter(request);
        log.info("End uuid: {} , ticketAndPnrClearInter result : {}", uuid, response);
        return RestResponse.ok(response);
    }


    /**
     * 票务处理。退票+清位(海外站)
     * @param request
     * @return
     */
    @RequestMapping(value = "clearHWZ", method = RequestMethod.POST)
    RestResponse<Boolean> ticketAndPnrClearHWZ(@RequestBody TicketAndPnrClearRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("Start uuid: {} , ticketAndPnrClearInter: {}", uuid, JSON.toJSONString(request));
        Boolean response = ticketAndPnrClearManager.ticketAndPnrClearHWZ(request);
        log.info("End uuid: {} , ticketAndPnrClearInter result : {}", uuid, response);
        return RestResponse.ok(response);
    }



}
