/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatMapOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5195714735244141664L;
/*  20 */   private String flightLayout = "";
/*     */ 
/*  25 */   private String airplaneType = "";
/*     */ 
/*  30 */   private String airplaneClass = "";
/*     */ 
/*  35 */   private String flightPlan = "";
/*     */ 
/*     */   public String getFlightLayout()
/*     */   {
/*  44 */     return this.flightLayout;
/*     */   }
/*     */ 
/*     */   public void setFlightLayout(String i)
/*     */   {
/*  51 */     this.flightLayout = i;
/*     */   }
/*     */ 
/*     */   public String getAirplaneType()
/*     */   {
/*  58 */     return this.airplaneType;
/*     */   }
/*     */ 
/*     */   public void setAirplaneType(String i)
/*     */   {
/*  65 */     this.airplaneType = i;
/*     */   }
/*     */ 
/*     */   public String getAirplaneClass()
/*     */   {
/*  72 */     return this.airplaneClass;
/*     */   }
/*     */ 
/*     */   public void setAirplaneClass(String i)
/*     */   {
/*  79 */     this.airplaneClass = i;
/*     */   }
/*     */ 
/*     */   public String getFlightPlan()
/*     */   {
/*  86 */     return this.flightPlan;
/*     */   }
/*     */ 
/*     */   public void setFlightPlan(String i)
/*     */   {
/*  93 */     this.flightPlan = i;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/* 101 */     this.flightLayout = "";
/* 102 */     this.airplaneType = "";
/* 103 */     this.airplaneClass = "";
/* 104 */     this.flightPlan = "";
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.SeatMapOutputBean
 * JD-Core Version:    0.6.0
 */