package com.travelsky.ibe.client.pnr;

public class BookPassengerEx extends BookPassenger {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6212506322360127053L;

	public BookPassengerEx() {
		super();
		// TODO Auto-generated constructor stub
	}

	public BookPassengerEx(String name, int type, int age) throws Exception {
		super(name, type, age);
		// TODO Auto-generated constructor stub
	}

	public BookPassengerEx(String name) throws Exception {
		super(name);
		// TODO Auto-generated constructor stub
	}
	
	/*     */ public  int getAge() {
		/* 240 */     return super.getAge();//.age;
		/*     */   }
	
	/*     */  public String getName() {
		/* 244 */     return super.getName();//.name;
		/*     */   }
		/*     */ 
		/*     */  public int getPassengerType()
		/*     */   {
		/* 249 */     return super.getPassengerType();//.passtype;
		/*     */   }
		/*     */ 
		/*     */ public  int getType() {
		/* 253 */     return super.getType();//.type;
		/*     */   }	

}
