package com.hna.shopping.ibe.manager.ibe;

import com.hna.shopping.ibe.common.util.DateUtil;
import com.hna.shopping.ibe.ManagerApplication;
import com.hna.shopping.ibe.interfaces.dto.*;
import com.hna.shopping.ibe.interfaces.dto.PNRFC;
import com.hna.shopping.ibe.manager.PNRManager;
import com.travelsky.ibe.client.*;
import com.travelsky.ibe.client.pnr.*;
import com.travelsky.ibe.client.pnr.DETR;
import com.travelsky.ibe.client.pnr.DETRHistoryInfoItem;
import com.travelsky.ibe.client.pnr.DETRHistoryResult;
import com.travelsky.ibe.client.pnr.PNRContact;
import com.travelsky.ibe.client.pnr.PNRFN;
import com.travelsky.ibe.client.pnr.PNRFP;
import com.travelsky.ibe.client.pnr.PNRInfant;
import com.travelsky.ibe.client.pnr.PNROSI;
import com.travelsky.ibe.client.pnr.PNRPassenger;
import com.travelsky.ibe.client.pnr.PNRRMK;
import com.travelsky.ibe.client.pnr.PNRResp;
import com.travelsky.ibe.client.pnr.PNRSSR;
import com.travelsky.ibe.client.pnr.PNRSSR_FOID;
import com.travelsky.ibe.client.pnr.PNRTkt;
import com.travelsky.ibe.client.pnr.PNRTktNo;
import com.travelsky.ibe.client.pnr.RTResult;
import com.travelsky.ibe.client.pnr.SSResult;
import com.travelsky.ibe.client.pnr.SellSeat;
import com.travelsky.ibe.exceptions.IBEException;
import com.travelsky.util.QDateTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.DateUtils;
import org.apache.http.util.Args;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.Assert;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

import com.travelsky.ibe.client.*;
import com.travelsky.ibe.client.pnr.*;

/**
 * DOC
 *
 * <AUTHOR>
 */
//@RunWith(SpringRunner.class)
@ActiveProfiles("sit")
//@SpringBootTest(classes = ManagerApplication.class)
@Slf4j
public class IbeUtilTest {

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final String TIME_FORMAT = "HH:mm:ss";

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.S";

    public static final String TIME_FORMAT_HM = "HH:mm";

    public static final String DATETIMEFORMAT = "yyyyMMdd HH:mm:ss";

    public static final String DATEFORMAT = "yyyyMMdd";

//    @Test
    public void av1() throws Exception {
//        List<SegmentDTO> segmentDTOS = IbeUtil.av("jd", "SYX", "SJW", DateUtil.toDateYMD("20200413"));
//        List<SegmentDTO> segmentDTOS = IbeUtil.av("fu", "HRB", "SYX", DateUtil.toDateYMD("20171010"));
//        List<SegmentDTO> segmentDTOS = IbeUtil.av("hu", "HAK", "PEK", DateUtil.toDateYMD("20170923"), false, true);
//        log.info("segmentDTOS: {}", segmentDTOS);


    }

    /**
     * ibe.client.enablelog=true
     jd.server.ip=***********
     jd.server.backupIp=***********
     jd.server.port=6891
     jd.client.app=jdibe
     jd.client.office=xiy210
     jd.client.customno=0
     jd.client.validationno=64
     *
     * @param ibe
     */

    public static void connectHost(IBEClient ibe){
        String office = "xiy210";
//        office = "xiy201";
        String customno = "0";
        String validationno = "64";
        String appname = "jdibe";
        int port = 6895;
        String ip = "***********";
//        ip = "***********";
		ibe.setConnectionInfo(ip, port);

		ibe.setAgentInfo(office, customno, validationno);
		ibe.setAppName(appname);
		ibe.setSoTimeOut(10000);
		System.out.println(ibe.testConnect());

    }

    public static void connectHostHigh(IBEClient ibe){

        String office = "xiy201";
        String customno = "0";
        String validationno = "64";
        String appname = "jdair";
        int port = 6891;
        String ip = "***********";//**************
//        ip = "***********";
        ibe.setConnectionInfo(ip, port);

        ibe.setAgentInfo(office, customno, validationno);
        ibe.setAppName(appname);
        ibe.setSoTimeOut(10000);
        System.out.println(ibe.testConnect());

    }
    /**
     * 航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询
     */


    /**
     * AV（实现舱位可利用情况查询）
     * @return
     */
//    @Test
    public static void av(){// 查询航班
        //生成舱位查询对象
        AV avExample = new AV();// AV指令接口
        //连接主机，AV实现了IBEClient接口，所以可以使用写的静态方法完成主机配置
        connectHost(avExample);
        //处理查询指令参数,需注意多数方法要求的时间参数格式不同
        Date date=null;
        try {
            date = new SimpleDateFormat(DATETIMEFORMAT).parse("20220111 00:00:00"); //查询AV日期格式(yyyyMMdd HH:mm:ss)
        } catch (java.text.ParseException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        //查询航班
        AvResult result = null;
        try {
            //查询北京到上海国航航班舱位可利用情况
            result = avExample.getAvailability("PKX", "SYX", date, "JD",true,true);
            System.out.println("TxnTraceKey:" + avExample.getTxnTraceKey());
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(result);

        if (result != null) {
            for (int i = 0; i < result.getItemamount(); i++) {
                AvItem ai = result.getItem(i);
                for (int j = 0; j < ai.getSegmentnumber(); j++) {
                    AvSegment as = ai.getSegment(j);
                    for (int k = 0; k < 26; k++) {
                        char cangwei = as.getCangweiCodeSort(k);
                        //logger.debug("Cabin name :"+cangwei);
                        if (cangwei == '-') {
                            break;
                        }
                        char cabinInfo = as.getCangweiinfoOfSort(as.getCangweiCodeSort(k));
                        String cabin = String.valueOf(cangwei);
                        String info = String.valueOf(cabinInfo);
//                        if ("E".equals(info)) {
//                            continue;
//                        }
                        log.info(cabin + info);
                    }
                }
            }
        }
    }

    /**
     * SK航班时刻显示查询
     * @return
     */
    public static SkResult sk(){
        //生成航班时刻查询对象
        SK skExample = new SK();// AV指令接口
        //连接主机，AV实现了IBEClient接口，所以可以使用写的静态方法完成主机配置
        connectHost(skExample);
        //处理查询指令参数,需注意多数方法要求的时间参数格式不同
        Date date=null;
        try {
            date = new SimpleDateFormat(DATETIMEFORMAT).parse("20090820 00:00:00"); //查询AV日期格式(yyyyMMdd HH:mm:ss)
        } catch (java.text.ParseException e1) {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        //查询时刻
        SkResult sr = null;
        try {
            sr = skExample.getSchedule("PEK","SHA","20140121 00:00:00","JD",false,false);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(sr);
        //返回查询结果
        return sr;
    }


    /**
     * 实现显示航班经停点及起降时间的查询
     * @return
     */
    public static FFResult ff(){
        //生成查询对象
        FF ffExample = new FF();
        connectHost(ffExample);
        FFResult ffres=null;
        try {
            ffres = ffExample.flightTime("CA929", "21JAN14");//参数航班号，起飞日期（DDMONYY或DDMON）
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        System.out.println(ffres);
        return ffres;
    }

    /**
     * 实现显示航班飞行时间餐食等信息
     * @return
     */
    public static DsgResult dsg(){
        DSG dsgExample = new DSG();
        connectHost(dsgExample);
        DsgResult dsgres = null;
        try {
            //参数（航班号、舱位、日期,可以接受的格式为"DDMMM","DDMMMYY",或者"+"/"-"/"."分别代表明天/昨天/今天,或者为空(null)、航段城市对 可以为null)
            dsgres = dsgExample.displaySegment("CA929",'y',"21JAN14",null);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        System.out.println(dsgres);
        return dsgres;
    }
    /**
     * 实现航班运价查询
     * @return
     */
//    @Test
    public static void fd(){
        FD fdExample = new FD();
        connectHost(fdExample);
//		findPrice参数
//	    1.org - java.lang.String 始发地三字代码 [EN] Departure CityCode,A three-character code representing a departure city, e.g., PEK
//	    2.dst - java.lang.String 到达地三字代码 [EN] Arrival CityCode,A three-character code representing an arrival city, e.g., SHA
//	    3.date - java.lang.String 查询日期，格式为"DDMMMYY"，如"20NOV02", [EN] Specifies date,format:"DDDMMYY", e.g., 20NOV02.
//	    4.airline - java.lang.String 航空公司两字代码 可以设置为"all"表示为查询所有国内航空公司在此航线上的公布运价。 [EN] Airline Designator Code,A two-character code representing an Airline,e.g., CA "ALL" String indicate all airline.
//	    5.planeModel - java.lang.String 机型(未找到机型的以干线机型处理) 此参数为null或者""时表示不查询机场建设费 [EN] Airline Designator Code,A two-character code representing an Airline,e.g., CA
//	    6.passType - java.lang.String 旅客类型(""或null或者"ad"：成人；"IN"：婴儿；"CH"：儿童) 此参数的取值只影响机场建设费及燃油附加的数值，运价仍然为对应舱位的成人运价。
//	    7.fullFareBasis - boolean 应当设置为真。
        FDResult fdres = null;
        try {
            fdres = fdExample.findPrice("SJW", "HRB", "15JUN22", "JD", "320", "AD", false);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        System.out.println(fdres);

        //成人机场建设费
        System.out.println("成人机场建设费："+fdres.getAirportTax(0));
        //成人燃油附加费
        System.out.println("成人燃油附加费："+fdres.getFuelTax(0));
//        return fdres;
    }

    /**
     * 实现旅客姓名提取
     * @return
     */
    public static MultiResult ml(){
        ML mlExample = new ML();
        connectHost(mlExample);
//		multi参数：
//	    1. criteriacode - java.lang.String 指令参数:选项--可以为空,此时取默认'C',某些选项可以组合,如"GB",常用参数如下
//	     B 提取订妥座位的旅客(HK 和RR 的PNR)
//	     C 提取所有旅客记录
//	     R 提取RR旅客的记录
//	     T 提取已出票旅客
//	     X 提取取消的旅客
//	     G 提取团体旅客记录
//	     U 提取未证实的旅客(HL,US,UU,HN)
//	      N 组合选项,附着于其他选项前表示反义,如"NG"表示提取非团体(散客)定座记录
//	         详细技术细则请参见中国航信主页或向中国航信咨询.
//	    2.pnrOnce - boolean 指令参数:简略模式,为true表示每一个PNRNO只显示一次--默认为false
//	    3.flightNo - java.lang.String 指令参数:航班号--必须有值
//	    4.fltclass - char 指令参数: 提取舱位选择--默认为' ',即提取所有舱位
//	    5.date - java.lang.String 指令参数:查询航班的执行日期--可以为空,此时取默认为执行查询当日,格式"DDMMM","DDMMMYY"
//	    6.city - java.lang.String 指令参数:起飞城市或起飞到达城市对--可以为空,此时在该航班无经停情况下查询结果为本office在该航班上的订座记录
//	     如果有经停则是本office在该航班上订取得始发地至第一经停地的的订座记录
//	     如果只有始发城市则为本office在该航班该城市至下一到达地航段的订座记录
//	     如果起飞到达城市均已给出则为本office在该航班中对应航段的订座记录
//	     如果给出城市/城市对并非在该航班的航线中,则得到的结果中没有定座记录.
        MultiResult multi = null;
        try {
            multi = mlExample.multi("B",false,"JD5507",' ',"24SEP",null);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        System.out.println(multi);
        return multi;
    }


    /**
     * 旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录
     */


    /**
     * 成人单人单航段预定
     */
    public static void sellseat1(){
        SellSeat sellExample = new SellSeat();
        connectHostHigh(sellExample);

        try {
            //添加旅客姓名
            String name = "CESHI/LAIYUAN";
            sellExample.addAdult(name);

            //添加旅客航段信息
            String airNo = "JD5211";  //航班号
            char fltClass = 'X';     //舱位等级
            String orgCity = "PEK";   //始发城市
            String desCity = "ERL";  //到达城市
            String actionCode = "NN";    //行动代码
            int tktNum = 1;             //订座数
            String departureTime = "2019-01-12";  //起飞时间

            sellExample.addAirSeg(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);


            //添加旅客身份证信息
            String airline = "JD";   //航空公司两字代码
            String idtype = "PP";    //身份证件类型 NI身份证，CC信用卡，PP护照
            String id = "123412345";  //对应的身份证件号码

//            sellExample.addSSR_FQTV(airline,idtype,  name, id);
            sellExample.addSSR_FOID(airline,idtype, id, name);

            String contactinfo = "13520923427";
            //添加旅客联系组信息
            sellExample.addContact(contactinfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
            sellExample.addOSI("JD", "CTCT"+ contactinfo);
            sellExample.addOSI(new BookOSI("JD","BY PGS SYSTEM"));

            //添加旅客出票时限
            String dateLimit = "2018-11-22 23:50:00";
            sellExample.setTimelimit(dateLimit);


            BookInfomation bi = new BookInfomation();
            PNRFC infantFC=new PNRFC();
            infantFC.setInfant(true);
            bi.addFC(infantFC.toString());

            //完成PNR必须信息输入递交主机，生成PNR
            SSResult  ssr = sellExample.commit1();

            //PNR结果

            System.out.println(ssr.getPnrno());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    /**
     * 成人往返单人航段预定
     */
    public static void sellseatRT(){
        SellSeat sellExample = new SellSeat();
        connectHost(sellExample);

        try {
            //添加旅客姓名
            String name = "TESTCD/ADULT";
            sellExample.addAdult(name);

            //添加旅客航段信息
            String airNo = "SC4651";  //航班号
            char fltClass = 'Y';     //舱位等级
            String orgCity = "TAO";   //始发城市
            String desCity = "PEK";  //到达城市
            String actionCode = "NN";    //行动代码
            int tktNum = 1;             //订座数
            String departureTime = "2014-01-25";  //起飞时间

            sellExample.addAirSeg(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);

            //添加旅客乘车返程航段信息
            String airNo2 = "SC4651";  //航班号
            char fltClass2 = 'Y';     //舱位等级
            String orgCity2 = "PEK";   //始发城市
            String desCity2 = "TAO";  //到达城市
            String actionCode2 = "NN";    //行动代码
            int tktNum2 = 1;             //订座数
            String departureTime2 = "2014-01-29";  //起飞时间

            sellExample.addAirSeg(airNo2, fltClass2, orgCity2, desCity2, actionCode2, tktNum2, departureTime2);




            //添加旅客身份证信息
            String airline = "SC";   //航空公司两字代码
            String idtype = "NI";    //身份证件类型 NI身份证，CC信用卡，PP护照
            String id = "568427951";  //对应的身份证件号码

            sellExample.addSSR_FQTV(airline,idtype, id, name);

            String contactinfo = "13520923427";
            //添加旅客联系组信息
            sellExample.addContact(contactinfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
            sellExample.addOSI("JD", "CTCT"+ contactinfo);
            sellExample.addOSI(new BookOSI("JD","BY PGS SYSTEM"));


            //添加旅客出票时限
            String dateLimit = "2014-01-26 12:00:00";
            sellExample.setTimelimit(dateLimit);


            //完成PNR必须信息输入递交主机，生成PNR

            SSResult  ssr = sellExample.commit1();

            //PNR结果

            System.out.println(ssr.getPnrno());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }
    /**
     * 多人往返
     */
    public static void sellseatRT2(){
        SellSeat sellExample = new SellSeat();
        connectHost(sellExample);

        try {
            //添加旅客姓名
            String name = "LAIYUAN/TEST";
            sellExample.addAdult(name);
            String name2 = "IBETESTER/IBEGROUP";
            sellExample.addAdult(name2);
            String id = "120101115";  //对应的身份证件号码
            String id2 = "120101116";  //对应的身份证件号码

            //添加旅客航段信息
            String airNo = "JD";  //航班号
            char fltClass = 'Y';     //舱位等级
            String orgCity = "PEK";   //始发城市
            String desCity = "SHA";  //到达城市
            String actionCode = "NN";    //行动代码
            int tktNum = 1;             //订座数
            String departureTime = "2014-01-25";  //起飞时间

            sellExample.addAirSeg(airNo, fltClass, orgCity, desCity, actionCode, tktNum, departureTime);


            //信息航段的添加
            BookAirSeg airseg = new BookAirSeg("SHA","HGH");
            sellExample.addAirSeg(airseg);


            //添加旅客乘坐返程航段信息
            String airNo2 = "CA1703";  //航班号
            char fltClass2 = 'Y';     //舱位等级
            String orgCity2 = "HGH";   //始发城市
            String desCity2 = "PEK";  //到达城市
            String actionCode2 = "NN";    //行动代码
            int tktNum2 = 1;             //订座数
            String departureTime2 = "2014-01-28";  //起飞时间

            sellExample.addAirSeg(airNo2, fltClass2, orgCity2, desCity2, actionCode2, tktNum2, departureTime2);




            //添加旅客身份证信息
            String airline = "JD";   //航空公司两字代码
            String idtype = "NI";    //身份证件类型 NI身份证，CC信用卡，PP护照


            sellExample.addSSR_FOID(airline,idtype, id, name);
            sellExample.addSSR_FOID(airline,idtype, id2, name2);


            String contactinfo = "13520923427";
            //添加旅客联系组信息
            sellExample.addContact(contactinfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
            sellExample.addOSI("JD", "CTCT"+ contactinfo);
            sellExample.addOSI(new BookOSI("JD","BY PGS SYSTEM"));


            //添加旅客出票时限
            String dateLimit = "2014-01-27 12:00:00";
            sellExample.setTimelimit(dateLimit);


            sellExample.addFC("CNY500");
            sellExample.addFN(new BookFN());

            //完成PNR必须信息输入递交主机，生成PNR

            SSResult  ssr = sellExample.commit1();

            //PNR结果

            System.out.println(ssr.getPnrno());

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }



    /**
     * ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取
     */

    /**
     * 获取RTResult公共方法
     * @param prnno
     * @return
     */
    public static RTResult rtResult(String prnno){
        RT rtExample = new RT();
        connectHost(rtExample);
        RTResult rtres = null;
        try {
            rtres = rtExample.retrieve(prnno);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return rtres;
    }


    /**
     * 实现PNR信息提取
     */
//    @Test
    public void rt(){
        RTResult rre = rtResult("MDCRMP");
        System.out.println(rre.getTktnos().size()==0);


        Vector<PNRPassenger> paxs = rre.getPassengers();
        String paxName = "ZHANG/AIZHEN";
        int index =  0;
        for (PNRPassenger p : paxs) {
            if(paxName.equals(p.getName())) {
                index = p.getIndex();
                break;
            }
        }

        Vector v = rre.getSsrs();


        System.out.println("------------");

//        for (Object o:v) {
//            if (o.getClass().equals(PNRSSR.class)) {
//                PNRSSR ssr =  (PNRSSR)o;
//                ssr.getCitypair();//LJGPVG
//                String textInfo = ssr.getText();// C/0AA/PDBG/10KG CNY100 A/A/8984553013181C1
//                if ("ASVC".equals(ssr.getSSRType()) && ssr.getPsgrID().equals("P"+index) && (textInfo.indexOf("SEAT") > -1 || textInfo.indexOf("PDBG") > -1)) {
//                    if (textInfo.indexOf(" BT") > -1 ) {
//                        continue;
//                    }
//                    String[] array = textInfo.split("/");
//                    String emdNo = array[array.length-1];
//                    if (emdNo.length() > 2) {
//                        emdNo = emdNo.substring(0,emdNo.length() -2);
//                    }
//                    if ( Pattern.matches("[0-9]+", emdNo)) {
//                        System.out.println(emdNo);
//                    } else {
//                        System.err.println(emdNo);
//                    }
//                }
//                if ("INFT".equals(ssr.getSSRType())) {
//                    if("KK".equals(ssr.getActionCode())) {
//                        return;
//                    } else  if("UC".equals(ssr.getActionCode())) {
//                        return;
//                    } else  if("NN".equals(ssr.getActionCode())) {
//                        return;
//                    }
//                }
//            }
//        }
    }
    /**
     * 提取旅客组信息
     */
    public static void rt1(){
        RTResult rtres = rtResult("HMND9P");


        System.out.println(rtres);
        //显示旅客信息
        System.out.println("显示成人和儿童旅客：");
        for(int i=0;i>rtres.getPassengersCount();i++){
            PNRPassenger pnrPass = rtres.getPassengerAt(i);
            System.out.print("PNR序号："+pnrPass.getIndex());
            System.out.print("旅客姓名："+pnrPass.getName());
            System.out.print("年龄："+pnrPass.getAge());
            System.out.print("PNR中姓名："+pnrPass.getNameInPnr());
            System.out.println();
        }
        System.out.println();

        System.out.println("显示婴儿旅客：");

        for(int i=0;i>rtres.getInfantsCount();i++){
            PNRInfant pnrinfant = rtres.getInfantAt(i);
            System.out.print("PNR序号："+pnrinfant.getIndex());
            System.out.print("旅客姓名："+pnrinfant.getName());
            System.out.print("同机携带者："+pnrinfant.getCarrier());
            System.out.print("PNR中姓名："+pnrinfant.getNameInPnr());
            System.out.print("生日："+pnrinfant.getBirthString());
            System.out.println();

        }
        System.out.println();


    }
    /**
     * 提取航段组信息
     */
    public static void rt2(){
        RTResult rtres = rtResult("HMND9P");

        System.out.println(rtres);

        //显示航段信息
        System.out.println("显示航段组信息：");
        for(int i=0;i>rtres.getAirSegsCount();i++){
            PNRAirSeg pnrAirseg = rtres.getAirSegAt(i);
            System.out.print("PNR序号："+pnrAirseg.getIndex());
            System.out.print("航班号："+pnrAirseg.getAirNo());
            System.out.print("出发城市："+pnrAirseg.getOrgCity());
            System.out.print("到达城市："+pnrAirseg.getDesCity());
            System.out.print("出发时间："+pnrAirseg.getDepartureTimeString());
            System.out.print("到达时间:"+pnrAirseg.getArrivalTimeString());
            System.out.print("航班舱位："+pnrAirseg.getFltClass());
            System.out.println();
        }
        System.out.println();

    }
    /**
     * 提取联系组信息
     */
    public static void rt3(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示联系组信息
        System.out.println("联系组信息：");
        for(int i=0;i>rtres.getContactsCount();i++){
            PNRContact pnrCon = rtres.getContactAt(i);
            System.out.print("PNR序号："+pnrCon.getIndex());
        }

    }

    /**
     * 提取出票组信息
     */
    public static void rt4(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示出票组信息
        System.out.println("出票组信息：");
        for(int i=0;i>rtres.getTktsCount();i++){
            PNRTkt pnrTkt =  rtres.getTktAt(i);
            System.out.println("PNR序号："+pnrTkt.getIndex());
            System.out.println("出票的Office："+pnrTkt.getOffice());
        }
    }

    /**
     * 提取特殊服务组信息
     */
    public static void rt5(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示特殊服务组信息
        System.out.println("显示特殊服务组信息：");
        for(int i=0;i>rtres.getSSRsCount();i++){
            PNRSSR pnrssr = rtres.getSSRAt(i);
            System.out.println("PNR序号："+pnrssr.getIndex());
            System.out.println("服务类型："+pnrssr.getSSRType());
            System.out.println("服务信息："+pnrssr.getServeInfo());
            System.out.println("服务标志："+pnrssr.getPsgrID());
        }
    }
    /**
     * 提取OSI
     */
    public static void rt6(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示OSI信息
        System.out.println("显示OSI信息：");
        for(int i=0;i>rtres.getOSIsCount();i++){
            PNROSI pnrOsi = rtres.getOSIAt(i);
            System.out.println("PNR序号："+pnrOsi.getIndex());
            System.out.println("OSI信息："+pnrOsi.getOsi());
            System.out.println("需要OSI旅客编号："+pnrOsi.getPNum());
        }
    }
    /**
     * 提取运价信息
     */
    public static void rt7(){

        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示运价信息
        System.out.println("显示运价信息:");
        for(int i=0;i>rtres.getFNsCount();i++){
            PNRFN pnrFn = rtres.getFNAt(i);
            System.out.println("PNR序号："+pnrFn.getIndex());
            System.out.println("FN信息： "+pnrFn.getFn());
            System.out.println("FCNY:"+pnrFn.getAmount(BookFN.F));
            System.out.println("货币单位："+pnrFn.getCurrency(BookFN.A));
            System.out.println("SCNY:"+pnrFn.getAmount(BookFN.S));
            System.out.println("货币单位："+pnrFn.getCurrency(BookFN.S));
            System.out.println("XCNY:"+pnrFn.getAmount(BookFN.X));
            System.out.println("货币单位："+pnrFn.getCurrency(BookFN.X));
            for(int j=0;j>pnrFn.getTaxCnt();j++){
                System.out.println("第"+(j+1)+"项税："+pnrFn.getTaxAmount(BookFN.T, j));
                System.out.println("货币单位："+pnrFn.getTaxCurrency(BookFN.T, j));
            }
            System.out.println();

        }
        System.out.println("显示运价信息FP:");
        for(int i=0;i>rtres.getFNsCount();i++){
            PNRFP pnrFP = rtres.getFpAt(i);
            System.out.println("PNR序号："+pnrFP.getIndex());
            System.out.println("FP代码："+pnrFP.getPaytype());
            System.out.println("货币单位："+pnrFP.getCurrency());
            System.out.println("FP注释："+pnrFP.getRemark());
        }

    }
    /**
     * 提取REMARK
     */
    public static void rt8(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        //显示REMARK信息
        for(int i=0;i>rtres.getRMKsCount();i++){
            PNRRMK pnrRmk = rtres.getRMKAt(i);
            System.out.println("PNR序号："+pnrRmk.getIndex());
            System.out.println("REMARK内容："+pnrRmk.getRmkinfo());
        }

    }
    /**
     * 提取票号组
     */
    public static void rt9(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);
        for(int i=0;i>rtres.getTktnosCount();i++){
            PNRTktNo pnrTkno = rtres.getTktnoAt(i);
            System.out.println("PNR序号："+pnrTkno.getIndex());
            System.out.println("票号："+pnrTkno.getTktNo());
            System.out.println("票号对应的旅客序号："+pnrTkno.getPsgrID());
        }
    }
    /**
     * 提取责任组
     */
    public static void rt10(){
        RTResult rtres = rtResult("HMND9P");
        System.out.println(rtres);

        PNRResp pnrResp = rtres.getResp();
        System.out.println("PNR序号："+pnrResp.getIndex());
        System.out.println("PNR责任组："+pnrResp.getOfficecode());
    }

    /**
     * 提取责任组
     */
    public static void rtHis(){

        RT rtExample = new RT();
        connectHost(rtExample);
        RTResult rtres = null;
        try{
            rtres = rtExample.retrieveCompletely("MG5PKM");
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(rtres);

        String operateHis = rtres.getOringinalRT();
        String[] list = operateHis.split("\r\n");
        Map<String,String> map = new HashMap<String,String>();
        for (String one: list) {
            one = one.replaceAll("\\s+"," ");
            if ((one.startsWith("001") || one.startsWith("002")) && (one.split(" ").length == 5 || one.split(" ").length == 6)) {
//                log.info(one+":"+one.split(" ").length);
                String[] details = one.split(" ");
                if (one.indexOf(" IK ") > 0) { //找到操作配置
                    String bookTime = map.get(details[1]+details[2]);
                    if (bookTime != null) {
                        DateUtils.parseDate(bookTime,new String[]{"HHmmddMMM"});
                        break;
                    }
                }
                map.put(details[1]+details[2],details[3]+details[4]);//key是配置 value是时间
            }
        }
    }

    /**
     * PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改
     */



    /**
     * 删除订座记录
     */
    public static String deletePnr(){
        String pnrNo = "HMND9P";
        PnrManage manager = new PnrManage();
        connectHost(manager);
        String res = null;
        try {
            res = manager.deletePnr(pnrNo);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        System.out.println(res);
        return res;
    }

    /**
     * 删除指定编号组
     * @return
     */
    public static String deleteItem(){
        String pnrNo = "HMND9P";
        PnrManage manager = new PnrManage();
        connectHost(manager);
        String res = null;
        try {
            res = manager.deleteItem(pnrNo, new int[7]);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        System.out.println(res);
        return res;
    }

    /**
     * 为PNR添加新组
     * @return
     */
    public static String addPnrInfo(){
        String pnrNo = "HMND9P";
        BookInfomation bookinfo = new BookInfomation();

        PnrManage manager = new PnrManage();
        connectHost(manager);
        String res = null;
        try {
            bookinfo.addContact("Hello");
            res = manager.addPnrInfo(pnrNo, bookinfo);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        System.out.println(res);
        return res;
    }

    /**
     * 航班改期
     */
    public static void changAirSeg(){
        SellSeat ss = new SellSeat();
        connectHost(ss);
        try {
            ss.addPassenger(new BookPassenger("travelsky/test"));
            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            ss.addContact("NC");
            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
            String pnr = ss.commit();

            //改期操作

            PnrManage manager = new PnrManage();
            BookAirSeg oldseg = new  BookAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            BookAirSeg newseg = new  BookAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140126", "yyyymmdd"));
            manager.changeAirSeg(pnr, oldseg, newseg);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    /**
     * 修改出票组
     */
    public static void updatePnr(){
        SellSeat ss = new SellSeat();
        connectHost(ss);
        try {
            ss.addPassenger(new BookPassenger("travelsky/test"));
            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            ss.addContact("NC");
            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
            String pnr = ss.commit();

            //修改出票时限操作

            PnrManage manager = new PnrManage();
            BookInfomation bookinfo = new BookInfomation();
            bookinfo.addTktstatus(new BookTktStatus(QDateTime.stringToDate("20140125", "yyyymmdd")));
            manager.changeTkt(pnr, bookinfo);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    /**
     * 修改出票组
     */
//    @Test
    public void noPnr(){
        RT rt = new RT();
        connectHost(rt);

        String pnr = "PCKHB0";
        try {
            RTResult rtResult = rt.retrieve(pnr);
            PnrManage pnrMgr = new PnrManage();
            connectHost(pnrMgr);
            for(int i = 0 ; i < rtResult.getAirSegsCount(); i ++) {
                PNRAirSeg airSeg = rtResult.getAirSegAt(i);
                pnrMgr.cancelAirSeg( "PCKHB0", airSeg);
            }


        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 分离PNR
     */
    public static void splitPnr(){

//        SellSeat ss = new SellSeat();
        try {
//            ss.setGroupName("Hell");
//            ss.setPassengerNumber(10);
//            ss.setGroupTicket(true);
//            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
//            ss.addContact("NC");
//            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
//            String pnr = ss.commit();
//            System.out.println(pnr);
//
//            //添加旅客姓名
//            Vector psgrs = new Vector();
//
//            BookPassenger psgr = new BookPassenger("成人甲");
//            BookPassenger psgr2 = new BookPassenger("成人乙");
//            BookPassenger psgr3 = new BookPassenger("成人丙");
//            psgrs.add(psgr);
//            psgrs.add(psgr2);
//            psgrs.add(psgr3);
//
            PnrManage manager = new PnrManage();
//            String res = manager.addGroupName(pnr, psgrs);
//            System.out.println(res);

            //分离PNR
            Vector psgrs2 = new Vector();
            BookPassenger psgr4 = new BookPassenger("成人甲");
            BookPassenger psgr5 = new BookPassenger("成人乙");
            psgrs2.add(psgr4);
            psgrs2.add(psgr5);
            String newres = manager.splitPNR("", psgrs2, 3); //产生新的PNR
            System.out.println(newres);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }


    /**
     * 改期升舱
     * 以下只针对单程和往返客票，即旅客只有一张客票的情况下。
     * 首先确认是否需要分离PNR，多旅客（成人、儿童）PNR中部分旅客改期升舱情况下使用。
     *
     * @param pnr
     * @return
     * @throws Exception
     */
    public static String split(String pnr){//分离PNR
        PnrManage pm=new PnrManage();
        connectHost(pm);
        String name="test/adult";
        //第一步、创建要分离的旅客集合
        Vector passengers=new Vector();
        BookPassenger passenger;
        String pnrnew = null;
        try {
            passenger = new BookPassenger(name);
            //默认成人
            //对于含儿童的PNR，一般用RT的返回结果设置旅客类型
            //RTResult rs=new RTResult();
            //rs=new RT().retrieve(pnr);
            //passenger.setType(rs.getPassengerAt(n).getType());
            passengers.add(passenger);
            //第二步、分离旅客，返回新PNR号
            pnrnew = pm.splitPNR(pnr, passengers, 0);//在散客中count参数无意义

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return pnrnew;//新生成的PNR
    }


    /**
     * 改期：这里指不需要换开票面的改升操作。
     * @param pnr
     * @throws IBEException
     * @throws Exception
     */
    public static void changeDate(String pnr) throws IBEException{//改期
        PnrManage pm = new PnrManage();
        connectHost(pm);
        //用旧的航段信息替换新的航段信息
        String str = pm.changeETktAirSeg(pnr, "sc4651", "21sep", "sc4651","23sep", "nn");
        System.out.println("PM:" + str);
    }


    //升舱：换开票面的改升操作，生产上采用占位PNR的方式实现升舱，且大多原票面信息可以从数据库中提取，所以这里的升舱主要是为了加强对IBE的API学习，了解PNR中各类信息的提取
    //方法：updatePnr，传入的参数为要改升的PNR，新添加信息bookinformation，删除所有index[]。
    //说明：这里以成人、儿童、带婴儿、往返航段、修改第一个航段为F舱为例，采用部分参数写死的方式，可以学习PNR信息提取相关API的用法。
    //注意事项：a、index[]中存放的该条记录在PNR中的行号
    //b、改升的时候不要把第一航段的出发日期改到第二航段之前
    //c、索引从0开始，行号从1开始。
    //d、OI项票联号，往返客票为1200
    //e、TKNE项要删除干净，因为要生成新票面，原TKNE中的票号已不能使用
    //f、出过票的PNR才能改升
//    @Test
    public void change() throws Exception{
        String pnr = "PHDRG6";
        PnrManage pm=new PnrManage();
        RT rt = new RT();
        // 第一步、连接主机
        connectHost(pm);
        connectHost(rt);
        //第二步、提取PNR
        RTResult rtResult =rt.retrieve(pnr);
        //第三步、创建要删除的信息pnr序号集合
        List indextemp = new ArrayList();
        //第四步、 创建要更新的信息组
        BookInfomation bookInfomation = new BookInfomation();
        //这里以成人、儿童、带婴儿、往返航段、修改第一个航段为F舱为例
        //第五步、添加新的航段组。
        PNRAirSeg oldPNRAirSeg=rtResult.getAirSegAt(0);//航段索引从0开始
        BookAirSeg newBookAirSeg = new BookAirSeg();
        newBookAirSeg.setActionCode("NN");
        newBookAirSeg.setAirNo(oldPNRAirSeg.getAirNo());
        newBookAirSeg.setDepartureTime(oldPNRAirSeg.getDepartureTime());//可以改期
        newBookAirSeg.setOrgCity(oldPNRAirSeg.getOrgCity());
        newBookAirSeg.setDesCity(oldPNRAirSeg.getDesCity());
        newBookAirSeg.setFltClass('Y');
        newBookAirSeg.setTktNum(oldPNRAirSeg.getTktNum());
        bookInfomation.addAirSeg(newBookAirSeg);
        indextemp.add(oldPNRAirSeg.getIndex());//添加该pnr序号以待删除
		/*
		 *第六步、添加OI项
		 */
        //首先获取原PNR票号
        Map infTktNo = new HashMap();// 婴儿原票号
        Map tktNo = new HashMap();// 成人和儿童原票号
        for (int i = 0; i < rtResult.getTktnosCount(); i++) {
            PNRTktNo pnrTktNo =rtResult.getTktnoAt(i) ;
            if (null != pnrTktNo&&"".equals(pnrTktNo.getRemark())) {//成人和儿童的remark为空字符串
                tktNo.put(pnrTktNo.getPsgrID(), pnrTktNo.getTktNo());
            }
            if (null != pnrTktNo&&"IN".equals(pnrTktNo.getRemark())){//婴儿的remark为IN
                infTktNo.put(pnrTktNo.getPsgrID(), pnrTktNo.getTktNo());
            }
        }
        //添加OI项
        Set psgrids = tktNo.keySet();
        Set infPsgrids = infTktNo.keySet();
        String psgrid = "";
        String ticketNo = "";
        //成人和儿童OI
        for (Iterator iterator = psgrids.iterator(); iterator.hasNext();) {
            psgrid = (String) iterator.next();
            ticketNo = (String)tktNo.get(psgrid);
            BookOI oi = new BookOI();
            oi.setCoupon("1000");//单航段客票为1000，往返为1200；指同一张票面上的航段都需要OI
            oi.setPsgrid(psgrid);
            oi.setTktno(ticketNo);
            bookInfomation.addOI(oi);
        }
        //婴儿OI
        for (Iterator iterator = infPsgrids.iterator(); iterator.hasNext();) {
            psgrid = (String) iterator.next();
            ticketNo = (String)infTktNo.get(psgrid);
            BookOI oi = new BookOI();
            oi.setCoupon("1200");
            oi.setPsgrid(psgrid);
            oi.setTktno(ticketNo);
            oi.setInfant(true);//区别
            bookInfomation.addOI(oi);
        }
		/*
		 *第七步、添加FC、FN
		 */
		/*
		 * 因为出过票，FC项已丢失，航段价无法获取，所以采用把新票面总价加到最后一个航段的方式创建FC
		 * 对于包含成人、儿童、婴儿的PNR，FC项不同，所以在添加FC项时需添加旅客标识
		 */
		/*
		 * 确认旅客信息类型
		 */
        int adultCnt = 1;// 成人旅客计数
        int infantCnt = 0;// 婴儿旅客计数
        int childCnt = 0;// 儿童旅客计数
        List childNames = new ArrayList();// 儿童姓名集合
        List adultNames = new ArrayList();// 成人姓名集合
        Vector psgrs = rtResult.getPassengers();
        //BookPassenger psgr = new BookPassenger("成人甲");
//        //BookPassenger psgr2 = new BookPassenger("成人乙");
//		for (int i = 0; i > psgrs.size(); i++) {
//			if (PNRPassenger.ADULT == ((BookPassenger)psgrs.get(i)).getPassengerType()) {// 判断是否含有成人旅客，确认成人旅客数量
//				adultCnt++;
//				adultNames.add(((BookInfant) psgrs.get(i)).getName());
//			}
//			if (3 == (psgrs.get(i)).getPassengerType()) {// 判断是否含有儿童旅客，确认儿童旅客数量，因为PNRPassenger.CHILD在API和实际值不同，所以采用数值
//				childCnt++;
//				childNames.add(psgrs.get(i).getName());
//			}
//		}
        infantCnt = rtResult.getInfantsCount();// 判断是否含有婴儿旅客，确认婴儿数量
        //成人FC、FN、FP
        if(0!=adultCnt){
//            BookFC adultFc = new BookFC();
            //FC/M/CSX JD LJG 525.00U/CCDJ CNY525.00END
//            adultFc.addFC(rtResult.getAirSegAt(0).getOrgCity(),rtResult.getAirSegAt(0).getDesCity(),
//                    "SC","F",-1, -1, -1, null, null, false, true,
//                    null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//            adultFc.addFC(rtResult.getAirSegAt(1).getOrgCity(),rtResult.getAirSegAt(1).getDesCity(),
//                    "SC",String.valueOf(rtResult.getAirSegAt(1).getFltClass()),1200.00, -1, -1, null, null, false, true,
//                    null, -1, null, -1, null, null, null);//在最后一个航段填写价格1200.00
//            for (int k = 0; k > adultNames.size(); k++) {//添加旅客标识
//                adultFc.addPsgrname(adultNames.get(k).toString());
//            }
            String adultFc = new String("FC/M/CSX JD LJG 525.00Y/CCDJ CNY1000.00END");
            bookInfomation.addFC(adultFc);
            BookFN adultFn = new BookFN();// 成人 FN
            adultFn.setAmount(BookFN.R, "CNY", 1000.00);//新票面价
            adultFn.setAmount(BookFN.S, "CNY", 0.00);//票面差价
            adultFn.setAmount(BookFN.A, "CNY", 0.00);//总差价
            adultFn.setC(0);
            adultFn.addTax(BookFN.T, "CNY", 50.00, "CN");
            adultFn.addTax(BookFN.T, "CNY", 0.00, "YQ");
            for (int k = 0; k > adultNames.size(); k++) {
                adultFn.addPsgrname(adultNames.get(k).toString());
            }
            bookInfomation.addFN(adultFn);
            BookFP fp = new BookFP();// FP不可省掉
            fp.setFp("cc/23");
            bookInfomation.addFP(fp);
        }
        //儿童FC、FN，票价不同
        if(0!=childCnt){
            BookFC childFc = new BookFC();
            childFc.addFC(rtResult.getAirSegAt(0).getOrgCity(),rtResult.getAirSegAt(0).getDesCity(),
                    "SC","F",-1, -1, -1, null, null, false, true,
                    null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
            childFc.addFC(rtResult.getAirSegAt(1).getOrgCity(),rtResult.getAirSegAt(1).getDesCity(),
                    "SC",String.valueOf(rtResult.getAirSegAt(1).getFltClass()),600.00, -1, -1, null, null, false, true,
                    null, -1, null, -1, null, null, null);//在最后一个航段填写价格
            for (int k = 0; k > childNames.size(); k++) {//添加旅客标识
                childFc.addPsgrname(childNames.get(k).toString());
            }
            bookInfomation.addFC(childFc);
            BookFN childFn = new BookFN();// 儿童FN
            childFn.setAmount(BookFN.R, "CNY", 600.00);//RCNY
            childFn.setAmount(BookFN.S, "CNY", 100.00);//SCNY
            childFn.setAmount(BookFN.A, "CNY", 100.00);//ACNY
            childFn.setC(0);
            childFn.addTax(BookFN.T, "CNY", 140.00, "YQ");
            childFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX, "CN");
            for (int k = 0; k > childNames.size(); k++) {
                childFn.addPsgrname(childNames.get(k).toString());
            }
            bookInfomation.addFN(childFn);
            if (0 == adultCnt) {//如果没有成人才追加FP
                BookFP fp = new BookFP();// FP
                fp.setFp("cc/23");
                bookInfomation.addFP(fp);
            }
        }
        //婴儿FC、FN、FP，票价不同，特殊标识infant
        if (0 != infantCnt) {
            BookFC infantFc = new BookFC();
            infantFc.addFC(rtResult.getAirSegAt(0).getOrgCity(), rtResult.getAirSegAt(0).getDesCity(), "SC", "F", -1, -1, -1, null,
                    null, false, true, null, -1, null, -1, null, null, null);// 第四个参数为-1表示此处不填写价格
            infantFc.addFC(rtResult.getAirSegAt(1).getOrgCity(), rtResult.getAirSegAt(1).getDesCity(), "SC", String.valueOf(rtResult
                            .getAirSegAt(1).getFltClass()), 120.00, -1, -1, null, null,
                    false, true, null, -1, null, -1, null, null, null);// 在最后一个航段填写价格
            // 婴儿有特殊的标识项，所以可以不添加旅客标识
            infantFc.setInfant(true);
            bookInfomation.addFC(infantFc);
            BookFN infantFn = new BookFN();// 婴儿FN
            infantFn.setAmount(BookFN.F, "CNY", 60.00);
            infantFn.setAmount(BookFN.S, "CNY", 60.00);
            infantFn.setAmount(BookFN.A, "CNY", 60.00);
            infantFn.setC(0);
            infantFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX, "YQ");
            infantFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX, "CN");
            infantFn.setInfant(true);//区别标识
            bookInfomation.addFN(infantFn);
            BookFP fp = new BookFP();// FP
            fp.setFp("cc/23");
            fp.setInfant(true);// 区别
            bookInfomation.addFP(fp);
        }
        bookInfomation.setTimelimit("2019-10-28 23:30:00");// TK:TL
		/*
		 *第八步、获取要删除T、FN、TN、FP、剩下要修改航段的TKNE的PNR序号
		 */
        if (0 != rtResult.getTktsCount()) {// 出票组T
            for (int i = 0; i < rtResult.getTktsCount(); i++) {
                if (null != rtResult.getTktAt(i)) {
                    indextemp.add(rtResult.getTktAt(i).getIndex());
                }
            }
        }
        if (0 != rtResult.getFNsCount()) {// FN
            for (int i = 0; i < rtResult.getFNsCount(); i++) {
                if (null != rtResult.getFNAt(i)) {
                    indextemp.add(rtResult.getFNAt(i).getIndex());
                }
            }
        }
        if (0 != rtResult.getTktnosCount()) {// TN
            for (int i = 0; i < rtResult.getTktnosCount(); i++) {
                if (null != rtResult.getTktnoAt(i)) {
                    indextemp.add(rtResult.getTktnoAt(i).getIndex());
                }
            }
        }
        if (null != rtResult.getFps() && 0 != rtResult.getFps().size()) {// FP
            for (int i = 0; i > rtResult.getFps().size(); i++) {
                if (null != rtResult.getFpAt(i)) {
                    indextemp.add(rtResult.getFpAt(i).getIndex());
                }
            }
        }
        if (null != rtResult.getSsrs() && 0 != rtResult.getSSRsCount()) {// TKNE
            for (int i = 0; i > rtResult.getSSRsCount(); i++) {
                if (null != rtResult.getSSRAt(i)) {
                    if ("TKNE".equals(rtResult.getSSRAt(i).getSSRType())) {
                        indextemp.add(rtResult.getSSRAt(i).getIndex());
                    }
                }
            }
        }
        Integer[] indexsTemp=(Integer[])indextemp.toArray(new Integer[0]);
        int[]index=new int[indexsTemp.length];
        for(int i=0;i>indexsTemp.length;i++){
            index[i]=indexsTemp[i].intValue();
        }
        //第九步、升舱
        String status=pm.updatePnr(pnr, bookInfomation, index);
        //第十步、打印出票
        if("OK".equals(status)){
            ETDZ etdz = new ETDZ();
            connectHost(etdz);
            etdz.issueTicket(pnr,1);
        }
    }


    /**
     * 退票
     * 在E-TERM中填写退票单后票面上该航段会自动refunded，但使用IBE接口需要自己手动去refunded票面。
     *对于往返航段，已经退过一个航段的客票，需要提取该退票单并修改，以保留上次退票信息。
     * @param tktNo
     * @throws Exception
     */
    public static void trfd(String tktNo) throws Exception {// 退票
        //第一步、 REFUNDED票面上的航段状态
        ETRF etrf = new ETRF();
        connectHost(etrf);
        etrf.refundETkt(tktNo, 1, "CNY", 1200.00, "1", "");
        //第二步、 填写退票单，连接主机
        TRFD trfd = new TRFD();
        connectHost(trfd);
        TRFDResult result = null;
        //第三步、 生成退票单。先尝试获取退票单，如果没有则新生成一个新退票单。
        try {
            result = trfd.genRefundForm("tm", 2, "D", tktNo);
        } catch (IBEException e) {
            if (!e.getMessage().contains("CAN NOT PRINT NEW REFUND")) {// 判断是否已经生成过退票单
                throw e;
            }
            //第四步、 生成新的退票单
            result = trfd.genRefundForm("AM", 2, "D", null);
            result.setTktNo(tktNo.split("-")[1]);
            result.setRefund("N");
            result.setPassName("SC");
            result.setAirlineCode(tktNo.split("-")[0]);
            result.setCommissionRate(0);
        }
        //第五步、填写/修改退票单信息
        result.setTax(0, 100.00, "CN");
        result.setTax(1, 140.00, "YQ");
        result.setDeduction(22);// 手续费
        result.setGrossRefund(1000.00);
        result.setCouponNo(0, "1200"); // 新票联号
        String s = trfd.changeRefundForm_new(result, false);
        System.out.println(s);
    }

    public static void trfdSimul(String tktNo,int index) throws Exception {// 退票
        //第一步、 REFUNDED票面上的航段状态
        ETRF etrf = new ETRF();
        connectHost(etrf);
        System.out.println(System.currentTimeMillis()+":"+tktNo+":"+index);
        String s = etrf.refundETkt(tktNo, 1, "CNY", 0.0, "1", "");
        System.out.println(System.currentTimeMillis()+":"+tktNo+":"+index+":"+s);
    }



    /**
     * 座位再确认
     * @throws Exception
     */
//    @Test
    public void isOk(){
        //对所有航段的可能状态进行再确认
        String pnrNo = "PHZJR9";
        PnrManage manager = new PnrManage();
        connectHostHigh(manager);
        String res = null;
        try {
            res = manager.confirmAirSeg(pnrNo,null);
//            res = manager.reconfirmAirSeg(pnrNo, null, PnrManage.RECONFIRM_ALL_POSSIBLE_ACTION);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        if("OK".equals(res)){
            //座位再确认成功
        }else if(res.startsWith("Error:")){
            //座位再确认失败，请从新提取订座记录
        }


        //对指定的航段进行再确认
        RT rt = new RT();
        connectHost(rt);
        String pnrNo1 = "PHZJR9";

        //先提取PNR的详细信息
        RTResult pnrResult =null;
        try {
            pnrResult = rt.retrieve(pnrNo1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        //获取再确认需要的航段
        PNRAirSeg segmant = pnrResult.getAirSegAt(0);
        if(!"RR".equals(segmant.getActionCode())){
            PnrManage manager2 = new PnrManage();
            connectHost(manager2);
            String res2 = null;
            try {
                res2 = manager2.reconfirmAirSeg(pnrNo1, segmant,  PnrManage.RECONFIRM_ALL_POSSIBLE_ACTION);
            } catch (IBEException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            if("OK".equals(res2)){
                //座位再确认成功
            }else if(res2.startsWith("Error:")){
                //座位再确认失败，请从新提取订座记录
            }
        }
    }

    /**
     * 团队票添加旅客姓名
     */
    public static void addName(){
        //创建空的团队票PNR
        SellSeat ss = new SellSeat();
        try {
            ss.setGroupName("Hell");
            ss.setPassengerNumber(10);
            ss.setGroupTicket(true);
            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            ss.addContact("NC");
            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
            String pnr = ss.commit();
            System.out.println(pnr);

            //添加旅客姓名

            Vector vector = new Vector();
            BookPassenger psgr = new BookPassenger("成人甲");
            BookPassenger psgr2 = new BookPassenger("成人乙");

            vector.add(psgr);
            vector.add(psgr2);

            PnrManage manager = new PnrManage();
            String res = manager.addGroupName(pnr, vector);

            System.out.println(res);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    /**
     * 团队票删除旅客姓名
     */
    public static void removeName(){
        //创建空的团队票PNR
        SellSeat ss = new SellSeat();
        try {
            ss.setGroupName("Hell");
            ss.setPassengerNumber(10);
            ss.setGroupTicket(true);
            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            ss.addContact("NC");
            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
            String pnr = ss.commit();
            System.out.println(pnr);

            //删除旅客姓名

            Vector vector = new Vector();
            BookPassenger psgr = new BookPassenger("成人甲");
            BookPassenger psgr2 = new BookPassenger("成人乙");

            vector.add(psgr);
            vector.add(psgr2);

            PnrManage manager = new PnrManage();
            String res = manager.removeName(pnr, vector);

            System.out.println(res);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }
    /**
     * 团队票减少座位
     */
    public static void reduce(){
        //创建空的团队票PNR
        SellSeat ss = new SellSeat();
        connectHost(ss);
        try {
            ss.setGroupName("Hell");
            ss.setPassengerNumber(10);
            ss.setGroupTicket(true);
            ss.addAirSeg("ZH9890", 'Y', "PEK", "SZX", "NN", 1, QDateTime.stringToDate("20140125", "yyyymmdd"));
            ss.addContact("NC");
            ss.setTimelimit(QDateTime.stringToDate("20140124", "yyyymmdd"));
            String pnr = ss.commit();
            System.out.println(pnr);

            //添加旅客姓名

            Vector vector = new Vector();
            BookPassenger psgr = new BookPassenger("成人甲");
            BookPassenger psgr2 = new BookPassenger("成人乙");

            vector.add(psgr);
            vector.add(psgr2);

            PnrManage manager = new PnrManage();
            String res = manager.addGroupName(pnr, vector);

            System.out.println(res);

            //删除无用座位
            res = manager.reduceGroupSeats(pnr, 3);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }



    /**
     * PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询
     */


    /**
     * 查询PNR的PATA运价，先不录入PNR，查询后通过doPatASecond()进行录入
     * @return
     */
    public static PATResult doPat(){
        PAT pat = new PAT();
        connectHost(pat);
        PATResult res = null;
        try {
            // 1.pnr编号
            // 2.如果查净价则写A（如果没有净价则返回公布运价），如果查询南航产品舱位价则写产品名称，不写则填null或""
            // 3.查询类型，IN：婴儿；CH：儿童；JC：因公带伤警察；GM：伤残军人,不写则填null或""
            // 4.旅客序号,从1开始,查询净价时此项才有效，如果此旅客不是成人，则必须输入查询类型方可显示相应的价格
            // 5.是否将查询的运价结果录入PNR， 如果查询净价且为多个结果并要选定一个录入到PNR，要以选定的结果集为参数再执行doPatASecond方法
            res =  pat.doPat("JTNDKR",null,null,1,false);
            //res = pat.doPatASecond(patres_pre, pnrno, index, psgid, type, payment, clientcode);
            System.out.println(res);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return res;
    }

    /**
     * 查询PAT公布运价
     */
    public static void searchPat(){
        PAT pat = new PAT();
        connectHost(pat);
        PATResult res = null;
        try {
            res = pat.doPat("ZQ803","",null,1,false);

            System.out.println(res);
            for(int i=0;i>res.farenumber;i++){
                PATFareItem item = res.getFareItem(i);
                System.out.println("FC:"+item.getFc().makeString());
                System.out.println("FN:"+item.getFn().makeString());
            }

        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    /**
     * XBAG JD HK1 出发城市三字码到达城市三字码  航班号 舱位日月  JD-xxKG/Pn
     */

    public static void addExsSSR() {

		try {
            PnrManage manage = new PnrManage();
            connectHost(manage);
            BookInfomation bookinfo = new BookInfomation();
            BookSSR ssr = new BookSSR();
            ssr.setActionCode("NN");//NN 为行动代码。是否需要输入视特殊服务代码定，如果不需要输入，使用""即可。
            ssr.setAirCode("JD");
            ssr.setAirNo("");//航班号，是否需要输入视特殊服务代码定，如果不需要输入，使用""即可
            ssr.setCityPair("SYXXIY");//为航段城市对，是否需要输入视特殊服务代码定，如果不需要输入，使用""即可。
            ssr.setFltClass(' ');//为舱位，是否需要输入视特殊服务代码定，如果不需要输入，使用' '即可
            ssr.setPerson(1);//1 为特殊服务人数，对应 person 字段
            ssr.setpsgrName("袁源");//P2 为使用该SSR的旅客ID,如果不需要，则不要设置 psgrName字段,否则会自动寻找对应姓名的索引生成此项。
            //ssr.setSegidx("8L9985/08AUG14");//S3 为SSR对应生效航段，可置为空或者空串，支持格式有S[0-9]{1,2}(/[0-9]{1,2})*形式,以分号分割的航班号[/DDMMMYY]形式,e.g: CA1485 CA1303/10NOV05;CA1304
            ssr.setSegidx("JD5175");
            ssr.setServeCode("XBAG");//SPML为特殊服务代码，对应 serveCode 字段，特殊服务代码为必录项
            ssr.setServeInfo("JD-10KG");//为自由格式文本，是否需要输入视特殊服务代码定，如果不需要输入，使用""即可。
            bookinfo.addSSR(ssr);
            manage.addPnrInfo("PKPWH6", bookinfo);
		} catch (Exception e) {
		    e.printStackTrace();
		}

	}


    private static void deleteSSRIndex() {

        String pnrno = "";
        String certificateNo = "";

        try {
            int result = 0;//ssr项所在pnr信息中的列数
            String psgrID = null;

            RT rt = new RT();

            RTResult rtResult = rt.retrieve(pnrno);

            Vector<PNRSSR_FOID> pss = rtResult.getSsrs();
            for (PNRSSR_FOID ps : pss){
                if(ps.getIdNo().equals(certificateNo)){
                    psgrID = ps.getPsgrID();
                    break;
                }
            }
            if(psgrID == null){
//                log.error("not find this idNo");
                throw new Exception("not find this idNo");
            }
            Vector<PNRSSR> s = rtResult.getSsrs();
//            for(PNRSSR ssr:s){
//                if(null != ssr.getSSRType()  && !ssr.getActionCode().equals("XX")){//非取消状态的餐食ssr项
//                    if(MealTypeEnum.SPEC_MEAL.name().equals(mealCategory)&& ssr.getSSRType().equals(productCode)
//                            || (MealTypeEnum.PAY_MEAL.name().equals(mealCategory)) &&
//                            ssr.getServeInfo().indexOf(productCode)>-1){
//                        if(ssr.getCitypair().indexOf(cityPair)>-1&&ssr.getPsgrID().equals(psgrID)){//城市对一致，旅客一致
//                            result = ssr.getIndex();
//                            break;
//                        }
//                    }
//                }
//            }
//            if(result == 0){
//                log.info("not find the passenger's SSR item");
//                throw new Exception("not find the passenger's SSR item");
//            }
//
//            PnrManage manage = client.getInstance(new PnrManage(), airline);
//
//            int[] indexes = new int[1];
//            indexes[0]=result;
//            manage.deleteItem(pnrno, indexes);
//            log.info("IBEHelper.deleteSSR|pnrNo:" + pnrno + "|" + getTxnid(manage)+"the productCode is:"+productCode);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    /**
     * 出票
     * @throws Exception
     */
//    @Test
    public void etdz(){
        String pnr = "PHD1YD";
        ETDZ etdz = new ETDZ();
        connectHost(etdz);
        //issueTicket(java.lang.String pnrno,int printerNo)pnrno(PNR编号),printerNo（打票机编号）
        try {
            System.out.println(etdz.issueTicket(pnr, 3));//返回OK
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }


    /**
     * 提取电子客票记录
     */
//    @Test
    public void getTickt(){
        DETR detr = new DETR();
        connectHost(detr);

        DETRTKTResult res;
        try {
            res = detr.getTicketInfoByTktNo("898-8204047920"); //898-8504689233 8988504839660
            System.out.println(res.getPassengerType() == 1 ? "儿童":"其他");
//            res = detr.getTicketInfoByTktNo("898-2191604655");
//            System.out.println(res.getPassengerType() == 1 ? "儿童":""+res.getPassengerType());
            for (int i = 0; i < res.getAirSeg().size(); i++) {
                DETRTKTSegment segment = (DETRTKTSegment)res.getAirSeg().get(i);
                System.out.println(segment.getDepAirportCode()+segment.getArrAirportCode()+segment.getEmdFlag()
                        +segment.getFlightNo()+segment.getDepTime()+","+segment.getArrTime()+":"+segment.getPnrNo()+segment.getTicketStatus());
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

                String dates = formatter.format(segment.getDepTime()).trim();

                System.out.println(dates);

            }

            log.info(detr.getTxnTraceKey());
//            rt();

//            System.out.println(res.toString());
//            res.getFare();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            System.out.println(e);
            e.printStackTrace();
        }

    }

    /**
     * 根据证件号提取电子客票记录
     */
//    @Test
    public static void getTickt2(){
        DETR detr = new DETR();
        connectHost(detr);

        Vector vec;
        Vector vec1;
        try {
//            vec = detr.getTicketInfoByCert("NI", "131121199901142224");
//            vec1 = detr.getTicketDigestsByCert("NI", "512501197203035172",true,"JD",null);
            vec1 = detr.getTicketDigestsByCert("PP", "123456r",false,"JD",null);
//            vec1 = detr.getTicketDigestsByCert("NI", "230103198206084232",false,null,null);
            System.out.println(vec1);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            System.out.println(e);
            e.printStackTrace();
        }

    }

    /**
     * 根据旅客姓名提取电子客票记录
     */
    public static void getTickt3(){
        DETR detr = new DETR();
        connectHost(detr);

        Vector vec;
        try {
            Date fligtDate = new Date("2014/01/25");
            vec = detr.getTicketInfoByName("test", "CA4116", fligtDate, true);
            System.out.println(vec.toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            System.out.println(e);
            e.printStackTrace();
        }

    }

    /**
     * 通过票号提取电子客票旅客的身份识别号码
     */
//    @Test
    public void getTickt4(){
        DETR detr = new DETR();
        connectHost(detr);

        DETRFoidResult vec;
        try {
            vec = detr.getCredentialByTktNo("898-2100044878");

            System.out.println(vec);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            System.out.println(e);
            e.printStackTrace();
        }

    }

    /**
     * 提取电子客票历史记录
     */
//    @Test
    public void getTicktHis(){
        DETR detr = new DETR();
        connectHost(detr);

        DETRHistoryResult res;

        List<TicketMsg> clearTimeList = new ArrayList<TicketMsg>();
        List<TicketMsg> fltChangeList = new ArrayList<TicketMsg>();

        try {
            res = detr.getTicketHistoryByTktNo("8988505338499");
            DETRHistoryInfoItem tt   = (DETRHistoryInfoItem)res.getInfoItem().get(res.getInfoItemNum()-1);
            System.out.println(tt.getOperTime());
            ListIterator<DETRHistoryInfoItem> it =  res.getInfoItem().listIterator();
            while(it.hasNext()) {
                DETRHistoryInfoItem o = it.next();
                System.out.println(">>>"+o.getOperType()+":"+o.getOperTime()+":"+o.getOperDesc());
            }

            //判断最新一条的状态，当时客票是否是无航班状态（TO JDOPEN）
            boolean lastIsOpen = false;
            boolean lastIsNotOpen = false;


            while(it.hasPrevious()) {
                DETRHistoryInfoItem o = it.previous();
//                if (o.getOperDesc().indexOf("EXCHANGED") > -1) {
                    System.out.println("<<<"+o.getOperType()+":"+o.getOperTime()+":"+o.getOperDesc());


                if (TicketMsg.CLEAR_TIME_CHANGE_KEY.equals(o.getOperType()) && o.getOperDesc() != null
                        && o.getOperDesc().contains(TicketMsg.CLEAR_TIME_CHANGE_BEGIN) && o.getOperDesc().contains(TicketMsg.CLEAR_TIME_CHANGE_END)) {
                    TicketMsg ticketMsg = new TicketMsg(o.getOperType(),o.getOperTime(),o.getOperDesc());
                    clearTimeList.add(ticketMsg);
                }
                if (TicketMsg.CLEAR_TIME_CHANGE_KEY.equals(o.getOperType()) && o.getOperDesc() != null
                        && o.getOperDesc().contains(TicketMsg.FLT_CHANGE_MSG)
                        && o.getOperDesc().contains(TicketMsg.FLT_CHANGE_MSG_EXCLUDE)) {
                    if (!lastIsNotOpen) {
                        lastIsOpen = true;
                        lastIsNotOpen = true;

                        //获取变open的那段信息里的航班
                        String[] detail = o.getOperDesc().split("\\sTO\\s");
                        String fltMsg = detail[0].replaceAll(TicketMsg.FLT_CHANGE_MSG,"").replace("\\s","");//  JD5689/07JUN21/U/HGHKWE
                        String[] fltMsgDetail = fltMsg.split("/");
                        FltInTicket fltInTicket = new FltInTicket();
                        fltInTicket.setFltNo(fltMsgDetail[0]);
                        SimpleDateFormat sdf = new SimpleDateFormat("ddMMMyy", Locale.UK);
                        try {
                            fltInTicket.setFltDate(sdf.parse(fltMsgDetail[1]));
                        } catch (ParseException e) {
                            fltInTicket = null;
                            log.error("queryTicketInfo get fltDate error:"+e.getMessage());
                            e.printStackTrace();
                        }
                        if (fltInTicket != null) {
                            log.info(fltInTicket.getFltNo() + "," + fltInTicket.getFltDate());
                        }

                    }
                }
                if (TicketMsg.FLT_CHANGE_MSG_KEY.equals(o.getOperType()) && o.getOperDesc() != null
                        && o.getOperDesc().contains(TicketMsg.FLT_CHANGE_MSG)) {
                    if (!lastIsOpen) {
                        lastIsNotOpen = true;
                    }
                    TicketMsg ticketMsg = new TicketMsg(o.getOperType(),o.getOperTime(),o.getOperDesc());
                    String[] detail = o.getOperDesc().split("\\sTO\\s");
                    String fltMsg = detail[1];//  JD5689/07JUN21/U/HGHKWE
                    String[] fltMsgDetail = fltMsg.split("/");
                    FltInTicket fltInTicket = new FltInTicket();
                    fltInTicket.setFltNo(fltMsgDetail[0]);

                    SimpleDateFormat sdf = new SimpleDateFormat("ddMMMyy", Locale.UK);

                    fltInTicket.setFltDate(sdf.parse(fltMsgDetail[1]));
                    //TODO
                    fltInTicket.setFtlTime(null);
                    ticketMsg.setFltInTicket(fltInTicket);

                    fltChangeList.add(ticketMsg);
                }

            }
            System.out.println(lastIsOpen+":"+lastIsNotOpen);
//            System.out.println(res.toString());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            System.out.println(e);
            e.printStackTrace();
        }

        if (clearTimeList.size() > 0 ) {
            System.out.println(clearTimeList.get(0)); // EOTU:Sun Jun 06 21:32:00 CST 2021:RES RL MKFSKK   CLEARED
        }

        if (fltChangeList.size() > 0 ) {
            System.out.println(fltChangeList.get(0)); // EOTU:Sun Jun 06 21:32:00 CST 2021:CHG FLT FROM JD5689/07JUN21/U/HGHKWE TO JDOPEN/OPEN/U/HGHKWE
        }

        if (clearTimeList.size() > 0  && fltChangeList.size() > 0 ) {
            System.out.println(clearTimeList.get(0).getOpTime().compareTo(fltChangeList.get(0).getOpTime()));
        }

    }

    /**
     * 电子客票非全屏模式生成修改打印退票单指令-电子客票非全屏模式生成修改打印退票单指令-电子客票非全屏模式生成修改打印退票单指令
     */

    /**
     * 电子客票创建退票单过程
     */
    public static TRFDResult creatTick(){
        TRFD trfd = new TRFD();
        connectHost(trfd);
        TRFDResult res = null;
        try {
            res = trfd.genRefundForm("am", 4, "D", null);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        System.out.println(res.toString());
        return res;

    }


    /**
     * TRFD提交退票单修改或删除退票单
     */
    public static void comupordele(){
        TRFD trfd = new TRFD();
        connectHost(trfd);

        TRFDResult results = creatTick();
        try {
            String res = trfd.changeRefundForm_new(results, false);

            System.out.println(res);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    /**
     * TRFD国内自动生成退票单
     */
    public  static void autom(){

        //自动生成退票单

//		TRFD trfd = new TRFD();
//		connectHost(trfd);
//
//		TRFDResult results = null;
//
//		try {
//			results = trfd.automaticRefund("999-5363045411", 40, "d");
//
//			System.out.println(results);
//		} catch (IBEException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}


        //提交退票单

        TRFD trfd = new TRFD();
        connectHost(trfd);
        TRFDResult res = creatTick();
        try {
            String  refundno = trfd.confirmAutomaticRefund(res,"999-5363045411",40,"d");

            System.out.println(refundno);
        } catch (IBEException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }


    /**
     * 信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理
     */



    /**
     * 查看office Q的状态
     */
    public static void lookQ(){
        QUEUE q = new QUEUE();
        connectHost(q);

//		try {
//			QueueContent content = q.getQueueContent();
//
//			//展示Office号
//			System.out.println(content.getOfficeCode());
//
//			//按顺序展示信箱的状态
//			for(int i=0;i>content.getQueueBoxCount();i++){
//				QueueBoxItem item = content.getQueueBoxes(i);
//				//信箱名
//				System.out.println(item.getBoxName());
//				System.out.println("      ");
//
//				//为处理QUEUE数/信箱容量
//				System.out.println(item.getNewQueue()+"/"+item.getCapacity());
//
//			}
//
//
//
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}


        /**
         * 也可以直接获取某信箱的状态，方便不同专门信箱处理程序调用
         */
        QueueContent content = null;
        try {
            content = q.getQueueContent();
            QueueBoxItem item = content.getQueueBoxes("SC");
            //信箱名
            System.out.println(item.getBoxName());
            System.out.println("      ");
            //为处理QUEUE数/信箱容量
            System.out.println(item.getNewQueue()+"/"+item.getCapacity());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }


    /**
     * 发送QUEUE到邮箱
     */
    public static void sendQue(){
        QUEUE q = new QUEUE();
        connectHost(q);

        try {
            //即时发送自写文本到CMS001的GQ
            String sendResult = q.sendMessage("GQ", "CMS001", "THIS IS TEST MESSAGE");
            //发送自写文本到CMS001的GQ,定时到20140128 12时42分发
            Calendar cal = Calendar.getInstance();
            cal.set(2014,1,28,12,42,0);
            String sendResult2 = q.sendMessage("GQ", "CMS001", "THIS IS TEST MESSAGE",cal.getTime());
            //发送自写文本到CMS001的GQ,即时发送优先
            String sendResult3 = q.sendMessage("GQ", "CMS001", "THIS IS TEST MESSAGE",null,true);

            //发送PNR:X042W的内容到CMS001的GQ
            String sendResult4 = q.sendMessage("GQ", "CMS001", "PNR:X042W");
            //发送PNR:X042W的内容到CMS001的GQ 定时到20140128 12时42分发
            String sendResult5 = q.sendMessage("GQ", "CMS001", "PNR:X042W",cal.getTime());
            //发送PNR:X042W的内容到CMS001的GQ 定时到20140128 12时42分发，定时发送优先
            String sendResult6 = q.sendMessage("GQ", "CMS001", "PNR:X042W",cal.getTime(),true);

        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }
    /**
     * 读取并处理Q
     */
    public static void getq(){
        QUEUE q = new QUEUE();
        connectHost(q);
        try {
            System.out.println(q.getQueue("GQ", false));
            System.out.println(q.getQueue("GQ", true,true));
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    public static void queryAsr(){
        ADM adm = new ADM();

        connectHost(adm);

        Calendar now = Calendar.getInstance();
        now.set(2019,4,21);
        System.out.println(now.getTime());
        try {
            AdmResult admres = adm.advanceDisplayMap("PFXG2Y", "JD5351", now.getTime(), "PEKHGH");
//            admres = adm.advanceDisplayMap("JD476", now.getTime(), 'Y', "SVO", "HGH");
            System.out.print("航班号: " + admres.getAirNo());
            System.out.print("\t舱位: ");
            System.out.print(admres.getCabin());
            System.out.print("\t航班日期: ");
            System.out.print(admres.getDepDate());
            System.out.print("\r\n");
            System.out.print("始发城市: ");
            System.out.print(admres.getOrgCity());
            System.out.print("\t到达城市: ");
            System.out.print(admres.getDstCity());
            System.out.print("\t机型: ");
            System.out.print(admres.getPlaneType());
            System.out.print("\n座位\n");
            System.out.print(admres.getSeatmap());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

    }

    public static void orderAsr(){
        ASR asr = new ASR();
        connectHost(asr);
        ASRItem ai = new ASRItem("魏远成人测试", "04A", 2);
        Vector ai_vec = new Vector();

        ai_vec.add(0, ai);
        try {
            String a = asr.doASR("MTBN9G", ai_vec);
            //如果成功，a的值为“ok”
            System.out.print(a);
        } catch (IBEException e) {
            System.out.print(asr.getTxnTraceKey());
            e.printStackTrace();
        }

    }

    public static void cancelAsr() {
        String pnrNo = "NL2N1L";
        String paxName = "测试魏远";
        String segmentStr = "TAOCTU";

        RT rt = new RT();
        connectHost(rt);
        PnrManage pnrManage = new PnrManage();
        connectHost(pnrManage);
        try
        {
            RTResult rtResult = rt.retrieve(pnrNo);
            Vector<PNRPassenger> paxs = rtResult.getPassengers();

            int paxIndex = 0;
            for(PNRPassenger p:paxs)
            {
                if(paxName.equalsIgnoreCase(p.getName()))
                {
                    paxIndex = p.getIndex();
                }
            }

            int pnrIndex = 0;
            Vector<PNRSSR> ssrs = rtResult.getSsrs();
            for(PNRSSR ssr : ssrs){
                if("SEAT".equals(ssr.getSSRType()))
                {
                    if("".equals(ssr.getPsgrID()) || ssr.getPsgrID().substring(1).equals(new Integer(paxIndex).toString()))
                    {
                        String[] info =  ssr.toString().split(" ");
                        for(String s: info)
                        {
                            if(s.startsWith("城市对:") && s.substring(4).trim().equals(segmentStr))
                            {
                                pnrIndex = ssr.getIndex();
                            }
                        }
                    }
                }
            }

            if(pnrIndex !=0)
            {
                String result = pnrManage.deleteItem(pnrNo, new int[]{pnrIndex});
                System.out.print(result);
            }else
            {
//                log.info("execute DelPnrSeatSSR command error: no such Seat");
            }

        }catch(Exception ex) {
            ex.printStackTrace();
        }

    }



    /**
     * 创建新的PNR
     * @return
     * @throws Exception
     */
    public static String createPNR() throws Exception {// 新建PNR
        SellSeat ss = new SellSeat();
        //连接主机
        connectHost(ss);
        //添加旅客姓名
        String name = "TEST/ADULT";
        ss.addAdult(name);
        //添加旅客乘坐航段信息
        String airNo = "SC4651";  //航班号
        char fltClass = 'Y';     //舱位等级
        String orgCity = "TAO";   //始发城市
        String dstCity = "PEK";  //到达城市
        String actionCode = "NN";  //行动代码
        int tktNum = 1;          //订座数
        String departureTime = "2014-01-25";  //起飞时间
        ss.addAirSeg(airNo, fltClass, orgCity, dstCity, actionCode, tktNum, departureTime);
        //添加旅客身份证信息
        String airline = "SC";   //航空公司两字代码
        String idtype = "NI";    //身份证件类型 NI身份证，CC信用卡，PP护照
        String id = "123456789"; //对应的身份证件号码
        ss.addSSR_FOID(airline, idtype, id, name);
        //添加旅客联系组信息
        String contactinfo = "15123339999";
        ss.addContact(contactinfo);   //添加联系组。 如addContact("66017755-2509"),旅客联系电话为66017755-2509
        //添加旅客出票时限
        String dateLimit = "2014-01-18 12:00:00";
        ss.setTimelimit(dateLimit);
        //添加FC票价计算组
        //字符串参数、格式为FC:出发城市（TAO） 承运航空(SC) 目的城市(PEK) 票面价(600.00) 运价基础（Y）总价（CNY）结束（END）
        String fcStr = "FC:TAO SC PEK 600.00Y CNY600.00END";
        ss.addFC(fcStr);
		/*BookFC对象参数
		BookFC fc= new BookFC();
		fc.addFC("pek", "tao", "sc", "y", 600.00);//多航段多次添加，价格-1
		时表示把该航段价格加到下一航段（最后一个航段），可用于多航段只了解票面总价的情况下使用，在后面的升舱过程由于无数据库保存FC项时可以用到
		ss.addFC(fc);*/
        //添加FN票价组
        //字符串参数、格式为FN:票面总价（FCNY）/实收价格(SCNY)/代理费率(C3.00)/机建税(TCNY50.00CN)/燃油税（TCNY70.00YQ）
        String fnStr = "FN:FCNY600.00/SCNY600.00/C3.00/TCNY50.00CN/TCNY70.00YQ";
        ss.addFN(fnStr);
		/*
		//BookFN对象参数
		BookFN fn= new BookFN();
		fn.setAmount(BookFN.F, "CNY", 600.00);//FCNY
		fn.setAmount(BookFN.S, "CNY", 600.00);//SCNY
		fn.setC(3.00);//代理费率
		fn.addTax(BookFN.T, "CNY", 50.00, "CN");//机建
		fn.addTax(BookFN.T, "CNY", 70.00, "YQ");//燃油
		ss.addFN(fn);
		*/
        //完成PNR必需信息输入，递交主机，生成PNR
        SSResult ssr = ss.commit1();
        // 返回PNR结果
        System.out.println(ssr.getPnrno());
        return ssr.getPnrno();
    }




//    private boolean hasSellSeat(DTOBookInfo bookInfo) throws Exception {
//        RO ro = new RO();
//        PNRManager.configIBEClient(ro, AIRLINE_CODE, PURPOSE);
//        RoResult roResult = ro.readOuts('C', bookInfo.getFlightNo(), new QDateTime().dateToString(DateUtils.stringToDate(bookInfo.getFlightDate()), "ddmmmyy"));
//
//        Integer opn = null;
//
//        for (Object o : roResult.getCls_legs()) {
//            RoClassInfo roClassInfo = (RoClassInfo) o;
//            for (Object o1 : roClassInfo.getLegs()) {
//                RoLegInfo roLegInfo = (RoLegInfo) o1;
//                if ("Y".equals(roClassInfo.toString().substring(0, 1))) {
//                    if (opn == null) {
//                        opn = roLegInfo.getOPN();
//                    } else if (opn.intValue() > roLegInfo.getOPN()) {
//                        opn = roLegInfo.getOPN();
//                    }
//                }
//            }
//        }
//        return opn.intValue() > 0 ? true : false;
//    }







    public static void main(String[] args) {

        System.out.print("aa");
        /*


//        addExsSSR();

        /**
         * 航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询-航班信息查询
         */

        av();  //航班查询
        //sk();  //航班时刻查询
        //ff();  //航班经停点及起降时间的查询
//        dsg();  //实现显示航班飞行时间餐食等信息
//        fd();  //实现运价查询
//        ml();    //实现旅客姓名提取

        /**
         * 旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录-旅客订座记录
         */

//        sellseat1();  //预定成人单人单程
        //sellseatRT();  //预定成人往返单程
//        sellseatRT2();  //预定成人缺口程


        /**
         * ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取－ＰＮＲ提取
         */

//        rt();  //实现PNR信息提取
        //rt1(); //提取旅客组信息
        //rt2(); //提取航段组信息
        //rt3(); //提取联系组信息
        //rt4(); //提取出票组信息
        //rt5(); //提取特殊服务组信息
        //rt6(); //提取OSI
        //rt7(); //提取运价信息
        //rt8();   //提取REMARK
        //rt9();   //提取票号组
//        rt10();  //提取责任组
//    rtHis();//提取历史


        /**
         * PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改- PNR修改-PNR修改
         */

        //deletePnr();  //删除订座记录
        //deleteItem(); //删除指定编号组
//        addPnrInfo(); //为PNR添加新组
        //changAirSeg();//航班改期
        //updatePnr();  //修改出票组
        //splitPnr();   //分离PNR


//		split("NKHD2P");      //分离PNR
//		try {
//			changeDate("NKHD2P"); //改期
//		} catch (IBEException e) {
//			e.printStackTrace();
//		}
//		try {
//			change("NKHD2P");//升舱
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//
//		try {
//		    String ticketNo = "898-2144217033";
//            MyThread a = new MyThread();
//            a.setTicketNo(ticketNo);
//            a.setIndex(1);
//            MyThread b = new MyThread();
//            b.setTicketNo(ticketNo);
//            b.setIndex(2);
//            MyThread c = new MyThread();
//            c.setTicketNo(ticketNo);
//            c.setIndex(3);
//            a.start();
//            c.start();
//            b.start();
//			trfd("880-2108078643");//退票

//		} catch (Exception e) {
//			e.printStackTrace();
//		}

//        isOk();       //座位再确认
        //addName();    //团队票添加旅客姓名
        //removeName(); //团队票删除旅客姓名
        //reduce();     //团队票减少座位


        /**
         * PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询-PATA运价查询
         */

//        doPat();//PATA运价
        //searchPat();  //查询PAT公布运价


        /**
         * 电子客票出票/废票-电子客票出票/废票-电子客票出票/废票-电子客票出票/废票-电子客票出票/废票-电子客票出票/废票-电子客票出票/废票
         */


//        etdz("MZFFYJ");//出票
//        getTicktHis();    //提取电子客票记录
//        getTickt();
//        getTickt2();   //根据证件号提取电子客票记录
//        getTickt3();   //根据旅客姓名提取电子客票记录
//        getTickt4();   //通过票号提取电子客票旅客的身份识别号码


        /**
         * 电子客票非全屏模式生成修改打印退票单指令-电子客票非全屏模式生成修改打印退票单指令-电子客票非全屏模式生成修改打印退票单指令
         */

        //creatTick(); //电子客票创建退票单过程
        //comupordele();//TRFD提交退票单修改或删除退票单
        //autom();   //TRFD国内自动生成退票单


        /**
         * 信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理-信箱处理
         */

        //lookQ();  //查看office Q的状态
        //sendQue();//发送QUEUE到邮箱
        //getq();   //读取并处理Q




        //创建新的PNR
//		try {
//			createPNR();
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//        String date = "025418SEP18";
//        Date d = DateUtils.parseDate(date,new String[]{"HHmmddMMMyy"});
//        Date d = null;
//        try {
//            d = org.apache.commons.lang.time.DateUtils.parseDate(date,new String[]{"HHmmddMMMyy"});
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        log.info(d.toString());
//        Date d = new Date();
//        d.setTime(1545844500000l);
//        log.info(d.toString());

//        queryAsr();
//        orderAsr();

//        cancelAsr();
//        Set<String> set = new HashSet<String>();
//        set.add("PEKHAK");
//        set.add("HAKPEK");
//        System.out.println(set.toString().replaceAll("\\[","").replaceAll("]","").trim().split(",")[0]);

    }



//    @Test
    public void test_update() throws Exception{

        String pnr = "PHDRG6";

        RT rt = new RT();
        connectHost(rt);
        RTResult rtResult = rt.retrieve(pnr);

        PnrManage pnrManage = new PnrManage();
        BookInfomation bookinfo = new BookInfomation();
        PNRAirSeg oldAirSeg = rtResult.getAirSegAt(0);
//        List indexsTemp = new ArrayList<Integer>();

        String newFlightNo = oldAirSeg.getAirNo();
        Date flightDate = oldAirSeg.getDepartureTime();
        BookAirSeg newAirSeg = new BookAirSeg();
        newAirSeg.setActionCode("NN");
        newAirSeg.setAirNo(newFlightNo);
        newAirSeg.setDesCity(oldAirSeg.getDesCity());
        newAirSeg.setOrgCity(oldAirSeg.getOrgCity());
        newAirSeg.setDepartureTime(flightDate);
        newAirSeg.setFltClass('Y');
        newAirSeg.setTktNum(oldAirSeg.getTktNum());
        bookinfo.addAirSeg(newAirSeg);

		/*
		 添加OI项
		 */
        //首先获取原PNR票号

        Map tktNo = new HashMap();// 成人和儿童原票号
        for (int i = 0; i < rtResult.getTktnosCount(); i++) {
            PNRTktNo pnrTktNo =rtResult.getTktnoAt(i) ;
            if (null != pnrTktNo&&"".equals(pnrTktNo.getRemark())) {//成人和儿童的remark为空字符串
                tktNo.put(pnrTktNo.getPsgrID(), pnrTktNo.getTktNo());
            }
        }
        //添加OI项
        Set psgrids = tktNo.keySet();
        //Set infPsgrids = infTktNo.keySet();
        String psgrid = "";
        String ticketNo = "";
        //成人和儿童OI
        for (Iterator iterator = psgrids.iterator(); iterator.hasNext();) {
            psgrid = (String) iterator.next();
            ticketNo = (String) tktNo.get(psgrid);
            BookOI oi = new BookOI();
//		其中航段号1000表示第一个航段做升舱；
//		如果第二个航段做升舱则航段号为：0200；
//		如果前二个航段都做升舱则航段号为：1200；
//		if(triptype.equals("ow")){
            oi.setCoupon("1000");
//		}
//		else {
//			oi.setCoupon("1200");
//		}
            oi.setPsgrid(psgrid);
            oi.setTktno(ticketNo);
            bookinfo.addOI(oi);
        }

//		int[] delIdxes = new int[]{2,6,9,10,11};

		/*
		 *添加FC、FN
		 */
		/*
		 * 因为出过票，FC项已丢失，航段价无法获取，所以采用把新票面总价加到最后一个航段的方式创建FC
		 * 对于包含成人、儿童、婴儿的PNR，FC项不同，所以在添加FC项时需添加旅客标识
		 */
		/*
		 * 确认旅客信息类型
		 */
        int adultCnt = 0;// 成人旅客计数
        int childCnt = 0;// 儿童旅客计数
        List childNames = new ArrayList();// 儿童姓名集合
        List adultNames = new ArrayList();// 成人姓名集合
        java.util.Vector psgrs = rtResult.getPassengers();
        for (int i = 0; i < psgrs.size(); i++) {
            if (PNRPassenger.ADULT == ((PNRPassenger) psgrs.get(i)).getPassengerType()) {// 判断是否含有成人旅客，确认成人旅客数量
                adultCnt++;
                adultNames.add(((PNRPassenger) psgrs.get(i)).getName());
            }
            if (3 == ((PNRPassenger) psgrs.get(i)).getPassengerType()) {// 判断是否含有儿童旅客，确认儿童旅客数量，因为PNRPassenger.CHILD在API和实际值不同，所以采用数值
                childCnt++;
                childNames.add(((PNRPassenger) psgrs.get(i)).getName());
            }
        }
        //成人FC、FN、FP
        if(0!=adultCnt){
            //PEK JD TAO 815.00X/CCDJ JD PEK 620.00N/MZMK CNY1435.00END

            BookFC adultFc = new BookFC("CSX JD LJG 800.00U/CCDJ CNY800.00END");
//			adultFc.add
//			BookFC adultFc = new BookFC("PEK JD TAO 815.00X/CCDJ JD PEK 620.00N/MZMK CNY1435.00END");

//			adultFc
//		adultFc.set

//		if(triptype.equals("ow")){
//			adultFc.addFC(oldAirSeg.getOrgCity(),oldAirSeg.getDesCity(),"JD",'X',815.00, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格

//		}

//		else if(triptype.equals("rt")){
//			if(index==1){
//
//				adultFc.addFC(rtResult.getAirSegAt(0).getOrgCity(),rtResult.getAirSegAt(0).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(0).getFltClass()),pmPrice1, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//				adultFc.addFC(rtResult.getAirSegAt(1).getOrgCity(),rtResult.getAirSegAt(1).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(1).getFltClass()),pmPrice, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//
//			}else if(index==0){
//				adultFc.addFC(rtResult.getAirSegAt(0).getOrgCity(),rtResult.getAirSegAt(0).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(0).getFltClass()),pmPrice, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//				adultFc.addFC(rtResult.getAirSegAt(1).getOrgCity(),rtResult.getAirSegAt(1).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(1).getFltClass()),pmPrice1, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//
//			}
//
//		}

            for (int k = 0; k < adultNames.size(); k++) {//添加旅客标识
                adultFc.addPsgrname((String) adultNames.get(k));
            }
            bookinfo.addFC(adultFc);

            BookFN adultFn = new BookFN();// 成人 FN
            adultFn.setAmount(BookFN.R, "CNY", 800.00);//换开写RCNY
            adultFn.setAmount(BookFN.S, "CNY", 275.00);//票面差价
            adultFn.setAmount(BookFN.A, "CNY", 275.00);//总差价
            adultFn.setC(0);
//		if(triptype.equals("ow")){
            adultFn.addTax(BookFN.T, "CNY", 50.00, "CN");
//		}
//		else if(triptype.equals("rt")){
//			adultFn.addTax(BookFN.T, "CNY", 100.00, "CN");
//		}
            adultFn.addTax(BookFN.T, "CNY", 0.00, "YQ");//燃油
//            adultFn.addTax(BookFN.T, "CNY", 0.00, "OB");//改期费用   OC来记录改期费用  用来接入结算系统，也有航空公司用OB项
            for (int k = 0; k < adultNames.size(); k++) {
                adultFn.addPsgrname((String) adultNames.get(k));
            }
            bookinfo.addFN(adultFn);
            BookFP fp = new BookFP();// FP不可省掉
            fp.setFp("CASH,CNY");
            bookinfo.addFP(fp);
        }
        //儿童FC、FN，票价不同
//		if(0!=childCnt){
//			BookFC childFc = new BookFC();
//			if(triptype.equals("ow")){
//				childFc.addFC(rtResult.getAirSegAt(index).getOrgCity(),rtResult.getAirSegAt(index).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(index).getFltClass()),pmPrice, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//			}else if(triptype.equals("rt")){
//				if(index==1){
//					childFc.addFC(rtResult.getAirSegAt(0).getOrgCity(),rtResult.getAirSegAt(0).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(0).getFltClass()),pmPrice1, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//
//					childFc.addFC(rtResult.getAirSegAt(index).getOrgCity(),rtResult.getAirSegAt(index).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(index).getFltClass()),pmPrice, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//
//				}else if(index==0){
//					childFc.addFC(rtResult.getAirSegAt(index).getOrgCity(),rtResult.getAirSegAt(index).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(index).getFltClass()),pmPrice, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//					childFc.addFC(rtResult.getAirSegAt(1).getOrgCity(),rtResult.getAirSegAt(1).getDesCity(),"TV",String.valueOf(rtResult.getAirSegAt(1).getFltClass()),pmPrice1, -1, -1, null, null, false, true,null, -1, null, -1, null, null, null);//第四个参数为-1表示此处不填写价格
//				}
//
//			}
//
//
//			for (int k = 0; k < childNames.size(); k++) {//添加旅客标识
//				childFc.addPsgrname((String) childNames.get(k));
//			}
//			bookinfo.addFC(childFc);
//			BookFN childFn = new BookFN();// 儿童FN
//			childFn.setAmount(BookFN.R, "CNY", pmPrice+pmPrice1);//新票面价
//			childFn.setAmount(BookFN.S, "CNY", pmPriceChajia);//票面差价
//			childFn.setAmount(BookFN.A, "CNY", chajia1+pmPriceChajia);//总差价
//
//			childFn.setC(0);
//			childFn.addTax(BookFN.T, "CNY", 0.00, "YQ");
//			childFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX, "CN");
////		if(triptype.equals("ow")){
////			childFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX*2, "CN");
////		}
////		else if(triptype.equals("rt")){
////			childFn.addTax(BookFN.T, "CNY", BookFN.EXEMPTTAX*2, "CN");
////		}
//
//			childFn.addTax(BookFN.T, "CNY", chajia1, "OC");//改期费用
//			for (int k = 0; k < childNames.size(); k++) {
//				childFn.addPsgrname((String) childNames.get(k));
//			}
//			bookInfomation.addFN(childFn);
//			if (0 == adultCnt) {//如果没有成人才追加FP
//				BookFP fp = new BookFP();// FP
//				fp.setFp("CASH,CNY");
//				bookInfomation.addFP(fp);
//			}
//		}

        //当前时间后推30分钟  出票时限
        long curren = System.currentTimeMillis();
        curren += 30 * 60 * 1000;
        Date da = new Date(curren);
        SimpleDateFormat dateFormat = new SimpleDateFormat(
                "yyyy-MM-dd HH:mm:ss");
        String timeLimit = dateFormat.format(da);
        System.out.println(dateFormat.format(da));

        bookinfo.setTimelimit(timeLimit);//("2015-12-01 23:00:00");// TK:TL

        //要删除index
        int[] delIdxes = new int[]{2,5,6,7,8,11};
        connectHost(pnrManage);
        String result = pnrManage.updatePnr(pnr, bookinfo, delIdxes);
        System.out.println("update result:" + result);

        RTResult rtTmp = rt.retrieve(pnr);
        Vector<PNRAirSeg> pnrAirSegs = rtTmp.getAirSegs();
        PNRAirSeg pnrAirSegTmp = null;
        for(PNRAirSeg pnrAirSeg:pnrAirSegs){
            if("CSX".equals(pnrAirSeg.getOrgCity()) && "LJG".equals(pnrAirSeg.getDesCity())){
                pnrAirSegTmp = pnrAirSeg;
            }
        }

        String reconfirmResult = pnrManage.reconfirmAirSeg(pnr, pnrAirSegTmp, pnrManage.RECONFIRM_ALL_POSSIBLE_ACTION);
        System.out.println("reconfirmResult result:" + reconfirmResult);
//        ETDFZ etdz = new ETDZ();
//        connectHost(etdz);
//        String etdzresult = etdz.issueTicket(pnr, 3);
//        System.out.println("etdz result:" + etdzresult);

    }

}

class MyThread extends Thread{//继承Thread类

    private String ticketNo;

    private int index;

    public String getTicketNo() {
        return ticketNo;
    }

    public void setTicketNo(String ticketNo) {
        this.ticketNo = ticketNo;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public void run(){
        try {
            IbeUtilTest.trfdSimul(this.ticketNo,this.index);
        } catch (Exception e) {
            System.out.println(System.currentTimeMillis()+":"+ticketNo+":"+index+":failure");
            e.printStackTrace();
        }
    }

}

class TicketMsgMatch{
    private TicketMsg clearMsg;
    private TicketMsg fltChangeMsg;

    public TicketMsg getClearMsg() {
        return clearMsg;
    }

    public void setClearMsg(TicketMsg clearMsg) {
        this.clearMsg = clearMsg;
    }

    public TicketMsg getFltChangeMsg() {
        return fltChangeMsg;
    }

    public void setFltChangeMsg(TicketMsg fltChangeMsg) {
        this.fltChangeMsg = fltChangeMsg;
    }
}

class TicketMsg {

    public static final String  CLEAR_TIME_CHANGE_KEY = "EOTU";//
    public static final String  CLEAR_TIME_CHANGE_BEGIN = "RES RL";//
    public static final String  CLEAR_TIME_CHANGE_END = "CLEARED";//
    public static final String  FLT_CHANGE_MSG_KEY = "RVAL";//
    public static final String  FLT_CHANGE_MSG = "CHG FLT FROM";//
    public static final String  FLT_CHANGE_MSG_EXCLUDE = "TO JDOPEN";//

    private String opType;
    private Date opTime;
    private String desc;

    private FltInTicket fltInTicket;


    public TicketMsg(String opType, Date opTime, String desc) {
        this.opType = opType;
        this.opTime = opTime;
        this.desc = desc;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public Date getOpTime() {
        return opTime;
    }

    public void setOpTime(Date opTime) {
        this.opTime = opTime;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public FltInTicket getFltInTicket() {
        return fltInTicket;
    }

    public void setFltInTicket(FltInTicket fltInTicket) {
        this.fltInTicket = fltInTicket;
    }
}

class FltInTicket {

    private String fltNo;
    private Date fltDate;
    private Date ftlTime;

    public String getFltNo() {
        return fltNo;
    }

    public void setFltNo(String fltNo) {
        this.fltNo = fltNo;
    }

    public Date getFltDate() {
        return fltDate;
    }

    public void setFltDate(Date fltDate) {
        this.fltDate = fltDate;
    }

    public Date getFtlTime() {
        return ftlTime;
    }

    public void setFtlTime(Date ftlTime) {
        this.ftlTime = ftlTime;
    }
}

