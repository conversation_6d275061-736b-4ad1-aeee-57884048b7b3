/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8112951988167845598L;
/*     */   private String seatNumber;
/*     */   private String deckCode;
/*     */   private String seatValue;
/*     */   private String seatValueEx;
/*     */   private String seatChar;
/*     */   private String fareAmount;
/*     */   private String fareCurrency;
/*     */   private String andOrIndicator;
/*     */   private String mileageFee;
/*     */   private String fareIndex;
/*     */   private Commission commission;
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  51 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  58 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getDeckCode()
/*     */   {
/*  65 */     return this.deckCode;
/*     */   }
/*     */ 
/*     */   public void setDeckCode(String deckCode)
/*     */   {
/*  72 */     this.deckCode = deckCode;
/*     */   }
/*     */ 
/*     */   public String getSeatValue()
/*     */   {
/*  79 */     return this.seatValue;
/*     */   }
/*     */ 
/*     */   public void setSeatValue(String seatValue)
/*     */   {
/*  86 */     this.seatValue = seatValue;
/*     */   }
/*     */ 
/*     */   public String getSeatValueEx()
/*     */   {
/*  93 */     return this.seatValueEx;
/*     */   }
/*     */ 
/*     */   public void setSeatValueEx(String seatValueEx)
/*     */   {
/* 100 */     this.seatValueEx = seatValueEx;
/*     */   }
/*     */ 
/*     */   public String getSeatChar()
/*     */   {
/* 107 */     return this.seatChar;
/*     */   }
/*     */ 
/*     */   public void setSeatChar(String seatChar)
/*     */   {
/* 114 */     this.seatChar = seatChar;
/*     */   }
/*     */ 
/*     */   public String getFareAmount()
/*     */   {
/* 121 */     return this.fareAmount;
/*     */   }
/*     */ 
/*     */   public void setFareAmount(String fareAmount)
/*     */   {
/* 128 */     this.fareAmount = fareAmount;
/*     */   }
/*     */ 
/*     */   public String getFareCurrency()
/*     */   {
/* 135 */     return this.fareCurrency;
/*     */   }
/*     */ 
/*     */   public void setFareCurrency(String fareCurrency)
/*     */   {
/* 142 */     this.fareCurrency = fareCurrency;
/*     */   }
/*     */ 
/*     */   public String getAndOrIndicator()
/*     */   {
/* 149 */     return this.andOrIndicator;
/*     */   }
/*     */ 
/*     */   public void setAndOrIndicator(String andOrIndicator)
/*     */   {
/* 156 */     this.andOrIndicator = andOrIndicator;
/*     */   }
/*     */ 
/*     */   public String getMileageFee()
/*     */   {
/* 163 */     return this.mileageFee;
/*     */   }
/*     */ 
/*     */   public void setMileageFee(String mileageFee)
/*     */   {
/* 170 */     this.mileageFee = mileageFee;
/*     */   }
/*     */ 
/*     */   public String getFareIndex()
/*     */   {
/* 177 */     return this.fareIndex;
/*     */   }
/*     */ 
/*     */   public void setFareIndex(String fareIndex)
/*     */   {
/* 184 */     this.fareIndex = fareIndex;
/*     */   }
/*     */ 
/*     */   public Commission getCommission()
/*     */   {
/* 191 */     return this.commission;
/*     */   }
/*     */ 
/*     */   public void setCommission(Commission commission)
/*     */   {
/* 198 */     this.commission = commission;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatInfo
 * JD-Core Version:    0.6.0
 */