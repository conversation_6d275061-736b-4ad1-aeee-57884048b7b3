/*     */ package com.travelsky.hub.model.peentity.meal.output;
/*     */ 
/*     */ public class QueryMealInventoryRS extends BaseRS
/*     */ {
/*     */   private static final long serialVersionUID = -4196835686808739815L;
/*     */   private String ocAirlineID;
/*     */   private String ocFlightNumber;
/*     */   private String ocFlightSuffix;
/*     */   private String flightDate;
/*     */   private SegmentInfo segmentInfo;
/*     */ 
/*     */   public String getOcFlightNumber()
/*     */   {
/*  30 */     return this.ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public String getOcFlightSuffix()
/*     */   {
/*  45 */     return this.ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public void setOcFlightSuffix(String ocFlightSuffix)
/*     */   {
/*  53 */     this.ocFlightSuffix = ocFlightSuffix;
/*     */   }
/*     */ 
/*     */   public String getOcAirlineID()
/*     */   {
/*  61 */     return this.ocAirlineID;
/*     */   }
/*     */ 
/*     */   public void setOcFlightNumber(String ocFlightNumber)
/*     */   {
/*  68 */     this.ocFlightNumber = ocFlightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  75 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public SegmentInfo getSegmentInfo()
/*     */   {
/*  83 */     return this.segmentInfo;
/*     */   }
/*     */ 
/*     */   public void setOcAirlineID(String ocAirlineID)
/*     */   {
/*  90 */     this.ocAirlineID = ocAirlineID;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  97 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setSegmentInfo(SegmentInfo segmentInfo)
/*     */   {
/* 104 */     this.segmentInfo = segmentInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.QueryMealInventoryRS
 * JD-Core Version:    0.6.0
 */