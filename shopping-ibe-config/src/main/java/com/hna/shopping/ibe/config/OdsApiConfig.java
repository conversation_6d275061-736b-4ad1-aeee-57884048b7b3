package com.hna.shopping.ibe.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * ODS API 配置类
 * 确保 odsapi.properties 配置文件被正确加载
 */
@Configuration
@PropertySource(value = {"classpath:odsapi.properties"}, ignoreResourceNotFound = true)
@ConfigurationProperties(prefix = "odsapi")
public class OdsApiConfig {
    
    private String url;
    private String serialization;
    private Long timeout;
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getSerialization() {
        return serialization;
    }
    
    public void setSerialization(String serialization) {
        this.serialization = serialization;
    }
    
    public Long getTimeout() {
        return timeout;
    }
    
    public void setTimeout(Long timeout) {
        this.timeout = timeout;
    }
}
