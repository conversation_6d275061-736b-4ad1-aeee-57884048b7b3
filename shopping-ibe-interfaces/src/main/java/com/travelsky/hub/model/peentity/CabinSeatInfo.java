/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class CabinSeatInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String toCabin;
/*    */   private String reseatNum;
/*    */ 
/*    */   public String getToCabin()
/*    */   {
/* 22 */     return this.toCabin;
/*    */   }
/*    */ 
/*    */   public void setToCabin(String toCabin)
/*    */   {
/* 29 */     this.toCabin = toCabin;
/*    */   }
/*    */ 
/*    */   public String getReseatNum()
/*    */   {
/* 36 */     return this.reseatNum;
/*    */   }
/*    */ 
/*    */   public void setReseatNum(String reseatNum)
/*    */   {
/* 43 */     this.reseatNum = reseatNum;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.CabinSeatInfo
 * JD-Core Version:    0.6.0
 */