/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatFlagOutBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6876527929686352846L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String planeType;
/*     */   private String version;
/*     */   private String saleFlag;
/*     */   private String cancelFlag;
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  39 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  46 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  54 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  61 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  68 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  75 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getVersion()
/*     */   {
/*  82 */     return this.version;
/*     */   }
/*     */ 
/*     */   public void setVersion(String version)
/*     */   {
/*  89 */     this.version = version;
/*     */   }
/*     */ 
/*     */   public String getSaleFlag()
/*     */   {
/*  96 */     return this.saleFlag;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/* 103 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/* 110 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public void setSaleFlag(String saleFlag)
/*     */   {
/* 117 */     this.saleFlag = saleFlag;
/*     */   }
/*     */ 
/*     */   public String getCancelFlag()
/*     */   {
/* 124 */     return this.cancelFlag;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 131 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 138 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setCancelFlag(String cancelFlag)
/*     */   {
/* 145 */     this.cancelFlag = cancelFlag;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 153 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 160 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatFlagOutBean
 * JD-Core Version:    0.6.0
 */