/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class DelPsrInfoOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8161762013870645770L;
/*    */   private String resultCode;
/*    */   private String resultList;
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 31 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultList(String resultList)
/*    */   {
/* 41 */     this.resultList = resultList;
/*    */   }
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 47 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public String getResultList()
/*    */   {
/* 54 */     return this.resultList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DelPsrInfoOutput
 * JD-Core Version:    0.6.0
 */