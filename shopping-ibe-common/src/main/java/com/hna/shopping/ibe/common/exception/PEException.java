package com.hna.shopping.ibe.common.exception;

import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.responsecode.CodeEnum;
import com.hna.shopping.ibe.common.responsecode.CodePE;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
public class PEException extends RuntimeException {

    private String msg;
    private long code;



}