package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;
@Data
@ApiModel
public abstract class PNRObject implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1347761786645766406L;
	private String textInPNR = "N/A";                                                    
               
	                                                                                     
	public abstract int getIndex();                                                      
	                                                                                     
	                                                                             
	                                                                                     
//	public String getPsgrid()                                                            
//	  throws Exception, UnsupportedOperationException                                    
//	{                                                                                    
//	  //throw new UnsupportedOperationException("这个类型的条目不支持指定适用旅客序号");   
//		return null;
//	}                                                                                    

}
