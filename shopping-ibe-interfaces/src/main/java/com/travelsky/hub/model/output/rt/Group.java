/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Group
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6899671135186999825L;
/*     */   private String overbookInd;
/*     */   private String groupInd;
/*     */   private String bookLockInd;
/*     */   private String groupName;
/*     */   private String acPsgrNo;
/*     */   private String host;
/*     */   private String totalNoSeats;
/*     */ 
/*     */   public String getOverbookInd()
/*     */   {
/*  50 */     return this.overbookInd;
/*     */   }
/*     */ 
/*     */   public void setOverbookInd(String overbookInd)
/*     */   {
/*  57 */     this.overbookInd = overbookInd;
/*     */   }
/*     */ 
/*     */   public String getGroupInd()
/*     */   {
/*  64 */     return this.groupInd;
/*     */   }
/*     */ 
/*     */   public void setGroupInd(String groupInd)
/*     */   {
/*  71 */     this.groupInd = groupInd;
/*     */   }
/*     */ 
/*     */   public String getBookLockInd()
/*     */   {
/*  78 */     return this.bookLockInd;
/*     */   }
/*     */ 
/*     */   public void setBookLockInd(String bookLockInd)
/*     */   {
/*  85 */     this.bookLockInd = bookLockInd;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/*  92 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/*  99 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getAcPsgrNo()
/*     */   {
/* 106 */     return this.acPsgrNo;
/*     */   }
/*     */ 
/*     */   public void setAcPsgrNo(String acPsgrNo)
/*     */   {
/* 113 */     this.acPsgrNo = acPsgrNo;
/*     */   }
/*     */ 
/*     */   public String getHost()
/*     */   {
/* 120 */     return this.host;
/*     */   }
/*     */ 
/*     */   public void setHost(String host)
/*     */   {
/* 127 */     this.host = host;
/*     */   }
/*     */ 
/*     */   public String getTotalNoSeats()
/*     */   {
/* 134 */     return this.totalNoSeats;
/*     */   }
/*     */ 
/*     */   public void setTotalNoSeats(String totalNoSeats)
/*     */   {
/* 141 */     this.totalNoSeats = totalNoSeats;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Group
 * JD-Core Version:    0.6.0
 */