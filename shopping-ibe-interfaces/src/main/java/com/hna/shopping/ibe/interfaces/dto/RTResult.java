package com.hna.shopping.ibe.interfaces.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;

import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class RTResult extends BaseResponse {
public static final int ET_FLAG = 2;
	/**
	 * 
	 */
	private static final long serialVersionUID = -3501458655111227197L;
	private int flag = 0;
	

	private boolean blockSeatPnr = false;
	
	private String blockSeatType = "";
	private boolean group;
	private String groupname;
	private int passengerNumber;
	private int groupNumber;
	private String pnrcode;
	private List<PNRSegment> airSegs = Lists.newArrayList();
	private List<PNRContact> contacts = Lists.newArrayList();
	private List<PNRInfant> infants = Lists.newArrayList();
	private List<PNROSI> osis = Lists.newArrayList();
	private List<PNRPassenger> passengers = Lists.newArrayList();
	private PNRResp resp;
	private List<PNRRMK> rmks = Lists.newArrayList();
	private List<PNRSSR> ssrs = Lists.newArrayList();
	private List<PNRTkt> tkts = Lists.newArrayList();
	private List<PNRTktNo> tktnos = Lists.newArrayList();
	private List<PNROther> others = Lists.newArrayList();
//	private List bas;
	private List<PNRFC> fcs = Lists.newArrayList();
	private String OringinalRT;
	private List<PNRFN> fns = Lists.newArrayList();
	private List<PNRFP> fps = Lists.newArrayList();
	private List<PNREI> eis = Lists.newArrayList();
//	private List<pnroi> ois = Lists.newArrayList();
	private List<PNRTC> tcs = Lists.newArrayList();
	private List<PNRAuxiliaryService> auxiliary = Lists.newArrayList();
//	List<PNROP> ops = new ArrayList();
	
	private String dataProvider = "unknown";
	
	private int pnrver = 0;
	private Date createTime;
//	private ArrayList<PNRObjectInterface> pnrobjects = new ArrayList();
//	
//	public List<PNROP> getOps()
//	{
//	  return this.ops;
//	}
	
//	public int getOpsCount()
//	{
//	  return ((this.ops == null) ? 0 : this.ops.size()); }
//	
//	public PNROP getOpAt(int i) {
//	  return (((this.ops == null) || (i < 0) || (i >= this.ops.size())) ? null : (PNROP)this.ops.get(i)); }
	

	
	public int getAuxiliaryServicesCount() {
	  if (this.auxiliary == null)
	    return 0;
	  return this.auxiliary.size(); }
	
	public PNRAuxiliaryService getAuxiliaryServiceAt(int i) {
	  if ((i >= 0) && (i < this.auxiliary.size())) {
	    return ((PNRAuxiliaryService)this.auxiliary.get(i));
	  }
	  return null;
	}
	
//	public RTResult()
//	{
//	  this.group = false;
//	  this.groupname = null;
//	  this.passengerNumber = 0;
//	  this.groupNumber = 0;
//	  this.pnrcode = "";
//	  this.airSegs = new Vector();
//	  this.contacts = new Vector();
//	  this.infants = new Vector();
//	  this.osis = new Vector();
//	  this.passengers = new Vector();
//	  this.resp = new PNRResp();
//	  this.rmks = new Vector();
//	  this.ssrs = new Vector();
//	  this.tkts = new Vector();
//	  this.tktnos = new Vector();
//	  this.others = new Vector();
//	  this.fcs = new ArrayList();
//	  this.fns = new ArrayList();
//	  this.bas = new Vector();
//	  this.fps = new ArrayList();
//	  this.eis = new ArrayList();
//	  this.tcs = new ArrayList();
//	  this.ois = new ArrayList();
//	  this.auxiliary = new ArrayList();
//	}
	
	
	
	public int getAirSegsCount()
	{
	  return this.airSegs.size();
	}
	
	public PNRSegment getAirSegAt(int n)
	{
	  if (n <= this.airSegs.size() - 1) {
	    return ((PNRSegment)this.airSegs.get(n));
	  }
	  return null;
	}
	
	
	
//	public String toString()
//	{
//	  try
//	  {
//	    int i;
//
//	    StringBuffer strtmp = new StringBuffer();
//	    if (this == null) {
//	      strtmp.append("空pnr\n");
//	      return strtmp.toString();
//	    }
//	    if ((this.flag & 0x2) == 2)
//	      strtmp.append("ET PNR\r\n");
//	    strtmp.append("PNRNO:" + getPnrcode() + "\n");
//	    strtmp.append("\n");
//	    if (!(isBlockSeatPnr())) {
//	      strtmp.append("团体票标识:");
//	      if (isGroup()) {
//	        strtmp.append("团体票   ");
//	        strtmp.append("团名:");
//	        strtmp.append((getGroupname() == null) ? "" : getGroupname());
//	        strtmp.append("   团体人数:");
//	        strtmp.append(getGroupNumber());
//	      } else {
//	        strtmp.append("散客票\n");
//	      }
//	    } else {
//	      strtmp.append("BLOCK SEAT PNR 类型：" + this.blockSeatType + "\n");
//	    }
//	
//	    strtmp.append("\n姓名组\n");
//	    List<PNRPassenger> tmp = getPassengers();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRPassenger p = (PNRPassenger)tmp.get(i);
//	        strtmp.append("." + p.getIndex() + "." + p.getName());
//	        if (p.getType() == 0)
//	          strtmp.append("  成人  " + p.getNameInPnr() + "\n");
//	        else if (p.getType() == 1)
//	          strtmp.append("  儿童  " + p.getNameInPnr() + "\n");
//	        else if (p.getType() == 2)
//	          strtmp.append("  无人陪伴旅客  " + p.getNameInPnr() + "  年龄: " + p.getAge() + "\n");
//	      }
//	    strtmp.append("\n航段组\n");
//	    tmp = getAirSegs();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRSegment a = (PNRSegment)tmp.get(i);
//	        strtmp.append(".");
//	
//	        strtmp.append(a.toString() + "\n");
//	      }
////	    strtmp.append("\nOP组(OPTION ELEMENT)\n");
////	    List tmp1 = getOps();
////	    if (tmp1.size() == 0)
////	      strtmp.insert(strtmp.length() - 1, ":无");
////	    else
////	      for (i = 0; i < tmp1.size(); ++i) {
////	        PNROP c = (PNROP)tmp1.get(i);
////	        strtmp.append(i + 1);
////	        strtmp.append(".");
////	        strtmp.append(c.toString() + "\n");
////	      }
//	    strtmp.append("\n联系组\n");
//	    tmp = getContacts();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        PNRContact c = (PNRContact)tmp.get(i);
//	        strtmp.append(i + 1);
//	        strtmp.append("." + c.getIndex() + '.');
//	        strtmp.append(c.getCity() + " " + c.getContact() + "\n");
//	      }
//	    }
//	    strtmp.append("\n出票组\n");
//	    tmp = getTkts();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRTkt t = (PNRTkt)tmp.get(i);
//	        strtmp.append("." + t.toString());
//	      }
//	    strtmp.append("\nFC组\n");
//	    List l = this.fcs;
//	    if (l.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < l.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRFC o = getFCAt(i);
//	        strtmp.append("." + o.makeString() + "\n");
//	      }
//	    }
////	    strtmp.append("\n开账地址组（BA）\n");
////	    tmp = getBas();
////	    if (tmp.size() == 0)
////	      strtmp.insert(strtmp.length() - 1, ":无");
////	    else {
////	      for (i = 0; i < tmp.size(); ++i) {
////	        PNRBA b = (PNRBA)tmp.get(i);
////	        strtmp.append(String.valueOf(i + 1) + ".");
////	        strtmp.append(b.toString() + "\n");
////	      }
////	    }
//	    strtmp.append("\n辅助服务组\n");
//	    List temp = getAuxiliary();
//	    if (temp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < temp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRAuxiliaryService s = (PNRAuxiliaryService)temp.get(i);
//	        strtmp.append("." + s.getLineIndex() + "." + s.toString() + " " + s.getPsgrid() + "\n");
//	      }
//	    strtmp.append("\n特别服务组\n");
//	    tmp = getSsrs();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRSSR s = (PNRSSR)tmp.get(i);
//	        strtmp.append("." + s.getIndex() + "." + s.toString() + " " + s.getPsgrID() + "\n");
//	      }
//	    }
//	    strtmp.append("\n其他服务组\n");
//	    tmp = getOsis();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNROSI o = (PNROSI)tmp.get(i);
//	        strtmp.append("." + o.getIndex() + "." + o.getOsi() + " " + o.getPNum() + "\n");
//	      }
//	    }
//	    strtmp.append("\n备注组\n");
//	    tmp = getRmks();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRRMK r = (PNRRMK)tmp.get(i);
//	        strtmp.append("." + r.getIndex() + "." + r.getRmkinfo() + " " + r.getPsgrID() + "\n");
//	      }
//	    }
//	    strtmp.append("\nFN组\n");
//	    l = this.fns;
//	    if (l.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < l.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRFN o = getFNAt(i);
//	        strtmp.append("." + o.makeString() + "\n");
//	      }
//	    }
//	    strtmp.append("\nTC组\n");
//	    l = getTcs();
//	    if (l.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < l.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRTC o = getTcAt(i);
//	        strtmp.append("." + o.toString() + "\n");
//	      }
//	    }
////	    strtmp.append("\nOI组\n");
////	    l = getOis();
////	    if (l.size() == 0)
////	      strtmp.insert(strtmp.length() - 1, ":无");
////	    else {
////	      for (i = 0; i < l.size(); ++i) {
////	        strtmp.append(i + 1);
////	        PNROI o = getOiAt(i);
////	        strtmp.append("." + o.toString() + "\n");
////	      }
////	    }
//	    strtmp.append("\n票号组\n");
//	    tmp = getTktnos();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < tmp.size(); ++i) {
//	        PNRTktNo t = (PNRTktNo)tmp.get(i);
//	        strtmp.append(i + 1);
//	        strtmp.append("." + t.getIndex() + '.');
//	        strtmp.append(t.getTktNo() + " " + t.getPsgrID() + " " + t.getRemark() + "\n");
//	      }
//	    strtmp.append("\nEI组\n");
//	    l = getEis();
//	    if (l.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < l.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNREI o = getEiAt(i);
//	        strtmp.append("." + o.toString() + "\n");
//	      }
//	    strtmp.append("\nFP组\n");
//	    l = getFps();
//	    if (l.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else
//	      for (i = 0; i < l.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRFP o = getFpAt(i);
//	        strtmp.append("." + o.toString() + "\n");
//	      }
//	    strtmp.append("\n婴儿组\n");
//	    tmp = getInfants();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNRInfant f = (PNRInfant)tmp.get(i);
//	        strtmp.append("." + f.getIndex() + ".姓名：" + f.getName() + "  出生日期：" + f.getBrithString() + "  携带：" + f.getCarrier() + "  PNR显示名:" + f.getNameInPnr() + "\n");
//	      }
//	    }
//	
//	    strtmp.append("\n其他\n");
//	    tmp = getOthers();
//	    if (tmp.size() == 0)
//	      strtmp.insert(strtmp.length() - 1, ":无");
//	    else {
//	      for (i = 0; i < tmp.size(); ++i) {
//	        strtmp.append(i + 1);
//	        PNROther o = (PNROther)tmp.get(i);
//	        strtmp.append("." + o.toString() + "\n");
//	      }
//	    }
//	    strtmp.append("\n责任组\n");
//	    PNRResp r = getResp();
//	    if (r.getOfficecode().equals(""))
//	      strtmp.append("1." + r.getIndex() + "." + r.getCrssign() + " " + r.getPnrno() + " " + r.getRemark() + "\n");
//	    else {
//	      strtmp.append("1." + r.getIndex() + "." + r.getCrssign() + " " + r.getPnrno() + " " + r.getOfficecode() + "\n");
//	    }
//	    strtmp.append("FROM:" + this.dataProvider + "\r\n");
//	    if (getPnrver() > 0) {
//	      strtmp.append("lastenv:" + this.pnrver + "\r\n\r\n");
//	    }
//	    if (getCreateTime() != null)
//	      strtmp.append("created at:" + getCreateTime() + "\r\n");
//	    return strtmp.toString(); } catch (Exception e) {
//	  }
//	  return super.toString();
//	}
	
	public PNRContact getContactAt(int n)
	{
	  if (n <= this.contacts.size() - 1) {
	    return ((PNRContact)this.contacts.get(n));
	  }
	  return null;
	}
	
	public int getContactsCount()
	{
	  return this.contacts.size();
	}
	
	public PNRInfant getInfantAt(int n)
	{
	  if (n <= this.infants.size() - 1) {
	    return ((PNRInfant)this.infants.get(n));
	  }
	  return null;
	}
	
	public int getInfantsCount()
	{
	  return this.infants.size();
	}
	
	public PNROSI getOSIAt(int n)
	{
	  if (n <= this.osis.size() - 1) {
	    return ((PNROSI)this.osis.get(n));
	  }
	  return null;
	}
	
	public int getOSIsCount()
	{
	  return this.osis.size();
	}
	
	public PNROther getOtherAt(int n)
	{
	  if (n <= this.others.size() - 1) {
	    return ((PNROther)this.others.get(n));
	  }
	  return null;
	}
	
	public int getOthersCount()
	{
	  return this.others.size();
	}
	
	public PNRPassenger getPassengerAt(int n)
	{
	  if (n <= this.passengers.size() - 1) {
	    return ((PNRPassenger)this.passengers.get(n));
	  }
	  return null;
	}
	
	public int getPassengersCount()
	{
	  return this.passengers.size();
	}
	
	public PNRRMK getRMKAt(int n)
	{
	  if (n <= this.rmks.size() - 1) {
	    return ((PNRRMK)this.rmks.get(n));
	  }
	  return null;
	}
	
	public int getRMKsCount()
	{
	  return this.rmks.size();
	}
	
	public PNRSSR getSSRAt(int n)
	{
	  if (n <= this.ssrs.size() - 1) {
	    return ((PNRSSR)this.ssrs.get(n));
	  }
	  return null;
	}
	
	public int getSSRsCount()
	{
	  return this.ssrs.size();
	}
	
	public PNRTkt getTktAt(int n)
	{
	  if (n <= this.tkts.size() - 1) {
	    return ((PNRTkt)this.tkts.get(n));
	  }
	  return null;
	}
	
	public PNRTktNo getTktnoAt(int n)
	{
	  if (n <= this.tktnos.size() - 1) {
	    return ((PNRTktNo)this.tktnos.get(n));
	  }
	  return null;
	}
	
	public int getTktnosCount()
	{
	  return this.tktnos.size();
	}
	
	public int getTktsCount()
	{
	  return this.tkts.size();
	}
	
//	public Vector getBas()
//	{
//	  return this.bas;
//	}
//	
//	public int getBasCount()
//	{
//	  return this.bas.size();
//	}
	
//	public PNRBA getBaAt(int i)
//	{
//	  if ((i < this.bas.size()) && (i >= 0)) {
//	    return ((PNRBA)this.bas.get(i));
//	  }
//	  return null;
//	}
//	
	
	
	public PNRFC getFCAt(int i)
	{
	  if ((this.fcs.size() <= i) || (i < 0))
	    return null;
	  return ((PNRFC)this.fcs.get(i)); }
	
	public int getFCsCount() {
	  return this.fcs.size();
	}
	
	public PNRFN getFNAt(int i)
	{
	  if ((this.fns.size() <= i) || (i < 0))
	    return null;
	  return ((PNRFN)this.fns.get(i)); }
	
	public int getFNsCount() {
	  return this.fns.size(); }
	
	public PNREI getEiAt(int i) {
	  if ((i < 0) || (i >= this.eis.size()))
	    return null;
	  return ((PNREI)this.eis.get(i));
	}
	

	
	public PNRFP getFpAt(int i) {
	  if ((i < 0) || (i >= this.fps.size()))
	    return null;
	  return ((PNRFP)this.fps.get(i));
	}
	

	
//	public PNROI getOiAt(int i) {
//	  if ((i < 0) || (i >= this.ois.size()))
//	    return null;
//	  return ((PNROI)this.ois.get(i));
//	}
	

	public PNRTC getTcAt(int i) {
	  if ((i < 0) || (i >= this.tcs.size()))
	    return null;
	  return ((PNRTC)this.tcs.get(i));
	}
	

	
	public int getTcsCount() {
	  return this.tcs.size(); }
	
//	public int getOisCount() {
//	  return this.ois.size();
//	}
	
	public int getEisCount() {
	  return this.eis.size(); }
	
	public int getFpsCount() {
	  return this.fps.size(); }
	


	
	public String getBlockSeatType()
	{
	  if (!(isBlockSeatPnr()))
	    return "";
	  return this.blockSeatType; }
	
	
	

	public ArrayList getPaxByPsgrid(String iid) throws Exception {
	  if ((iid == null) || (iid.length() == 0))
	    return null;
	  String id = iid;
	  if (id.startsWith("P")) {
	    id = id.substring(1);
	  }
	  String[] ids = id.split("/");
	  ArrayList retVal = new ArrayList();
	  for (int i = 0; i < ids.length; ++i) {
	    String theid = ids[i];
	    int idx = 0;
	    try {
	      idx = Integer.parseInt(theid.trim());
	    } catch (Exception e) {
	      throw new Exception(theid + " is not valid psgrid. Input is " + iid);
	    }
	    PNRPassenger pax = getPassengerAt(idx - 1);
	    if (pax == null)
	      throw new Exception("pax " + idx + " not exists in pnr:" + this + " Input is " + iid);
	    retVal.add(pax);
	  }
	  return retVal;
	}
	

}
