/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ 
/*     */ public class SeatChartResultBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -2635169093551229993L;
/*     */   private String seatMap;
/*     */   private String planeClass;
/*     */   private String planeType;
/*     */   private Map<String, List<String>> seatsMap;
/*     */   private String originalSeatMap;
/*     */   private String flightLayout;
/*     */ 
/*     */   public String getFlightLayout()
/*     */   {
/*  55 */     return this.flightLayout;
/*     */   }
/*     */ 
/*     */   public void setFlightLayout(String flightLayout)
/*     */   {
/*  62 */     this.flightLayout = flightLayout;
/*     */   }
/*     */ 
/*     */   public String getSeatMap()
/*     */   {
/*  70 */     return this.seatMap;
/*     */   }
/*     */ 
/*     */   public void setSeatMap(String seatMap)
/*     */   {
/*  77 */     this.seatMap = seatMap;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/*  84 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/*  91 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getPlaneClass()
/*     */   {
/*  98 */     return this.planeClass;
/*     */   }
/*     */ 
/*     */   public void setPlaneClass(String planeClass)
/*     */   {
/* 105 */     this.planeClass = planeClass;
/*     */   }
/*     */ 
/*     */   public Map<String, List<String>> getSeatsMap()
/*     */   {
/* 112 */     return this.seatsMap;
/*     */   }
/*     */ 
/*     */   public void setSeatsMap(Map<String, List<String>> seatsMap)
/*     */   {
/* 119 */     this.seatsMap = seatsMap;
/*     */   }
/*     */ 
/*     */   public String getOriginalSeatMap()
/*     */   {
/* 126 */     return this.originalSeatMap;
/*     */   }
/*     */ 
/*     */   public void setOriginalSeatMap(String originalSeatMap)
/*     */   {
/* 133 */     this.originalSeatMap = originalSeatMap;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatChartResultBean
 * JD-Core Version:    0.6.0
 */