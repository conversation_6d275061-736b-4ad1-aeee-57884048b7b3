/*     */ package com.travelsky.hub.model.peentity.reserve.create.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class UpgInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8841419416965437962L;
/*     */   private String seatNumber;
/*     */   private String cabin;
/*     */   private String seniorSeatNumber;
/*     */   private String seniorCabin;
/*     */   private String seniorRBD;
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/*  54 */     this.cabin = cabin;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/*  61 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/*  68 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  75 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeniorCabin(String seniorCabin)
/*     */   {
/*  82 */     this.seniorCabin = seniorCabin;
/*     */   }
/*     */ 
/*     */   public String getSeniorCabin()
/*     */   {
/*  89 */     return this.seniorCabin;
/*     */   }
/*     */ 
/*     */   public void setSeniorSeatNumber(String seniorSeatNumber)
/*     */   {
/*  96 */     this.seniorSeatNumber = seniorSeatNumber;
/*     */   }
/*     */ 
/*     */   public String getSeniorSeatNumber()
/*     */   {
/* 103 */     return this.seniorSeatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeniorRBD(String seniorRBD)
/*     */   {
/* 110 */     this.seniorRBD = seniorRBD;
/*     */   }
/*     */ 
/*     */   public String getSeniorRBD()
/*     */   {
/* 117 */     return this.seniorRBD;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.reserve.create.input.UpgInfo
 * JD-Core Version:    0.6.0
 */