package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import lombok.Data;

/**
 * Zero
 * 2022/4/24 16:59
 **/
@Data
public class SeatReleaseRQ extends BaseRequest{
    private String flightNumber;
    private String fromCity;
    private String toCity;
    private String flightDate;
    private String seatNumber;
    private String flightClass;
    private String seatType;//座位类型ASR/CRS/ALL ， 默认为 ALL

    private String pnrNo;
    private String passengerName;
    private boolean onlyRelease;
}
