/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class BagTagsInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1174111456182947716L;
/*     */   private String baggageType;
/*     */   private String airlineNumber;
/*     */   private String bagTag;
/*     */   private String baggageDestination;
/*     */   private String isDeleted;
/*     */ 
/*     */   public String getBaggageType()
/*     */   {
/*  45 */     return this.baggageType;
/*     */   }
/*     */ 
/*     */   public void setBaggageType(String baggageType)
/*     */   {
/*  52 */     this.baggageType = baggageType;
/*     */   }
/*     */ 
/*     */   public String getAirlineNumber()
/*     */   {
/*  59 */     return this.airlineNumber;
/*     */   }
/*     */ 
/*     */   public void setAirlineNumber(String airlineNumber)
/*     */   {
/*  66 */     this.airlineNumber = airlineNumber;
/*     */   }
/*     */ 
/*     */   public String getBagTag()
/*     */   {
/*  73 */     return this.bagTag;
/*     */   }
/*     */ 
/*     */   public void setBagTag(String bagTag)
/*     */   {
/*  80 */     this.bagTag = bagTag;
/*     */   }
/*     */ 
/*     */   public String getBaggageDestination()
/*     */   {
/*  87 */     return this.baggageDestination;
/*     */   }
/*     */ 
/*     */   public void setBaggageDestination(String baggageDestination)
/*     */   {
/*  94 */     this.baggageDestination = baggageDestination;
/*     */   }
/*     */ 
/*     */   public String getIsDeleted()
/*     */   {
/* 101 */     return this.isDeleted;
/*     */   }
/*     */ 
/*     */   public void setIsDeleted(String isDeleted)
/*     */   {
/* 108 */     this.isDeleted = isDeleted;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.BagTagsInfo
 * JD-Core Version:    0.6.0
 */