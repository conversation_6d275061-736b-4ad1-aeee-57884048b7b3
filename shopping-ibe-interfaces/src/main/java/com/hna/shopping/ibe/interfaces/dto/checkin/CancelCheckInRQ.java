package com.hna.shopping.ibe.interfaces.dto.checkin; /**
 * <AUTHOR>
 * @date 2020/10/29
 */

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class CancelCheckInRQ extends BaseRequest implements Serializable {
    private String flightDate;
    private String flightNo;
    private String certId;//票号
    private String certType = "NI";//TN
    private String fromCity;
    private String toCity;
    private String checkCode;
    private String outboundEtCode;
}
