/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.common.exception;

import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import com.hna.shopping.ibe.common.responsecode.CodePE;
import com.hna.shopping.ibe.common.responsecode.RestResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
//import javax.validation.ConstraintViolation;
//import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    /**
     * PE异常
     */
    @ExceptionHandler(value = PEException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, PEException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(CodePE.INVOKE_PE_ERROR);
        exception.setMessage(e.getMsg());
        exception.setCode(e.getCode());
        log.error("PEException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

    /**
     * 系统自定义全局异常
     *
     * @param req
     * @param e
     * @return
     * @throws Exception
     */
    @ExceptionHandler(value = GlobalException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, GlobalException e) {
        RestResponse exception;
        if (StringUtils.isNotEmpty(e.getMessage())) {
            exception = RestResponse.exceptionV2(e.getCodeEnum(), e.getMessage());
        } else {
            exception = RestResponse.exception(e.getCodeEnum());
        }
        log.error("GlobalException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

    /**
     * controller 参数转化时, 主要从这里捕获错误信息
     */
    @ExceptionHandler(value = HttpMessageNotReadableException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, HttpMessageNotReadableException e)
            throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
        log.error("HttpMessageNotReadableException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

    /**
     * 这个兜底
     *
     * @param req
     * @param e
     * @return
     * @throws Exception
     */
    @ExceptionHandler(value = RuntimeException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, RuntimeException e) throws Exception {
        RestResponse exception = RestResponse.exception(CodeDefault.INTERNAL_SERVER_ERROR);
        log.error("RuntimeException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, ConstraintViolationException e) {
        for (ConstraintViolation<?> constraintViolation : e.getConstraintViolations()) {
            if (constraintViolation.getMessage() != null){
                RestResponse exception = RestResponse.exceptionV2(CodeDefault.ILLEGAL_ARGUMENT, constraintViolation.getMessage());
                log.error("ConstraintViolationException: {}, error!", JSONObject.toJSONString(exception), e);
                return exception;
            }
        }
        RestResponse exception = RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT);
        log.error("ConstraintViolationException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public RestResponse exception(HttpServletRequest req, MethodArgumentNotValidException e) throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("data", e.getLocalizedMessage());
        RestResponse exception = RestResponse.exception(CodeDefault.ILLEGAL_ARGUMENT, map);
        log.error("MethodArgumentNotValidException: {}, error!", JSONObject.toJSONString(exception), e);
        return exception;
    }

}
