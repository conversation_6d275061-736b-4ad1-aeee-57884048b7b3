package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.Calendar;

public class AuxiliaryService implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -132762716964608348L;
	String serviceName;                                                  
	protected String act_id;                                             
	protected String actionCode;                                         
	protected int nbr;                                                   
	protected String city;                                               
	protected Calendar inDate;                                           
	protected Calendar outDate;                                          
	protected String bed;                                                
	protected String room;                                               
	protected String hotel;                                              
	protected String text;                                               
	protected String address;                                            
	                                                                     
	public String getServiceName()                                       
	{                                                                    
	  return this.serviceName;                                           
	}                                                                    
	                                                                     
	public void setServiceName(String serviceName)                       
	{                                                                    
	  this.serviceName = serviceName;                                    
	}                                                                    
	                                                                     
	public String getAct_id()                                            
	{                                                                    
	  return this.act_id; }                                              
	                                                                     
	public void setAct_id(String act_id) {                               
	  this.act_id = act_id; }                                            
	                                                                     
	public String getActionCode() {                                      
	  return this.actionCode; }                                          
	                                                                     
	public void setActionCode(String actionCode) {                       
	  this.actionCode = actionCode; }                                    
	                                                                     
	public int getNbr() {                                                
	  return this.nbr; }                                                 
	                                                                     
	public void setNbr(int nbr) {                                        
	  this.nbr = nbr; }                                                  
	                                                                     
	public String getCity() {                                            
	  return this.city;                                                  
	}                                                                    
	                                                                     
	public void setCity(String city)                                     
	{                                                                    
	  this.city = city; }                                                
	                                                                     
	public Calendar getInDate() {                                        
	  return this.inDate;                                                
	}                                                                    
	                                                                     
	public void setInDate(Calendar inDate)                               
	{                                                                    
	  this.inDate = inDate;                                              
	}                                                                    
	                                                                     
	public Calendar getOutDate() {                                       
	  return this.outDate;                                               
	}                                                                    
	                                                                     
	public void setOutDate(Calendar outDate)                             
	{                                                                    
	  this.outDate = outDate; }                                          
	                                                                     
	public String getBed() {                                             
	  return this.bed;                                                   
	}                                                                    
	                                                                     
	public void setBed(String bed)                                       
	{                                                                    
	  this.bed = bed; }                                                  
	                                                                     
	public String getRoom() {                                            
	  return this.room;                                                  
	}                                                                    
	                                                                     
	public void setRoom(String room)                                     
	{                                                                    
	  this.room = room; }                                                
	                                                                     
	public String getHotel() {                                           
	  return this.hotel;                                                 
	}                                                                    
	                                                                     
	public void setHotel(String hotel)                                   
	{                                                                    
	  this.hotel = hotel;                                                
	}                                                                    
	                                                                     
	public String getText() {                                            
	  return this.text;                                                  
	}                                                                    
	                                                                     
	public void setText(String text)                                     
	{                                                                    
	  this.text = text; }                                                
	                                                                     
	public String getAddress() {                                         
	  return this.address;                                               
	}                                                                    
	                                                                     
	public void setAddress(String address)                               
	{                                                                    
	  this.address = address;                                            
	}                                                                    
	                                                                     
	public String toString()                                             
	{                                                                    
	  StringBuffer sb = new StringBuffer("Auxiliary Service:");          
	  try                                                                
	  {                                                                  
	    sb.append(getServiceName());                                     
	    sb.append(" ");                                                  
	    sb.append(this.act_id);                                          
	    sb.append(" ");                                                  
	    sb.append(this.actionCode);                                      
	    sb.append(this.nbr);                                             
	    if (this.inDate != null) {                                       
	      sb.append(" indate:");                                         
	      sb.append(QDateTime.calendarToString(this.inDate, "ddmmmyy")); 
	    }                                                                
	    if (this.outDate != null) {                                      
	      sb.append(" outdate:");                                        
	      sb.append(QDateTime.calendarToString(this.outDate, "ddmmmyy"));
	    }                                                                
	    if (this.bed != null) {                                          
	      sb.append(" bed:");                                            
	      sb.append(this.bed);                                           
	    }                                                                
	    if (this.room != null) {                                         
	      sb.append(" room:");                                           
	      sb.append(this.room);                                          
	    }                                                                
	    if (this.hotel != null) {                                        
	      sb.append(" hotel:");                                          
	      sb.append(this.hotel);                                         
	    }                                                                
	    if (this.text != null) {                                         
	      sb.append(" text:");                                           
	      sb.append(this.text);                                          
	    }                                                                
	    if (this.address != null) {                                      
	      sb.append(" address:");                                        
	      sb.append(this.address);                                       
	    }                                                                
	  } catch (Exception e) {                                            
	    sb.append("\r\ntoString truncated because " + e);                
	  }                                                                  
	  return sb.toString();                                              
	}                                                                    

}
