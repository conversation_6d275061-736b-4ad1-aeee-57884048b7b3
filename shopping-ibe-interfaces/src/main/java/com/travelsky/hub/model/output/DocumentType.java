/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class DocumentType
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9114835767765043294L;
/*    */   private String documentTypeID;
/*    */   private String name;
/*    */   private String description;
/*    */ 
/*    */   public String getDocumentTypeID()
/*    */   {
/* 32 */     return this.documentTypeID;
/*    */   }
/*    */ 
/*    */   public String getName()
/*    */   {
/* 39 */     return this.name;
/*    */   }
/*    */ 
/*    */   public String getDescription()
/*    */   {
/* 46 */     return this.description;
/*    */   }
/*    */ 
/*    */   public void setDocumentTypeID(String documentTypeID)
/*    */   {
/* 53 */     this.documentTypeID = documentTypeID;
/*    */   }
/*    */ 
/*    */   public void setName(String name)
/*    */   {
/* 60 */     this.name = name;
/*    */   }
/*    */ 
/*    */   public void setDescription(String description)
/*    */   {
/* 67 */     this.description = description;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.DocumentType
 * JD-Core Version:    0.6.0
 */