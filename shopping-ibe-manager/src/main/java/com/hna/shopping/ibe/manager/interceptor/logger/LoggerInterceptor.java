package com.hna.shopping.ibe.manager.interceptor.logger;


import com.alibaba.fastjson.JSONObject;
import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.IntStream;

/**
 * 拦截记录日志
 *
 * <AUTHOR>
 * @Date 2019-07-24
 */
@Aspect
@Component
@Slf4j
public class LoggerInterceptor {

    @Pointcut("@within(com.hna.shopping.ibe.manager.interceptor.logger.Logger)")
    public void classLoggers() {
    }

    @Pointcut("@annotation(com.hna.shopping.ibe.manager.interceptor.logger.Logger)")
    public void methodLoggers() {
    }

    /**
     * 拦截器具体实现
     *
     * @param pjp
     * @return
     */
    @Around("classLoggers() || methodLoggers() ")
    public Object interceptor(ProceedingJoinPoint pjp) {
        Object result;
        try {
            MDCPutTracer();
            long startTime = System.currentTimeMillis();
            log.info(pjp.getSignature() + " execute start,uuid:{},params:{},startTime:{}", MDC.get("UUID"), getReqParams(pjp), startTime);
            result = pjp.proceed();
            long endTime = System.currentTimeMillis();
            log.info(pjp.getSignature() + " execute end,uuid:{},result:{},endTime:{},executeTime:{}", MDC.get("UUID"), JSONObject.toJSON(result), endTime, endTime - startTime);
        } catch (GlobalException e) {
            throw e;
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            throw new GlobalException(CodeDefault.INTERNAL_SERVER_ERROR, e);
        }
        return result;
    }

    /**
     * 日志链追踪
     */
    private void MDCPutTracer() {
        String value = Objects.nonNull(MDC.get("UUID")) ? MDC.get("UUID") : UUID.randomUUID().toString().replace("-", "");
        MDC.put("UUID", value);
    }

    /**
     * 获取参数
     *
     * @param pjp
     * @return
     */
    private String getReqParams(ProceedingJoinPoint pjp) {
        Map<String, Object> mapParams = new HashMap<>();
        String[] parameterNames = ((MethodSignature) pjp.getSignature()).getParameterNames();
        if (Objects.nonNull(parameterNames)) {
            IntStream.range(0, parameterNames.length).forEach(
                    i -> {
                        if (!(pjp.getArgs()[i] instanceof HttpServletRequest) && !(pjp.getArgs()[i] instanceof HttpServletResponse)) {
                            mapParams.put(parameterNames[i], pjp.getArgs()[i]);
                        }
                    }
            );
        }
        try {
            return JSONObject.toJSONString(mapParams);
        } catch (Exception e) {
            return mapParams.toString();
        }
    }
}