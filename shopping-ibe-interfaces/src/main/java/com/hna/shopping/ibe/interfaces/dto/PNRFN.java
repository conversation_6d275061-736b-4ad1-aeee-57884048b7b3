package com.hna.shopping.ibe.interfaces.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
@ApiModel
public class PNRFN extends FareBox{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3889140767882199352L;
	int index;                                                                                          
	String psgrid;                                                                                      
	String inputMode = "";                                                                              
	                                                                                                    
	private String textInPNR = "N/A";                                                                   
	                                                                                        
	                                                                                                    
	public String makeString() {                                                                        
	  return super.makeString() + "\r\n输入模式：" + this.inputMode;                                    
	}                                                                                                   
	                                                                                                    
	
}
