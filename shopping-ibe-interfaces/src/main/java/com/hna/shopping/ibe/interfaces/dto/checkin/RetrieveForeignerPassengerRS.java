package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:06
 * @Version 1.0
 */
@Data
public class RetrieveForeignerPassengerRS extends BaseResponse {
    @ApiModelProperty("航空公司代码")
    private String airlineCode;
    @ApiModelProperty("航班编号")
    private String flightNumber;
    @ApiModelProperty("起飞机场")
    private String departureAirport;
    @ApiModelProperty("到达机场")
    private String arrivalAirport;
    @ApiModelProperty("起飞时间 航班日期 DDMMMYY")
    private String departureDate;
    @ApiModelProperty("旅客主机编号")
    private String hostNumber;
    @ApiModelProperty("是否拒绝登机")
    private boolean deniedBoardingVolunteerInd;
    @ApiModelProperty("api信息")
    private ApiInfo apiInfo;

    @Data
    public static class ApiInfo{
        @ApiModelProperty("旅客姓氏")
        private String surName;
        @ApiModelProperty("出生日期 YYMMDD")
        private String birthDate;
        @ApiModelProperty("性别")
        private String gender;
        @ApiModelProperty("是否过站旅客")
        private boolean transferInd;
        @ApiModelProperty("是否为主要护照持有者")
        private boolean primaryHolderInd;
        @ApiModelProperty("证件类型\n" +
                "P：护照\n" +
                "T：台胞证\n" +
                "W：港澳通行证\n" +
                "C：回乡证\n" +
                "D：赴台通行证\n" +
                "A：因公港澳通行证\n" +
                "F：其它证件")
        private String docType;
        @ApiModelProperty("证件持有者所属国家(国家2字码或3字码)(与docIssueCountry一致)")
        private String docHolderNationality;
        @ApiModelProperty("证件有效的截止日期(yymmdd日期格式)")
        private String expireDate;
        @ApiModelProperty("签发国家")
        private String docIssueCountry;
        @ApiModelProperty("旅客名(First Name)")
        private String givenName;
        @ApiModelProperty("中间名")
        private String middleName;
        @ApiModelProperty("出生地")
        private String birthLocation;

        @ApiModelProperty("证件有效的开始日期(YYMMDD日期格式)")
        private String effectiveDate;
        @ApiModelProperty("居住国")
        private String residenceCountry;
        @ApiModelProperty("visa信息")
        private DocInfo visaInfo;
        @ApiModelProperty("其他证件信息")
        private DocInfo otherDocInfo;
        @ApiModelProperty("居住国地址")
        private Address homeAddress;
        @ApiModelProperty("目的地地址")
        private Address destAddress;

        @Data
        public static class DocInfo {
            @ApiModelProperty("签发国家")
            private String issuePlace;
            @ApiModelProperty("证件有效截止日期(YYMMDD格式)")
            private String expireDate;
            @ApiModelProperty("证件有效开始日期(YYMMDD格式)")
            private String effectiveDate;
            @ApiModelProperty("证件类型 A：外交人员 C：过境人员")
            private String type;
            @ApiModelProperty("证件号码")
            private String iD;
        };


        @Data
        public static class Address {
            @ApiModelProperty("街道号码")
            private String streetNmbr;
            @ApiModelProperty("城市名称")
            private String cityName;
            @ApiModelProperty("州")
            private String stateCode;
            @ApiModelProperty("国家")
            private String countryName;
            @ApiModelProperty("邮政编码")
            private String postalCode;
            @ApiModelProperty("电话")
            private String telephone;
        };
    }


}


