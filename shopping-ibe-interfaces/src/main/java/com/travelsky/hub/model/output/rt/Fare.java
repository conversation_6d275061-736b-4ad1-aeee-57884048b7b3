/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class Fare
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2269993576963193067L;
/*    */   private FareCalculation fareCalculation;
/*    */   private List<FareBox> fareBoxList;
/*    */ 
/*    */   public FareCalculation getFareCalculation()
/*    */   {
/* 31 */     return this.fareCalculation;
/*    */   }
/*    */ 
/*    */   public void setFareCalculation(FareCalculation fareCalculation)
/*    */   {
/* 38 */     this.fareCalculation = fareCalculation;
/*    */   }
/*    */ 
/*    */   public List<FareBox> getFareBoxList()
/*    */   {
/* 45 */     return this.fareBoxList;
/*    */   }
/*    */ 
/*    */   public void setFareBoxList(List<FareBox> fareBoxList)
/*    */   {
/* 52 */     this.fareBoxList = fareBoxList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Fare
 * JD-Core Version:    0.6.0
 */