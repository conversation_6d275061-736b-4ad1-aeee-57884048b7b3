/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatPref
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String type;
/*    */   private String prate;
/*    */ 
/*    */   public String getPrate()
/*    */   {
/* 34 */     return this.prate;
/*    */   }
/*    */ 
/*    */   public void setPrate(String prate)
/*    */   {
/* 42 */     this.prate = prate;
/*    */   }
/*    */ 
/*    */   public String getType()
/*    */   {
/* 50 */     return this.type;
/*    */   }
/*    */ 
/*    */   public void setType(String type)
/*    */   {
/* 58 */     this.type = type;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatPref
 * JD-Core Version:    0.6.0
 */