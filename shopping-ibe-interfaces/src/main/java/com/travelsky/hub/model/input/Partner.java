/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Partner
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 19 */   private String hostNumber = "";
/* 20 */   private String ticketID = "";
/* 21 */   private String index = "";
/* 22 */   private String sequenceNumber = "";
/*    */   private InfInfoBean infInfo;
/*    */ 
/*    */   public String getHostNumber()
/*    */   {
/* 33 */     return this.hostNumber;
/*    */   }
/*    */ 
/*    */   public void setHostNumber(String hostNumber)
/*    */   {
/* 40 */     this.hostNumber = hostNumber;
/*    */   }
/*    */ 
/*    */   public String getTicketID()
/*    */   {
/* 47 */     return this.ticketID;
/*    */   }
/*    */ 
/*    */   public void setTicketID(String ticketID)
/*    */   {
/* 54 */     this.ticketID = ticketID;
/*    */   }
/*    */ 
/*    */   public String getIndex()
/*    */   {
/* 61 */     return this.index;
/*    */   }
/*    */ 
/*    */   public void setIndex(String index)
/*    */   {
/* 68 */     this.index = index;
/*    */   }
/*    */ 
/*    */   public String getSequenceNumber()
/*    */   {
/* 75 */     return this.sequenceNumber;
/*    */   }
/*    */ 
/*    */   public void setSequenceNumber(String sequenceNumber)
/*    */   {
/* 82 */     this.sequenceNumber = sequenceNumber;
/*    */   }
/*    */ 
/*    */   public InfInfoBean getInfInfo()
/*    */   {
/* 89 */     return this.infInfo;
/*    */   }
/*    */ 
/*    */   public void setInfInfo(InfInfoBean infInfo)
/*    */   {
/* 96 */     this.infInfo = infInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.Partner
 * JD-Core Version:    0.6.0
 */