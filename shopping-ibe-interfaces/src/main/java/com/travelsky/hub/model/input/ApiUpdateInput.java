/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import com.travelsky.hub.model.output.ApiInfo;
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ApiUpdateInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7982985199193726556L;
/*     */   private String airlineCode;
/*     */   private String flightClass;
/*     */   private String flightDate;
/*     */   private String flightNumber;
/*     */   private String fromCity;
/*     */   private String ticketNumber;
/*     */   private String destCity;
/*     */   private ApiInfo apiInfo;
/*     */   private String hostNumber;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  43 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  51 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  59 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  67 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  75 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  83 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  91 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  99 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 107 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 115 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/* 123 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/* 131 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public ApiInfo getApiInfo()
/*     */   {
/* 140 */     return this.apiInfo;
/*     */   }
/*     */ 
/*     */   public void setApiInfo(ApiInfo apiInfo)
/*     */   {
/* 148 */     this.apiInfo = apiInfo;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 156 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 164 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 172 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 180 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ApiUpdateInput
 * JD-Core Version:    0.6.0
 */