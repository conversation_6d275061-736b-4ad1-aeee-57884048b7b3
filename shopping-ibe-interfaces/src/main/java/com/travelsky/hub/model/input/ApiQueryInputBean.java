/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ApiQueryInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5787833967689276798L;
/*     */   private String hostNumber;
/*     */   private String departureDate;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String airlineCode;
/*     */   private String arrivalAirport;
/*     */   private String tktNumber;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  36 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  43 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  51 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  58 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  66 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  74 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  81 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  89 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  97 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 105 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 112 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 119 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String tktNumber)
/*     */   {
/* 126 */     this.tktNumber = tktNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 133 */     return this.hostNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.ApiQueryInputBean
 * JD-Core Version:    0.6.0
 */