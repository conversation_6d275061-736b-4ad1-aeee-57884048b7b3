package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import lombok.Data;

import java.util.Date;

/**
 * Zero
 * 2020/10/23 15:21
 **/
@Data
public class PrintBoardingRQ extends BaseRequest{
    private String depCode;
    private String arrCode;
    private String flightNo;
    private String cabin;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private Date depTime;
    private String airline;
    private String ticketNo;
    private String passengerName;
    private String boardingNumber;//should
    private String seatNumber;
    private String tourIndex;
    private String groupName;
    private String local;
    private String reissue;//Y
}
