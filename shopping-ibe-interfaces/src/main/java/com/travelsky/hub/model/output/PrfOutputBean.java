/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PrfOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String depTmRangPref;
/*     */   private String ckinTimePref;
/*     */   private String bkDayPref;
/*     */   private List<SeatPref> seatPrefList;
/*     */   private List<CkinTypePref> ckinTypePrefList;
/*     */ 
/*     */   public String getDepTmRangPref()
/*     */   {
/*  50 */     return this.depTmRangPref;
/*     */   }
/*     */ 
/*     */   public void setDepTmRangPref(String depTmRangPref)
/*     */   {
/*  58 */     this.depTmRangPref = depTmRangPref;
/*     */   }
/*     */ 
/*     */   public String getCkinTimePref()
/*     */   {
/*  66 */     return this.ckinTimePref;
/*     */   }
/*     */ 
/*     */   public void setCkinTimePref(String ckinTimePref)
/*     */   {
/*  74 */     this.ckinTimePref = ckinTimePref;
/*     */   }
/*     */ 
/*     */   public String getBkDayPref()
/*     */   {
/*  82 */     return this.bkDayPref;
/*     */   }
/*     */ 
/*     */   public void setBkDayPref(String bkDayPref)
/*     */   {
/*  90 */     this.bkDayPref = bkDayPref;
/*     */   }
/*     */ 
/*     */   public List<SeatPref> getSeatPrefList()
/*     */   {
/*  98 */     return this.seatPrefList;
/*     */   }
/*     */ 
/*     */   public void setSeatPrefList(List<SeatPref> seatPrefList)
/*     */   {
/* 106 */     this.seatPrefList = seatPrefList;
/*     */   }
/*     */ 
/*     */   public List<CkinTypePref> getCkinTypePrefList()
/*     */   {
/* 114 */     return this.ckinTypePrefList;
/*     */   }
/*     */ 
/*     */   public void setCkinTypePrefList(List<CkinTypePref> ckinTypePrefList)
/*     */   {
/* 122 */     this.ckinTypePrefList = ckinTypePrefList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PrfOutputBean
 * JD-Core Version:    0.6.0
 */