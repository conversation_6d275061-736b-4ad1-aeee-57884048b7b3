/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class FlightBoardQueryBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String flightDate;
/*    */   private String flightNo;
/*    */   private String deptAirport;
/*    */   private String option;
/*    */ 
/*    */   public String getFlightDate()
/*    */   {
/* 30 */     return this.flightDate;
/*    */   }
/*    */ 
/*    */   public void setFlightDate(String flightDate)
/*    */   {
/* 37 */     this.flightDate = flightDate;
/*    */   }
/*    */ 
/*    */   public String getFlightNo()
/*    */   {
/* 44 */     return this.flightNo;
/*    */   }
/*    */ 
/*    */   public void setFlightNo(String flightNo)
/*    */   {
/* 51 */     this.flightNo = flightNo;
/*    */   }
/*    */ 
/*    */   public String getDeptAirport()
/*    */   {
/* 58 */     return this.deptAirport;
/*    */   }
/*    */ 
/*    */   public void setDeptAirport(String deptAirport)
/*    */   {
/* 65 */     this.deptAirport = deptAirport;
/*    */   }
/*    */ 
/*    */   public String getOption()
/*    */   {
/* 73 */     return this.option;
/*    */   }
/*    */ 
/*    */   public void setOption(String option)
/*    */   {
/* 80 */     this.option = option;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.FlightBoardQueryBean
 * JD-Core Version:    0.6.0
 */