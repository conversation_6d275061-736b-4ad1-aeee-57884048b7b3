/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ public class BaseInputBean
/*     */ {
/*     */   private String tktNumber;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String flightClass;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String hostNumber;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  42 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  49 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  56 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/*  63 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  70 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  77 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  84 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  91 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  98 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 105 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getTktNumber()
/*     */   {
/* 113 */     return this.tktNumber;
/*     */   }
/*     */ 
/*     */   public void setTktNumber(String number)
/*     */   {
/* 120 */     this.tktNumber = number;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 127 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 134 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 141 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 148 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.BaseInputBean
 * JD-Core Version:    0.6.0
 */