/*     */ package com.travelsky.hub.wdoe.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class HostPsgDetailInputBean extends BaseInputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -5542828940631591948L;
/*  17 */   private String seatNumber = "";
/*     */ 
/*  19 */   private String boardingNumber = "";
/*     */ 
/*  21 */   private String etNumber = "";
/*     */ 
/*  23 */   private String bagtag = "";
/*     */ 
/*  25 */   private String hostNbr = "";
/*     */ 
/*  27 */   private String iOption = "0";
/*     */ 
/*     */   public String getBagtag()
/*     */   {
/*  34 */     return this.bagtag;
/*     */   }
/*     */ 
/*     */   public void setBagtag(String bagtag)
/*     */   {
/*  41 */     this.bagtag = bagtag;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/*  48 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/*  55 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getEtNumber()
/*     */   {
/*  62 */     return this.etNumber;
/*     */   }
/*     */ 
/*     */   public void setEtNumber(String etNumber)
/*     */   {
/*  69 */     this.etNumber = etNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNbr()
/*     */   {
/*  76 */     return this.hostNbr;
/*     */   }
/*     */ 
/*     */   public void setHostNbr(String hostNbr)
/*     */   {
/*  83 */     this.hostNbr = hostNbr;
/*     */   }
/*     */ 
/*     */   public String getIOption()
/*     */   {
/*  90 */     return this.iOption;
/*     */   }
/*     */ 
/*     */   public void setIOption(String option)
/*     */   {
/*  97 */     this.iOption = option;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 104 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 111 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.HostPsgDetailInputBean
 * JD-Core Version:    0.6.0
 */