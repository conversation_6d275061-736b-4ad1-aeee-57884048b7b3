/*    */ package com.travelsky.hub.model.output.hbpamseat;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class MultiPsrCheckInSeatOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -1152342535712444443L;
/*    */   private List<CheckInPassengerInfo> passengerInfoList;
/*    */ 
/*    */   public List<CheckInPassengerInfo> getPassengerInfoList()
/*    */   {
/* 27 */     return this.passengerInfoList;
/*    */   }
/*    */ 
/*    */   public void setPassengerInfoList(List<CheckInPassengerInfo> passengerInfoList)
/*    */   {
/* 34 */     this.passengerInfoList = passengerInfoList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpamseat.MultiPsrCheckInSeatOutput
 * JD-Core Version:    0.6.0
 */