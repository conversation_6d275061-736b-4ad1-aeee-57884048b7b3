/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class RemoveBagOutputBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String deptAirportCode;
/*     */   private String destAirportCode;
/*     */   private String etNumber;
/*     */   private String flightSubClass;
/*     */   private String passengerName;
/*     */   private String passengerStatus;
/*     */   private String seatNumber;
/*     */   private String boardNumber;
/*     */   private String hostNumber;
/*     */   private BagInfo bagInfo;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  61 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  68 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  84 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDeptAirportCode()
/*     */   {
/*  91 */     return this.deptAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDeptAirportCode(String deptAirportCode)
/*     */   {
/*  98 */     this.deptAirportCode = deptAirportCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 105 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getDestAirportCode()
/*     */   {
/* 112 */     return this.destAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDestAirportCode(String destAirportCode)
/*     */   {
/* 119 */     this.destAirportCode = destAirportCode;
/*     */   }
/*     */ 
/*     */   public String getFlightSubClass()
/*     */   {
/* 127 */     return this.flightSubClass;
/*     */   }
/*     */ 
/*     */   public void setFlightSubClass(String flightSubClass)
/*     */   {
/* 134 */     this.flightSubClass = flightSubClass;
/*     */   }
/*     */ 
/*     */   public String getEtNumber()
/*     */   {
/* 141 */     return this.etNumber;
/*     */   }
/*     */ 
/*     */   public void setEtNumber(String etNumber)
/*     */   {
/* 148 */     this.etNumber = etNumber;
/*     */   }
/*     */ 
/*     */   public String getPassengerName()
/*     */   {
/* 155 */     return this.passengerName;
/*     */   }
/*     */ 
/*     */   public void setPassengerName(String passengerName)
/*     */   {
/* 162 */     this.passengerName = passengerName;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 169 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 176 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 183 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 190 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getBoardNumber()
/*     */   {
/* 197 */     return this.boardNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardNumber(String boardNumber)
/*     */   {
/* 204 */     this.boardNumber = boardNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 211 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 218 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 225 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 232 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public BagInfo getBagInfo()
/*     */   {
/* 239 */     return this.bagInfo;
/*     */   }
/*     */ 
/*     */   public void setBagInfo(BagInfo bagInfo)
/*     */   {
/* 246 */     this.bagInfo = bagInfo;
/*     */   }
/*     */ 
/*     */   public static long getSerialversionuid()
/*     */   {
/* 253 */     return 1L;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.RemoveBagOutputBean
 * JD-Core Version:    0.6.0
 */