/*    */ package com.travelsky.hub.model.peentity.meal.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class OfficeInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -5881549266460946377L;
/*    */   private String airline;
/*    */   private String station;
/*    */ 
/*    */   public String getAirline()
/*    */   {
/* 22 */     return this.airline;
/*    */   }
/*    */ 
/*    */   public void setAirline(String airline) {
/* 26 */     this.airline = airline;
/*    */   }
/*    */ 
/*    */   public String getStation() {
/* 30 */     return this.station;
/*    */   }
/*    */ 
/*    */   public void setStation(String station) {
/* 34 */     this.station = station;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.input.OfficeInfo
 * JD-Core Version:    0.6.0
 */