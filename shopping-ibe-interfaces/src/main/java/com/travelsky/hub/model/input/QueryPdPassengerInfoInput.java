/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class QueryPdPassengerInfoInput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7134809930709666049L;
/*    */   private RetrievePassengerInfoInputBean prInfoReq;
/*    */   private TxnInfo txnInfo;
/*    */ 
/*    */   public RetrievePassengerInfoInputBean getPrInfoReq()
/*    */   {
/* 37 */     return this.prInfoReq;
/*    */   }
/*    */ 
/*    */   public void setPrInfoReq(RetrievePassengerInfoInputBean prInfoReq)
/*    */   {
/* 44 */     this.prInfoReq = prInfoReq;
/*    */   }
/*    */ 
/*    */   public TxnInfo getTxnInfo()
/*    */   {
/* 51 */     return this.txnInfo;
/*    */   }
/*    */ 
/*    */   public void setTxnInfo(TxnInfo txnInfo)
/*    */   {
/* 58 */     this.txnInfo = txnInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.QueryPdPassengerInfoInput
 * JD-Core Version:    0.6.0
 */