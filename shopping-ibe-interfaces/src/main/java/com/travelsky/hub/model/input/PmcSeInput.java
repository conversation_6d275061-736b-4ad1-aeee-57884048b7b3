/*     */ package com.travelsky.hub.model.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PmcSeInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String airlineCode;
/*     */   private String flightDate;
/*     */   private String flightNum;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String ticketNo;
/*     */   private String cabin;
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  58 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  69 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  78 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  89 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNum()
/*     */   {
/*  98 */     return this.flightNum;
/*     */   }
/*     */ 
/*     */   public void setFlightNum(String flightNum)
/*     */   {
/* 109 */     this.flightNum = flightNum;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/* 118 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/* 129 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/* 138 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/* 149 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public String getTicketNo()
/*     */   {
/* 158 */     return this.ticketNo;
/*     */   }
/*     */ 
/*     */   public void setTicketNo(String ticketNo)
/*     */   {
/* 169 */     this.ticketNo = ticketNo;
/*     */   }
/*     */ 
/*     */   public String getCabin()
/*     */   {
/* 178 */     return this.cabin;
/*     */   }
/*     */ 
/*     */   public void setCabin(String cabin)
/*     */   {
/* 189 */     this.cabin = cabin;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.PmcSeInput
 * JD-Core Version:    0.6.0
 */