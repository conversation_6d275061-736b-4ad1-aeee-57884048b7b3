package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 通过 IBE 获取的票面信息
 */
@Data
public class IssueTicketPassenger implements Serializable {


    private static final long serialVersionUID = -6812679218791245413L;


    /**
     * 旅客类型，用于计算手续费，IBE需要旅客类型
     */
    private String passengerType;

    /**
     * 旅客姓名
     */
    private String passengerName;

    /**
     * 证件号
     */
    private String idType;

    /**
     * 证件类型
     */
    private String passengerID;

    /**
     * 手机号（CTCM）
     */
    private String mobileNo;

    private String pnrNo;

    private Date pnrCreateTime;

    private String ticketNo;

    private Date issueTicketTime;

    private BigDecimal netFare;
    private BigDecimal marketFare;
    private BigDecimal fuelTax;
    private BigDecimal airportTax;

    private BigDecimal netFareBack;
    private BigDecimal marketFareBack;
    private BigDecimal fuelTaxBack;
    private BigDecimal airportTaxBack;
}
