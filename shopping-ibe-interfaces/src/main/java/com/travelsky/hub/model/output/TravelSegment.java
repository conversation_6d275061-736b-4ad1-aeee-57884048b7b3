/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class TravelSegment
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String carrierSegmentID;
/*    */   private DocCheckDecision docCheckDecision;
/*    */ 
/*    */   public String getCarrierSegmentID()
/*    */   {
/* 27 */     return this.carrierSegmentID;
/*    */   }
/*    */ 
/*    */   public void setCarrierSegmentID(String carrierSegmentID)
/*    */   {
/* 36 */     this.carrierSegmentID = carrierSegmentID;
/*    */   }
/*    */ 
/*    */   public DocCheckDecision getDocCheckDecision()
/*    */   {
/* 45 */     return this.docCheckDecision;
/*    */   }
/*    */ 
/*    */   public void setDocCheckDecision(DocCheckDecision docCheckDecision)
/*    */   {
/* 54 */     this.docCheckDecision = docCheckDecision;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.TravelSegment
 * JD-Core Version:    0.6.0
 */