/*     */ package com.travelsky.hub.model.peentity;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatChartQBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -7732262759769207212L;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String flightDate;
/*     */   private String fromCity;
/*     */   private String toCity;
/*     */   private String psrLevel;
/*     */   private String flightClass;
/*     */   private String planeType;
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  22 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  40 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/*  51 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  58 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  66 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setToCity(String toCity)
/*     */   {
/*  73 */     this.toCity = toCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  80 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getToCity()
/*     */   {
/*  87 */     return this.toCity;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/*  95 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 102 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 109 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/* 116 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/* 123 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getPsrLevel()
/*     */   {
/* 130 */     return this.psrLevel;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 137 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setPsrLevel(String psrLevel)
/*     */   {
/* 144 */     this.psrLevel = psrLevel;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatChartQBean
 * JD-Core Version:    0.6.0
 */