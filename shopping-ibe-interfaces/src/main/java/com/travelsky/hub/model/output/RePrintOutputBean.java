/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.LinkedList;
/*    */ import java.util.List;
/*    */ 
/*    */ public class RePrintOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/* 20 */   private List dataFlows = new LinkedList();
/*    */ 
/*    */   public List getDataFlows()
/*    */   {
/* 26 */     return this.dataFlows;
/*    */   }
/*    */ 
/*    */   public void setDataFlows(List dataFlows)
/*    */   {
/* 33 */     this.dataFlows = dataFlows;
/*    */   }
/*    */ 
/*    */   public void clear()
/*    */   {
/* 39 */     this.dataFlows.clear();
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.RePrintOutputBean
 * JD-Core Version:    0.6.0
 */