/*
 * Copyright (C) 2017 eKing Technology, Inc. All Rights Reserved.
 */

package com.hna.shopping.ibe.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.google.common.collect.Maps;
import org.apache.commons.lang3.time.FastDateFormat;

import com.hna.shopping.ibe.common.exception.GlobalException;
import com.hna.shopping.ibe.common.responsecode.CodeDefault;

/**
 * 适合各种时间转换的 DateUtil 后续改为 joda-time 来实现
 */
public class DateUtil {
    // 这里不用 yyyy-MM-dd 如果外面需要用，就 replace("-", "") 一下再用
    private final static String YMD = "yyyyMMdd";
    private final static String yyyy_MM_dd = "yyyy-MM-dd";
    private final static String YMDHMS = "yyyyMMdd HHmmss";
    private final static String yyy_MM_dd_HMS = "yyyy-MM-dd HH:mm:ss";

    /**
     * date 转化成 String
     *
     * @param date
     * @return
     */
    public static String toStringYMD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(YMD);
        return sdf.format(date);
    }

    public static String toStringYYYY_MM_DD(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyy_MM_dd);
        return sdf.format(date);
    }

    /**
     * date 转化成 String
     *
     * @param date
     * @return
     */
    public static String toStringYMDHMS(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(YMDHMS);
        return sdf.format(date);
    }
    /**
     * date 转化成 String
     *
     * @param date
     * @return
     */
    public static String toStringYYYY_MM_dd_HMS(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyy_MM_dd_HMS);
        return sdf.format(date);
    }

    /**
     * string 转化成 date
     *
     * @param str
     * @return
     */
    public static Date toDateYMD(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat(YMD);
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            throw new GlobalException(CodeDefault.ILLEGAL_DATE_FORMAT, e);
        }
    }

    public static Date toDateYYYY_MM_DD(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat(yyyy_MM_dd);
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            throw new GlobalException(CodeDefault.ILLEGAL_DATE_FORMAT, e);
        }
    }

    /**
     * string 转化成 date
     *
     * @param str
     * @return
     */
    public static Date toDateYMDHMS(String str) {
        SimpleDateFormat sdf = new SimpleDateFormat(YMDHMS);
        try {
            return sdf.parse(str);
        } catch (ParseException e) {
            throw new GlobalException(CodeDefault.ILLEGAL_DATE_FORMAT, e);
        }
    }

    public static Date stringToDate(String date) {
        if (date == null)
            return null;

        if (date.endsWith(".0")) {
            date = date.substring(0, date.length() - 2);
        }

        Calendar cd = Calendar.getInstance();
        StringTokenizer token = new StringTokenizer(date, "-/ :");
        if (token.hasMoreTokens()) {
            cd.set(Calendar.YEAR, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.YEAR, 1970);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.MONTH, Integer.parseInt(token.nextToken()) - 1);
        } else {
            cd.set(Calendar.MONTH, 0);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.DAY_OF_MONTH, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.DAY_OF_MONTH, 1);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.HOUR_OF_DAY, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.HOUR_OF_DAY, 0);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.MINUTE, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.MINUTE, 0);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.SECOND, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.SECOND, 0);
        }
        if (token.hasMoreTokens()) {
            cd.set(Calendar.MILLISECOND, Integer.parseInt(token.nextToken()));
        } else {
            cd.set(Calendar.MILLISECOND, 0);
        }

        return cd.getTime();
    }

    public static String toStringYMDHM(Date date) {
        // SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return FastDateFormat.getInstance("yyyy-MM-dd HH:mm", Locale.US).format(date);
    }

    public static String dateToString(Date date) {
        return FastDateFormat.getInstance("yyyy-MM-dd", Locale.US).format(date);
    }

    public static String dateToString(Date date, String format) {
        return FastDateFormat.getInstance(format, Locale.US).format(date);
    }

    public static Date changeDateToDate(Date date, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(YMDHMS);
        String dateStr = sdf.format(date);
        return toDateYMD(dateStr);
    }


    public static Date stringToUsDateDDMMMyy(String sdate) {
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("ddMMMyy", Locale.US);
            Date date = (Date) formatter.parse(sdate);
            return date;
        } catch (Exception e) {

        }
        return null;
    }

    public static String getMoth(String s) {
        HashMap<String, String> objectObjectHashMap = Maps.newHashMap();
        objectObjectHashMap.put("JAN", "01");
        objectObjectHashMap.put("FEB", "02");
        objectObjectHashMap.put("MAR", "03");
        objectObjectHashMap.put("APR", "04");
        objectObjectHashMap.put("MAY", "05");
        objectObjectHashMap.put("JUN", "06");
        objectObjectHashMap.put("JUL", "07");
        objectObjectHashMap.put("AUG", "08");
        objectObjectHashMap.put("SEP", "09");
        objectObjectHashMap.put("OCT", "10");
        objectObjectHashMap.put("NOV", "11");
        objectObjectHashMap.put("DEC", "12");
        String s1 = objectObjectHashMap.get(s);
        if (s1 == null) {
            throw new RuntimeException("提取月份数据异常");
        }
        return s1;
    }
}