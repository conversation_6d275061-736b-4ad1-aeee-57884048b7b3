/*    */ package com.travelsky.hub.model.peentity.upgemds.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class IssueEMDSServiceOrderRS
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -9152742947586880190L;
/*    */   private String resultCode;
/*    */   private String satTransactionID;
/*    */   private Document document;
/*    */   private String resultMessage;
/*    */ 
/*    */   public String getResultCode()
/*    */   {
/* 47 */     return this.resultCode;
/*    */   }
/*    */ 
/*    */   public void setResultCode(String resultCode)
/*    */   {
/* 54 */     this.resultCode = resultCode;
/*    */   }
/*    */ 
/*    */   public String getSatTransactionID()
/*    */   {
/* 61 */     return this.satTransactionID;
/*    */   }
/*    */ 
/*    */   public void setSatTransactionID(String satTransactionID)
/*    */   {
/* 68 */     this.satTransactionID = satTransactionID;
/*    */   }
/*    */ 
/*    */   public Document getDocument()
/*    */   {
/* 75 */     return this.document;
/*    */   }
/*    */ 
/*    */   public void setDocument(Document document)
/*    */   {
/* 82 */     this.document = document;
/*    */   }
/*    */ 
/*    */   public String getResultMessage()
/*    */   {
/* 89 */     return this.resultMessage;
/*    */   }
/*    */ 
/*    */   public void setResultMessage(String resultMessage)
/*    */   {
/* 96 */     this.resultMessage = resultMessage;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.output.IssueEMDSServiceOrderRS
 * JD-Core Version:    0.6.0
 */