/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfoBag
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 8653661848214636883L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String departureDate;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  44 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/*  51 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  58 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  65 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  72 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  79 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/*  86 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/*  93 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 100 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 107 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.FlightInfoBag
 * JD-Core Version:    0.6.0
 */