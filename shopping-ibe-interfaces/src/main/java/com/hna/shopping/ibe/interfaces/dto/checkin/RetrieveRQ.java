package com.hna.shopping.ibe.interfaces.dto.checkin;

import com.hna.shopping.ibe.interfaces.dto.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: deyi
 * @Date: 2019/11/19 15:06
 * @Version 1.0
 */
@Data
public class RetrieveRQ extends BaseRequest {
    private List<Identity> identitys;

    @Data
    public static class Identity {
        @ApiModelProperty("证件号码/票号")
        private String certificateNumber;
        @ApiModelProperty("TN：票号 PP：护照 NI：身份证")
        private String certificateType;
        @ApiModelProperty("乘机人姓名")
        private String passengerName;
    }
}
