package com.hna.shopping.ibe.interfaces.dto;

import com.travelsky.ibe.client.pnr.BookInformation;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by lm.tang on 2018/8/20.
 * 更新pnr的请求参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel
public class UpdatePnrReq extends BaseRequest{

    private String pnr;
    /**
     * 要删除的index集合
     */
    private List<Integer> delIndexes;
    /**
     * 要添加的pnr信息
     */
    private BookInformation bookInformation;


}
