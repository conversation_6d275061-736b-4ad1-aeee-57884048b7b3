/*     */ package com.travelsky.hub.model.input.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PartnerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -4559911673976792299L;
/*     */   private String index;
/*     */   private String ticketId;
/*     */   private String sequenceNumber;
/*     */   private String hostNumber;
/*     */   private String cabinType;
/*     */   private String gender;
/*     */   private String chd;
/*     */   private Infant inf;
/*     */   private String surName;
/*     */   private String snrFlag;
/*     */   private String seatNumber;
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  66 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  73 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/*  79 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/*  86 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  92 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  99 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 106 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 113 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 120 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 127 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 133 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 140 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public Infant getInf()
/*     */   {
/* 147 */     return this.inf;
/*     */   }
/*     */ 
/*     */   public void setInf(Infant inf)
/*     */   {
/* 154 */     this.inf = inf;
/*     */   }
/*     */ 
/*     */   public String getTicketId()
/*     */   {
/* 160 */     return this.ticketId;
/*     */   }
/*     */ 
/*     */   public void setTicketId(String ticketId)
/*     */   {
/* 167 */     this.ticketId = ticketId;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 174 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 181 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getSnrFlag()
/*     */   {
/* 188 */     return this.snrFlag;
/*     */   }
/*     */ 
/*     */   public void setSnrFlag(String snrFlag)
/*     */   {
/* 195 */     this.snrFlag = snrFlag;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 202 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 209 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.PartnerInfo
 * JD-Core Version:    0.6.0
 */