/*    */ package com.travelsky.hub.wdoe.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ThroughFlightOutputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 7051729653169577813L;
/*    */   private String result;
/*    */ 
/*    */   public String getResult()
/*    */   {
/* 24 */     return this.result;
/*    */   }
/*    */ 
/*    */   public void setResult(String result)
/*    */   {
/* 31 */     this.result = result;
/*    */   }
/*    */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.wdoe.output.ThroughFlightOutputBean
 * JD-Core Version:    0.6.0
 */