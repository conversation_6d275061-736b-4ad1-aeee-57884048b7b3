/*    */ package com.travelsky.hub.model.peentity.bookingSeat.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ServiceItem
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2293255209257105143L;
/*    */   private String segmentID;
/*    */   private String serviceID;
/*    */   private SeatInfo seatInfo;
/*    */ 
/*    */   public String getSegmentID()
/*    */   {
/* 37 */     return this.segmentID;
/*    */   }
/*    */ 
/*    */   public void setSegmentID(String segmentID)
/*    */   {
/* 44 */     this.segmentID = segmentID;
/*    */   }
/*    */ 
/*    */   public String getServiceID()
/*    */   {
/* 51 */     return this.serviceID;
/*    */   }
/*    */ 
/*    */   public void setServiceID(String serviceID)
/*    */   {
/* 58 */     this.serviceID = serviceID;
/*    */   }
/*    */ 
/*    */   public SeatInfo getSeatInfo()
/*    */   {
/* 65 */     return this.seatInfo;
/*    */   }
/*    */ 
/*    */   public void setSeatInfo(SeatInfo seatInfo)
/*    */   {
/* 72 */     this.seatInfo = seatInfo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.bookingSeat.input.ServiceItem
 * JD-Core Version:    0.6.0
 */