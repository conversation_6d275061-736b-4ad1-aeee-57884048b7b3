/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class AcceptedPsrBean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*  24 */   private String SeatNumber = "";
/*     */   private List BordingStreams;
/*  34 */   private String BoardingNumber = "";
/*     */ 
/*  40 */   private String checkCode = "";
/*     */   private String standByNumber;
/*     */   private String pstCkiStatus;
/*     */ 
/*     */   public String getCheckCode()
/*     */   {
/*  52 */     return this.checkCode;
/*     */   }
/*     */ 
/*     */   public void setCheckCode(String i)
/*     */   {
/*  59 */     this.checkCode = i;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/*  66 */     return this.SeatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String i)
/*     */   {
/*  73 */     this.SeatNumber = i;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/*  80 */     return this.BoardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String i)
/*     */   {
/*  87 */     this.BoardingNumber = i;
/*     */   }
/*     */ 
/*     */   public void clear()
/*     */   {
/*  93 */     this.SeatNumber = "";
/*  94 */     this.BoardingNumber = "";
/*  95 */     this.BordingStreams = null;
/*  96 */     this.checkCode = "";
/*     */   }
/*     */ 
/*     */   public List getBordingStreams()
/*     */   {
/* 103 */     return this.BordingStreams;
/*     */   }
/*     */ 
/*     */   public void setBordingStreams(List bordingStreams)
/*     */   {
/* 110 */     this.BordingStreams = bordingStreams;
/*     */   }
/*     */ 
/*     */   public String getStandByNumber()
/*     */   {
/* 118 */     return this.standByNumber;
/*     */   }
/*     */ 
/*     */   public void setStandByNumber(String standByNumber)
/*     */   {
/* 126 */     this.standByNumber = standByNumber;
/*     */   }
/*     */ 
/*     */   public String getPstCkiStatus()
/*     */   {
/* 134 */     return this.pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPstCkiStatus(String pstCkiStatus)
/*     */   {
/* 142 */     this.pstCkiStatus = pstCkiStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.AcceptedPsrBean
 * JD-Core Version:    0.6.0
 */