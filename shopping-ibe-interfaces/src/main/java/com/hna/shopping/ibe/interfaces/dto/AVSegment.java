package com.hna.shopping.ibe.interfaces.dto;

import java.io.Serializable;
import java.util.List;

import com.google.common.collect.Lists;

import io.swagger.annotations.ApiModel;
import lombok.Data;


@Data
//@EqualsAndHashCode(callSuper=true)
@ApiModel
public class AVSegment implements Serializable{

    private static final long serialVersionUID = 1l;

	   private int segmentNumber = 0;
	   
	   private List<AVFlightInfo> flights = Lists.newArrayList();
	 
	   String journeytime = "N/A ";
}
