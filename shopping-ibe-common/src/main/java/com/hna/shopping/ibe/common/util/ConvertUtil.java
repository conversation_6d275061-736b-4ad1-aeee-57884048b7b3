/*
 * Copyright (C) 2017 eKing Technology, Inc. All Rights Reserved.
 */

package com.hna.shopping.ibe.common.util;

import com.google.common.base.Function;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.dozer.DozerBeanMapperSingletonWrapper;
import org.dozer.Mapper;

import java.util.List;

public class ConvertUtil {
    /**
     * 全局唯一一个 mapper
     */
    private static Mapper MAPPER = DozerBeanMapperSingletonWrapper.getInstance();

    /**
     * 对象 -> 对象 转换
     *
     * @param source
     * @param destination
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> T map(F source, T destination) {
        if (source == null || destination == null) {
            return null;
        }
        MAPPER.map(source, destination);
        return destination;
    }

    /**
     * 对象列表 -> Class 转换
     * 如果返回结果需要直接序列化，「不要」调用这个函数
     *
     * @param fromList
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> List<T> mapNoSerialize(List<F> fromList, final Class<T> toClass) {
        // 因为 transform 是 lazily 执行，在远程调用时，会因为序列化问题报错，具体看 transform doc
        // Caused by: java.io.NotSerializableException: ConvertUtil$1
        // 一个暂时的解法是，调用 mapForSerialize 函数
        return Lists.transform(fromList, new Function<F, T>() {
            @Override
            public T apply(F from) {
                return map(from, toClass);
            }
        });
    }

    /**
     * 对象列表 -> Class 转换
     * 如果返回结果需要序列化，「使用」这个函数
     *
     * @param fromList
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> List<T> mapForSerialize(List<F> fromList, final Class<T> toClass) {
        return ImmutableList.copyOf(Lists.transform(fromList, new Function<F, T>() {
            @Override
            public T apply(F from) {
                return map(from, toClass);
            }
        }));
    }

    /**
     * 对象 -> Class 转换
     *
     * @param from
     * @param toClass
     * @param <F>
     * @param <T>
     * @return
     */
    public static <F, T> T map(F from, final Class<T> toClass) {
        if (from == null) {
            return null;
        }
        return MAPPER.map(from, toClass);
    }
}