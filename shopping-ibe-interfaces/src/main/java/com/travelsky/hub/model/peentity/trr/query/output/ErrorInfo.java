/*    */ package com.travelsky.hub.model.peentity.trr.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ErrorInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -2646405854105935546L;
/*    */   private String errorSSR;
/*    */   private String availableSSR;
/*    */ 
/*    */   public String getErrorSSR()
/*    */   {
/* 36 */     return this.errorSSR;
/*    */   }
/*    */ 
/*    */   public void setErrorSSR(String errorSSR)
/*    */   {
/* 43 */     this.errorSSR = errorSSR;
/*    */   }
/*    */ 
/*    */   public String getAvailableSSR()
/*    */   {
/* 50 */     return this.availableSSR;
/*    */   }
/*    */ 
/*    */   public void setAvailableSSR(String availableSSR)
/*    */   {
/* 57 */     this.availableSSR = availableSSR;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.ErrorInfo
 * JD-Core Version:    0.6.0
 */