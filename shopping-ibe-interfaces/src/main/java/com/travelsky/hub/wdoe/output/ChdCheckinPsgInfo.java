/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class ChdCheckinPsgInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -6819501278491392746L;
/*  18 */   private String surName = "";
/*  19 */   private String chnName = "";
/*  20 */   private String cabinType = "";
/*     */   private List<String> seatNumber;
/*  22 */   private String chd = "";
/*  23 */   private String docType = "";
/*  24 */   private String docID = "";
/*  25 */   private String ticketID = "";
/*  26 */   private String sequenceNumber = "";
/*     */   private List<String> boardingNumber;
/*  29 */   private String ticketPrice = "";
/*     */   private List<String> dataStream;
/*     */   private String standByNumber;
/*     */   private String pstCkiStatus;
/*     */ 
/*     */   public String getSurName()
/*     */   {
/*  42 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/*  49 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getChnName()
/*     */   {
/*  56 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/*  63 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  70 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  77 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/*  84 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/*  91 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/*  98 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 105 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/* 112 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/* 119 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 126 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 133 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 140 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 147 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketPrice()
/*     */   {
/* 154 */     return this.ticketPrice;
/*     */   }
/*     */ 
/*     */   public void setTicketPrice(String ticketPrice)
/*     */   {
/* 161 */     this.ticketPrice = ticketPrice;
/*     */   }
/*     */ 
/*     */   public List<String> getDataStream()
/*     */   {
/* 169 */     return this.dataStream;
/*     */   }
/*     */ 
/*     */   public void setDataStream(List<String> dataStream)
/*     */   {
/* 176 */     this.dataStream = dataStream;
/*     */   }
/*     */ 
/*     */   public List<String> getSeatNumber()
/*     */   {
/* 183 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(List<String> seatNumber)
/*     */   {
/* 190 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public List<String> getBoardingNumber()
/*     */   {
/* 197 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(List<String> boardingNumber)
/*     */   {
/* 204 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getStandByNumber()
/*     */   {
/* 212 */     return this.standByNumber;
/*     */   }
/*     */ 
/*     */   public void setStandByNumber(String standByNumber)
/*     */   {
/* 220 */     this.standByNumber = standByNumber;
/*     */   }
/*     */ 
/*     */   public String getPstCkiStatus()
/*     */   {
/* 228 */     return this.pstCkiStatus;
/*     */   }
/*     */ 
/*     */   public void setPstCkiStatus(String pstCkiStatus)
/*     */   {
/* 236 */     this.pstCkiStatus = pstCkiStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.ChdCheckinPsgInfo
 * JD-Core Version:    0.6.0
 */