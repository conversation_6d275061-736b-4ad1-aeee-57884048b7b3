/*    */ package com.travelsky.hub.model.peentity;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SeatOrderOutPutBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -4418026545593746591L;
/*    */   private String orderNum;
/*    */   private String orderStatus;
/*    */   private String errorMsg;
/*    */   private String errorCode;
/*    */ 
/*    */   public String getOrderNum()
/*    */   {
/* 22 */     return this.orderNum;
/*    */   }
/*    */ 
/*    */   public String getOrderStatus()
/*    */   {
/* 31 */     return this.orderStatus;
/*    */   }
/*    */ 
/*    */   public String getErrorMsg()
/*    */   {
/* 40 */     return this.errorMsg;
/*    */   }
/*    */ 
/*    */   public String getErrorCode()
/*    */   {
/* 49 */     return this.errorCode;
/*    */   }
/*    */ 
/*    */   public void setOrderNum(String orderNum)
/*    */   {
/* 56 */     this.orderNum = orderNum;
/*    */   }
/*    */ 
/*    */   public void setOrderStatus(String orderStatus)
/*    */   {
/* 64 */     this.orderStatus = orderStatus;
/*    */   }
/*    */ 
/*    */   public void setErrorMsg(String errorMsg)
/*    */   {
/* 71 */     this.errorMsg = errorMsg;
/*    */   }
/*    */ 
/*    */   public void setErrorCode(String errorCode)
/*    */   {
/* 78 */     this.errorCode = errorCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.SeatOrderOutPutBean
 * JD-Core Version:    0.6.0
 */