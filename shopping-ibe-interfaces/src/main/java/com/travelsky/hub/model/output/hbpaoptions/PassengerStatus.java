/*    */ package com.travelsky.hub.model.output.hbpaoptions;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PassengerStatus
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -6940282556557212248L;
/*    */   private String boardingNumber;
/*    */   private String passengerDeleted;
/*    */   private String standbyNumber;
/*    */   private String standbyReason;
/*    */ 
/*    */   public String getBoardingNumber()
/*    */   {
/* 35 */     return this.boardingNumber;
/*    */   }
/*    */ 
/*    */   public void setBoardingNumber(String boardingNumber)
/*    */   {
/* 42 */     this.boardingNumber = boardingNumber;
/*    */   }
/*    */ 
/*    */   public String getPassengerDeleted()
/*    */   {
/* 49 */     return this.passengerDeleted;
/*    */   }
/*    */ 
/*    */   public void setPassengerDeleted(String passengerDeleted)
/*    */   {
/* 56 */     this.passengerDeleted = passengerDeleted;
/*    */   }
/*    */ 
/*    */   public String getStandbyNumber()
/*    */   {
/* 63 */     return this.standbyNumber;
/*    */   }
/*    */ 
/*    */   public void setStandbyNumber(String standbyNumber)
/*    */   {
/* 70 */     this.standbyNumber = standbyNumber;
/*    */   }
/*    */ 
/*    */   public String getStandbyReason()
/*    */   {
/* 77 */     return this.standbyReason;
/*    */   }
/*    */ 
/*    */   public void setStandbyReason(String standbyReason)
/*    */   {
/* 84 */     this.standbyReason = standbyReason;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.hbpaoptions.PassengerStatus
 * JD-Core Version:    0.6.0
 */