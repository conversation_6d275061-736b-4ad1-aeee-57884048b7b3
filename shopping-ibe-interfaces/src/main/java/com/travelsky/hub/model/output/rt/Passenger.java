/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Passenger
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 5620363950062813862L;
/*     */   private List<Remark> remarkList;
/*     */   private PassengerInfo passengerInfo;
/*     */   private Infant infant;
/*     */   private List<Segment> segmentList;
/*     */   private Fare fare;
/*     */   private List<AuxiliaryService> auxiliaryServiceList;
/*     */   private List<Osi> otherServiceInformationList;
/*     */   private List<TourCode> tourCodeList;
/*     */   private List<EndorsementInformation> endorsementInformationList;
/*     */ 
/*     */   public List<Remark> getRemarkList()
/*     */   {
/*  62 */     return this.remarkList;
/*     */   }
/*     */ 
/*     */   public void setRemarkList(List<Remark> remarkList)
/*     */   {
/*  69 */     this.remarkList = remarkList;
/*     */   }
/*     */ 
/*     */   public PassengerInfo getPassengerInfo()
/*     */   {
/*  76 */     return this.passengerInfo;
/*     */   }
/*     */ 
/*     */   public void setPassengerInfo(PassengerInfo passengerInfo)
/*     */   {
/*  83 */     this.passengerInfo = passengerInfo;
/*     */   }
/*     */ 
/*     */   public Infant getInfant()
/*     */   {
/*  90 */     return this.infant;
/*     */   }
/*     */ 
/*     */   public void setInfant(Infant infant)
/*     */   {
/*  97 */     this.infant = infant;
/*     */   }
/*     */ 
/*     */   public List<Segment> getSegmentList()
/*     */   {
/* 104 */     return this.segmentList;
/*     */   }
/*     */ 
/*     */   public void setSegmentList(List<Segment> segmentList)
/*     */   {
/* 111 */     this.segmentList = segmentList;
/*     */   }
/*     */ 
/*     */   public Fare getFare()
/*     */   {
/* 118 */     return this.fare;
/*     */   }
/*     */ 
/*     */   public void setFare(Fare fare)
/*     */   {
/* 125 */     this.fare = fare;
/*     */   }
/*     */ 
/*     */   public List<AuxiliaryService> getAuxiliaryServiceList()
/*     */   {
/* 132 */     return this.auxiliaryServiceList;
/*     */   }
/*     */ 
/*     */   public void setAuxiliaryServiceList(List<AuxiliaryService> auxiliaryServiceList)
/*     */   {
/* 139 */     this.auxiliaryServiceList = auxiliaryServiceList;
/*     */   }
/*     */ 
/*     */   public List<Osi> getOtherServiceInformationList()
/*     */   {
/* 146 */     return this.otherServiceInformationList;
/*     */   }
/*     */ 
/*     */   public void setOtherServiceInformationList(List<Osi> otherServiceInformationList)
/*     */   {
/* 153 */     this.otherServiceInformationList = otherServiceInformationList;
/*     */   }
/*     */ 
/*     */   public List<TourCode> getTourCodeList()
/*     */   {
/* 160 */     return this.tourCodeList;
/*     */   }
/*     */ 
/*     */   public void setTourCodeList(List<TourCode> tourCodeList)
/*     */   {
/* 167 */     this.tourCodeList = tourCodeList;
/*     */   }
/*     */ 
/*     */   public List<EndorsementInformation> getEndorsementInformationList()
/*     */   {
/* 174 */     return this.endorsementInformationList;
/*     */   }
/*     */ 
/*     */   public void setEndorsementInformationList(List<EndorsementInformation> endorsementInformationList)
/*     */   {
/* 181 */     this.endorsementInformationList = endorsementInformationList;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Passenger
 * JD-Core Version:    0.6.0
 */