/*    */ package com.travelsky.hub.model.peentity.meal.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class ClassStatusInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8656841510529372674L;
/*    */   private String classOfServiceType;
/*    */   private String classOfServiceTypeAvailable;
/*    */ 
/*    */   public String getClassOfServiceType()
/*    */   {
/* 39 */     return this.classOfServiceType;
/*    */   }
/*    */ 
/*    */   public void setClassOfServiceType(String classOfServiceType)
/*    */   {
/* 46 */     this.classOfServiceType = classOfServiceType;
/*    */   }
/*    */ 
/*    */   public String getClassOfServiceTypeAvailable()
/*    */   {
/* 53 */     return this.classOfServiceTypeAvailable;
/*    */   }
/*    */ 
/*    */   public void setClassOfServiceTypeAvailable(String classOfServiceTypeAvailable)
/*    */   {
/* 60 */     this.classOfServiceTypeAvailable = classOfServiceTypeAvailable;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.meal.output.ClassStatusInfo
 * JD-Core Version:    0.6.0
 */