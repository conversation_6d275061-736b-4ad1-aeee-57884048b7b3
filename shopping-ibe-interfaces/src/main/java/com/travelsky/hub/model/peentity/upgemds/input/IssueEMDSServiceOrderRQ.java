/*     */ package com.travelsky.hub.model.peentity.upgemds.input;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class IssueEMDSServiceOrderRQ
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 2495524581902654882L;
/*     */   private BookingTicketingRefID bookingTicketingRefID;
/*     */   private Reference reference;
/*     */   private Segment segment;
/*     */   private Traveller traveller;
/*     */   private OrderItem orderItem;
/*     */   private PaymentDetail paymentDetail;
/*     */   private String airlineVendorID;
/*     */ 
/*     */   public String getAirlineVendorID()
/*     */   {
/*  59 */     return this.airlineVendorID;
/*     */   }
/*     */ 
/*     */   public void setAirlineVendorID(String airlineVendorID)
/*     */   {
/*  66 */     this.airlineVendorID = airlineVendorID;
/*     */   }
/*     */ 
/*     */   public BookingTicketingRefID getBookingTicketingRefID()
/*     */   {
/*  73 */     return this.bookingTicketingRefID;
/*     */   }
/*     */ 
/*     */   public void setBookingTicketingRefID(BookingTicketingRefID bookingTicketingRefID)
/*     */   {
/*  80 */     this.bookingTicketingRefID = bookingTicketingRefID;
/*     */   }
/*     */ 
/*     */   public Reference getReference()
/*     */   {
/*  87 */     return this.reference;
/*     */   }
/*     */ 
/*     */   public void setReference(Reference reference)
/*     */   {
/*  94 */     this.reference = reference;
/*     */   }
/*     */ 
/*     */   public Segment getSegment()
/*     */   {
/* 101 */     return this.segment;
/*     */   }
/*     */ 
/*     */   public void setSegment(Segment segment)
/*     */   {
/* 108 */     this.segment = segment;
/*     */   }
/*     */ 
/*     */   public Traveller getTraveller()
/*     */   {
/* 115 */     return this.traveller;
/*     */   }
/*     */ 
/*     */   public void setTraveller(Traveller traveller)
/*     */   {
/* 122 */     this.traveller = traveller;
/*     */   }
/*     */ 
/*     */   public OrderItem getOrderItem()
/*     */   {
/* 129 */     return this.orderItem;
/*     */   }
/*     */ 
/*     */   public void setOrderItem(OrderItem orderItem)
/*     */   {
/* 136 */     this.orderItem = orderItem;
/*     */   }
/*     */ 
/*     */   public PaymentDetail getPaymentDetail()
/*     */   {
/* 143 */     return this.paymentDetail;
/*     */   }
/*     */ 
/*     */   public void setPaymentDetail(PaymentDetail paymentDetail)
/*     */   {
/* 150 */     this.paymentDetail = paymentDetail;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.upgemds.input.IssueEMDSServiceOrderRQ
 * JD-Core Version:    0.6.0
 */