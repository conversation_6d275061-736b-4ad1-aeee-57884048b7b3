/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class PrintPsgInfoOutput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 4739986899459874781L;
/*    */   private String btpstream;
/*    */ 
/*    */   public String getBtpstream()
/*    */   {
/* 26 */     return this.btpstream;
/*    */   }
/*    */ 
/*    */   public void setBtpstream(String btpstream)
/*    */   {
/* 34 */     this.btpstream = btpstream;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PrintPsgInfoOutput
 * JD-Core Version:    0.6.0
 */