/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class InBoundInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String filingAirline;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String cabinType;
/*     */   private String arrivalAirport;
/*     */   private String seatInfo;
/*     */   private String parentCabinType;
/*     */ 
/*     */   public InBoundInfo()
/*     */   {
/*  31 */     this.filingAirline = "";
/*  32 */     this.flightNumber = "";
/*  33 */     this.flightDate = "";
/*  34 */     this.cabinType = "";
/*  35 */     this.arrivalAirport = "";
/*  36 */     this.parentCabinType = "";
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  43 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  50 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  58 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  65 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getFilingAirline()
/*     */   {
/*  72 */     return this.filingAirline;
/*     */   }
/*     */ 
/*     */   public void setFilingAirline(String filingAirline)
/*     */   {
/*  79 */     this.filingAirline = filingAirline;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/*  86 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/*  93 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 100 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 107 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatInfo()
/*     */   {
/* 114 */     return this.seatInfo;
/*     */   }
/*     */ 
/*     */   public void setSeatInfo(String seatInfo)
/*     */   {
/* 121 */     this.seatInfo = seatInfo;
/*     */   }
/*     */ 
/*     */   public String getParentCabinType()
/*     */   {
/* 128 */     return this.parentCabinType;
/*     */   }
/*     */ 
/*     */   public void setParentCabinType(String parentCabinType)
/*     */   {
/* 135 */     this.parentCabinType = parentCabinType;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.InBoundInfo
 * JD-Core Version:    0.6.0
 */