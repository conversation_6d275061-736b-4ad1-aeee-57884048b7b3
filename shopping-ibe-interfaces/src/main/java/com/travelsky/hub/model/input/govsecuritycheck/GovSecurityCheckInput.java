/*     */ package com.travelsky.hub.model.input.govsecuritycheck;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class GovSecurityCheckInput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 4032112447509395248L;
/*     */   private String airlineCode;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String fromCity;
/*     */   private String destCity;
/*     */   private String flightClass;
/*     */   private String hostNumber;
/*     */   private String ticketNumber;
/*     */ 
/*     */   public String getFromCity()
/*     */   {
/*  39 */     return this.fromCity;
/*     */   }
/*     */ 
/*     */   public void setFromCity(String fromCity)
/*     */   {
/*  46 */     this.fromCity = fromCity;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  69 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  76 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getDestCity()
/*     */   {
/*  83 */     return this.destCity;
/*     */   }
/*     */ 
/*     */   public void setDestCity(String destCity)
/*     */   {
/*  90 */     this.destCity = destCity;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  96 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 103 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightClass()
/*     */   {
/* 109 */     return this.flightClass;
/*     */   }
/*     */ 
/*     */   public void setFlightClass(String flightClass)
/*     */   {
/* 116 */     this.flightClass = flightClass;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 123 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 130 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getTicketNumber()
/*     */   {
/* 137 */     return this.ticketNumber;
/*     */   }
/*     */ 
/*     */   public void setTicketNumber(String ticketNumber)
/*     */   {
/* 144 */     this.ticketNumber = ticketNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 150 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 157 */     this.flightDate = flightDate;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.govsecuritycheck.GovSecurityCheckInput
 * JD-Core Version:    0.6.0
 */