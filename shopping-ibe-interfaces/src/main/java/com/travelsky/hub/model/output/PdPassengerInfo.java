/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PdPassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 6523448847493885966L;
/*     */   private String airlineId;
/*     */   private String flightSuffix;
/*     */   private String flightNumber;
/*     */   private String flightDate;
/*     */   private String departureAirportCode;
/*     */   private String arrivalAirportCode;
/*     */   private String passengerFullName;
/*     */   private String chineseName;
/*     */   private List<BaggageInfo> baggageInfos;
/*     */   private IdentityInformation identityInformation;
/*     */   private List<OperationInformation> operationInformations;
/*     */   private String icsPassengerNameRecord;
/*     */   private String crsPassengerNameRecord;
/*     */   private String isSkyPriority;
/*     */   private String hostNumber;
/*     */   private String currentPrimaryClass;
/*     */   private String currentSubClass;
/*     */   private List<SeatInformation> seatInformations;
/*     */   private List<FrequentFlyerInformation> frequentFlyerInformations;
/*     */   private AdvancePassengerInformation advancePassengerInformation;
/*     */   private String groupName;
/*     */   private String groupCount;
/*     */   private String boardingNumber;
/*     */   private String standbyNumber;
/*     */   private MarketingCarrieFlightInformation marketingCarrieFlightInformation;
/*     */   private List<ElectronicTicketInformation> electronicTicketInformations;
/*     */   private List<SpecialServicesInformation> specialServicesInformations;
/*     */   private List<TransferOrRecheckInformation> transferOrRecheckInformations;
/*     */   private String passengerStatus;
/*     */   private String passengerType;
/*     */   private List<PassengerOtherInfo> passengerOtherInfos;
/*     */   private List<GovernmentSecurityInformation> governmentSecurityInformations;
/*     */   private List<RemarksInfo> remarksInfos;
/*     */   private List<ContactInformation> contactInformation;
/*     */   private String fba;
/*     */   private String gfba;
/*     */ 
/*     */   public String getAirlineId()
/*     */   {
/* 170 */     return this.airlineId;
/*     */   }
/*     */ 
/*     */   public void setAirlineId(String airlineId)
/*     */   {
/* 177 */     this.airlineId = airlineId;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 184 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightSuffix()
/*     */   {
/* 191 */     return this.flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightSuffix(String flightSuffix)
/*     */   {
/* 198 */     this.flightSuffix = flightSuffix;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 205 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getFlightDate()
/*     */   {
/* 212 */     return this.flightDate;
/*     */   }
/*     */ 
/*     */   public void setFlightDate(String flightDate)
/*     */   {
/* 219 */     this.flightDate = flightDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirportCode()
/*     */   {
/* 226 */     return this.departureAirportCode;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirportCode(String departureAirportCode)
/*     */   {
/* 233 */     this.departureAirportCode = departureAirportCode;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirportCode()
/*     */   {
/* 240 */     return this.arrivalAirportCode;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirportCode(String arrivalAirportCode)
/*     */   {
/* 247 */     this.arrivalAirportCode = arrivalAirportCode;
/*     */   }
/*     */ 
/*     */   public String getPassengerFullName()
/*     */   {
/* 254 */     return this.passengerFullName;
/*     */   }
/*     */ 
/*     */   public void setPassengerFullName(String passengerFullName)
/*     */   {
/* 261 */     this.passengerFullName = passengerFullName;
/*     */   }
/*     */ 
/*     */   public String getChineseName()
/*     */   {
/* 268 */     return this.chineseName;
/*     */   }
/*     */ 
/*     */   public void setChineseName(String chineseName)
/*     */   {
/* 275 */     this.chineseName = chineseName;
/*     */   }
/*     */ 
/*     */   public List<BaggageInfo> getBaggageInfos()
/*     */   {
/* 282 */     return this.baggageInfos;
/*     */   }
/*     */ 
/*     */   public void setBaggageInfos(List<BaggageInfo> baggageInfos)
/*     */   {
/* 289 */     this.baggageInfos = baggageInfos;
/*     */   }
/*     */ 
/*     */   public IdentityInformation getIdentityInformation()
/*     */   {
/* 296 */     return this.identityInformation;
/*     */   }
/*     */ 
/*     */   public void setIdentityInformation(IdentityInformation identityInformation)
/*     */   {
/* 303 */     this.identityInformation = identityInformation;
/*     */   }
/*     */ 
/*     */   public List<OperationInformation> getOperationInformations()
/*     */   {
/* 310 */     return this.operationInformations;
/*     */   }
/*     */ 
/*     */   public void setOperationInformations(List<OperationInformation> operationInformations)
/*     */   {
/* 317 */     this.operationInformations = operationInformations;
/*     */   }
/*     */ 
/*     */   public String getIcsPassengerNameRecord()
/*     */   {
/* 324 */     return this.icsPassengerNameRecord;
/*     */   }
/*     */ 
/*     */   public void setIcsPassengerNameRecord(String icsPassengerNameRecord)
/*     */   {
/* 331 */     this.icsPassengerNameRecord = icsPassengerNameRecord;
/*     */   }
/*     */ 
/*     */   public String getCrsPassengerNameRecord()
/*     */   {
/* 338 */     return this.crsPassengerNameRecord;
/*     */   }
/*     */ 
/*     */   public void setCrsPassengerNameRecord(String crsPassengerNameRecord)
/*     */   {
/* 345 */     this.crsPassengerNameRecord = crsPassengerNameRecord;
/*     */   }
/*     */ 
/*     */   public String getIsSkyPriority()
/*     */   {
/* 352 */     return this.isSkyPriority;
/*     */   }
/*     */ 
/*     */   public void setIsSkyPriority(String isSkyPriority)
/*     */   {
/* 359 */     this.isSkyPriority = isSkyPriority;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 366 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 373 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getCurrentPrimaryClass()
/*     */   {
/* 380 */     return this.currentPrimaryClass;
/*     */   }
/*     */ 
/*     */   public void setCurrentPrimaryClass(String currentPrimaryClass)
/*     */   {
/* 387 */     this.currentPrimaryClass = currentPrimaryClass;
/*     */   }
/*     */ 
/*     */   public String getCurrentSubClass()
/*     */   {
/* 394 */     return this.currentSubClass;
/*     */   }
/*     */ 
/*     */   public void setCurrentSubClass(String currentSubClass)
/*     */   {
/* 401 */     this.currentSubClass = currentSubClass;
/*     */   }
/*     */ 
/*     */   public List<SeatInformation> getSeatInformations()
/*     */   {
/* 408 */     return this.seatInformations;
/*     */   }
/*     */ 
/*     */   public void setSeatInformations(List<SeatInformation> seatInformations)
/*     */   {
/* 415 */     this.seatInformations = seatInformations;
/*     */   }
/*     */ 
/*     */   public List<FrequentFlyerInformation> getFrequentFlyerInformations()
/*     */   {
/* 422 */     return this.frequentFlyerInformations;
/*     */   }
/*     */ 
/*     */   public void setFrequentFlyerInformations(List<FrequentFlyerInformation> frequentFlyerInformations)
/*     */   {
/* 429 */     this.frequentFlyerInformations = frequentFlyerInformations;
/*     */   }
/*     */ 
/*     */   public AdvancePassengerInformation getAdvancePassengerInformation()
/*     */   {
/* 436 */     return this.advancePassengerInformation;
/*     */   }
/*     */ 
/*     */   public void setAdvancePassengerInformation(AdvancePassengerInformation advancePassengerInformation)
/*     */   {
/* 443 */     this.advancePassengerInformation = advancePassengerInformation;
/*     */   }
/*     */ 
/*     */   public String getGroupName()
/*     */   {
/* 450 */     return this.groupName;
/*     */   }
/*     */ 
/*     */   public void setGroupName(String groupName)
/*     */   {
/* 457 */     this.groupName = groupName;
/*     */   }
/*     */ 
/*     */   public String getGroupCount()
/*     */   {
/* 464 */     return this.groupCount;
/*     */   }
/*     */ 
/*     */   public void setGroupCount(String groupCount)
/*     */   {
/* 471 */     this.groupCount = groupCount;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 478 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 485 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getStandbyNumber()
/*     */   {
/* 492 */     return this.standbyNumber;
/*     */   }
/*     */ 
/*     */   public void setStandbyNumber(String standbyNumber)
/*     */   {
/* 499 */     this.standbyNumber = standbyNumber;
/*     */   }
/*     */ 
/*     */   public MarketingCarrieFlightInformation getMarketingCarrieFlightInformation()
/*     */   {
/* 506 */     return this.marketingCarrieFlightInformation;
/*     */   }
/*     */ 
/*     */   public void setMarketingCarrieFlightInformation(MarketingCarrieFlightInformation marketingCarrieFlightInformation)
/*     */   {
/* 513 */     this.marketingCarrieFlightInformation = marketingCarrieFlightInformation;
/*     */   }
/*     */ 
/*     */   public List<ElectronicTicketInformation> getElectronicTicketInformations()
/*     */   {
/* 520 */     return this.electronicTicketInformations;
/*     */   }
/*     */ 
/*     */   public void setElectronicTicketInformations(List<ElectronicTicketInformation> electronicTicketInformations)
/*     */   {
/* 527 */     this.electronicTicketInformations = electronicTicketInformations;
/*     */   }
/*     */ 
/*     */   public List<SpecialServicesInformation> getSpecialServicesInformations()
/*     */   {
/* 534 */     return this.specialServicesInformations;
/*     */   }
/*     */ 
/*     */   public void setSpecialServicesInformations(List<SpecialServicesInformation> specialServicesInformations)
/*     */   {
/* 541 */     this.specialServicesInformations = specialServicesInformations;
/*     */   }
/*     */ 
/*     */   public List<TransferOrRecheckInformation> getTransferOrRecheckInformations()
/*     */   {
/* 548 */     return this.transferOrRecheckInformations;
/*     */   }
/*     */ 
/*     */   public void setTransferOrRecheckInformations(List<TransferOrRecheckInformation> transferOrRecheckInformations)
/*     */   {
/* 555 */     this.transferOrRecheckInformations = transferOrRecheckInformations;
/*     */   }
/*     */ 
/*     */   public String getPassengerStatus()
/*     */   {
/* 562 */     return this.passengerStatus;
/*     */   }
/*     */ 
/*     */   public void setPassengerStatus(String passengerStatus)
/*     */   {
/* 569 */     this.passengerStatus = passengerStatus;
/*     */   }
/*     */ 
/*     */   public String getPassengerType()
/*     */   {
/* 576 */     return this.passengerType;
/*     */   }
/*     */ 
/*     */   public void setPassengerType(String passengerType)
/*     */   {
/* 583 */     this.passengerType = passengerType;
/*     */   }
/*     */ 
/*     */   public List<PassengerOtherInfo> getPassengerOtherInfos()
/*     */   {
/* 590 */     return this.passengerOtherInfos;
/*     */   }
/*     */ 
/*     */   public void setPassengerOtherInfos(List<PassengerOtherInfo> passengerOtherInfos)
/*     */   {
/* 597 */     this.passengerOtherInfos = passengerOtherInfos;
/*     */   }
/*     */ 
/*     */   public List<GovernmentSecurityInformation> getGovernmentSecurityInformations()
/*     */   {
/* 604 */     return this.governmentSecurityInformations;
/*     */   }
/*     */ 
/*     */   public void setGovernmentSecurityInformations(List<GovernmentSecurityInformation> governmentSecurityInformations)
/*     */   {
/* 611 */     this.governmentSecurityInformations = governmentSecurityInformations;
/*     */   }
/*     */ 
/*     */   public List<RemarksInfo> getRemarksInfos()
/*     */   {
/* 618 */     return this.remarksInfos;
/*     */   }
/*     */ 
/*     */   public void setRemarksInfos(List<RemarksInfo> remarksInfos)
/*     */   {
/* 625 */     this.remarksInfos = remarksInfos;
/*     */   }
/*     */ 
/*     */   public List<ContactInformation> getContactInformation()
/*     */   {
/* 632 */     return this.contactInformation;
/*     */   }
/*     */ 
/*     */   public void setContactInformation(List<ContactInformation> contactInformation)
/*     */   {
/* 639 */     this.contactInformation = contactInformation;
/*     */   }
/*     */ 
/*     */   public String getFba()
/*     */   {
/* 647 */     return this.fba;
/*     */   }
/*     */ 
/*     */   public void setFba(String fba)
/*     */   {
/* 655 */     this.fba = fba;
/*     */   }
/*     */ 
/*     */   public String getGfba()
/*     */   {
/* 663 */     return this.gfba;
/*     */   }
/*     */ 
/*     */   public void setGfba(String gfba)
/*     */   {
/* 671 */     this.gfba = gfba;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.PdPassengerInfo
 * JD-Core Version:    0.6.0
 */