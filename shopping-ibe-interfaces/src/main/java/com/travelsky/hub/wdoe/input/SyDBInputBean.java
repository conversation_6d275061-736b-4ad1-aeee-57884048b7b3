/*    */ package com.travelsky.hub.wdoe.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SyDBInputBean extends BaseInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -8515895274527180292L;
/*    */   private String flightTime;
/*    */ 
/*    */   public String getFlightTime()
/*    */   {
/* 24 */     return this.flightTime;
/*    */   }
/*    */ 
/*    */   public void setFlightTime(String flightTime)
/*    */   {
/* 32 */     this.flightTime = flightTime;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.input.SyDBInputBean
 * JD-Core Version:    0.6.0
 */