/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class CheckedSegbean
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String index;
/*     */   private String airlineCode;
/*     */   private String flightNo;
/*     */   private String suffix;
/*     */   private String deptCity;
/*     */   private String deptCountry;
/*     */   private String deptDate;
/*     */   private String deptTime;
/*     */   private String arrivalCity;
/*     */   private String arrivalCountry;
/*     */   private String arrivalDate;
/*     */   private String arrivalTime;
/*     */   private Boolean transferInd;
/*     */   private String status;
/*     */ 
/*     */   public String getIndex()
/*     */   {
/*  78 */     return this.index;
/*     */   }
/*     */ 
/*     */   public void setIndex(String index)
/*     */   {
/*  85 */     this.index = index;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/*  92 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/*  99 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNo()
/*     */   {
/* 106 */     return this.flightNo;
/*     */   }
/*     */ 
/*     */   public void setFlightNo(String flightNo)
/*     */   {
/* 113 */     this.flightNo = flightNo;
/*     */   }
/*     */ 
/*     */   public String getSuffix()
/*     */   {
/* 120 */     return this.suffix;
/*     */   }
/*     */ 
/*     */   public void setSuffix(String suffix)
/*     */   {
/* 127 */     this.suffix = suffix;
/*     */   }
/*     */ 
/*     */   public String getDeptCity()
/*     */   {
/* 134 */     return this.deptCity;
/*     */   }
/*     */ 
/*     */   public void setDeptCity(String deptCity)
/*     */   {
/* 141 */     this.deptCity = deptCity;
/*     */   }
/*     */ 
/*     */   public String getDeptCountry()
/*     */   {
/* 148 */     return this.deptCountry;
/*     */   }
/*     */ 
/*     */   public void setDeptCountry(String deptCountry)
/*     */   {
/* 155 */     this.deptCountry = deptCountry;
/*     */   }
/*     */ 
/*     */   public String getDeptDate()
/*     */   {
/* 162 */     return this.deptDate;
/*     */   }
/*     */ 
/*     */   public void setDeptDate(String deptDate)
/*     */   {
/* 169 */     this.deptDate = deptDate;
/*     */   }
/*     */ 
/*     */   public String getDeptTime()
/*     */   {
/* 176 */     return this.deptTime;
/*     */   }
/*     */ 
/*     */   public void setDeptTime(String deptTime)
/*     */   {
/* 183 */     this.deptTime = deptTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalCity()
/*     */   {
/* 190 */     return this.arrivalCity;
/*     */   }
/*     */ 
/*     */   public void setArrivalCity(String arrivalCity)
/*     */   {
/* 197 */     this.arrivalCity = arrivalCity;
/*     */   }
/*     */ 
/*     */   public String getArrivalCountry()
/*     */   {
/* 204 */     return this.arrivalCountry;
/*     */   }
/*     */ 
/*     */   public void setArrivalCountry(String arrivalCountry)
/*     */   {
/* 211 */     this.arrivalCountry = arrivalCountry;
/*     */   }
/*     */ 
/*     */   public String getArrivalDate()
/*     */   {
/* 218 */     return this.arrivalDate;
/*     */   }
/*     */ 
/*     */   public void setArrivalDate(String arrivalDate)
/*     */   {
/* 225 */     this.arrivalDate = arrivalDate;
/*     */   }
/*     */ 
/*     */   public String getArrivalTime()
/*     */   {
/* 232 */     return this.arrivalTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalTime(String arrivalTime)
/*     */   {
/* 239 */     this.arrivalTime = arrivalTime;
/*     */   }
/*     */ 
/*     */   public Boolean getTransferInd()
/*     */   {
/* 246 */     return this.transferInd;
/*     */   }
/*     */ 
/*     */   public void setTransferInd(Boolean transferInd)
/*     */   {
/* 253 */     this.transferInd = transferInd;
/*     */   }
/*     */ 
/*     */   public String getStatus()
/*     */   {
/* 260 */     return this.status;
/*     */   }
/*     */ 
/*     */   public void setStatus(String status)
/*     */   {
/* 267 */     this.status = status;
/*     */   }
/*     */ }

/* Location:           E:\code\hna\travelskysvc\WEB-INF\classes\
 * Qualified Name:     com.travelsky.hub.model.output.CheckedSegbean
 * JD-Core Version:    0.6.0
 */