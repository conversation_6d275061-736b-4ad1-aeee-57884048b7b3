/*    */ package com.travelsky.hub.util;
/*    */ 
/*    */ public final class ErrCode
/*    */ {
/*    */   public static final long SUCESS = 0L;
/*    */   public static final String SUCCESS = "0";
/*    */   public static final long ERR_SVC = -10000L;
/*    */   public static final long ERR_SVC_TIMEOUT = -10001L;
/*    */   public static final String ERR_CONNECTION = "-1000";
/*    */   public static final String ERR_SESSION_CODE = "-1011";
/*    */   public static final String ERR_SESSION_CODE_NULL = "-1015";
/*    */   public static final String ERR_SESSION_CODE_NOT_LOGIN = "-1055";
/*    */   public static final String ERR_SESSION_CODE_LOGIN_FAIL = "-1057";
/*    */   public static final String ERR_SESSION_MAX = "051-04-01-00003";
/*    */   public static final String ERR_SESSION_CODE_NULL_SEC = "-2106";
/* 30 */   public static String ERR_SESSION_TRR_INVALID = "PE-0029-19";
/* 31 */   public static String ERR_SESSION_IS_EMPTY = "PE-8001-19";
/*    */   public static final long ERR_QUERY_FLIGHT_PARAMS = -10101L;
/*    */   public static final long ERR_QUERY_FLIGHT_NOT_FOUND = -10102L;
/*    */   public static final long ERR_QUERY_FLIGHT_REMOTE_SVC = -10103L;
/*    */   public static final long ERR_QUERY_TKT_PARAMS = -10201L;
/*    */   public static final long ERR_QUERY_TKT_NOT_FOUND = -10202L;
/*    */   public static final long ERR_QUERY_TKT_REMOTE_SVC = -10203L;
/*    */   public static final long ERR_QUERY_FFP_PARAMS = -10301L;
/*    */   public static final long ERR_QUERY_FFP_NOT_FOUND = -10302L;
/*    */   public static final long ERR_QUERY_FFP_REMOTE_SVC = -10303L;
/*    */   public static final long ERR_SEAT_RESERVE_PARAMS = -10401L;
/*    */   public static final long ERR_SEAT_RESERVE_CONFLICT = -10402L;
/*    */   public static final long ERR_SEAT_RESERVE_REMOTE_SVC = -10403L;
/*    */   public static final long ERR_SEAT_RESERVE_CANNOT_ASR = -10404L;
/*    */   public static final long ERR_SEAT_INPUT = -10405L;
/*    */   public static final long ERR_PSG_ALREADY_ASR = -10406L;
/*    */   public static final long ERR_QUERY_WEATHER_PARAMS = -10501L;
/*    */   public static final long ERR_QUERY_WEATHER_REMOTE_SVC = -10502L;
/*    */   public static final long ERR_QUERY_BOARD_PARAMS = -10601L;
/*    */   public static final long ERR_QUERY_BOARD_REMOTE_SVC = -10602L;
/*    */   public static final long ERR_QUERY_PSRSTATUS_PARAMS = -10701L;
/*    */   public static final String ERR_QUERY_PSRSTATUS_FLIGHT = "-10702";
/*    */   public static final long ERR_QUERY_PSRSTATUS_OTHERS = -10703L;
/*    */   public static final long ERR_QUERY_PSRSTATUS_REMOTE_SVC = -10704L;
/*    */   public static final String ERR_QUERY_PSRSTATUSHO_FLIGHT = "-10705";
/*    */   public static final long ERR_CKI_PSR_PARAMS = -10801L;
/*    */   public static final long ERR_CKI_PSR_SEAT_CONFLICT = -10802L;
/*    */   public static final long ERR_CKI_PSR_OR_FLIGHT_STATUS = -10803L;
/*    */   public static final long ERR_CKI_PSR_REMOTE_SVC = -10804L;
/*    */   public static final long ERR_QUERY_ASR_PARAMS = -10901L;
/*    */   public static final long ERR_QUERY_ASR_REMOTE_SVC = -10902L;
/*    */   public static final long ERR_PW_PSR_PARAMS = -11001L;
/*    */   public static final long ERR_PW_PSR_NOT_CHECKIN = -11002L;
/*    */   public static final long ERR_PW_PSR_OR_FLIGHT_STATUS = -11003L;
/*    */   public static final long ERR_PW_PSR_REMOTE_SVC = -11104L;
/*    */   public static final long ERR_PW_PSR_BAG_ERR = -11105L;
/*    */   public static final long ERR_PW_CHANNEL_ERR = -11106L;
/*    */   public static final long ERR_PW_XRES_ERR = -11107L;
/*    */   public static final long ERR_QUEYR_AVAILABLEFLIGHT_PARAMS = -11201L;
/*    */   public static final long ERR_QUERY_AVAILABLEFLIGHT_REMOTE_SVC = -11202L;
/*    */   public static final long ERR_QUERY_AVAILABLEFLIGHT_REMOTE_SVC_IPLIMITED = -11203L;
/*    */   public static final long ERR_QUEYR_FLIGHTINIT_PARAMS = -11301L;
/*    */   public static final long ERR_QUERY_FLIGHTINIT_REMOTE_SVC = -11302L;
/*    */   public static final long ERR_QUEYR_TICKETSTATUS_PARAMS = -11401L;
/*    */   public static final long ERR_QUERY_TICKETSTATUS_REMOTE_SVC = -11402L;
/*    */   public static final long ERR_QUEYR_SEATCHART_PARAMS = -11501L;
/*    */   public static final long ERR_QUERY_SEATCHART_REMOTE_SVC = -11502L;
/*    */   public static final long ERR_MULTISEAT_RESERVE_PARAMS = -11601L;
/*    */   public static final long ERR_MULTISEAT_RESERVE_CONFLICT = -11602L;
/*    */   public static final long ERR_MULTISEAT_RESERVE_REMOTE_SVC = -11603L;
/*    */   public static final long ERR_MULTISEAT_RESERVE_CANNOT_ASR = -11604L;
/*    */   public static final long ERR_MULTISEAT_INPUT = -11605L;
/*    */   public static final long ERR_MULTIPSG_ALREADY_ASR = -11606L;
/*    */   public static final long ERR_WDOE_SVC = -5001L;
/*    */   public static final long ERR_WDOE_SVC_BUSINESS = -5002L;
/*    */   public static final String ERR_WDOE_RESULT_XML_FORMAT = "-5003";
/*    */   public static final long ERR_WDOE_SVC_TIMEOUT = -5004L;
/*    */   public static final long ERR_IBE_SVC = -6001L;
/*    */   public static final long ERR_IBE_SVC_BUSINESS = -6002L;
/*    */   public static final long DETR_TKT_NOTFOUND_ERROR = -11701L;
/*    */   public static final String IBE_SYSTEM_ERROR = "9000";
/*    */   public static final String DETRTKTSEGMENT_NOTFOUND_ERROR = "9002";
/*    */   public static final String RTRESULT_NOTFOUND_ERROR = "9003";
/*    */   public static final String FQTV_CARD_NOTFOUND_ERROR = "9004";
/*    */   public static final String DETRTKTRESULT_NOTFOUND_ERROR = "9005";
/*    */   public static final String PSGNAME_NOTMATCH_ERROR = "9006";
/*    */   public static final String GET_ADMSEATMAP_ERROR = "9007";
/*    */   public static final String SEAT_PREF_NOTFOUND_ERROR = "9008";
/*    */   public static final String SEATNO_INPUT_ERROR = "9009";
/*    */   public static final String SEATNO_NOTFOUND_ERROR = "9010";
/*    */   public static final String SEAT_ISAISLE_ERROR = "9011";
/*    */   public static final String SEAT_ISBOOKED_ERROR = "9012";
/*    */   public static final String SEAT_NOTAVAILABLE_ERROR = "9013";
/*    */   public static final String SEAT_PREF_INPUT_ERROR = "9014";
/*    */   public static final String PSG_ALREADY_ASR_ERROR = "9015";
/*    */   public static final String OPERATION_TIMEOUT_ERROR = "9016";
/*    */   public static final long ERR_REPRINT_PARAMS = -11801L;
/*    */   public static final long ERR_REPRINT_REMOTE_SVC = -11802L;
/*    */   public static final long ERR_MULTI_PSR_CHECKIN_PARAMS = -11901L;
/*    */   public static final long ERR_MULTI_PSR_CHECKIN_REMOTE_SVC = -11902L;
/*    */   public static final long ERR_PREHB_PU_OPTIONS_PARAMS = -12001L;
/*    */   public static final long ERR_PREHB_PU_OPTIONS_REMOTE_SVC = -12002L;
/*    */   public static final long ERR_HBPU_OPTIONS_PARAMS = -14001L;
/*    */   public static final long ERR_HBPU_OPTIONS_REMOTE_SVC = -14002L;
/*    */   public static final long ERR_FFB_PARAMS = -13001L;
/*    */   public static final long ERR_FFB_REMOTE_SVC = -13002L;
/*    */   public static final long ERR_SYCONTROL_PARAMS = -15001L;
/*    */   public static final long ERR_SYCONTROL_REMOTE_SVC = -15002L;
/*    */   public static final long ERR_CHEECKIN_STATUS_QUERY_PARAMS = -19101L;
/*    */   public static final long ERR_CONNECT_FLT_CONF_QUERY_PARAMS = -19201L;
/*    */   public static final long ERR_CONNECT_FLT_CHECKIN_PARAMS = -19301L;
/*    */   public static final long ERR_PASSENGER_UPDATE_PARAMS = -19401L;
/*    */   public static final long ERR_API_QUERY_PARAMS = -20001L;
/*    */   public static final long ERR_API_QUERY_SVC = -20002L;
/*    */   public static final long ERR_API_QUERY_REMOTE_SVC = -20003L;
/*    */   public static final long ERR_AQQCONF_QUERY_PARAMS = -21001L;
/*    */   public static final long ERR_AQQCONF_QUERY_SVC = -21002L;
/*    */   public static final long ERR_AQQCONF_QUERY_REMOTE_SVC = -21003L;
/*    */   public static final long ERR_AQQRES_QUERY_PARAMS = -22001L;
/*    */   public static final long ERR_AQQRES_QUERY_SVC = -22002L;
/*    */   public static final long ERR_AQQRES_QUERY_REMOTE_SVC = -22003L;
/*    */   public static final long ERR_API_UPDATE_PARAMS = -23001L;
/*    */   public static final long ERR_API_UPDATE_SVC = -23002L;
/*    */   public static final long ERR_API_UPDATE_REMOTE_SVC = -23003L;
/*    */   public static final long ERR_AQQ_REQUEST_PARAMS = -24001L;
/*    */   public static final long ERR_AQQ_REQUEST_SVC = -24002L;
/*    */   public static final long ERR_AQQ_REQUEST_REMOTE_SVC = -24003L;
/*    */   public static final long ERR_QUERY_SEATMAP_PARAMS = -25001L;
/*    */   public static final long ERR_QUERY_SEATMAP_SVC = -25002L;
/*    */   public static final long ERR_QUERY_SEATMAP_REMOTE_SVC = -25003L;
/*    */   public static final String ERR_QUERY_SEATMAP_NOSEAT = "-90191";
/*    */   public static final String ERR_QUERY_SEATMAP_CONFIG = "-90301";
/*    */   public static final long ERR_PREFER_SEAT_PARAMS = -28001L;
/*    */   public static final long ERR_PREFER_SEAT_SVC = -28002L;
/*    */   public static final long ERR_PREFER_SEAT_REMOTE_SVC = -28003L;
/*    */   public static final int WDOE_ERR_EBP_AUTHINFO = -2509;
/*    */   public static final int WDOE_ERR_EBP_CALLSVC_FAILED = -2510;
/*    */   public static final int WDOE_ERR_EBP_ISNULL = -2511;
/*    */   public static final int WDOE_ERR_EBP_BARCODE_FAIL = -2512;
/*    */   public static final int WDOE_ERR_EBP_FAIL = -2513;
/*    */   public static final int WDOE_ERR_EBP_AUTH = -2514;
/*    */   public static final int WDOE_ERR_EBP_RULE = -2515;
/*    */   public static final int WDOE_ERR_EBP_RULE_MISMATCH = -2516;
/*    */   public static final String WDOE_ERR_SEAT_RULE_NULL = "-2706";
/*    */   public static final String WDOE_ERR_UPGFLT_NULL = "-10040";
/*    */   public static final long ERR_GET_EBOARDINGPASS_PARAMS = -26001L;
/*    */   public static final long ERR_HOST_PR_PARAMS = -27001L;
/*    */   public static final long ERR_HOST_PR_SVC = -27002L;
/*    */   public static final long ERR_HOST_PR_REMOTE_SVC = -27003L;
/*    */   public static final int ERROR_COMMON_CONNECT = 2;
/*    */   public static final int ERROR_CONNECT_TIMEOUT = 7;
/*    */   public static final int ERROR_COMMON_SERVICE = 3;
/*    */   public static final int ERROR_COMMON_MALFORMEDURL = 5;
/*    */   public static final int ERR_SYPRQUERY_QUERY = -200103;
/*    */   public static final int ERR_SYPRQUERY_FLIGHDATE = -200101;
/*    */   public static final int ERR_SYPRQUERY_FLIGHTNO = -200102;
/*    */   public static final int ERR_SYPRQUERY_DEPTAPTCODE = -200103;
/*    */   public static final int ERR_SYPRQUERY_ARVAPTCODE = -200104;
/*    */   public static final int ERR_SYPRQUERY_ETCODE = -200105;
/*    */   public static final int ERR_WEATHER_QUERY = -200601;
/*    */   public static final int ERR_WEATHER_AIRPORTCODE = -200601;
/*    */   public static final int ERR_FLIGHTBORD_QUERY = -200801;
/*    */   public static final int ERR_FLIGHTBORD_FLIGHDATE = -200802;
/*    */   public static final int ERR_FLIGHTBORD_FLIGHTNO = -200801;
/*    */   public static final int ERR_FLIGHTBORD_DEPTAIRPORT = -200803;
/*    */   public static final int ERR_PSRCHECKIN_QUERY = -200504;
/*    */   public static final int ERR_PSRCHECKIN_FLIGHTDATE = -200502;
/*    */   public static final int ERR_PSRCHECKIN_FLIGHTNO = -200501;
/*    */   public static final int ERR_PSRCHECKIN_SEATNO = -200503;
/*    */   public static final int ERR_PSRCHECKIN_FROMCITY = -200507;
/*    */   public static final int ERR_PSRCHECKIN_TOCITY = -200506;
/*    */   public static final int ERR_PSRCHECKIN_TOURINDEX = -200512;
/*    */   public static final int ERR_PSRCHECKIN_ETCODE = -200511;
/*    */   public static final int ERR_PSRCHECKIN_CHECKMSG = -200514;
/*    */   public static final int ERR_PSRCHECKIN_EXSTTYPE = -200515;
/*    */   public static final int ERR_PSRCHECKIN_EXSTWEIGHT = -200516;
/*    */   public static final int ERR_PSRCHECKIN_EXSTTYPENULL = -200517;
/*    */   public static final int ERR_DETRINPUT_QUERY = -200001;
/*    */   public static final int ERR_DETRINPUT_CERTIFNUMBER = -200001;
/*    */   public static final int ERR_DETRINPUT_CERTIFTYPE = -200002;
/*    */   public static final int ERR_MULTICHECKIN_QUERY = -200918;
/*    */   public static final int ERR_MULTICHECKIN_QUERYSIZE = -900901;
/*    */   public static final int ERR_MULTICHECKIN_FLIGHTNO = -200901;
/*    */   public static final int ERR_MULTICHECKIN_FLIGHTDATE = -200902;
/*    */   public static final int ERR_MULTICHECKIN_SEATNO = -200903;
/*    */   public static final int ERR_MULTICHECKIN_TOCITY = -200906;
/*    */   public static final int ERR_MULTICHECKIN_FROMCITY = -200907;
/*    */   public static final int ERR_MULTICHECKIN_TKTNUM = -200911;
/*    */   public static final int ERR_MULTICHECKIN_TOURINDEX = -200912;
/*    */   public static final int ERR_MULTICHECKIN_HOSTNUM = -200913;
/*    */   public static final int ERR_MULTICHECKIN_INDEX = -200915;
/*    */   public static final int ERR_MULTICHECKIN_TKTID = -200916;
/*    */   public static final int ERR_MULTICHECKIN_SEQNUM = -200917;
/*    */   public static final int ERR_MULTICHECKIN_PARTNER = -200918;
/*    */   public static final int ERR_MULTICHECKIN_CKINMSG = -200932;
/*    */   public static final int ERR_PSGPWINFO_QUERY = -200706;
/*    */   public static final int ERR_PSGPWINFO_FLIGHTNO = -200701;
/*    */   public static final int ERR_PSGPWINFO_FLIGHTDATE = -200702;
/*    */   public static final int ERR_PSGPWINFO_TOCITY = -200703;
/*    */   public static final int ERR_PSGPWINFO_FROMCITY = -200704;
/*    */   public static final int ERR_PSGPWINFO_CERTID = -200705;
/*    */   public static final int ERR_PSGPWINFO_CERTTYPE = -200706;
/*    */   public static final int ERR_SEATCHART_QUERY = -200301;
/*    */   public static final int ERR_SEATCHART_FROMCITY = -200301;
/*    */   public static final int ERR_SEATCHART_CABIN = -200302;
/*    */   public static final int ERR_SEATCHART_FLIGHTDATE = -200303;
/*    */   public static final int ERR_SEATCHART_FLIGHTNO = -200304;
/*    */   public static final int ERR_SEATCHART_TOCITY = -200305;
/*    */   public static final int ERR_REPRINT_QUERY = -201203;
/*    */   public static final int ERR_REPRINT_AIRLINECODE = -201201;
/*    */   public static final int ERR_REPRINT_FLIGHTCLASS = -201202;
/*    */   public static final int ERR_REPRINT_FLIGHTDATE = -201203;
/*    */   public static final int ERR_REPRINT_FLIGHTNUM = -201204;
/*    */   public static final int ERR_REPRINT_FROMCITY = -201205;
/*    */   public static final int ERR_REPRINT_TOCITY = -201206;
/*    */   public static final int ERR_REPRINT_PASSNAME = -201209;
/*    */   public static final int ERR_REPRINT_REISSUE = -201210;
/*    */   public static final int ERR_REPRINT_TKTNUM = -201211;
/*    */   public static final int ERR_REPRINT_TOURINDEX = -201212;
/*    */   public static final int ERR_HBOPTION_QUERY = -200401;
/*    */   public static final int ERR_HBOPTION_AIRLINECODE = -200401;
/*    */   public static final int ERR_HBOPTION_FLIGHTNUM = -200402;
/*    */   public static final int ERR_HBOPTION_FLIGHTDATE = -200403;
/*    */   public static final int ERR_HBOPTION_HOSTNUM = -200404;
/*    */   public static final int ERR_HBOPTION_CABIN = -200405;
/*    */   public static final int ERR_HBOPTION_DESTCITY = -200406;
/*    */   public static final int ERR_HBOPTION_DEPTCITY = -200407;
/*    */   public static final int ERR_HBOPTION_SEATNO = -200408;
/*    */   public static final int ERR_HBOPTION_SEATNOORFFP = -200412;
/*    */   public static final int ERR_HBOPTION_FFINFO = -200413;
/*    */   public static final int ERR_CHECKINSTA_QUERY = -200201;
/*    */   public static final int ERR_CHECKINSTA_TKTNUM = -200201;
/*    */   public static final int ERR_CHECKINSTA_AIRLINECODE = -200202;
/*    */   public static final int ERR_CHECKINSTA_FLIGHTDATE = -200203;
/*    */   public static final int ERR_CHECKINSTA_DEPATUREFROM = -200205;
/*    */   public static final int ERR_UPDATEINPUT_QUERY = -201301;
/*    */   public static final int ERR_UPDATEINPUT_AIRLINECODE = -201301;
/*    */   public static final int ERR_UPDATEINPUT_FLIGHTNUM = -201302;
/*    */   public static final int ERR_UPDATEINPUT_FLIGHTDATE = -201303;
/*    */   public static final int ERR_UPDATEINPUT_PSRCLASS = -201305;
/*    */   public static final int ERR_UPDATEINPUT_TOCITY = -201306;
/*    */   public static final int ERR_UPDATEINPUT_FROMCITY = -201307;
/*    */   public static final int ERR_UPDATEINPUT_PASSMUN = -201308;
/*    */   public static final int ERR_UPDATEINPUT_TKTNUM = -201309;
/*    */   public static final int ERR_UPDATEINPUT_TOURINDEX = -201310;
/*    */   public static final int ERR_UPDATEINPUT_MSGTYPE = -201326;
/*    */   public static final int ERR_UPDATEINPUT_MSGTEXT = -201327;
/*    */   public static final int ERR_THROUGHFLIGHT_QUERY = -201001;
/*    */   public static final int ERR_THROUGHFLIGHT_AIRLINECODE = -201001;
/*    */   public static final int ERR_THROUGHFLIGHT_FLIGHTNUM = -201002;
/*    */   public static final int ERR_THROUGHFLIGHT_DEPTCITY = -201003;
/*    */   public static final int ERR_THROUGHFLIGHT_DESTCITY = -201004;
/*    */   public static final int ERR_HBPUOINPUT_QUERY = -201101;
/*    */   public static final int ERR_HBPUOINPUT_AIRLINECODE = -201101;
/*    */   public static final int ERR_HBPUOINPUT_FLIGHTNUM = -201102;
/*    */   public static final int ERR_HBPUOINPUT_FLIGHTDATE = -201103;
/*    */   public static final int ERR_HBPUOINPUT_HOSTNUM = -201104;
/*    */   public static final int ERR_HBPUOINPUT_PSRCLASS = -201105;
/*    */   public static final int ERR_HBPUOINPUT_TOCITY = -201106;
/*    */   public static final int ERR_HBPUOINPUT_FROMCITY = -201107;
/*    */   public static final int ERR_HBPUOINPUT_PASSMUN = -201108;
/*    */   public static final int ERR_HBPUOINPUT_TKTNUM = -201109;
/*    */   public static final int ERR_HBPUOINPUT_TOURINDEX = -201110;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_AIRLINECODE = -201113;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_FLIGHTNUM = -201114;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_FLIGHTDATE = -201115;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_HOSTNUM = -201116;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_CABIN = -201117;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_TOCITY = -201118;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_FROMCITY = -201119;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_NAME = -201120;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_TKTNUM = -201121;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_INDEX = -201122;
/*    */   public static final int ERR_HBPUOINPUT_TRROU_CHECKMSG = -201125;
/*    */   public static final int ERR_APIQUERYINPUT_QUERY = -201401;
/*    */   public static final int ERR_APIQUERYINPUT_AIRLINECODE = -201401;
/*    */   public static final int ERR_APIQUERYINPUT_FLIGHTNUM = -201402;
/*    */   public static final int ERR_APIQUERYINPUT_DEPARTAIRPORT = -201403;
/*    */   public static final int ERR_APIQUERYINPUT_ARRIAIREPORT = -201404;
/*    */   public static final int ERR_APIQUERYINPUT_DEPARDATE = -201405;
/*    */   public static final int ERR_APIQUERYINPUT_TKTNUMBER = -201406;
/*    */   public static final int ERR_APIQUERYINPUT_HOSTNUMBER = -201410;
/*    */   public static final int ERR_AQQQUERYINPUT_QUERY = -201601;
/*    */   public static final int ERR_AQQQUERYINPUT_AIRLINECODE = -201601;
/*    */   public static final int ERR_AQQQUERYINPUT_FLIGHTNUM = -201602;
/*    */   public static final int ERR_AQQQUERYINPUT_DESTCITY = -201603;
/*    */   public static final int ERR_AQQQUERYINPUT_DEPTCITY = -201604;
/*    */   public static final int ERR_AQQRESQUEST_QUERY = -201701;
/*    */   public static final int ERR_AQQRESQUEST_AIRLINE = -201701;
/*    */   public static final int ERR_AQQRESQUEST_FILGHTNUMBER = -201702;
/*    */   public static final int ERR_AQQRESQUEST_FROMCITY = -201703;
/*    */   public static final int ERR_AQQRESQUEST_DESTCITY = -201704;
/*    */   public static final int ERR_AQQRESQUEST_FLIGHTDATE = -201705;
/*    */   public static final int ERR_AQQRESQUEST_TKTNUMBER = -201706;
/*    */   public static final int ERR_AQQRESQUEST_FLIGHTCLASS = -201707;
/*    */   public static final int ERR_AQQRESQUEST_HOSTNUMBER = -201709;
/*    */   public static final int ERR_AQQRESQUERY_QUERY = -201801;
/*    */   public static final int ERR_AQQRESQUERY_AIRLINECODE = -201801;
/*    */   public static final int ERR_AQQRESQUERY_FLIGHTNUM = -201802;
/*    */   public static final int ERR_AQQRESQUERY_FROMCITY = -201803;
/*    */   public static final int ERR_AQQRESQUERY_FLIGHTDATE = -201804;
/*    */   public static final int ERR_AQQRESQUERY_TKTNUMBER = -201805;
/*    */   public static final int ERR_AQQRESQUERY_HOSTNUMBER = -201807;
/*    */   public static final int ERR_APIUPDATE_QUERY = -201501;
/*    */   public static final int ERR_APIUPDATE_AIRLINECODE = -201501;
/*    */   public static final int ERR_APIUPDATE_FLIGHTNUM = -201502;
/*    */   public static final int ERR_APIUPDATE_FROMCITY = -201503;
/*    */   public static final int ERR_APIUPDATE_DESTCITY = -201504;
/*    */   public static final int ERR_APIUPDATE_FLIGHTDATE = -201505;
/*    */   public static final int ERR_APIUPDATE_TKTNUM = -201506;
/*    */   public static final int ERR_APIUPDATE_FLIGHTCLASS = -201507;
/*    */   public static final int ERR_APIUPDATE_APIINFO = -201508;
/*    */   public static final int ERR_APIUPDATE_APIINFO_VISAINFO = -201509;
/*    */   public static final int ERR_APIUPDATE_APIINFO_OTHDOCINFO = -201510;
/*    */   public static final int ERR_APIUPDATE_APIINFO_HOMEADDRESS = -201511;
/*    */   public static final int ERR_APIUPDATE_APIINFO_DESTADDRESS = -201512;
/*    */   public static final int ERR_APIUPDATE_HOSTNUMBER = -201517;
/*    */   public static final int ERR_NEWSEATMAP_QUERY = -201901;
/*    */   public static final int ERR_NEWSEATMAP_AIRLINECODEY = -201901;
/*    */   public static final int ERR_NEWSEATMAP_FLIGHTNUM = -201902;
/*    */   public static final int ERR_NEWSEATMAP_FLIGHTORICITY = -201903;
/*    */   public static final int ERR_NEWSEATMAP_FLIGHTDESCITY = -201904;
/*    */   public static final int ERR_NEWSEATMAP_FLIGHTDATE = -201905;
/*    */   public static final int ERR_NEWSEATMAP_FLIGHTCLASS = -201906;
/*    */   public static final int ERR_NEWSEATMAP_PAXLEVEL = -201907;
/*    */   public static final int ERR_PREFERSEAT_QUERY = -202001;
/*    */   public static final int ERR_PREFERSEAT_FLIGHTNO = -202002;
/*    */   public static final int ERR_PREFERSEAT_FLIGHTORICITY = -202003;
/*    */   public static final int ERR_PREFERSEAT_FLIGHTDESCITY = -202004;
/*    */   public static final int ERR_PREFERSEAT_FLIGHTDATE = -202005;
/*    */   public static final int ERR_PREFERSEAT_FLIGHTCLASS = -202006;
/*    */   public static final int ERR_PREFERSEAT_PAXLEVEL = -202007;
/*    */   public static final int ERR_PREFERSEAT_SEATPERFRE = -202008;
/*    */   public static final int ERR_EBPINFO_QUERY = -202101;
/*    */   public static final int ERR_EBPINFO_DATASTREAM = -202101;
/*    */   public static final int ERR_SEATCHARTIN_QUERY = -202201;
/*    */   public static final int ERR_SEATCHARTIN_FLIGHTNO = -202201;
/*    */   public static final int ERR_SEATCHARTIN_FLIGHTDATE = -202202;
/*    */   public static final int ERR_SEATCHARTIN_FLIGHTORICITY = -202203;
/*    */   public static final int ERR_SEATCHARTIN_FLIGHTDESCITY = -202204;
/*    */   public static final int ERR_SEATCHARTIN_ETCODE = -202205;
/*    */   public static final int ERR_SEATCHARTIN_FLIGHTCLASS = -202206;
/*    */   public static final int ERR_QUERYCHECKCODE_QUERY = -202301;
/*    */   public static final int ERR_QUERYCHECKCODE_ETNUMBER = -202301;
/*    */   public static final int ERR_QUERYCHECKCODE_TOURINDEX = -202302;
/*    */   public static final int ERR_FLIGHTINFO_ISNULL = -202401;
/*    */   public static final int ERR_FLIGHTINFO_FLIGHTNUMBER = -202402;
/*    */   public static final int ERR_FLIGHTINFO_FLIGHTDATE = -202403;
/*    */   public static final int ERR_FLIGHTINFO_DEPARTUREAIRPORT = -202404;
/*    */   public static final int ERR_FLIGHTINFO_ARRIVALAIRPORT = -202405;
/*    */   public static final int ERR_FLIGHTINFO_AIRCODE = -202406;
/*    */   public static final int ERR_PSRIDENTITYINFO_ISNULL = -202501;
/*    */   public static final int ERR_PSRIDENTITYINFO_AIRCODE = -202502;
/*    */   public static final int ERR_PSRIDENTITYINFO_FLIGHTNUMBER = -202503;
/*    */   public static final int ERR_PSRIDENTITYINFO_FLIGHTDATE = -202504;
/*    */   public static final int ERR_PSRIDENTITYINFO_CENTTYPE = -202505;
/*    */   public static final int ERR_PSRIDENTITYINFO_CENTNO = -202506;
/*    */   public static final int ERR_PSRIDENTITYINFO_PSRLEVEL = -202507;
/*    */   public static final int ERR_PSRIDENTITYINFO_CURRENCY = -202508;
/*    */   public static final int ERR_UPGRATEORDERINFO_ISNULL = -202601;
/*    */   public static final int ERR_UPGRATEORDERINFO_FLIGHTNUMBER = -202602;
/*    */   public static final int ERR_UPGRATEORDERINFO_FLIGHTDATE = -202603;
/*    */   public static final int ERR_UPGRATEORDERINFO_DEPARTUREAIRPORT = -202604;
/*    */   public static final int ERR_UPGRATEORDERINFO_ARRIVALAIRPORT = -202605;
/*    */   public static final int ERR_UPGRATEORDERINFO_PSRNAME = -202606;
/*    */   public static final int ERR_UPGRATEORDERINFO_PNR = -202607;
/*    */   public static final int ERR_UPGRATEORDERINFO_TICKETNUMBER = -202608;
/*    */   public static final int ERR_UPGRATEORDERINFO_TOURINDEX = -202609;
/*    */   public static final int ERR_UPGRATEORDERINFO_CENTERTYPE = -202610;
/*    */   public static final int ERR_UPGRATEORDERINFO_CENTERNO = -202611;
/*    */   public static final int ERR_UPGRATEORDERINFO_CONTRACTINFO = -202612;
/*    */   public static final int ERR_UPGRATEORDERINFO_CABIN = -202613;
/*    */   public static final int ERR_UPGRATEORDERINFO_SEAT = -202614;
/*    */   public static final int ERR_UPGRATEORDERINFO_SENIORCABIN = -202615;
/*    */   public static final int ERR_UPGRATEORDERINFO_UPPRICE = -202616;
/*    */   public static final int ERR_UPGRATEORDERINFO_FLTDATEFORMAT = -202617;
/*    */   public static final int ERR_UPGRATEORDERINFO_CABINFORMAT = -202618;
/*    */   public static final int ERR_UPGRATEORDERINFO_SENIORCANINFORMAT = -202619;
/*    */   public static final int ERR_UPGRATEORDERINFO_SEATFORMAT = -202620;
/*    */   public static final int ERR_UPGINFO_ISNULL = -202701;
/*    */   public static final int ERR_UPGINFO_ORDERNUM = -202702;
/*    */   public static final int ERR_UPGINFO_PAYTYPE = -202703;
/*    */   public static final int ERR_UPGINFO_BILLNO = -202704;
/*    */   public static final int ERR_UPGINFO_BANKTYPE = -202705;
/*    */   public static final int ERR_PSRFLTINFO_ISNULL = -202801;
/*    */   public static final int ERR_PSRFLTINFO_FLIGHTNUMBER = -202802;
/*    */   public static final int ERR_PSRFLTINFO_FLIGHTDATE = -202803;
/*    */   public static final int ERR_PSRFLTINFO_DEPARTUREAIRPORT = -202804;
/*    */   public static final int ERR_PSRFLTINFO_ARRIVALAIRPORT = -202805;
/*    */   public static final int ERR_PSRFLTINFO_PSRNAME = -202806;
/*    */   public static final int ERR_PSRFLTINFO_TICKETNUMBER = -202807;
/*    */   public static final int ERR_PSRFLTINFO_TOURINDEX = -202808;
/*    */   public static final int ERR_UPGORDERUPDATE_ISNULL = -203101;
/*    */   public static final int ERR_UPGORDERUPDATE_ORDERNUM = -203102;
/*    */   public static final int ERR_UPGORDERUPDATE_ORDERSTATUS = -203103;
/*    */   public static final int ERR_GETUPGFLT_AIRLINE = -203201;
/*    */   public static final int ERR_GETORDERDETAIL_ORDERNUM = -202901;
/*    */   public static final int ERR_CANLEORDER_ORDERNUM = -203001;
/*    */   public static final int ERR_REFUNDORDER_ORDERNUM = -203003;
/*    */   public static final int ERR_PSRBAKINFO_ISNULL = -203010;
/*    */   public static final int ERR_PSRBAKINFO_AIRLINECODE = -203011;
/*    */   public static final int ERR_PSRBAKINFO_FLIGHTNUM = -203012;
/*    */   public static final int ERR_PSRBAKINFO_FLTDATE = -203013;
/*    */   public static final int ERR_PSRBAKINFO_DEPT = -203014;
/*    */   public static final int ERR_PSRBAKINFO_DEST = -203015;
/*    */   public static final int ERR_PSRBAKINFO_CABIN = -203016;
/*    */   public static final int ERR_PSRBAKINFO_FLTDATEFORMAT = -203017;
/*    */   public static final int ERR_SEATORDERINFO_ISNULL = -203301;
/*    */   public static final int ERR_SEATORDERINFO_FLIGHTNUMBER = -203302;
/*    */   public static final int ERR_SEATORDERINFO_FLIGHTDATE = -203303;
/*    */   public static final int ERR_SEATORDERINFO_DEPARTUREAIRPORT = -203304;
/*    */   public static final int ERR_SEATORDERINFO_ARRIVALAIRPORT = -203305;
/*    */   public static final int ERR_SEATORDERINFO_PSRNAME = -203306;
/*    */   public static final int ERR_SEATORDERINFO_TICKETNUMBER = -203307;
/*    */   public static final int ERR_SEATORDERINFO_TOURINDEX = -203308;
/*    */   public static final int ERR_SEATORDERINFO_PNR = -203309;
/*    */   public static final int ERR_SEATORDERINFO_CENTERTYPE = -203310;
/*    */   public static final int ERR_SEATORDERINFO_CENTERNO = -203311;
/*    */   public static final int ERR_SEATORDERINFO_CABINTYPE = -203312;
/*    */   public static final int ERR_SEATORDERINFO_HOSTNO = -203313;
/*    */   public static final int ERR_SEATORDERINFO_SEATNO = -203314;
/*    */   public static final int ERR_SEATORDERINFO_PRICE = -203315;
/*    */   public static final int ERR_SEATORDERINFO_CURRENCY = -203316;
/*    */   public static final int ERR_SEATORDERINFO_PSRLEVEL = -203318;
/*    */   public static final int ERR_SEATPAYINFO_ISNULL = -203401;
/*    */   public static final int ERR_SEATPAYINFO_ORDERNUM = -203402;
/*    */   public static final int ERR_SEATPAYINFO_PAYTYPE = -203403;
/*    */   public static final int ERR_SEATPAYINFO_BILLNO = -203404;
/*    */   public static final int ERR_SEATPAYINFO_BANKTYPE = -203405;
/*    */   public static final int ERR_SEATPAYINFO_ISSUCCESS = -203407;
/*    */   public static final int ERR_SEATPAYINFO_AMOUNT = -203408;
/*    */   public static final int ERR_SEATPSRFLTINFO_ISNULL = -203501;
/*    */   public static final int ERR_SEATPSRFLTINFO_FLIGHTNUMBER = -203502;
/*    */   public static final int ERR_SEATPSRFLTINFO_FLIGHTDATE = -203503;
/*    */   public static final int ERR_SEATPSRFLTINFO_DEPARTUREAIRPORT = -203504;
/*    */   public static final int ERR_SEATPSRFLTINFO_ARRIVALAIRPORT = -203505;
/*    */   public static final int ERR_SEATPSRFLTINFO_TICKETNUMBER = -203506;
/*    */   public static final int ERR_SEATPSRFLTINFO_TOURINDEX = -203507;
/*    */   public static final int ERR_SEATORDERCREATRESULT_ORDERNUM = -203601;
/*    */   public static final int ERR_SEATORDERISCUCCESS_ORDERNUM = -203701;
/*    */   public static final int ERR_SEATORDERCANCLE_ORDERNUM = -203801;
/*    */   public static final int ERR_SEATORDERDETAIL_ORDERNUM = -203901;
/*    */   public static final int ERR_QUESTSEATORDERCANCLE_ORDERNUM = -204001;
/*    */   public static final int ERR_UPGORDER_CREATEUPGORDER111 = -10300;
/*    */   public static final int ERR_UPGORDER_CREATEPAYRECORD1111 = -10302;
/*    */   public static final int ERR_UPGORDER_CREATEOPERATION111 = -10303;
/*    */   public static final int ERR_UPGORDER_ORDERNUMBERISNULL = -11301;
/*    */   public static final int ERR_UPGORDER_ORDERNOTEXIST = -11303;
/*    */   public static final int ERR_UPGORDER_QUERYORDERFAIL = -11306;
/*    */   public static final int ERR_UPGORDER_ORDERSTATUSISNOTALLOW = -11305;
/*    */   public static final int ERR_UPGORDER_ORDERSTATUSUPDATEFAILD = -11307;
/*    */   public static final int ERR_SEATCHARTINBEAN = -204101;
/*    */   public static final int ERR_SEATCHARTINBEAN_AIRLINECODE = -204102;
/*    */   public static final int ERR_SEATCHARTINBEAN_FLIGHTNO = -204103;
/*    */   public static final int ERR_SEATCHARTINBEAN_FLIGHTDATE = -204104;
/*    */   public static final int ERR_SEATCHARTINBEAN_FROMCITY = -204105;
/*    */   public static final int ERR_SEATCHARTINBEAN_TOCITY = -204106;
/*    */   public static final int ERR_SEATCHARTINBEAN_FLIGHTCLASS = -204107;
/*    */   public static final int ERR_SEATCHARTINBEAN_PSRLEVEL = -204108;
/*    */   public static final String ERR_SEATCHARTINBEAN_PARAM_VALID = "PE-9071-17";
/*    */   public static final String ERR_SEATCHART_DEFAULT = "PE-9013-79";
/*    */   public static final int ERR_SEATORDERRECORD_ORDERNUM = -204201;
/*    */   public static final int ERR_SEATREFUNDINFO = -204301;
/*    */   public static final int ERR_SEATREFUNDINFO_ISSUCCESS = -204302;
/*    */   public static final int ERR_SEATREFUNDINFO_ORDERNUM = -204303;
/*    */   public static final int ERR_SEATREFUNDINFO_BILLNO = -204304;
/*    */   public static final int ERR_SEATREFUNDINFO_AMOUNT = -204305;
/*    */   public static final int ERR_CERTORETNO = -204401;
/*    */   public static final int ERR_PSRINFO_PSRLEVEL = -204402;
/*    */   public static final int ERR_HBPACHD_FLIGHTNO = -204501;
/*    */   public static final int ERR_HBPACHD_FLIGHTDATE = -204502;
/*    */   public static final int ERR_HBPACHD_FROMCITY = -204503;
/*    */   public static final int ERR_HBPACHD_TOCITY = -204504;
/*    */   public static final int ERR_HBPACHD_TKTNUM = -204505;
/*    */   public static final int ERR_HBPACHD_CLASS = -204506;
/*    */   public static final int ERR_HBPACHD_TOURINDEX = -204507;
/*    */   public static final int ERR_HBPACHD_PARTNER = -204508;
/*    */   public static final int ERR_HBPACHD_INDEX = -204509;
/*    */   public static final int ERR_HBPACHD_TKTID = -204510;
/*    */   public static final int ERR_HBPACHD_SURNAME = -204511;
/*    */   public static final int ERR_HBPACHD_CABINTYPE = -204512;
/*    */   public static final int ERR_HBPACHD_CHD = -204513;
/*    */   public static final int ERR_HBPACHD_PSRNAME = -204514;
/*    */   public static final int ERR_HBPACHD_HOSTNUM = -204515;
/*    */   public static final int ERR_HBPACHD_GENDER = -204516;
/*    */   public static final int ERR_HBPACHD_CHECKMSG = -204517;
/*    */   public static final int ERR_HBPACHD_QUERYINOF = -904501;
/*    */   public static final int ERR_HBPACHD_SERVICE = -904502;
/*    */   public static final int ERR_HBPUOCHD = -204601;
/*    */   public static final int ERR_HBPUOCHD_AIRLINECODE = -204601;
/*    */   public static final int ERR_HBPUOCHD_DEPTCITY = -204602;
/*    */   public static final int ERR_HBPUOCHD_DESTCITY = -204603;
/*    */   public static final int ERR_HBPUOCHD_FLIGHTDATE = -204604;
/*    */   public static final int ERR_HBPUOCHD_FLIGHTNUM = -204605;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT = -204606;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT_AIRLINECODE = -204607;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT_DEPTCITY = -204608;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT_DESTCITY = -204609;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT_FLIGHTDATE = -204610;
/*    */   public static final int ERR_HBPUOCHD_CONNFLIGHT_FLIGHTNUM = -204611;
/*    */   public static final int ERR_HBPUOCHD_PARTNER = -204612;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_TKTNUMBER = -204614;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_FLIGHTCLASS = -204613;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_INDEX = -204615;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_SURNAME = -204616;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_CHD = -204617;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_GENDER = -204618;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_HOSTNUM = -204619;
/*    */   public static final int ERR_HBPUOCHD_PARTNER_PRECABIN = -204620;
/*    */   public static final int ERR_HBPUOCHD_CHINMSG = -204621;
/*    */   public static final String ERR_PSGBAG_SERVICE = "-204701";
/*    */   public static final long ERR_PSGBAG_INPUTNUll = -204701L;
/*    */   public static final long ERR_PSGBAG_AIRLINECOD = -204702L;
/*    */   public static final long ERR_PSGBAG_FLIGHTNUM = -204703L;
/*    */   public static final long ERR_PSGBAG_DEPCITY = -204704L;
/*    */   public static final long ERR_PSGBAG_ARRCITY = -204705L;
/*    */   public static final long ERR_PSGBAG_HOSTNUM = -204706L;
/*    */   public static final long ERR_PSGBAG_CABIN = -204707L;
/*    */   public static final long ERR_PSGBAG_BAGQTY = -204708L;
/*    */   public static final long ERR_PSGBAG_BAGWGT = -204709L;
/*    */   public static final long ERR_PSGBAG_FLIGHTDATE = -204710L;
/*    */   public static final long ERR_PSGBAG_TAGNULL = -204711L;
/*    */   public static final long ERR_PSGBAG_ETNUMBERNULL = -204713L;
/*    */   public static final long ERR_PSGQUERY_QUERY = -204801L;
/*    */   public static final long ERR_PSGQUERY_FLIGHTNO = -204801L;
/*    */   public static final long ERR_PSGQUERY_FLIGHTDATE = -204802L;
/*    */   public static final long ERR_PSGQUERY_DEPTPORT = -204803L;
/*    */   public static final long ERR_PSGQUERY_ETNUMBER = -204804L;
/*    */   public static final long ERR_PSGBAG_DEL_QUERYINOF = -204901L;
/*    */   public static final long ERR_PSGBAG_DEL_INPUTNUll = -204901L;
/*    */   public static final long ERR_PSGBAG_DEL_AIRLINECOD = -204902L;
/*    */   public static final long ERR_PSGBAG_DEL_FLIGHTNUM = -204903L;
/*    */   public static final long ERR_PSGBAG_DEL_DEPCITY = -204904L;
/*    */   public static final long ERR_PSGBAG_DEL_ARRCITY = -204905L;
/*    */   public static final long ERR_PSGBAG_DEL_HOSTNUM = -204906L;
/*    */   public static final long ERR_PSGBAG_DEL_CABIN = -204907L;
/*    */   public static final long ERR_PSGBAG_DEL_BAGQTY = -204908L;
/*    */   public static final long ERR_PSGBAG_DEL_BAGWGT = -204909L;
/*    */   public static final long ERR_PSGBAG_DEL_FLIGHTDATE = -204910L;
/*    */   public static final long ERR_PSGBAG_DEL_TAGNULL = -204911L;
/*    */   public static final long ERR_PSGBAG_DEL_TAGWEIGHT = -204912L;
/*    */   public static final long ERR_PSGBAG_DEL_ETNUMBERNULL = -204913L;
/*    */   public static final long ERR_PRECKI_SEATMAP_INPUT_EMPTY = -205001L;
/*    */   public static final long ERR_PRECKI_SEATMAP_AIRLINECODE = -205002L;
/*    */   public static final long ERR_PRECKI_SEATMAP_FLIGHTNO = -205003L;
/*    */   public static final long ERR_PRECKI_SEATMAP_FLIGHTDATE = -205004L;
/*    */   public static final long ERR_PRECKI_SEATMAP_DPTAPT = -205005L;
/*    */   public static final long ERR_PRECKI_SEATMAP_ARRAPT = -205006L;
/*    */   public static final long ERR_PRECKI_SEATMAP_PNR = -205007L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_INPUT_EMPTY = -205101L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_AIRLINECODE = -205102L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_FLIGHTNO = -205103L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_FLIGHTDATE = -205104L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_DPTTIME = -205105L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_DPTAPT = -205106L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_ARRAPT = -205107L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_CABIN = -205108L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_PSGNAME = -205109L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_CERTTYPE = -205110L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_CERTNO = -205111L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_TICKETID = -205112L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_SEQNO = -205113L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_PNR = -205114L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_MOBILENO = -205115L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_SEATNO = -205116L;
/*    */   public static final long ERR_PRECKI_SEATBOOK_CERTTYPE_NOTNI = -205117L;
/*    */   public static final long ERR_PRECKI_AIRLINECODE = -205201L;
/*    */   public static final long ERR_PRECKI_FLIGHTNUMBER = -205202L;
/*    */   public static final long ERR_PRECKI_FLIGHTDATE = -205203L;
/*    */   public static final long ERR_PRECKI_DEPARTUREAIRPORT = -205204L;
/*    */   public static final long ERR_PRECKI_ARRIVALAIRPORT = -205205L;
/*    */   public static final long ERR_PRECKI_TICKETNUMBER = -205206L;
/*    */   public static final long ERR_PRECKI_SEQUENCENUMBER = -205207L;
/*    */   public static final long ERR_PRECKI_INPUT = -205208L;
/*    */   public static final long ERR_PRECKIINFO_AIRLINECODE = -205301L;
/*    */   public static final long ERR_PRECKIINFO_FLIGHTNUMBER = -205302L;
/*    */   public static final long ERR_PRECKIINFO_FLIGHTDATE = -205303L;
/*    */   public static final long ERR_PRECKIINFO_DEPARTUREAIRPORT = -205304L;
/*    */   public static final long ERR_PRECKIINFO_ARRIVALAIRPORT = -205305L;
/*    */   public static final long ERR_PRECKIINFO_TICKETNUMBER = -205306L;
/*    */   public static final long ERR_PRECKIINFO_SEQUENCENUMBER = -205307L;
/*    */   public static final long ERR_PRECKIINFO_INPUT = -205308L;
/*    */   public static final int ERR_PSRIDENTITYINFOA_ISNULL = -205401;
/*    */   public static final int ERR_PSRIDENTITYINFOA_AIRCODE = -205402;
/*    */   public static final int ERR_PSRIDENTITYINFOA_FLIGHTNUMBER = -205403;
/*    */   public static final int ERR_PSRIDENTITYINFOA_FLIGHTDATE = -205404;
/*    */   public static final int ERR_PSRIDENTITYINFOA_CENTTYPE = -205405;
/*    */   public static final int ERR_PSRIDENTITYINFOA_CENTNO = -205406;
/*    */   public static final int ERR_PSRIDENTITYINFOA_PSRLEVEL = -205407;
/*    */   public static final int ERR_PSRIDENTITYINFOA_CURRENCY = -205408;
/*    */   public static final int ERR_EDISEREQ_INPUT = -205601;
/*    */   public static final int ERR_EDISEREQ_AIRLINECODE = -205602;
/*    */   public static final int ERR_EDISEREQ_FLIGHTNUM = -205603;
/*    */   public static final int ERR_EDISEREQ_FLIGHTDATE = -205604;
/*    */   public static final int ERR_EDISEREQ_DEPTCITY = -205605;
/*    */   public static final int ERR_EDISEREQ_ARVCITY = -205606;
/*    */   public static final int ERR_EDISEREQ_CABIN = -205607;
/*    */   public static final int ERR_EDISEREQ_ETCODE = -205608;
/*    */   public static final int ERR_EDISEREQ_FIRSTDETCITY = -205609;
/*    */   public static final int ERR_EDISERES_INPUT = -205701;
/*    */   public static final int ERR_EDISERES_AIRLINECODE = -205702;
/*    */   public static final int ERR_EDISERES_FLIGHTNUM = -205703;
/*    */   public static final int ERR_EDISERES_FLIGHTDATE = -205704;
/*    */   public static final int ERR_EDISERES_DEPTCITY = -205705;
/*    */   public static final int ERR_EDISERES_ARVCITY = -205706;
/*    */   public static final int ERR_EDISERES_CABIN = -205707;
/*    */   public static final int ERR_EDISERES_ETCODE = -205708;
/*    */   public static final int ERR_EDIHBPUOREQ_INPUT = -205801;
/*    */   public static final int ERR_EDIHBPUOREQ_FLIGHT = -205802;
/*    */   public static final int ERR_EDIHBPUOREQ_AIRLINECODE = -205803;
/*    */   public static final int ERR_EDIHBPUOREQ_FLIGHTNUM = -205804;
/*    */   public static final int ERR_EDIHBPUOREQ_FLIGHTDATE = -205805;
/*    */   public static final int ERR_EDIHBPUOREQ_DEPTCITY = -205806;
/*    */   public static final int ERR_EDIHBPUOREQ_ARVCITY = -205807;
/*    */   public static final int ERR_EDIHBPUOREQ_PASSENGER = -205808;
/*    */   public static final int ERR_EDIHBPUOREQ_PSGNAME = -205809;
/*    */   public static final int ERR_EDIHBPUOREQ_CABIN = -205810;
/*    */   public static final int ERR_EDIHBPUOREQ_ETCODE = -205811;
/*    */   public static final int ERR_EDIHBPUOREQ_TOURINDEX = -205812;
/*    */   public static final int ERR_EDIHBPUOREQ_HOSTNUM = -205813;
/*    */   public static final int ERR_EDIHBPUOREQ_SEATNO = -205814;
/*    */   public static final int ERR_EDIHBPUORES_INPUT = -205901;
/*    */   public static final int ERR_EDIHBPUORES_FLIGHT = -205902;
/*    */   public static final int ERR_EDIHBPUORES_AIRLINECODE = -205903;
/*    */   public static final int ERR_EDIHBPUORES_FLIGHTNUM = -205904;
/*    */   public static final int ERR_EDIHBPUORES_FLIGHTDATE = -205905;
/*    */   public static final int ERR_EDIHBPUORES_DEPTCITY = -205906;
/*    */   public static final int ERR_EDIHBPUORES_ARVCITY = -205907;
/*    */   public static final int ERR_EDIHBPUORES_PASSENGER = -205908;
/*    */   public static final int ERR_EDIHBPUORES_PSGNAME = -205909;
/*    */   public static final int ERR_EDIHBPUORES_CABIN = -205910;
/*    */   public static final int ERR_EDIHBPUORES_ETCODE = -205911;
/*    */   public static final int ERR_EDIHBPUORES_TOURINDEX = -205912;
/*    */   public static final int ERR_EDIHBPUORES_HOSTNUM = -205913;
/*    */   public static final int ERR_EDIHBPUORES_SEATNO = -205914;
/*    */   public static final int ERR_DAPIREQ_INPUTNULL = -206001;
/*    */   public static final int ERR_DAPIREQ_AIRLINECODE = -206002;
/*    */   public static final int ERR_DAPIREQ_FLIGHTNUMBER = -206003;
/*    */   public static final int ERR_DAPIREQ_FLIGHTDATE = -206004;
/*    */   public static final int ERR_DAPIREQ_DEPTCITY = -206005;
/*    */   public static final int ERR_DAPIREQ_CERTTYPE = -206006;
/*    */   public static final int ERR_DAPIREQ_CERTNO = -206007;
/*    */   public static final int ERR_DAPISEUTREQ_INPUTNULL = -206101;
/*    */   public static final int ERR_DAPISEUTREQ_AIRLINECODE = -206102;
/*    */   public static final int ERR_DAPISEUTREQ_FLIGHTNUMBER = -206103;
/*    */   public static final int ERR_DAPISEUTREQ_FLIGHTDATE = -206104;
/*    */   public static final int ERR_DAPISEUTREQ_DEPTCITY = -206105;
/*    */   public static final int ERR_DAPISEUTREQ_CERTTYPE = -206106;
/*    */   public static final int ERR_DAPISEUTREQ_CERTNO = -206107;
/*    */   public static final int ERR_ADCREQ_INPUTNULL = -206200;
/*    */   public static final int ERR_ADCREQ_AIRLINECODE = -206201;
/*    */   public static final int ERR_ADCREQ_FLIGHTNUMBER = -206202;
/*    */   public static final int ERR_ADCREQ_FLIGHTDATE = -206203;
/*    */   public static final int ERR_ADCREQ_TICKETNUMBER = -206204;
/*    */   public static final int ERR_ADCREQ_FROMCITY = -206205;
/*    */   public static final int ERR_ADCREQ_HOSTNUMBER = -206206;
/*    */   public static final int ERR_ADCREQ_CABIN = -206207;
/*    */   public static final int ERR_ADCRESQUERY_INPUTNULL = -206300;
/*    */   public static final int ERR_ADCRESQUERY_AIRLINECODE = -206301;
/*    */   public static final int ERR_ADCRESQUERY_FLIGHTNUMBER = -206302;
/*    */   public static final int ERR_ADCRESQUERY_FLIGHTDATE = -206303;
/*    */   public static final int ERR_ADCRESQUERY_ETCODE = -206304;
/*    */   public static final int ERR_ADCRESQUERY_FROMCITY = -206305;
/*    */   public static final int ERR_ADCRESQUERY_HOSTNUM = -206306;
/*    */   public static final int ERR_ADCRESQUERY_CABIN = -206307;
/*    */   public static final int ERR_QUERYPRFBEAN_DOCID = -206400;
/*    */   public static final int ERR_EBAG_INPUTNULL = -206500;
/*    */   public static final int ERR_EBAG_AIRLINECODE = -206501;
/*    */   public static final int ERR_EBAG_FLIGHTNUMBER = -206502;
/*    */   public static final int ERR_EBAG_FLIGHTDATE = -206503;
/*    */   public static final int ERR_EBAG_FROMCITY = -206504;
/*    */   public static final int ERR_EBAG_BOARDINGNUMBER = -206505;
/*    */   public static final int ERR_EBAG_TOCITY = -206506;
/*    */   public static final int ERR_EBAG_TICKETNUMBER = -206507;
/*    */   public static final int ERR_EBAG_CABIN = -206508;
/*    */   public static final int ERR_EBAG_HOSTNUMBER = -206509;
/*    */   public static final int ERR_EBAG_PSRNAME = -206510;
/*    */   public static final int ERR_GETEBOARDINGCARD_INPUTNULL = -206600;
/*    */   public static final int ERR_GETEBOARDINGCARD_AIRLINECODE = -206601;
/*    */   public static final int ERR_GETEBOARDINGCARD_FLIGHTNUMBER = -206602;
/*    */   public static final int ERR_GETEBOARDINGCARD_FLIGHTDATE = -206603;
/*    */   public static final int ERR_GETEBOARDINGCARD_FROMCITY = -206604;
/*    */   public static final int ERR_GETEBOARDINGCARD_BOARDINGNUMBER = -206605;
/*    */   public static final int ERR_GETEBOARDINGCARD_TOCITY = -206606;
/*    */   public static final int ERR_GETEBOARDINGCARD_TICKENUMBER = -206607;
/*    */   public static final int ERR_GETEBOARDINGCARD_CABIN = -206608;
/*    */   public static final int ERR_GETEBOARDINGCARD_TOURINDEX = -206609;
/*    */   public static final int ERR_GETEBOARDINGCARD_PSRNAME = -206610;
/*    */   public static final int ERR_QUERYPMCSEATCHART_INPUTNULL = -206700;
/*    */   public static final int ERR_QUERYPMCSEATCHART_AIRLINECODE = -206701;
/*    */   public static final int ERR_QUERYPMCSEATCHART_FLIGHTNUM = -206702;
/*    */   public static final int ERR_QUERYPMCSEATCHART_FLIGHTDATE = -206703;
/*    */   public static final int ERR_QUERYPMCSEATCHART_FROMCITY = -206704;
/*    */   public static final int ERR_QUERYPMCSEATCHART_TOCITY = -206705;
/*    */   public static final int ERR_QUERYPMCSEATCHART_TICKETNO = -206706;
/*    */   public static final int ERR_QUERYPMCSEATCHART_CABIN = -206707;
/*    */   public static final int ERR_TDCRESQUERY_INPUTNULL = -206900;
/*    */   public static final int ERR_TDCRESQUERY_AIRLINECODE = -206901;
/*    */   public static final int ERR_TDCRESQUERY_FLIGHTNUMBER = -206902;
/*    */   public static final int ERR_TDCRESQUERY_FLIGHTDATE = -206903;
/*    */   public static final int ERR_TDCRESQUERY_ETCODE = -206904;
/*    */   public static final int ERR_TDCRESQUERY_FROMCITY = -206905;
/*    */   public static final int ERR_TDCRESQUERY_HOSTNUM = -206906;
/*    */   public static final int ERR_TDCRESQUERY_CABIN = -206907;
/*    */   public static final int ERR_TDCREQ_INPUTNULL = -206800;
/*    */   public static final int ERR_TDCREQ_AIRLINECODE = -206801;
/*    */   public static final int ERR_TDCREQ_FLIGHTNUMBER = -206802;
/*    */   public static final int ERR_TDCREQ_FLIGHTDATE = -206803;
/*    */   public static final int ERR_TDCREQ_TICKETNUMBER = -206804;
/*    */   public static final int ERR_TDCREQ_FROMCITY = -206805;
/*    */   public static final int ERR_TDCREQ_HOSTNUMBER = -206806;
/*    */   public static final int ERR_TDCREQ_CABIN = -206807;
/*    */   public static final String ERR_QURYINVENTORY_NULL = "029-03-00-13201";
/*    */   public static final String ERR_QURYINVENTORY_OTHER = "029-03-00-13202";
/*    */   public static final String ERR_QURYINVENTORY_OCAIRLINEID = "029-03-01-13202";
/*    */   public static final String ERR_QURYINVENTORY_OCFLIGHTNUMBER = "029-03-01-13204";
/*    */   public static final String ERR_QURYINVENTORY_OCFLIGHTSUFFIX = "029-03-01-13205";
/*    */   public static final String ERR_QURYINVENTORY_DEPARTUREDATE = "029-03-01-13206";
/*    */   public static final String ERR_QURYINVENTORY_DEPARTUREAIRPORT = "029-03-01-13207";
/*    */   public static final String ERR_QURYINVENTORY_ARRIVALAIRPORT = "029-03-01-13208";
/*    */   public static final String ERR_QURYINVENTORY_OFFICEINFO = "029-03-01-13210";
/*    */   public static final String ERR_QURYINVENTORY_AIRLINE = "029-03-01-13211";
/*    */   public static final String ERR_QURYINVENTORY_STATION = "029-03-01-13212";
/*    */   public static final String ERR_BOOKINGMEAL_NULL = "029-03-00-13301";
/*    */   public static final String ERR_BOOKINGMEAL_OTHER = "029-03-00-13302";
/*    */   public static final String ERR_BOOKINGMEAL_PASSENGERNAME = "029-03-01-13301";
/*    */   public static final String ERR_BOOKINGMEAL_PASSENGERTYPE = "029-03-01-13302";
/*    */   public static final String ERR_BOOKINGMEAL_CERTIFICATIONTYPE = "029-03-01-13303";
/*    */   public static final String ERR_BOOKINGMEAL_CERTIFICATIONNO = "029-03-01-13304";
/*    */   public static final String ERR_BOOKINGMEAL_PNRNUMBER = "029-03-01-13305";
/*    */   public static final String ERR_BOOKINGMEAL_FLIGHTSEGMENTS = "029-03-01-13306";
/*    */   public static final String ERR_BOOKINGMEAL_SEGMENTID = "029-03-01-13307";
/*    */   public static final String ERR_BOOKINGMEAL_DEPARTUREDATE = "029-03-01-13308";
/*    */   public static final String ERR_BOOKINGMEAL_DEPARTUREAIRPORT = "029-03-01-13309";
/*    */   public static final String ERR_BOOKINGMEAL_ARRIVALAIRPORT = "029-03-01-13310";
/*    */   public static final String ERR_BOOKINGMEAL_MCRBD = "029-03-01-13311";
/*    */   public static final String ERR_BOOKINGMEAL_MCFLIGHTNUMBER = "029-03-01-13312";
/*    */   public static final String ERR_BOOKINGMEAL_MCAIRLINEID = "029-03-01-13313";
/*    */   public static final String ERR_BOOKINGMEAL_MCFLIGHTSUFFIX = "029-03-01-13314";
/*    */   public static final String ERR_BOOKINGMEAL_OCFLIGHTNUMBER = "029-03-01-13315";
/*    */   public static final String ERR_BOOKINGMEAL_OCAIRLINEID = "029-03-01-13316";
/*    */   public static final String ERR_BOOKINGMEAL_OCFLIGHTSUFFIX = "029-03-01-13317";
/*    */   public static final String ERR_BOOKINGMEAL_SERVICEITEMS = "029-03-01-13318";
/*    */   public static final String ERR_BOOKINGMEAL_SERVICEITEMS_SEGMENTID = "029-03-01-13319";
/*    */   public static final String ERR_BOOKINGMEAL_SERVICEID = "029-03-01-13320";
/*    */   public static final String ERR_BOOKINGMEAL_SERVICEITEMS_SSRCODE = "029-03-01-13321";
/*    */   public static final String ERR_BOOKINGMEAL_OFFICEINFO = "029-03-01-13323";
/*    */   public static final String ERR_BOOKINGMEAL_OFFICEINFO_AIRLINE = "029-03-01-13324";
/*    */   public static final String ERR_BOOKINGMEAL_OFFICEINFO_STATION = "029-03-01-13325";
/*    */   public static final String ERR_QUERYANCILLARY_NULL = "029-03-00-13501";
/*    */   public static final String ERR_QUERYANCILLARY_OTHER = "029-03-00-13502";
/*    */   public static final String ERR_QUERYANCILLARY_CERTIFICATENUMBER = "029-03-01-13501";
/*    */   public static final String ERR_QUERYANCILLARY_CERTIFICATETYPE = "029-03-01-13502";
/*    */   public static final String ERR_QUERYANCILLARY_PNRCODE = "029-03-01-13503";
/*    */   public static final String ERR_QUERYANCILLARY_FLIGHTSEGMENT = "029-03-01-13504";
/*    */   public static final String ERR_QUERYANCILLARY_INTERFACE = "029-03-01-13512";
/*    */   public static final String ERR_QUERYANCILLARY_OCAIRLINEID = "029-03-01-13506";
/*    */   public static final String ERR_QUERYANCILLARY_OCFLIGHTNUMBER = "029-03-01-13505";
/*    */   public static final String ERR_QUERYANCILLARY_OCFLIGHTSUFFIX = "029-03-01-13507";
/*    */   public static final String ERR_QUERYANCILLARY_ARRIVALDATE = "029-03-01-13508";
/*    */   public static final String ERR_QUERYANCILLARY_DEPARTUREAIRPORT = "029-03-01-13509";
/*    */   public static final String ERR_QUERYANCILLARY_ARRIVALAIRPORT = "029-03-01-13510";
/*    */   public static final String ERR_QUERYANCILLARY_DEPARTUREDATE = "029-03-01-13511";
/*    */   public static final String ERR_QUERYANCILLARY_OFFICEINFO = "029-03-01-13513";
/*    */   public static final String ERR_QUERYANCILLARY_AIRLINE = "029-03-01-13514";
/*    */   public static final String ERR_QUERYANCILLARY_STATION = "029-03-01-13515";
/*    */   public static final String ERR_CANCEL_NULL = "029-03-00-13401";
/*    */   public static final String ERR_CANCEL_OTHER = "029-03-00-13402";
/*    */   public static final String ERR_CANCEL_PNRCODE = "029-03-01-13401";
/*    */   public static final String ERR_CANCEL_ORDERITEMID = "029-03-01-13402";
/*    */   public static final String ERR_CANCEL_FAILED = "029-03-01-13403";
/*    */   public static final String ERR_CANCEL_OFFICEINFO = "029-03-01-13404";
/*    */   public static final String ERR_CANCEL_AIRLINE = "029-03-01-13405";
/*    */   public static final String ERR_CANCEL_STATION = "029-03-01-13406";
/*    */   public static final String ERR_MEAL_CONNECT = "029-04-00-10000";
/*    */   public static final String ERR_ACC_EXPRESSBAGS = "051-04-00-00301";
/*    */   public static final String ERR_DEL_EXPRESSBAGS = "051-04-00-00101";
/*    */   public static final String ERR_QRY_EXPRESSBAGS = "051-04-00-00201";
/*    */   public static final String ERR_PSG_ACTIVE = "051-04-00-00501";
/*    */   public static final String ERR_DIFFERSEATCHART = "051-04-00-00401";
/*    */   public static final String XML_ERROR = "051-04-01-00002";
/*    */   public static final String ERR_USER = "051-04-01-00001";
/*    */   public static final String MSI_ERR = "051-04-03-00001";
/*    */   public static final String ERR_PSGPRINTBAG = "051-04-00-00601";
/*    */   public static final String ERR_QUERYPASSENGER = "051-04-00-00701";
/*    */   public static final String ERR_BOOKING_SEAT = "PE-9001-10";
/*    */   public static final String ERR_USER_INFO_CODE = "PE-9002-11";
/*    */   public static final String ERR_CHECK_USERINFO = "-1005";
/*    */   public static final String ERR_RETRIEVEPASSENGER = "PE-4001-15";
/*    */   public static final String ERR_SEAT_RESERVATION = "051-04-00-00901";
/*    */   public static final String ERR_SEATRELEASE = "051-04-00-00801";
/*    */   public static final String NULL_HBPUOPTION_MESSAGE = "PE-4004-18";
/*    */   public static final String ERR_HBPUOPTIONS = "PE-4005-19";
/*    */   public static final String ERR_HBPWOPTIONS = "PE-4003-17";
/*    */   public static final String ERR_HBPWOPTIONS_DEPTCITY = "PE-4007-11";
/*    */   public static final String ERR_HBPWOPTIONS_CKIN_FF = "PE-4008-12";
/*    */   public static final String ERR_QUERYORDERDETAIL = "PE-4006-10";
/*    */   public static final String ERR_ADDBAGGAGE = "PE-4002-16";
/*    */   public static final String ERR_DELBAGGAGE = "PE-4001-37";
/*    */   public static final String ERR_MULTIPSRCHECKINSEAT = "PE-4009-13";
/*    */   public static final String ERR_GOV_SECURITY_CHECK = "PE-4010-15";
/*    */   public static final String ERR_GOV_NO_RESULT = "-54411";
/*    */   public static final String ERR_PREPSRCHECKIN = "PE-4011-16";
/*    */   public static final String ERR_POOLINGBAGGAGEALLOWANCE = "PE-4036-13";
/*    */   public static final String PARAM_ERR_CREATE_RESERVE_UPG = "PE-9034-16";
/*    */   public static final String OTHER_ERR_CREATE_RESERVE_UPG = "PE-9002-77";
/*    */   public static final String CANCEL_RESERVE_FORM = "PE-9035-17";
/*    */   public static final String CANCEL_RESERVE_DEFAULT_ERROR = "PE-9003-78";
/*    */   public static final String ERR_TRR_DOREBOOK_OTHER = "PE-9030-12";
/*    */   public static final String ERR_PARAM_DOREBOOKRQ = "PE-9021-12";
/*    */   public static final String ERR_PARAM_SEGMENTS = "PE-9004-13";
/*    */   public static final String ERR_PARAM_PNRNO = "PE-9005-14";
/*    */   public static final String ERR_PARAM_AIRPORT = "PE-9006-15";
/*    */   public static final String ERR_PARAM_CHANNEL = "PE-9007-16";
/*    */   public static final String ERR_PARAM_SPLITPNR = "PE-9008-17";
/*    */   public static final String ERR_PARAM_COUPONNO = "PE-9023-14";
/*    */   public static final String ERR_PARAM_TICKTENO = "PE-9022-13";
/*    */   public static final String ERR_PARAM_SOURCESEGMENT = "PE-9024-15";
/*    */   public static final String ERR_PARAM_NEWSEGMENT = "PE-9025-16";
/*    */   public static final String ERR_PARAM_ARRIVALAIRPORT = "PE-9014-14";
/*    */   public static final String ERR_PARAM_DEPARTUREAIRPORT = "PE-9015-15";
/*    */   public static final String ERR_PARAM_ARRIVALDATETIME = "PE-9017-17";
/*    */   public static final String ERR_PARAM_FLIGHTNO = "PE-9027-18";
/*    */   public static final String ERR_PARAM_FLIGHTDATETIME = "PE-9026-17";
/*    */   public static final String ERR_PARAM_CABIN = "PE-9013-13";
/*    */   public static final String ERR_PARAM_ISVVIP = "PE-9028-19";
/*    */   public static final String TRR_DEFAULT_ERR = "PE-9033-15";
/*    */   public static final String ERR_PARAM_QUERYREBOOKFLIGHTRQ = "PE-9003-12";
/*    */   public static final String ERR_PARAM_INVOLUNTARYIDENTIFIER = "PE-9009-18";
/*    */   public static final String ERR_PARAM_TICKETNO_QUERY = "PE-9010-10";
/*    */   public static final String ERR_PARAM_DEPARTUREDATETIME = "PE-9016-16";
/*    */   public static final String ERR_PARAM_MCFLIGHTNUMBER = "PE-9012-12";
/*    */   public static final String ERR_PARAM_ISCHANGED = "PE-9018-18";
/*    */   public static final String ERR_PARAM_MCAIRLINECODE = "PE-9011-11";
/*    */   public static final String ERR_PARAM_QUERYDEPTDATE = "PE-9020-11";
/*    */   public static final String ERR_PARAM_ISINBOUND = "PE-9019-19";
/*    */   public static final String ERR_TRR_OTHERERROR = "PE-9029-10";
/*    */   public static final String ERR_HBPW_FLIGHTNUMBER = "PE-4014-19";
/*    */   public static final String ERR_HBPW_AIRLINECODE = "PE-4012-17";
/*    */   public static final String ERR_HBPW_DEPARTUREDATE = "PE-4018-13";
/*    */   public static final String ERR_HBPW_DEPARTUREAIRPORT = "PE-4016-11";
/*    */   public static final String ERR_HBPW_SURNAME = "PE-4022-18";
/*    */   public static final String ERR_HBPW_ARRIVALAIRPORT = "PE-4020-16";
/*    */   public static final String ERR_HBPW_SEATNUMBER = "PE-4026-12";
/*    */   public static final String ERR_HBPW_CABINTYPE = "PE-4024-10";
/*    */   public static final String ERR_HBPW_SEQUENCENUMBER = "PE-4032-19";
/*    */   public static final String ERR_HBPW_TICKETID = "PE-4030-17";
/*    */   public static final String ERR_DELPSR = "PE-4033-10";
/*    */   public static final String ERR_CHANGE_VALID = "PE-9037-19";
/*    */   public static final String ERR_CHANGE_OTHER = "PE-9001-76";
/*    */   public static final String ERR_OTHER_QUERY_RESERVE = "PE-9004-79";
/*    */   public static final String ERR_QUERY_RESERVE = "PE-9036-18";
/*    */   public static final String ERR_ISSUE_EMDS = "PE-9038-10";
/*    */   public static final String ERR_ISSUE_EMDS_OTHER_ERRCODE = "PE-9005-70";
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.util.ErrCode
 * JD-Core Version:    0.6.0
 */