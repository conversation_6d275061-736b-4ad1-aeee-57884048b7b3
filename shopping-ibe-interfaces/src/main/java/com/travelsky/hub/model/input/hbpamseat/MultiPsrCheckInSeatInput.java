/*    */ package com.travelsky.hub.model.input.hbpamseat;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class MultiPsrCheckInSeatInput
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3223272837844479054L;
/*    */   private FlightInfo flightInfo;
/*    */   private PassengerInfo passengerInfo;
/*    */   private List<PartnerInfo> partnerInfoList;
/*    */ 
/*    */   public FlightInfo getFlightInfo()
/*    */   {
/* 35 */     return this.flightInfo;
/*    */   }
/*    */ 
/*    */   public void setFlightInfo(FlightInfo flightInfo)
/*    */   {
/* 42 */     this.flightInfo = flightInfo;
/*    */   }
/*    */ 
/*    */   public PassengerInfo getPassengerInfo()
/*    */   {
/* 49 */     return this.passengerInfo;
/*    */   }
/*    */ 
/*    */   public void setPassengerInfo(PassengerInfo passengerInfo)
/*    */   {
/* 56 */     this.passengerInfo = passengerInfo;
/*    */   }
/*    */ 
/*    */   public List<PartnerInfo> getPartnerInfoList()
/*    */   {
/* 63 */     return this.partnerInfoList;
/*    */   }
/*    */ 
/*    */   public void setPartnerInfoList(List<PartnerInfo> partnerInfoList)
/*    */   {
/* 70 */     this.partnerInfoList = partnerInfoList;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.MultiPsrCheckInSeatInput
 * JD-Core Version:    0.6.0
 */