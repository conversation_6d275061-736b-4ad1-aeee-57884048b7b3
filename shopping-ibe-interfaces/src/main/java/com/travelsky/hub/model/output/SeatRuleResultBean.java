/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class SeatRuleResultBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private List seatRuleResult;
/*    */ 
/*    */   public List getSeatRuleResult()
/*    */   {
/* 25 */     return this.seatRuleResult;
/*    */   }
/*    */ 
/*    */   public void setSeatRuleResult(List seatRuleResult)
/*    */   {
/* 32 */     this.seatRuleResult = seatRuleResult;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.SeatRuleResultBean
 * JD-Core Version:    0.6.0
 */