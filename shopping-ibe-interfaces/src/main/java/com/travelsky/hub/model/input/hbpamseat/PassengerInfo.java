/*     */ package com.travelsky.hub.model.input.hbpamseat;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class PassengerInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3604685864860161845L;
/*     */   private String surName;
/*     */   private String cabinType;
/*     */   private String ticketId;
/*     */   private String sequenceNumber;
/*     */   private String hostNumber;
/*     */   private String seatNumber;
/*     */   private String snrFlag;
/*     */   private String gender;
/*     */   private String chd;
/*     */   private Infant inf;
/*     */ 
/*     */   public String getSurName()
/*     */   {
/*  62 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/*  69 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/*  76 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/*  83 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getTicketId()
/*     */   {
/*  90 */     return this.ticketId;
/*     */   }
/*     */ 
/*     */   public void setTicketId(String ticketId)
/*     */   {
/*  97 */     this.ticketId = ticketId;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 104 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 111 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ 
/*     */   public String getHostNumber()
/*     */   {
/* 118 */     return this.hostNumber;
/*     */   }
/*     */ 
/*     */   public void setHostNumber(String hostNumber)
/*     */   {
/* 125 */     this.hostNumber = hostNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 132 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 139 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ 
/*     */   public String getSnrFlag()
/*     */   {
/* 146 */     return this.snrFlag;
/*     */   }
/*     */ 
/*     */   public void setSnrFlag(String snrFlag)
/*     */   {
/* 153 */     this.snrFlag = snrFlag;
/*     */   }
/*     */ 
/*     */   public String getGender()
/*     */   {
/* 160 */     return this.gender;
/*     */   }
/*     */ 
/*     */   public void setGender(String gender)
/*     */   {
/* 167 */     this.gender = gender;
/*     */   }
/*     */ 
/*     */   public String getChd()
/*     */   {
/* 174 */     return this.chd;
/*     */   }
/*     */ 
/*     */   public void setChd(String chd)
/*     */   {
/* 181 */     this.chd = chd;
/*     */   }
/*     */ 
/*     */   public Infant getInf()
/*     */   {
/* 188 */     return this.inf;
/*     */   }
/*     */ 
/*     */   public void setInf(Infant inf)
/*     */   {
/* 195 */     this.inf = inf;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpamseat.PassengerInfo
 * JD-Core Version:    0.6.0
 */