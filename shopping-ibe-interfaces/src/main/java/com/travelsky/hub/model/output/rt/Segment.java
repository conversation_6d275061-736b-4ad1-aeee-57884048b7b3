/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class Segment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 943627881132244749L;
/*     */   private String scheduleChangeInd;
/*     */   private String marcketRestrictionInd;
/*     */   private String dcsPNRDeletedInd;
/*     */   private FlightInfo flightInfo;
/*     */   private Reservation reservation;
/*     */   private List<SpecialRequestService> serviceList;
/*     */   private Delivery delivery;
/*     */   private TicketInfo ticket;
/*     */   private TicketInfo infantTicket;
/*     */ 
/*     */   public String getScheduleChangeInd()
/*     */   {
/*  59 */     return this.scheduleChangeInd;
/*     */   }
/*     */ 
/*     */   public void setScheduleChangeInd(String scheduleChangeInd)
/*     */   {
/*  66 */     this.scheduleChangeInd = scheduleChangeInd;
/*     */   }
/*     */ 
/*     */   public String getMarcketRestrictionInd()
/*     */   {
/*  73 */     return this.marcketRestrictionInd;
/*     */   }
/*     */ 
/*     */   public void setMarcketRestrictionInd(String marcketRestrictionInd)
/*     */   {
/*  80 */     this.marcketRestrictionInd = marcketRestrictionInd;
/*     */   }
/*     */ 
/*     */   public String getDcsPNRDeletedInd()
/*     */   {
/*  87 */     return this.dcsPNRDeletedInd;
/*     */   }
/*     */ 
/*     */   public void setDcsPNRDeletedInd(String dcsPNRDeletedInd)
/*     */   {
/*  94 */     this.dcsPNRDeletedInd = dcsPNRDeletedInd;
/*     */   }
/*     */ 
/*     */   public FlightInfo getFlightInfo()
/*     */   {
/* 101 */     return this.flightInfo;
/*     */   }
/*     */ 
/*     */   public void setFlightInfo(FlightInfo flightInfo)
/*     */   {
/* 108 */     this.flightInfo = flightInfo;
/*     */   }
/*     */ 
/*     */   public Reservation getReservation()
/*     */   {
/* 115 */     return this.reservation;
/*     */   }
/*     */ 
/*     */   public void setReservation(Reservation reservation)
/*     */   {
/* 122 */     this.reservation = reservation;
/*     */   }
/*     */ 
/*     */   public List<SpecialRequestService> getServiceList()
/*     */   {
/* 129 */     return this.serviceList;
/*     */   }
/*     */ 
/*     */   public void setServiceList(List<SpecialRequestService> serviceList)
/*     */   {
/* 136 */     this.serviceList = serviceList;
/*     */   }
/*     */ 
/*     */   public Delivery getDelivery()
/*     */   {
/* 143 */     return this.delivery;
/*     */   }
/*     */ 
/*     */   public void setDelivery(Delivery delivery)
/*     */   {
/* 150 */     this.delivery = delivery;
/*     */   }
/*     */ 
/*     */   public TicketInfo getTicket()
/*     */   {
/* 157 */     return this.ticket;
/*     */   }
/*     */ 
/*     */   public void setTicket(TicketInfo ticket)
/*     */   {
/* 164 */     this.ticket = ticket;
/*     */   }
/*     */ 
/*     */   public TicketInfo getInfantTicket()
/*     */   {
/* 171 */     return this.infantTicket;
/*     */   }
/*     */ 
/*     */   public void setInfantTicket(TicketInfo infantTicket)
/*     */   {
/* 178 */     this.infantTicket = infantTicket;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Segment
 * JD-Core Version:    0.6.0
 */