/*
 * Copyright (C) 2016 eKing Technology, Inc. All Rights Reserved.
 */
package com.hna.shopping.ibe.controller.rest;

import com.hna.shopping.ibe.common.responsecode.RestResponse;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(description = "最基础功能示例")
@RestController
@RequestMapping("/api/welcome")
@Slf4j
public class WelcomeController {
    @RequestMapping(value = "", method = RequestMethod.GET)
    RestResponse<String> welcome(@RequestParam(value = "name") String name) {
        return RestResponse.ok(name);
    }
}
