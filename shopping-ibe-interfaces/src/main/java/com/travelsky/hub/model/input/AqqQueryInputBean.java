/*    */ package com.travelsky.hub.model.input;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class AqqQueryInputBean
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 1L;
/*    */   private String airlineCode;
/*    */   private String destCity;
/*    */   private String deptCity;
/*    */   private String flightNumber;
/*    */ 
/*    */   public String getFlightNumber()
/*    */   {
/* 35 */     return this.flightNumber;
/*    */   }
/*    */ 
/*    */   public void setFlightNumber(String flightNumber)
/*    */   {
/* 43 */     this.flightNumber = flightNumber;
/*    */   }
/*    */ 
/*    */   public String getDestCity()
/*    */   {
/* 51 */     return this.destCity;
/*    */   }
/*    */ 
/*    */   public void setDestCity(String destCity)
/*    */   {
/* 59 */     this.destCity = destCity;
/*    */   }
/*    */ 
/*    */   public String getDeptCity()
/*    */   {
/* 67 */     return this.deptCity;
/*    */   }
/*    */ 
/*    */   public void setDeptCity(String deptCity)
/*    */   {
/* 75 */     this.deptCity = deptCity;
/*    */   }
/*    */ 
/*    */   public String getAirlineCode()
/*    */   {
/* 82 */     return this.airlineCode;
/*    */   }
/*    */ 
/*    */   public void setAirlineCode(String airlineCode)
/*    */   {
/* 90 */     this.airlineCode = airlineCode;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.AqqQueryInputBean
 * JD-Core Version:    0.6.0
 */