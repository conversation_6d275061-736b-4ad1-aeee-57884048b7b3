/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SupplementaryName
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3154130494257401592L;
/*    */   private String overfolwInd;
/*    */   private String text;
/*    */ 
/*    */   public String getOverfolwInd()
/*    */   {
/* 30 */     return this.overfolwInd;
/*    */   }
/*    */ 
/*    */   public void setOverfolwInd(String overfolwInd)
/*    */   {
/* 37 */     this.overfolwInd = overfolwInd;
/*    */   }
/*    */ 
/*    */   public String getText()
/*    */   {
/* 44 */     return this.text;
/*    */   }
/*    */ 
/*    */   public void setText(String text)
/*    */   {
/* 51 */     this.text = text;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.SupplementaryName
 * JD-Core Version:    0.6.0
 */