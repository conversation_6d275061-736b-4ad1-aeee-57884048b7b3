package com.hna.shopping.ibe.interfaces.dto.checkin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: deyi
 * @Date: 2020/11/25 9:35
 * @Version 1.0
 */
@Data
public class SeatChartRS {
    private String resultCode;
    private String resultMessage;
    private List<LineInfo> lineInfos;
    private SeatMap seatMapInfo;

    @Data
    public static class LineInfo{
        private String compartmentId;
        private String emptyLine;
        private String legId;
        private String lineAttr;
        private String lineId;
        private String lineNo;
        private String lineSeq;
        private List<SeatInfo> seatInfos;
    }

    @Data
    public static class SeatInfo{
        private String seatNumber;
        private String deckCode;
        private String seatValue;
        private String seatValueEx;
        private String seatChar;
        private String fareAmount;
        private String fareCurrency;
        private String andOrIndicator;
        private String mileageFee;
        private String row;
        private String col;
    }

}
