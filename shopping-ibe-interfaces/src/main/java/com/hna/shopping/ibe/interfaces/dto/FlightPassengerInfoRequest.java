package com.hna.shopping.ibe.interfaces.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class FlightPassengerInfoRequest implements Serializable {

	private static final long serialVersionUID = -876864144246838384L;

	/**
	 * 航班日期。如：2024-06-10  	必输，只能为指定日期，不能为日期范围
	 */
	private String flightDate;

	/**
	 * 航班号。必填 如：JD5762  HU496
	 */
	private String flightNo;

	/**
	 * 出发地. 如：PKX
	 */
	private String depCode;

	/**
	 * 目的地. 如：HAK
	 */
	private String arrCode;

	/**
	 * 客票号. 如	880-123456789
	 */
	private String ticketNo;

}
