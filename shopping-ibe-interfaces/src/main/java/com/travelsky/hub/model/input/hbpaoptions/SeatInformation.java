/*     */ package com.travelsky.hub.model.input.hbpaoptions;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class SeatInformation
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 7309447279799755460L;
/*     */   private String seatAttribute;
/*     */   private String assignSeatType;
/*     */   private String seatNo;
/*     */   private String startRow;
/*     */   private String preference;
/*     */ 
/*     */   public String getSeatAttribute()
/*     */   {
/*  38 */     return this.seatAttribute;
/*     */   }
/*     */ 
/*     */   public void setSeatAttribute(String seatAttribute)
/*     */   {
/*  45 */     this.seatAttribute = seatAttribute;
/*     */   }
/*     */ 
/*     */   public String getAssignSeatType()
/*     */   {
/*  52 */     return this.assignSeatType;
/*     */   }
/*     */ 
/*     */   public void setAssignSeatType(String assignSeatType)
/*     */   {
/*  59 */     this.assignSeatType = assignSeatType;
/*     */   }
/*     */ 
/*     */   public String getSeatNo()
/*     */   {
/*  66 */     return this.seatNo;
/*     */   }
/*     */ 
/*     */   public void setSeatNo(String seatNo)
/*     */   {
/*  73 */     this.seatNo = seatNo;
/*     */   }
/*     */ 
/*     */   public String getStartRow()
/*     */   {
/*  80 */     return this.startRow;
/*     */   }
/*     */ 
/*     */   public void setStartRow(String startRow)
/*     */   {
/*  87 */     this.startRow = startRow;
/*     */   }
/*     */ 
/*     */   public String getPreference()
/*     */   {
/*  94 */     return this.preference;
/*     */   }
/*     */ 
/*     */   public void setPreference(String preference)
/*     */   {
/* 101 */     this.preference = preference;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.input.hbpaoptions.SeatInformation
 * JD-Core Version:    0.6.0
 */