/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class Segment
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -9095726425600411075L;
/*     */   private String flightNumber;
/*     */   private String transitBoolean;
/*     */   private String carrierCode;
/*     */   private String segmentOrdinal;
/*     */   private String departureAirport;
/*     */   private String departureDateTime;
/*     */   private String arrivalAirport;
/*     */   private String arrivalDateTime;
/*     */   private String age;
/*     */   private String status;
/*     */   private String segmentGUID;
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/*  69 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public String getTransitBoolean()
/*     */   {
/*  76 */     return this.transitBoolean;
/*     */   }
/*     */ 
/*     */   public String getCarrierCode()
/*     */   {
/*  83 */     return this.carrierCode;
/*     */   }
/*     */ 
/*     */   public String getSegmentOrdinal()
/*     */   {
/*  90 */     return this.segmentOrdinal;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  97 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public String getDepartureDateTime()
/*     */   {
/* 104 */     return this.departureDateTime;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 111 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalDateTime()
/*     */   {
/* 118 */     return this.arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public String getAge()
/*     */   {
/* 125 */     return this.age;
/*     */   }
/*     */ 
/*     */   public String getStatus()
/*     */   {
/* 132 */     return this.status;
/*     */   }
/*     */ 
/*     */   public String getSegmentGUID()
/*     */   {
/* 139 */     return this.segmentGUID;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 146 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public void setTransitBoolean(String transitBoolean)
/*     */   {
/* 153 */     this.transitBoolean = transitBoolean;
/*     */   }
/*     */ 
/*     */   public void setCarrierCode(String carrierCode)
/*     */   {
/* 160 */     this.carrierCode = carrierCode;
/*     */   }
/*     */ 
/*     */   public void setSegmentOrdinal(String segmentOrdinal)
/*     */   {
/* 167 */     this.segmentOrdinal = segmentOrdinal;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 174 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureDateTime(String departureDateTime)
/*     */   {
/* 181 */     this.departureDateTime = departureDateTime;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 188 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalDateTime(String arrivalDateTime)
/*     */   {
/* 195 */     this.arrivalDateTime = arrivalDateTime;
/*     */   }
/*     */ 
/*     */   public void setAge(String age)
/*     */   {
/* 202 */     this.age = age;
/*     */   }
/*     */ 
/*     */   public void setStatus(String status)
/*     */   {
/* 209 */     this.status = status;
/*     */   }
/*     */ 
/*     */   public void setSegmentGUID(String segmentGUID)
/*     */   {
/* 216 */     this.segmentGUID = segmentGUID;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.Segment
 * JD-Core Version:    0.6.0
 */