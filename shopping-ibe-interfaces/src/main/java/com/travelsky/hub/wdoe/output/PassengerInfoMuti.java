/*     */ package com.travelsky.hub.wdoe.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ import java.util.List;
/*     */ 
/*     */ public class PassengerInfoMuti
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -1018084312L;
/*  15 */   private String airlineCode = "";
/*  16 */   private String flightNumber = "";
/*  17 */   private String departureDate = "";
/*  18 */   private String departureAirport = "";
/*  19 */   private String arrivalAirport = "";
/*  20 */   private String surName = "";
/*  21 */   private String chnName = "";
/*  22 */   private String cabinType = "";
/*  23 */   private String ticketID = "";
/*     */ 
/*  25 */   private String boardingNumber = "";
/*  26 */   private String seatNumber = "";
/*  27 */   private String docType = "";
/*  28 */   private String docID = "";
/*     */ 
/*  31 */   private String rph = "";
/*  32 */   private String bagWeight = "";
/*  33 */   private String bagQuantity = "";
/*  34 */   private String bagArrivalAirport = "";
/*     */   private List bagTagList;
/*     */ 
/*     */   public String getBagArrivalAirport()
/*     */   {
/*  43 */     return this.bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setBagArrivalAirport(String bagArrivalAirport)
/*     */   {
/*  51 */     this.bagArrivalAirport = bagArrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getBagQuantity()
/*     */   {
/*  59 */     return this.bagQuantity;
/*     */   }
/*     */ 
/*     */   public void setBagQuantity(String bagQuantity)
/*     */   {
/*  67 */     this.bagQuantity = bagQuantity;
/*     */   }
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/*  75 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/*  83 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getDocID()
/*     */   {
/*  91 */     return this.docID;
/*     */   }
/*     */ 
/*     */   public void setDocID(String docID)
/*     */   {
/*  99 */     this.docID = docID;
/*     */   }
/*     */ 
/*     */   public String getDocType()
/*     */   {
/* 107 */     return this.docType;
/*     */   }
/*     */ 
/*     */   public void setDocType(String docType)
/*     */   {
/* 115 */     this.docType = docType;
/*     */   }
/*     */ 
/*     */   public String getRPH()
/*     */   {
/* 123 */     return this.rph;
/*     */   }
/*     */ 
/*     */   public void setRPH(String rph)
/*     */   {
/* 131 */     this.rph = rph;
/*     */   }
/*     */ 
/*     */   public String getSurName()
/*     */   {
/* 139 */     return this.surName;
/*     */   }
/*     */ 
/*     */   public void setSurName(String surName)
/*     */   {
/* 147 */     this.surName = surName;
/*     */   }
/*     */ 
/*     */   public List getBagTagList()
/*     */   {
/* 154 */     return this.bagTagList;
/*     */   }
/*     */ 
/*     */   public void setBagTagList(List bagTagList)
/*     */   {
/* 161 */     this.bagTagList = bagTagList;
/*     */   }
/*     */ 
/*     */   public String getChnName()
/*     */   {
/* 169 */     return this.chnName;
/*     */   }
/*     */ 
/*     */   public void setChnName(String chnName)
/*     */   {
/* 177 */     this.chnName = chnName;
/*     */   }
/*     */ 
/*     */   public String getAirlineCode()
/*     */   {
/* 184 */     return this.airlineCode;
/*     */   }
/*     */ 
/*     */   public void setAirlineCode(String airlineCode)
/*     */   {
/* 191 */     this.airlineCode = airlineCode;
/*     */   }
/*     */ 
/*     */   public String getFlightNumber()
/*     */   {
/* 198 */     return this.flightNumber;
/*     */   }
/*     */ 
/*     */   public void setFlightNumber(String flightNumber)
/*     */   {
/* 205 */     this.flightNumber = flightNumber;
/*     */   }
/*     */ 
/*     */   public String getDepartureDate()
/*     */   {
/* 212 */     return this.departureDate;
/*     */   }
/*     */ 
/*     */   public void setDepartureDate(String departureDate)
/*     */   {
/* 219 */     this.departureDate = departureDate;
/*     */   }
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/* 226 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/* 233 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/* 240 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/* 247 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getCabinType()
/*     */   {
/* 254 */     return this.cabinType;
/*     */   }
/*     */ 
/*     */   public void setCabinType(String cabinType)
/*     */   {
/* 261 */     this.cabinType = cabinType;
/*     */   }
/*     */ 
/*     */   public String getTicketID()
/*     */   {
/* 268 */     return this.ticketID;
/*     */   }
/*     */ 
/*     */   public void setTicketID(String ticketID)
/*     */   {
/* 275 */     this.ticketID = ticketID;
/*     */   }
/*     */ 
/*     */   public String getBoardingNumber()
/*     */   {
/* 282 */     return this.boardingNumber;
/*     */   }
/*     */ 
/*     */   public void setBoardingNumber(String boardingNumber)
/*     */   {
/* 289 */     this.boardingNumber = boardingNumber;
/*     */   }
/*     */ 
/*     */   public String getSeatNumber()
/*     */   {
/* 296 */     return this.seatNumber;
/*     */   }
/*     */ 
/*     */   public void setSeatNumber(String seatNumber)
/*     */   {
/* 303 */     this.seatNumber = seatNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.wdoe.output.PassengerInfoMuti
 * JD-Core Version:    0.6.0
 */