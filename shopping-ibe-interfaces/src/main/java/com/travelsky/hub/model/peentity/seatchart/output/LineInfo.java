/*    */ package com.travelsky.hub.model.peentity.seatchart.output;
/*    */ 
///*    */ import com.alibaba.fastjson.annotation.JSONField;
/*    */ import java.io.Serializable;
/*    */ import java.util.List;
/*    */ 
/*    */ public class LineInfo
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7920337750516295549L;
/*    */   private String compartmentID;
/*    */   private String emptyLine;
/*    */ 
///*    */   @JSONField(name="legId")
/*    */   private String legID;
/*    */   private String lineAttr;
/*    */ 
///*    */   @JSONField(name="lineId")
/*    */   private String lineID;
/*    */   private String lineNo;
/*    */   private String lineSeq;
/*    */   private List<SeatInfo> seatInfos;
/*    */ 
/*    */   public void setCompartmentID(String compartmentID)
/*    */   {
/* 14 */     this.compartmentID = compartmentID; } 
/* 14 */   public void setEmptyLine(String emptyLine) { this.emptyLine = emptyLine; } 
/* 14 */   public void setLegID(String legID) { this.legID = legID; } 
/* 14 */   public void setLineAttr(String lineAttr) { this.lineAttr = lineAttr; } 
/* 14 */   public void setLineID(String lineID) { this.lineID = lineID; } 
/* 14 */   public void setLineNo(String lineNo) { this.lineNo = lineNo; } 
/* 14 */   public void setLineSeq(String lineSeq) { this.lineSeq = lineSeq; } 
/* 14 */   public void setSeatInfos(List<SeatInfo> seatInfos) { this.seatInfos = seatInfos;
/*    */   }
/*    */ 
/*    */   public String getCompartmentID()
/*    */   {
/* 26 */     return this.compartmentID;
/*    */   }
/*    */ 
/*    */   public String getEmptyLine()
/*    */   {
/* 31 */     return this.emptyLine;
/*    */   }
/*    */ 
/*    */   public String getLegID()
/*    */   {
/* 37 */     return this.legID;
/*    */   }
/*    */ 
/*    */   public String getLineAttr()
/*    */   {
/* 42 */     return this.lineAttr;
/*    */   }
/*    */ 
/*    */   public String getLineID()
/*    */   {
/* 48 */     return this.lineID;
/*    */   }
/*    */ 
/*    */   public String getLineNo()
/*    */   {
/* 53 */     return this.lineNo;
/*    */   }
/*    */ 
/*    */   public String getLineSeq()
/*    */   {
/* 58 */     return this.lineSeq;
/*    */   }
/*    */ 
/*    */   public List<SeatInfo> getSeatInfos()
/*    */   {
/* 63 */     return this.seatInfos;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.seatchart.output.LineInfo
 * JD-Core Version:    0.6.0
 */