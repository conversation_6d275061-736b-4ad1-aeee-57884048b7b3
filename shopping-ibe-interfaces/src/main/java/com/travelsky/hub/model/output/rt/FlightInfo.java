/*     */ package com.travelsky.hub.model.output.rt;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class FlightInfo
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 3254581925537586947L;
/*     */   private String infomationSegmentInd;
/*     */   private String classGrade;
/*     */   private String codeShare;
/*     */   private String openInd;
/*     */   private String codeShareInd;
/*     */   private String arnkInd;
/*     */   private Arrival arrival;
/*     */   private Depature dep;
/*     */   private CarrierInfo marketingCarrierInfo;
/*     */   private CarrierInfo operatingCarrierInfo;
/*     */ 
/*     */   public String getInfomationSegmentInd()
/*     */   {
/*  62 */     return this.infomationSegmentInd;
/*     */   }
/*     */ 
/*     */   public void setInfomationSegmentInd(String infomationSegmentInd)
/*     */   {
/*  69 */     this.infomationSegmentInd = infomationSegmentInd;
/*     */   }
/*     */ 
/*     */   public String getClassGrade()
/*     */   {
/*  76 */     return this.classGrade;
/*     */   }
/*     */ 
/*     */   public void setClassGrade(String classGrade)
/*     */   {
/*  83 */     this.classGrade = classGrade;
/*     */   }
/*     */ 
/*     */   public String getCodeShare()
/*     */   {
/*  90 */     return this.codeShare;
/*     */   }
/*     */ 
/*     */   public void setCodeShare(String codeShare)
/*     */   {
/*  97 */     this.codeShare = codeShare;
/*     */   }
/*     */ 
/*     */   public String getOpenInd()
/*     */   {
/* 104 */     return this.openInd;
/*     */   }
/*     */ 
/*     */   public void setOpenInd(String openInd)
/*     */   {
/* 111 */     this.openInd = openInd;
/*     */   }
/*     */ 
/*     */   public String getCodeShareInd()
/*     */   {
/* 118 */     return this.codeShareInd;
/*     */   }
/*     */ 
/*     */   public void setCodeShareInd(String codeShareInd)
/*     */   {
/* 125 */     this.codeShareInd = codeShareInd;
/*     */   }
/*     */ 
/*     */   public String getArnkInd()
/*     */   {
/* 132 */     return this.arnkInd;
/*     */   }
/*     */ 
/*     */   public void setArnkInd(String arnkInd)
/*     */   {
/* 139 */     this.arnkInd = arnkInd;
/*     */   }
/*     */ 
/*     */   public Arrival getArrival()
/*     */   {
/* 146 */     return this.arrival;
/*     */   }
/*     */ 
/*     */   public void setArrival(Arrival arrival)
/*     */   {
/* 153 */     this.arrival = arrival;
/*     */   }
/*     */ 
/*     */   public Depature getDep()
/*     */   {
/* 160 */     return this.dep;
/*     */   }
/*     */ 
/*     */   public void setDep(Depature dep)
/*     */   {
/* 167 */     this.dep = dep;
/*     */   }
/*     */ 
/*     */   public CarrierInfo getMarketingCarrierInfo()
/*     */   {
/* 174 */     return this.marketingCarrierInfo;
/*     */   }
/*     */ 
/*     */   public void setMarketingCarrierInfo(CarrierInfo marketingCarrierInfo)
/*     */   {
/* 181 */     this.marketingCarrierInfo = marketingCarrierInfo;
/*     */   }
/*     */ 
/*     */   public CarrierInfo getOperatingCarrierInfo()
/*     */   {
/* 188 */     return this.operatingCarrierInfo;
/*     */   }
/*     */ 
/*     */   public void setOperatingCarrierInfo(CarrierInfo operatingCarrierInfo)
/*     */   {
/* 195 */     this.operatingCarrierInfo = operatingCarrierInfo;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.FlightInfo
 * JD-Core Version:    0.6.0
 */