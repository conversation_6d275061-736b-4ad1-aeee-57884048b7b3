/*    */ package com.travelsky.hub.model.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class IdentityInformation
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = -7176454920078876592L;
/*    */   private String foidNumber;
/*    */   private String foidType;
/*    */ 
/*    */   public String getFoidType()
/*    */   {
/* 25 */     return this.foidType;
/*    */   }
/*    */ 
/*    */   public void setFoidType(String foidType)
/*    */   {
/* 32 */     this.foidType = foidType;
/*    */   }
/*    */ 
/*    */   public String getFoidNumber()
/*    */   {
/* 38 */     return this.foidNumber;
/*    */   }
/*    */ 
/*    */   public void setFoidNumber(String foidNumber)
/*    */   {
/* 45 */     this.foidNumber = foidNumber;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.IdentityInformation
 * JD-Core Version:    0.6.0
 */