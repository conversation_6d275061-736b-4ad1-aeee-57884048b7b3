/*    */ package com.travelsky.hub.model.peentity.trr.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Capacity
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3330118173314870977L;
/*    */   private Integer cap;
/*    */   private Integer maxSeat;
/*    */   private Integer bkdSeat;
/*    */   private Integer availableSeat;
/*    */ 
/*    */   public Integer getCap()
/*    */   {
/* 47 */     return this.cap;
/*    */   }
/*    */ 
/*    */   public void setCap(Integer cap)
/*    */   {
/* 54 */     this.cap = cap;
/*    */   }
/*    */ 
/*    */   public Integer getMaxSeat()
/*    */   {
/* 61 */     return this.maxSeat;
/*    */   }
/*    */ 
/*    */   public void setMaxSeat(Integer maxSeat)
/*    */   {
/* 68 */     this.maxSeat = maxSeat;
/*    */   }
/*    */ 
/*    */   public Integer getBkdSeat()
/*    */   {
/* 75 */     return this.bkdSeat;
/*    */   }
/*    */ 
/*    */   public void setBkdSeat(Integer bkdSeat)
/*    */   {
/* 82 */     this.bkdSeat = bkdSeat;
/*    */   }
/*    */ 
/*    */   public Integer getAvailableSeat()
/*    */   {
/* 89 */     return this.availableSeat;
/*    */   }
/*    */ 
/*    */   public void setAvailableSeat(Integer availableSeat)
/*    */   {
/* 96 */     this.availableSeat = availableSeat;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.Capacity
 * JD-Core Version:    0.6.0
 */