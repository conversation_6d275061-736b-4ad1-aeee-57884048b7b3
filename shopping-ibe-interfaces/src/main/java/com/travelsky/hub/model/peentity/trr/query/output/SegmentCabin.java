/*    */ package com.travelsky.hub.model.peentity.trr.query.output;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class SegmentCabin
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 3031980079424477495L;
/*    */   private String RPH;
/*    */   private CapacityMap capacityMap;
/*    */   private String isCabinFull;
/*    */   private SemantVo semantVo;
/*    */ 
/*    */   public String getRPH()
/*    */   {
/* 47 */     return this.RPH;
/*    */   }
/*    */ 
/*    */   public void setRPH(String RPH)
/*    */   {
/* 54 */     this.RPH = RPH;
/*    */   }
/*    */ 
/*    */   public CapacityMap getCapacityMap()
/*    */   {
/* 61 */     return this.capacityMap;
/*    */   }
/*    */ 
/*    */   public void setCapacityMap(CapacityMap capacityMap)
/*    */   {
/* 68 */     this.capacityMap = capacityMap;
/*    */   }
/*    */ 
/*    */   public String getIsCabinFull()
/*    */   {
/* 75 */     return this.isCabinFull;
/*    */   }
/*    */ 
/*    */   public void setIsCabinFull(String isCabinFull)
/*    */   {
/* 82 */     this.isCabinFull = isCabinFull;
/*    */   }
/*    */ 
/*    */   public SemantVo getSemantVo()
/*    */   {
/* 89 */     return this.semantVo;
/*    */   }
/*    */ 
/*    */   public void setSemantVo(SemantVo semantVo)
/*    */   {
/* 96 */     this.semantVo = semantVo;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.peentity.trr.query.output.SegmentCabin
 * JD-Core Version:    0.6.0
 */