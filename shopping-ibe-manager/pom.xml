<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hna.shopping.ibe</groupId>
        <artifactId>shopping-ibe-parent</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>shopping-ibe-manager</artifactId>
    
    <repositories>
        <!-- 配置nexus远程仓库 -->
        <repository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <!-- 配置从哪个仓库中下载构件，即jar包 -->
    <pluginRepositories>
        <pluginRepository>
            <id>thirdparty</id>
            <name>thirdparty</name>
            <url>http://maven.haihangyun.com/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <dependencies>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-config</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
         <dependency>
             <groupId>com.hna.shopping.ibe</groupId>
             <artifactId>shopping-ibe-dao</artifactId>
             <version>0.0.1-SNAPSHOT</version>
         </dependency>
        <dependency>
            <groupId>com.hna.shopping.ibe</groupId>
            <artifactId>shopping-ibe-interfaces</artifactId>
            <version>0.1.6-SNAPSHOT</version>
        </dependency>

<!--         <dependency>-->
<!--             <groupId>ebuildapi</groupId>-->
<!--             <artifactId>ebuildapi</artifactId>-->
<!--             <version>2.0.0</version>-->
<!--         </dependency>-->
        <dependency>
            <groupId>ebuildapi</groupId>
            <artifactId>ebuildapi-enc-2f</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>fakepath</groupId>
            <artifactId>ebuild-axi</artifactId>
            <version>1.7.0</version>
        </dependency>
        <!--<dependency>-->
        	<!--<groupId>org.springframework.boot</groupId>-->
        	<!--<artifactId>spring-boot-starter-data-cassandra</artifactId>-->
        <!--</dependency>-->
    </dependencies>


</project>