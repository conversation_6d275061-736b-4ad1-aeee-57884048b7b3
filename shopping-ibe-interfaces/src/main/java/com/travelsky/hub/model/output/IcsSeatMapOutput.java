/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class IcsSeatMapOutput
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = -8957215688716166757L;
/*     */   private String departureAirport;
/*     */   private String arrivalAirport;
/*     */   private String planeType;
/*     */   private String sequenceNumber;
/*     */   private String seatMap;
/*     */   private String seatMapOld;
/*     */ 
/*     */   public String getDepartureAirport()
/*     */   {
/*  44 */     return this.departureAirport;
/*     */   }
/*     */ 
/*     */   public void setDepartureAirport(String departureAirport)
/*     */   {
/*  51 */     this.departureAirport = departureAirport;
/*     */   }
/*     */ 
/*     */   public String getArrivalAirport()
/*     */   {
/*  58 */     return this.arrivalAirport;
/*     */   }
/*     */ 
/*     */   public void setArrivalAirport(String arrivalAirport)
/*     */   {
/*  65 */     this.arrivalAirport = arrivalAirport;
/*     */   }
/*     */ 
/*     */   public String getPlaneType()
/*     */   {
/*  72 */     return this.planeType;
/*     */   }
/*     */ 
/*     */   public void setPlaneType(String planeType)
/*     */   {
/*  79 */     this.planeType = planeType;
/*     */   }
/*     */ 
/*     */   public String getSeatMap()
/*     */   {
/*  86 */     return this.seatMap;
/*     */   }
/*     */ 
/*     */   public void setSeatMap(String seatMap)
/*     */   {
/*  93 */     this.seatMap = seatMap;
/*     */   }
/*     */ 
/*     */   public String getSeatMapOld()
/*     */   {
/* 100 */     return this.seatMapOld;
/*     */   }
/*     */ 
/*     */   public void setSeatMapOld(String seatMapOld)
/*     */   {
/* 107 */     this.seatMapOld = seatMapOld;
/*     */   }
/*     */ 
/*     */   public String getSequenceNumber()
/*     */   {
/* 114 */     return this.sequenceNumber;
/*     */   }
/*     */ 
/*     */   public void setSequenceNumber(String sequenceNumber)
/*     */   {
/* 121 */     this.sequenceNumber = sequenceNumber;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.IcsSeatMapOutput
 * JD-Core Version:    0.6.0
 */