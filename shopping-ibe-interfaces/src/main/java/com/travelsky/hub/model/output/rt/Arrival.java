/*    */ package com.travelsky.hub.model.output.rt;
/*    */ 
/*    */ import java.io.Serializable;
/*    */ 
/*    */ public class Arrival
/*    */   implements Serializable
/*    */ {
/*    */   private static final long serialVersionUID = 8262836301222240340L;
/*    */   private String arrivalDayChange;
/*    */   private String aircraftScheduledDateTime;
/*    */   private String destination;
/*    */   private String terminalName;
/*    */ 
/*    */   public String getArrivalDayChange()
/*    */   {
/* 44 */     return this.arrivalDayChange;
/*    */   }
/*    */ 
/*    */   public void setArrivalDayChange(String arrivalDayChange)
/*    */   {
/* 51 */     this.arrivalDayChange = arrivalDayChange;
/*    */   }
/*    */ 
/*    */   public String getAircraftScheduledDateTime()
/*    */   {
/* 58 */     return this.aircraftScheduledDateTime;
/*    */   }
/*    */ 
/*    */   public void setAircraftScheduledDateTime(String aircraftScheduledDateTime)
/*    */   {
/* 65 */     this.aircraftScheduledDateTime = aircraftScheduledDateTime;
/*    */   }
/*    */ 
/*    */   public String getDestination()
/*    */   {
/* 72 */     return this.destination;
/*    */   }
/*    */ 
/*    */   public void setDestination(String destination)
/*    */   {
/* 79 */     this.destination = destination;
/*    */   }
/*    */ 
/*    */   public String getTerminalName()
/*    */   {
/* 86 */     return this.terminalName;
/*    */   }
/*    */ 
/*    */   public void setTerminalName(String terminalName)
/*    */   {
/* 93 */     this.terminalName = terminalName;
/*    */   }
/*    */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.rt.Arrival
 * JD-Core Version:    0.6.0
 */