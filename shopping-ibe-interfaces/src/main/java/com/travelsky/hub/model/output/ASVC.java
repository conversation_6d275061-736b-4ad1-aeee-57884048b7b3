/*     */ package com.travelsky.hub.model.output;
/*     */ 
/*     */ import java.io.Serializable;
/*     */ 
/*     */ public class ASVC
/*     */   implements Serializable
/*     */ {
/*     */   private static final long serialVersionUID = 1L;
/*     */   private String emdTypeCode;
/*     */   private String ssrCode;
/*     */   private String subCode;
/*     */   private String documentNum;
/*     */   private String couponNum;
/*     */   private String emdStatus;
/*     */   private String bagWeight;
/*     */   private String expense;
/*     */   private String connectionInfo;
/*     */   private String autoIndicator;
/*     */ 
/*     */   public String getBagWeight()
/*     */   {
/*  70 */     return this.bagWeight;
/*     */   }
/*     */ 
/*     */   public void setBagWeight(String bagWeight)
/*     */   {
/*  77 */     this.bagWeight = bagWeight;
/*     */   }
/*     */ 
/*     */   public String getExpense()
/*     */   {
/*  84 */     return this.expense;
/*     */   }
/*     */ 
/*     */   public void setExpense(String expense)
/*     */   {
/*  91 */     this.expense = expense;
/*     */   }
/*     */ 
/*     */   public String getAutoIndicator()
/*     */   {
/*  98 */     return this.autoIndicator;
/*     */   }
/*     */ 
/*     */   public void setAutoIndicator(String autoIndicator)
/*     */   {
/* 105 */     this.autoIndicator = autoIndicator;
/*     */   }
/*     */ 
/*     */   public String getConnectionInfo()
/*     */   {
/* 113 */     return this.connectionInfo;
/*     */   }
/*     */ 
/*     */   public void setConnectionInfo(String connectionInfo)
/*     */   {
/* 120 */     this.connectionInfo = connectionInfo;
/*     */   }
/*     */ 
/*     */   public String getEmdTypeCode()
/*     */   {
/* 128 */     return this.emdTypeCode;
/*     */   }
/*     */ 
/*     */   public void setEmdTypeCode(String emdTypeCode)
/*     */   {
/* 136 */     this.emdTypeCode = emdTypeCode;
/*     */   }
/*     */ 
/*     */   public String getSsrCode()
/*     */   {
/* 144 */     return this.ssrCode;
/*     */   }
/*     */ 
/*     */   public void setSsrCode(String ssrCode)
/*     */   {
/* 152 */     this.ssrCode = ssrCode;
/*     */   }
/*     */ 
/*     */   public String getSubCode()
/*     */   {
/* 160 */     return this.subCode;
/*     */   }
/*     */ 
/*     */   public void setSubCode(String subCode)
/*     */   {
/* 168 */     this.subCode = subCode;
/*     */   }
/*     */ 
/*     */   public String getDocumentNum()
/*     */   {
/* 176 */     return this.documentNum;
/*     */   }
/*     */ 
/*     */   public void setDocumentNum(String documentNum)
/*     */   {
/* 184 */     this.documentNum = documentNum;
/*     */   }
/*     */ 
/*     */   public String getCouponNum()
/*     */   {
/* 191 */     return this.couponNum;
/*     */   }
/*     */ 
/*     */   public void setCouponNum(String couponNum)
/*     */   {
/* 199 */     this.couponNum = couponNum;
/*     */   }
/*     */ 
/*     */   public String getEmdStatus()
/*     */   {
/* 207 */     return this.emdStatus;
/*     */   }
/*     */ 
/*     */   public void setEmdStatus(String emdStatus)
/*     */   {
/* 215 */     this.emdStatus = emdStatus;
/*     */   }
/*     */ }

/* Location:           D:\5.9.6\TravelskySvc.jar
 * Qualified Name:     com.travelsky.hub.model.output.ASVC
 * JD-Core Version:    0.6.0
 */